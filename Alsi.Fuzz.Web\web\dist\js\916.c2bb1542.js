"use strict";(self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[]).push([[916],{6916:function(e,t,a){a.r(t),a.d(t,{default:function(){return h}});a(4114);var n=a(6768),l=a(4232),s=a(1387),u=a(1219),i=a(144),d=a(7477);const c={class:"side-nav-wrapper"};var r={__name:"SideNav",setup(e){const t=(0,i.KR)(!1),a=(0,s.lq)(),u=()=>{t.value=!t.value};return(e,s)=>{const r=(0,n.g2)("el-icon"),o=(0,n.g2)("el-menu-item"),p=(0,n.g2)("font-awesome-icon"),k=(0,n.g2)("el-menu");return(0,n.uX)(),(0,n.CE)("div",c,[(0,n.bF)(k,{collapse:t.value,"collapse-transition":!1,class:(0,l.C4)(["side-nav",{"side-nav-collapsed":t.value}]),router:"","default-active":(0,i.R1)(a).path},{default:(0,n.k6)((()=>[(0,n.bF)(o,{index:"/test-plan"},{default:(0,n.k6)((()=>[(0,n.bF)(r,null,{default:(0,n.k6)((()=>[(0,n.bF)((0,i.R1)(d.Document))])),_:1}),s[0]||(s[0]=(0,n.Lk)("span",null,"Basic Setting",-1))])),_:1}),(0,n.bF)(o,{index:"/test-plan/hardware"},{default:(0,n.k6)((()=>[(0,n.bF)(p,{icon:"sliders"}),s[1]||(s[1]=(0,n.Lk)("span",null,"Hardware Setting",-1))])),_:1}),(0,n.bF)(o,{index:"/test-plan/case-setting"},{default:(0,n.k6)((()=>[(0,n.bF)(r,null,{default:(0,n.k6)((()=>[(0,n.bF)((0,i.R1)(d.Setting))])),_:1}),s[2]||(s[2]=(0,n.Lk)("span",null,"Case Setting",-1))])),_:1}),(0,n.bF)(o,{index:"/test-plan/sequence-setting"},{default:(0,n.k6)((()=>[(0,n.bF)(p,{icon:"laptop-code"}),s[3]||(s[3]=(0,n.Lk)("span",null,"Sequence Setting",-1))])),_:1}),(0,n.bF)(o,{index:"/test-plan/interoperation"},{default:(0,n.k6)((()=>[(0,n.bF)(r,null,{default:(0,n.k6)((()=>[(0,n.bF)((0,i.R1)(d.Connection))])),_:1}),s[4]||(s[4]=(0,n.Lk)("span",null,"Interoperation",-1))])),_:1}),(0,n.bF)(o,{index:"/test-plan/test-cases"},{default:(0,n.k6)((()=>[(0,n.bF)(r,null,{default:(0,n.k6)((()=>[(0,n.bF)((0,i.R1)(d.List))])),_:1}),s[5]||(s[5]=(0,n.Lk)("span",null,"Test Cases",-1))])),_:1}),(0,n.bF)(o,{index:"/test-plan/test-run"},{default:(0,n.k6)((()=>[(0,n.bF)(r,null,{default:(0,n.k6)((()=>[(0,n.bF)((0,i.R1)(d.VideoPlay))])),_:1}),s[6]||(s[6]=(0,n.Lk)("span",null,"Test Run",-1))])),_:1}),(0,n.bF)(o,{index:"/test-plan/test-results"},{default:(0,n.k6)((()=>[(0,n.bF)(r,null,{default:(0,n.k6)((()=>[(0,n.bF)((0,i.R1)(d.DataAnalysis))])),_:1}),s[7]||(s[7]=(0,n.Lk)("span",null,"Results",-1))])),_:1})])),_:1},8,["collapse","class","default-active"]),(0,n.Lk)("div",{class:"sidebar-toggle",onClick:u},[(0,n.bF)(r,{size:16},{default:(0,n.k6)((()=>[((0,n.uX)(),(0,n.Wv)((0,n.$y)(t.value?"ArrowRight":"ArrowLeft")))])),_:1})])])}}},o=a(1241);const p=(0,o.A)(r,[["__scopeId","data-v-12d3cf9c"]]);var k=p,f=a(2616);const b={class:"testplan-container"},v={class:"panel-header"},F={class:"title-section"},_={class:"plan-content"},g={class:"side-menu"},L={class:"content-area"};var m=(0,n.pM)({__name:"TestPlanView",setup(e){const t=(0,s.rd)();f.f.setRouter(t);const a=f.f.getState(),i=(0,n.EW)((()=>a.currentPlan)),d=e=>{document.title=e?`FUZZ - ${e}`:"FUZZ"};return(0,n.sV)((async()=>{try{const e=await f.f.getCurrentPlan();e?d(e.manifest.name):(u.nk.error("No test plan is currently open"),t.push("/"))}catch(e){u.nk.error("Failed to load test plan"),t.push("/")}})),(e,t)=>{const a=(0,n.g2)("router-view"),s=(0,n.g2)("el-card");return(0,n.uX)(),(0,n.CE)("div",b,[(0,n.bF)(s,{class:"testplan-card"},{header:(0,n.k6)((()=>[(0,n.Lk)("div",v,[(0,n.Lk)("div",F,[(0,n.Lk)("h2",null,(0,l.v_)(i.value?.manifest.name),1)])])])),default:(0,n.k6)((()=>[(0,n.Lk)("div",_,[(0,n.Lk)("div",g,[(0,n.bF)(k)]),(0,n.Lk)("div",L,[(0,n.bF)(a)])])])),_:1})])}}});const w=(0,o.A)(m,[["__scopeId","data-v-f1364f7e"]]);var h=w}}]);
//# sourceMappingURL=916.c2bb1542.js.map