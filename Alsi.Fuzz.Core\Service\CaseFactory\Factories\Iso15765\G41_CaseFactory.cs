using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso15765
{
    public class G41_CaseFactory : CaseFactoryBase
    {
        public override CaseMutation[] Generate(MutationOptions options)
        {
            var list = new List<CaseMutation>();

            GenerateG411(list, options);

            GenerateG4113_RandomData(list, options);

            GenerateG411_InvalidHighNibble(list, options);

            GenerateG412(list, options);

            return list.ToArray();
        }
        
        private void GenerateG411(List<CaseMutation> list, MutationOptions options)
        {
            byte validHighNibble = 0x1;

            // FFDL = 4095
            var groupPath = Iso15765CaseGroupConsts.BigData().ProtocolFormat().ValidNpci().GivenParameters().Path;
            var ffDl = 4095;
            var caseMutation = CaseMutation.Create($"G4111-HighNibble_{validHighNibble:X}-FFDL_4095", groupPath)
                .MutateDlc(8)
                .Mutate(MutationFieldType.TP_BigData_FF_HighNibble, $"0x{validHighNibble:X}")
                .Mutate(MutationFieldType.TP_BigData_FF_DL_12b, $"{ffDl}")
                .MutateTpParameters(options.TpParametersBigDataPayload, options.TpParametersBigDataRepeat, ffDl);
            list.Add(caseMutation);

            // FFDL = 4094
            ffDl = 4094;
            caseMutation = CaseMutation.Create($"G4112-HighNibble_{validHighNibble:X}-FFDL_4094", groupPath)
                .MutateDlc(8)
                .Mutate(MutationFieldType.TP_BigData_FF_HighNibble, $"0x{validHighNibble:X}")
                .Mutate(MutationFieldType.TP_BigData_FF_DL_12b, $"{ffDl}")
                .MutateTpParameters(options.TpParametersBigDataPayload, options.TpParametersBigDataRepeat, ffDl);
            list.Add(caseMutation);
        }
        
        private void GenerateG4113_RandomData(List<CaseMutation> list, MutationOptions options)
        {
            var groupPath = Iso15765CaseGroupConsts.BigData().ProtocolFormat().ValidNpci().RandomParameters().Path;
            byte validHighNibble = 0x1;
            int ffDl = 4095;

            // 确定样本数量
            int sampleCount = options.Coverage == CoverageType.Normal ? 64 : 256;

            var lhsSampler = new LhsSampler();

            for (int i = 0; i < sampleCount; i++)
            {
                var tpParameters = new List<byte>();
                int remainingBytes = ffDl;

                while (remainingBytes > 0)
                {
                    int groupSize = Math.Min(16, remainingBytes);
                    var bytesGroup = lhsSampler.GenerateByteBasedSamples(groupSize, 1);
                    tpParameters.AddRange(bytesGroup.First());
                    remainingBytes -= groupSize;
                }

                var caseMutation = CaseMutation.Create($"G4113-HighNibble_{validHighNibble:X}-FFDL_{ffDl}-Random_{i + 1}",groupPath)
                    .MutateDlc(8)
                    .Mutate(MutationFieldType.TP_BigData_FF_HighNibble, $"0x{validHighNibble:X}")
                    .Mutate(MutationFieldType.TP_BigData_FF_DL_12b, $"{ffDl}")
                    .MutateTpParameters(tpParameters.ToArray());
                list.Add(caseMutation);
            }
        }

        private void GenerateG411_InvalidHighNibble(List<CaseMutation> list, MutationOptions options)
        {
            var groupPath = Iso15765CaseGroupConsts.BigData().ProtocolFormat().FirstFrame().InvalidNpci().InvalidByte1HighValidFfDl();
            byte[] invalidHighNibbles = new byte[] { 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0xA, 0xB, 0xC, 0xD, 0xE, 0xF };
            int ffDl = 4095;

            // 确定每个无效 HighNibble 的样本数量
            int samplesPerHighNibble = options.Coverage == CoverageType.Normal ? 2 : 8;

            foreach (var highNibble in invalidHighNibbles)
            {
                for (int i = 0; i < samplesPerHighNibble; i++)
                {
                    var caseMutation = CaseMutation.Create($"G4114-HighNibble_{highNibble:X}-FFDL_{ffDl}",groupPath)
                        .MutateDlc(8)
                        .Mutate(MutationFieldType.TP_BigData_FF_HighNibble, $"0x{highNibble:X}")
                        .Mutate(MutationFieldType.TP_BigData_FF_DL_12b, $"{ffDl}")
                        .MutateTpParameters(options.TpParametersBigDataPayload, options.TpParametersBigDataRepeat, ffDl);
                    list.Add(caseMutation);
                }
            }
        }

        private void GenerateG412(List<CaseMutation> list, MutationOptions options)
        {
            var groupPath = Iso15765CaseGroupConsts.BigData().ProtocolFormat().ConsecutiveFrame().InvalidNpci().InvalidByte1HighValidSN();
            // 定义要测试的 SN 值
            int[] targetSNs = new[] { 1, 30, 59, 88, 117, 146, 175, 204, 233, 262, 291, 320, 349, 378, 407, 436, 465, 494, 523, 552, 581 };

            // 测试场景1：不同的 Byte#1.HighNibble 值
            byte[] highNibbles = new byte[] { 0x0, 0x3, 0x6, 0x9, 0xC, 0xF };

            foreach (var highNibble in highNibbles)
            {
                foreach (var targetSN in targetSNs)
                {
                    var byte1 = (byte)((highNibble << 4) + (targetSN % 0x10));

                    var caseMutation = CaseMutation.Create($"G4121-Byte1_{byte1:X2}-TargetSN_{targetSN}",groupPath)
                        .MutateDlc(8)
                        .Mutate(MutationFieldType.TP_BigData_FF_DL_12b, $"{4095}")
                        .Mutate(MutationFieldType.TP_BigData_CF_Byte1, $"0x{byte1:X}")
                        .Mutate(MutationFieldType.TP_BigData_CF_TargetSN, targetSN.ToString())
                        .MutateTpParameters(options.TpParametersBigDataPayload, options.TpParametersBigDataRepeat, 4095);
                    list.Add(caseMutation);
                }
            }

            // 测试场景2：Byte1 为 2h，测试不同的 SN 高半字节
            groupPath = Iso15765CaseGroupConsts.BigData().ProtocolFormat().ConsecutiveFrame().InvalidNpci().ValidByte1HighInvalidSN();
            byte[] snList = new byte[] { 0x0, 0x3, 0x6, 0x9, 0xC, 0xF };

            foreach (var sn in snList)
            {
                var byte1 = 0x20 + sn;
                foreach (var targetSN in targetSNs)
                {
                    var caseMutation = CaseMutation.Create($"G4122-Byte1_{byte1:X2}-TargetSN_{targetSN}", groupPath)
                        .MutateDlc(8)
                        .Mutate(MutationFieldType.TP_BigData_FF_DL_12b, $"{4095}")
                        .Mutate(MutationFieldType.TP_BigData_CF_Byte1, $"0x{byte1:X}")
                        .Mutate(MutationFieldType.TP_BigData_CF_TargetSN, targetSN.ToString())
                        .MutateTpParameters(options.TpParametersBigDataPayload, options.TpParametersBigDataRepeat, 4095);
                    list.Add(caseMutation);
                }
            }
        }
    }
}
