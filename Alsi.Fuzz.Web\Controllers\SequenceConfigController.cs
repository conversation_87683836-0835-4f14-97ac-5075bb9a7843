using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service;
using Alsi.Fuzz.Core.Storage;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Http;

namespace Alsi.Fuzz.Web.Controllers
{
    public class SequenceConfigController : WebControllerBase
    {
        private readonly TestPlanService _testPlanService;

        public SequenceConfigController()
        {
            var historyService = new TestPlanHistoryService();
            _testPlanService = new TestPlanService(new TestPlanStorage(), historyService);
        }

        [HttpGet]
        public IHttpActionResult GetSequenceConfig()
        {
            var currentPlan = TestPlanManager.Instance.GetCurrentPlan();
            var selectedSequence = currentPlan?.Config?.SequenceConfigList.FirstOrDefault(x => x.IsSelected);
            if (selectedSequence == null)
            {
                return Ok(new SequenceConfigDto());
            }

            return Ok(new SequenceConfigDto
            {
                SequencePackageName = selectedSequence.SequencePackageName,
                TestSuiteName = selectedSequence.TestSuiteName,
                SequencePackageXml = selectedSequence.SequencePackageXml,
                IsBuiltIn = selectedSequence.IsBuiltIn,
                CustomName = selectedSequence.CustomName,
                BasePackageName = selectedSequence.BasePackageName,
                LastModified = selectedSequence.LastModified,
                CreationTime = selectedSequence.CreationTime
            });
        }

        [HttpGet]
        [ActionName("custom-packages")]
        public IHttpActionResult GetCustomPackages(string testSuiteName)
        {
            var currentPlan = TestPlanManager.Instance.GetCurrentPlan();
            if (currentPlan == null)
            {
                return Ok(Array.Empty<SequenceConfigDto>());
            }

            var customPackages = currentPlan.Config.SequenceConfigList
                .Where(x => x.TestSuiteName == testSuiteName && !x.IsBuiltIn)
                .Select(Mapper.Map<SequenceConfigDto>)
                .ToList();

            return Ok(customPackages);
        }

        [HttpPost]
        public async Task<IHttpActionResult> UpdateSequenceConfig([FromBody] SequenceConfigDto request)
        {
            // 互操作测试运行时，不能更新 Sequence 配置
            var interoperation = TestPlanManager.Instance.Interoperation;
            if (interoperation.StatusPollingService.IsTesterRunning)
            {
                throw new Exception("The interoperation is running, can't save sequence configuration.");
            }

            var path = TestPlanManager.Instance.GetCurrentPlanPath();
            var updatedPlan = await _testPlanService.SequenceService.UpdateSequenceConfigAsync(path, request);

            // 更新当前计划中的序列配置
            var currentPlan = TestPlanManager.Instance.GetCurrentPlan();
            if (currentPlan != null)
            {
                currentPlan.Config.SequenceConfigList = updatedPlan.Config.SequenceConfigList;
            }

            // 保存 Sequence 配置的时候，重置互操作结果
            interoperation.StatusPollingService.Reset();

            return GetSequenceConfig();

        }

        [HttpDelete]
        public async Task<IHttpActionResult> DeleteCustomSequencePackage(string testSuiteName, string customName)
        {
            var interoperation = TestPlanManager.Instance.Interoperation;

            // 互操作测试运行时，不能删除自定义包
            if (interoperation.StatusPollingService.IsTesterRunning)
            {
                throw new Exception("The interoperation is running, can't delete custom sequence package.");
            }

            var path = TestPlanManager.Instance.GetCurrentPlanPath();
            var updatedPlan = await _testPlanService.SequenceService.DeleteCustomSequencePackageAsync(path, testSuiteName, customName);

            // 更新当前计划中的序列配置
            var currentPlan = TestPlanManager.Instance.GetCurrentPlan();
            if (currentPlan != null)
            {
                currentPlan.Config.SequenceConfigList = updatedPlan.Config.SequenceConfigList;
            }

            return GetSequenceConfig();

        }
    }
}
