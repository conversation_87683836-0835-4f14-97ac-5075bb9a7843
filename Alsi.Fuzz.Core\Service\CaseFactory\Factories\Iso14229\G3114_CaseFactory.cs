using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts;
using Alsi.Fuzz.Core.Service.CaseFactory.Iso14229;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G3114_CaseFactory : CaseFactoryBase
    {
        public override void Generate(MutationOptions options, Action<CreateCaseInfo> createCase)
        {
            var xmlServices = options.XmlServices;
            var supportedXmlServicesWithSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == true)
                .ToArray();

            foreach (var xmlService in supportedXmlServicesWithSubfunction)
            {
                var sid = xmlService.Id;
                var serviceName = xmlService.IsoUdsServiceDisplayName;

                foreach (var otherService in supportedXmlServicesWithSubfunction)
                {
                    if (otherService.Id == xmlService.Id)
                    {
                        continue;
                    }

                    // 别的 SID 的 subfunction
                    if (!otherService.SubfunctionId.HasValue || otherService.Parameter2k.Length <= 0)
                    {
                        continue;
                    }

                    var otherServiceName = otherService.IsoUdsServiceDisplayName;
                    //var groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                    //                .GetService(serviceName)
                    //                .Invalid()
                    //                .Parameter1WithSub(otherServiceName)
                    //                .Path;
                    //TODO 存疑
                    var groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                                    .GetService(serviceName)
                                    .Invalid()
                                    .Parameter2K()
                                    .Path;
                    var name = $"Sid{sid:X2}-otherServiceParam2k{otherService.Id:X}-mySubfunctionId{xmlService.SubfunctionId.Value:X2} -{groupPath}";

                    var payload = new List<byte> { sid, xmlService.SubfunctionId.Value };
                    payload.AddRange(otherService.Parameter2k);

                    //var caseMutation = CaseMutation.Create($"G3114-Sid{sid:X2}-Subfunc{xmlService.SubfunctionId.Value:X2}-OtherSid{otherService.Id:X}")
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G3114-Sid{sid:X2}-Subfunc{xmlService.SubfunctionId.Value:X2}-OtherSid{otherService.Id:X}", caseMutation, sid, xmlService.SubfunctionId));
                }
            }
        }
    }
}
