using System.Xml.Serialization;

namespace Alsi.Fuzz.Core.Models.TestSuites.Steps.Diagnostic
{
    public class SendDiagStep : StepBase
    {
        [XmlAttribute("hex-payload")]
        public string HexPayload { get; set; }

        private bool? _mutate;

        [XmlIgnore]
        public bool Mutate
        {
            // 默认异变标识为 True
            get { return _mutate ?? true; }
            set { _mutate = value; }
        }

        [XmlAttribute("mutate")]
        public string MutateString
        {
            get => _mutate.HasValue ? _mutate.Value.ToString() : null;
            set => _mutate = string.IsNullOrEmpty(value) ? (bool?)null : bool.Parse(value);
        }
    }
}
