<template>
  <div class="basic-setting-container" v-loading="loading">
    <div class="description-section">
      <div class="section-header">
        <h4>Description</h4>
        <el-button 
          v-if="!isEditing" 
          type="primary" 
          text 
          size="small" 
          @click="startEditing"
          :icon="Edit"
        >
          Edit
        </el-button>
      </div>
      
      <el-skeleton :rows="3" animated v-if="loading"></el-skeleton>
      
      <template v-else>
        <div v-if="!isEditing" class="description-content">
          <p v-if="testPlan?.manifest.description">{{ testPlan?.manifest.description }}</p>
          <el-empty v-else description="No description" :image-size="100"></el-empty>
        </div>
        
        <div v-else class="description-edit">
          <el-input
            v-model="editDescription"
            type="textarea"
            :rows="3"
            placeholder="Enter test plan description"
            maxlength="500"
            show-word-limit
          />
          <div class="edit-actions">
            <el-button type="primary" @click="saveDescription" :loading="isSaving">
              Save
            </el-button>
            <el-button @click="cancelEditing">Cancel</el-button>
          </div>
        </div>
      </template>
    </div>
    
    <div class="additional-info-section">
      <h4>Additional Information</h4>
      <el-descriptions border :column="1">
        <el-descriptions-item label="File Path">
          <el-tooltip :content="testPlan?.path" placement="top">
            <div class="path-display">{{ testPlan?.path }}</div>
          </el-tooltip>
        </el-descriptions-item>
        <el-descriptions-item label="Created">
          <div class="path-display">{{ formatDateTime(testPlan?.manifest.created) }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="Modified">
          <div class="path-display">{{ formatDateTime(testPlan?.manifest.modified) }}</div>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { Edit } from '@element-plus/icons-vue';
import { testPlanService } from '@/services/testPlanService';
import { formatDateTime } from '@/utils/timeUtils';

const state = testPlanService.getState();
const testPlan = computed(() => state.currentPlan);
const loading = computed(() => state.isLoading);

const isEditing = ref(false);
const editDescription = ref('');
const isSaving = ref(false);

const loadCurrentPlan = async () => {
  await testPlanService.getCurrentPlan();
};

const startEditing = () => {
  editDescription.value = testPlan.value?.manifest.description || '';
  isEditing.value = true;
};

const cancelEditing = () => {
  isEditing.value = false;
};

const saveDescription = async () => {
  if (!testPlan.value) return;
  
  isSaving.value = true;
  try {
    await testPlanService.updateBasicInfo(editDescription.value);
    isEditing.value = false;
  } finally {
    isSaving.value = false;
  }
};

onMounted(() => {
  loadCurrentPlan();
});
</script>

<style scoped>
.basic-setting-container {
  margin: 0 auto;
  background-color: #ffffff;
  border-radius: 8px;
  flex: 1;
  padding: 15px 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
}

.description-section {
  margin-bottom: 25px;
}

.description-content {
  min-height: 60px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.description-content p {
  margin: 0;
  white-space: pre-line;
  line-height: 1.5;
}

.description-edit {
  margin-bottom: 10px;
}

.edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 15px;
}

.additional-info-section {
  margin-top: 25px;
}

.additional-info-section h4 {
  margin: 0 0 15px 0;
  font-size: 1rem;
  font-weight: 500;
}

.path-display {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

</style>
