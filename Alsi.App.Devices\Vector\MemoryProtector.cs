using Alsi.Common.Utils;
using System.Runtime.InteropServices;

namespace Alsi.App.Devices.Vector
{
    [StructLayout(LayoutKind.Explicit, Size = byteLength + 4 + byteLength)]
    public unsafe struct MemoryProtector
    {
        [FieldOffset(0)]
        public fixed byte prefixData[byteLength];

        [FieldOffset(byteLength)]
        public int eventHandle;

        [FieldOffset(byteLength + 4)]
        public fixed byte postfixData[byteLength];

        public const int byteLength = 16;

        private static byte[] ConvertBytes(byte* byteRef)
        {
            var bytes = new byte[byteLength];
            for (var i = 0; i < byteLength; i++)
            {
                bytes[i] = byteRef[i];
            }
            return bytes;
        }

        public static MemoryProtector Create()
        {
            var safeStruct = new MemoryProtector();
            for (var i = 0; i < byteLength; i++)
            {
                safeStruct.prefixData[i] = 0xCC;
            }

            safeStruct.eventHandle = -1;

            for (var i = 0; i < byteLength; i++)
            {
                safeStruct.postfixData[i] = 0xCC;
            }
            return safeStruct;
        }

        public override string ToString()
        {
            fixed (byte* pPrefixData = prefixData)
            {
                fixed (byte* pPostfixData = postfixData)
                {
                    return $"{ConvertBytes(pPrefixData).ToHex()} {eventHandle:X} {ConvertBytes(pPostfixData).ToHex()}";
                }
            }
        }
    }
}
