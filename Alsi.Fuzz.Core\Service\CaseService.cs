using Alsi.Fuzz.Core.Service.Results;
using System;
using System.IO;
using System.Linq;

namespace Alsi.Fuzz.Core.Service
{
    public class CaseService
    {
        public CaseResult[] GetLatestCaseResults(TestType testType)
        {
            var testPlanName = Path.GetFileNameWithoutExtension(TestPlanManager.Instance.GetCurrentPlanPath());
            var resultReader = new TestResultReaderService();
            var testResults = resultReader.GetTestResults();
            var latestTestResult = testResults.Where(x => x.TestPlanName == testPlanName && x.TestType == testType)
                .OrderByDescending(x => x.CreationTime)
                .FirstOrDefault();
            if (latestTestResult != null)
            {
                return resultReader.GetCaseResults(latestTestResult);
            }

            return Array.Empty<CaseResult>();
        }
    }
}
