using Alsi.App;
using Alsi.App.Controllers;
using Alsi.App.Devices;
using Alsi.Fuzz.Core.Contracts.Tester;
using Alsi.Fuzz.Core.Service.Results;
using Alsi.Fuzz.Core.Utils;
using Alsi.Fuzz.Tester.Testers;
using System;
using System.Collections.Generic;
using System.Web.Http;

namespace Alsi.Fuzz.Tester.Controllers
{
    public class TesterController : ControllerBase
    {
        [HttpGet]
        public IHttpActionResult Get()
        {
            return Ok(new
            {
                Program.IsRunning
            });
        }

        [HttpPost]
        [Route("api/tester/stop")]
        public IHttpActionResult Stop()
        {
            TesterEnv.Snapshot.ProcessState = ExecutionState.Failure;
            return Ok();
        }

        [HttpPost]
        [Route("api/tester/exit")]
        public IHttpActionResult Exit()
        {
            Program.IsRunning = false;
            TesterEnv.Snapshot.ProcessState = ExecutionState.Failure;
            return Ok();
        }

        [HttpPost]
        [Route("api/tester/pause")]
        public IHttpActionResult Pause()
        {
            if (TesterEnv.Snapshot.ProcessState == ExecutionState.Running)
            {
                Program.IsPaused = true;
                TesterEnv.Snapshot.ProcessState = ExecutionState.Paused;
            }
            return Ok();
        }

        [HttpPost]
        [Route("api/tester/resume")]
        public IHttpActionResult Resume()
        {
            Program.IsPaused = false;
            TesterEnv.Snapshot.ProcessState = ExecutionState.Running;
            return Ok();
        }

        [HttpPost]
        [Route("api/tester/snapshot")]
        public IHttpActionResult GetSnapshot([FromBody] ReqeustTesterSnapshot request)
        {
            var snapshot = TesterEnv.Snapshot;
            return Ok(new TesterSnapshot
            {
                ProcessState = snapshot.ProcessState,
                CurrentOperation = snapshot.CurrentOperation,
                TestResult = snapshot.TestResult,
                CaseSteps = request.IsInteroperation ? snapshot.CaseSteps : Array.Empty<CaseStep>(),
                CaseResults = request.IsInteroperation ? snapshot.CaseResults : Array.Empty<CaseResult>()
            });
        }

        [HttpPost]
        [Route("api/tester/execute")]
        public IHttpActionResult Execute([FromBody] ExecutionRequest request)
        {
            var package = SequencePackageUtils.LoadFromString(request.SequencePackageXml);

            // 验证请求
            if (request == null || package == null)
            {
                return BadRequest("The sequence package is empty");
            }

            var hardwareConfig = request.HardwareConfig;
            if (hardwareConfig == null || !hardwareConfig.IsConfigured)
            {
                return BadRequest("The hardware is not configured");
            }

            var deviceChannelName = hardwareConfig.GetSelectedDeviceChannelName();

            AppEnv.Logger.Info($"Device Channel Name: {deviceChannelName}");

            if (!DeviceEnv.TryGetDeviceChannel(deviceChannelName, out var deviceChannel))
            {
                var message = $"Failed to check channel device info. \nPlease verify device status in Hardware Settings: {deviceChannelName}";
                return BadRequest(message);
            }

            var testResult = request.TestResult;

            var rxFrameFilterIds = new HashSet<int>();
            if (request.CaseConfig.EnableLogFilter)
            {
                rxFrameFilterIds.Add(-1);
                rxFrameFilterIds.Add(request.CaseConfig.ResponseId);
                rxFrameFilterIds.Add(request.CaseConfig.RequestId);
            }

            TesterEnv.BeginTester(testResult, request.CaseResults, request.CaseEntries, rxFrameFilterIds);

            // 异步执行
            TesterEnv.PackageTester = new PackageTester();

            TesterEnv.PackageTester.Start(request, deviceChannel, package);

            return Ok(new { Success = true, Message = "测试已启动" });
        }
    }
}
