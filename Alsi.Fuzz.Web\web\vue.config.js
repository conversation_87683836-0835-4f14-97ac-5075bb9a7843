const { defineConfig } = require('@vue/cli-service')
const webpack = require('webpack') // 添加 webpack 导入

module.exports = defineConfig({
  transpileDependencies: true,

  configureWebpack: {
    plugins: [
      // 使用 DefinePlugin 来定义 Vue 特性标志
      new webpack.DefinePlugin({
        __VUE_OPTIONS_API__: JSON.stringify(true),
        __VUE_PROD_DEVTOOLS__: JSON.stringify(false),
        __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: JSON.stringify(false)
      })
    ],
    resolve: {
      alias: {
        // ...existing aliases
      }
    }
  }
})
