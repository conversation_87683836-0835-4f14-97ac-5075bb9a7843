using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso15765
{
    public class G31_G32_CaseFactory : CaseFactoryBase
    {
        public override CaseMutation[] Generate(MutationOptions options)
        {
            var list = new List<CaseMutation>();
            GenerateG31Cases(options, list);
            GenerateG32Cases(options, list);
            return list.ToArray();
        }

        private static void GenerateG31Cases(MutationOptions options, List<CaseMutation> list)
        {
            var dlc = 8;
            var groupPath = "";

            // G311
            groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().SingleFrame().Dlc8().ValidNpci().GivenParameters().Path;
            for (byte sf_dl = 1; sf_dl <= 7; sf_dl++)
            {
                var caseMutation = CaseMutation.Create($"G311-SF_DL_{sf_dl:X}", groupPath)
                    .MutateDlc(dlc)
                    .MutateTpSfByte1(sf_dl)
                    .MutateTpParameters(GetTpParameters(options.TpParameters, sf_dl));
                list.Add(caseMutation);
            }

            // G312
            groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().SingleFrame().Dlc8().ValidNpci().RandomParameters().Path;
            for (byte sf_dl = 1; sf_dl <= 7; sf_dl++)
            {
                var count = 0;
                if (options.Coverage == CoverageType.Normal)
                {
                    count = (int)Math.Pow(2, sf_dl);
                }
                else if (options.Coverage == CoverageType.High)
                {
                    count = (int)Math.Pow(2, sf_dl + 1);
                }
                else
                {
                    throw new Exception("Unknown coverage type");
                }

                var lhsSampler = new LhsSampler();
                var dataList = lhsSampler.GenerateByteBasedSamples(sf_dl).Take(count);
                foreach (var parameters in dataList)
                {
                    var caseMutation = CaseMutation.Create($"G312-SF_DL_{sf_dl:X}-ParametersRandom", groupPath)
                        .MutateDlc(dlc)
                        .MutateTpSfByte1(sf_dl)
                        .MutateTpParameters(parameters);
                    list.Add(caseMutation);
                }
            }

            // G313
            groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().SingleFrame().Dlc8().InvalidNpci().ValidByte1HighInvalidSfDl().Path;
            for (byte sf_dl = 0; sf_dl <= 0xF; sf_dl++)
            {
                if (sf_dl >= 1 && sf_dl <= 7)
                {
                    continue;
                }

                var caseMutation = CaseMutation.Create($"G313-SF_DL_{sf_dl:X}", groupPath)
                    .MutateDlc(dlc)
                    .MutateTpSfByte1(sf_dl)
                    .MutateTpParameters(options.TpParameters.Take(6).ToArray());
                list.Add(caseMutation);
            }

            // G314
            groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().SingleFrame().Dlc8().InvalidNpci().InvalidByte1HighValidSfDl().Path;
            for (byte highNibble = 4; highNibble <= 0xF; highNibble++)
            {
                var sf_dls = Array.Empty<byte>();
                if (options.Coverage == CoverageType.Normal)
                {
                    sf_dls = RandomUniqueBytes(1, 7, 3);
                }
                else if (options.Coverage == CoverageType.High)
                {
                    sf_dls = Enumerable.Range(1, 7).Select(x => (byte)x).ToArray();
                }

                for (var i = 0; i < sf_dls.Length; i++)
                {
                    var sf_dl = sf_dls[i];
                    var caseMutation = CaseMutation.Create($"G314-HighNibble{highNibble:X}-SF_DL_{sf_dl:X}", groupPath)
                        .MutateDlc(dlc)
                        .MutateTpSfByte1((byte)(highNibble * 0x10 + sf_dl))
                        .MutateTpParameters(GetTpParameters(options.TpParameters, sf_dl));
                    list.Add(caseMutation);
                }
            }

            // G315
            groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().SingleFrame().Dlc8().InvalidNpci().InvalidByte1HighInvalidSfDl().Path;
            for (byte highNibble = 4; highNibble <= 0xF; highNibble++)
            {
                var sf_dls = Array.Empty<byte>();
                if (options.Coverage == CoverageType.Normal)
                {
                    // 注：这里的随机范围是 Invalid 0,8~F
                    // 为了调用简便，使用 8~0x10随机，然后把0x10替换为0
                    var bytes = RandomUniqueBytes(8, 0x10, 3);
                    for (int i = 0; i < bytes.Length; i++)
                    {
                        if (bytes[i] == 0x10)
                        {
                            bytes[i] = 0;
                        }
                    }
                    sf_dls = bytes.OrderBy(x => x).ToArray();
                }
                else if (options.Coverage == CoverageType.High)
                {
                    // 注：这里的范围是 Invalid 0,8~F
                    // 为了调用简便，使用 8~0x10，然后把0x10替换为0
                    var bytes = Enumerable.Range(8, 9).Select(x => (byte)x).ToArray();
                    for (int i = 0; i < bytes.Length; i++)
                    {
                        if (bytes[i] == 0x10)
                        {
                            bytes[i] = 0;
                        }
                    }
                    sf_dls = bytes.OrderBy(x => x).ToArray();
                }

                for (var i = 0; i < sf_dls.Length; i++)
                {
                    var sf_dl = sf_dls[i];
                    var caseMutation = CaseMutation.Create($"G315-HighNibble{highNibble:X}-SF_DL_{sf_dl:X}", groupPath)
                        .MutateDlc(dlc)
                        .MutateTpSfByte1((byte)(highNibble * 0x10 + sf_dl))
                        .MutateTpParameters(options.TpParameters.Take(6).ToArray());
                    list.Add(caseMutation);
                }
            }
        }

        private static void GenerateG32Cases(MutationOptions options, List<CaseMutation> list)
        {
            if (options.CommunicationType != App.Devices.Core.CommunicationType.CanFd)
            {
                return;
            }

            var dlc = 0xF;

            // G321
            var groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().SingleFrame().DlcMoreThan8().ValidNpci().GivenParameters().Path;
            byte increment = 1;
            if (options.Coverage == CoverageType.Normal)
            {
                increment = 2;
            }
            else if (options.Coverage == CoverageType.High)
            {
                increment = 1;
            }

            for (byte byte2 = 1; byte2 <= 62; byte2 += increment)
            {
                var caseMutation = CaseMutation.Create($"G321-Byte1_00-Byte2_{byte2:X2}", groupPath)
                    .MutateDlc(dlc)
                    .MutateTpSfByte1(0)
                    .MutateTpSfByte2(byte2)
                    .MutateTpParameters(GetTpParameters(options.TpParameters, byte2));
                list.Add(caseMutation);
            }

            // G322
            groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().SingleFrame().DlcMoreThan8().ValidNpci().RandomParameters().Path;
            for (byte byte2 = 1; byte2 <= 62; byte2++)
            {
                var count = 0;
                if (options.Coverage == CoverageType.Normal)
                {
                    count = 2;
                }
                else if (options.Coverage == CoverageType.High)
                {
                    count = 4;
                }
                else
                {
                    throw new Exception("Unknown coverage type");
                }

                var lhsSampler = new LhsSampler();
                var dataList = lhsSampler.GenerateByteBasedSamples(byte2).Take(count);
                foreach (var parameters in dataList)
                {
                    var caseMutation = CaseMutation.Create($"G322-Byte1_00-Byte2_{byte2:X2}-ParametersRandom", groupPath)
                        .MutateDlc(dlc)
                        .MutateTpSfByte1(0)
                        .MutateTpSfByte2(byte2)
                        .MutateTpParameters(parameters);
                    list.Add(caseMutation);
                }
            }

            // G323
            groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().SingleFrame().DlcMoreThan8().InvalidNpci().ValidByte1InvalidSfDl();
            var invalidByte2List = new List<byte>() { 0 };
            invalidByte2List.AddRange(Enumerable.Range(63, 255 - 63 + 1).Select(x => (byte)x));

            int caseCountG323 = 48;
            if (options.Coverage == CoverageType.High)
            {
                caseCountG323 = invalidByte2List.Count;//194
            }

            // 抽取起始点可以变动。  即 0，63, 64，65, 66, 67 
            // 相应的固定抽样数组为：
            // {0,   66,   70,   74... 242,  246,  250}
            // {63,  67,   71,   75... 243,  247,  251}
            // {64,  68,   72,   76... 244,  248,  252}
            // {65,  69,   73,   77... 245,  249,  253}
            // {66,  70,   74,   78... 246,  250,  254}
            // {67,  71,   75,   79... 247,  251,  255}
            var sampledInvalidByte2List = UniformSample(invalidByte2List.ToArray(), caseCountG323);
            foreach (var byte2 in sampledInvalidByte2List)
            {
                var caseMutation = CaseMutation.Create($"G323-Byte1_00-Byte2_{byte2:X2}", groupPath)
                    .MutateDlc(dlc)
                    .MutateTpSfByte1(0)
                    .MutateTpSfByte2(byte2)
                    .MutateTpParameters(options.TpParameters);
                list.Add(caseMutation);
            }

            // G324
            groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().SingleFrame().DlcMoreThan8().InvalidNpci().InvalidByte1ValidSfDl();
            for (var byte1 = 0x1; byte1 <= 0xFF; byte1++)
            {
                // Invalid 01~0F,0x33~FF
                if (byte1 > 0x0F && byte1 < 0x33)
                {
                    continue;
                }

                var count = 1;
                if (options.Coverage == CoverageType.Normal)
                {
                    count = 1;
                }
                else if (options.Coverage == CoverageType.High)
                {
                    count = 4;
                }
                var byte2Array = RandomUniqueBytes(1, 63, count);
                foreach (var byte2 in byte2Array)
                {
                    var caseMutation = CaseMutation.Create($"G324-Byte1_{byte1:X2}-Byte2_{byte2:X2}", groupPath)
                        .MutateDlc(dlc)
                        .MutateTpSfByte1((byte)byte1)
                        .MutateTpSfByte2(byte2)
                        .MutateTpParameters(options.TpParameters);
                    list.Add(caseMutation);
                }
            }

            //G325

            groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().SingleFrame().DlcMoreThan8().InvalidNpci().InvalidByte1InvalidSfDl();
            for (var byte1 = 0x1; byte1 <= 0xFF; byte1++)
            {
                // Invalid 01~0F,0x33~FF
                if (byte1 > 0x0F && byte1 < 0x33)
                {
                    continue;
                }
                //sampledInvalidByte2List 来源是G323
                foreach (var byte2 in sampledInvalidByte2List)
                {
                    var caseMutation = CaseMutation.Create($"G323-Byte1_00-Byte2_{byte2:X2}", groupPath)
                        .MutateDlc(dlc)
                        .MutateTpSfByte1(0)
                        .MutateTpSfByte2(byte2)
                        .MutateTpParameters(options.TpParameters);
                    list.Add(caseMutation);
                }
            }
        }
    }
}
