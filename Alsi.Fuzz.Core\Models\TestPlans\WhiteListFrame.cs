using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso11898;
using System;
using static Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso11898.G31_CaseFactory;

namespace Alsi.Fuzz.Core.Models.TestPlans
{
    public class WhiteListFrameSignal
    {
        public WhiteListFrameSignal(string name, int startBit, double offset, int length)
        {
            Name = name;
            StartBit = startBit;
            Offset = offset;// Not used currently
            Length = length;
        }

        public string Name { get; set; }
        public int StartBit { get; set; }
        public double Offset { get; set; }// Not used currently
        public int Length { get; set; }
    }

    public class WhiteListFrame
    {
        public WhiteListFrame(uint id, string name, byte dlc, string transmitter, string[] receivers, WhiteListFrameSignal[] whiteListFrameSignals)
        {
            if (id > 0x80000000)
            {
                Id = (int)(id - 0x80000000);
                IsExt = true;
            }
            else
            {
                Id = (int)id;
                IsExt = false;
            }

            Name = name;
            Dlc = dlc;
            Transmitter = transmitter;
            Receivers = receivers;
            WhiteListFrameSignals = whiteListFrameSignals;
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public byte Dlc { get; set; }
        public bool IsExt { get; set; }
        public string Transmitter { get; set; }
        public string[] Receivers { get; set; } = Array.Empty<string>();
        public WhiteListFrameSignal[] WhiteListFrameSignals { get; set; } = Array.Empty<WhiteListFrameSignal>();

    }
}
