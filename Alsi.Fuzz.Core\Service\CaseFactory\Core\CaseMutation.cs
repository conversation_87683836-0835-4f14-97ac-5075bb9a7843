using Alsi.App.Devices.Core;
using Alsi.Common.Utils;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Core
{
    public class CaseMutation
    {
        public CaseMutation(params MutationField[] fields)
        {
            Fields = fields;
        }

        public string Name { get; set; } = string.Empty;

        public string GroupPath { get; set; } = string.Empty;

        public MutationField[] Fields { get; set; } = Array.Empty<MutationField>();

        public static CaseMutation Create(string name, string groupPath = "Unknown")
        {
            return new CaseMutation()
            {
                //TODO: 名称按照分组命名规则变更
                Name = name,
                // TODO: 考虑是否同一个方法直接追加叶子结点的分组名称，还是另外追加方法?
                GroupPath = groupPath
            };
        }

        public CaseMutation Mutate(MutationFieldType fieldType, string value)
        {
            return Append(new MutationField(fieldType, value));
        }

        public CaseMutation MutateId(int id)
        {
            return Mutate(MutationFieldType.Id, $"0x{id:X}");
        }

        public CaseMutation MutateDlc(int dlc)
        {
            return Mutate(MutationFieldType.Dlc, dlc.ToString());
        }

        public CaseMutation MutateRtr(byte rtr)
        {
            return Mutate(MutationFieldType.Rtr, rtr == 1 ? "1" : "0");
        }

        public CaseMutation MutateExt(bool isExt)
        {
            return Mutate(MutationFieldType.Ext, isExt ? "1" : "0");
        }

        public CaseMutation MutateData(byte[] data)
        {
            return Mutate(MutationFieldType.Data, $"hex: {data.ToHex()}");
        }

        public CaseMutation MutatePayload(byte[] data)
        {
            return Mutate(MutationFieldType.Payload, $"hex: {data.ToHex()}");
        }

        public CaseMutation MutatePayloadLength(int payloadLength)
        {
            return Mutate(MutationFieldType.PayloadLength, payloadLength.ToString());
        }

        public CaseMutation MutateTpCfByte1List(byte[] cfByte1List)
        {
            return Mutate(MutationFieldType.TP_CF_BYTE1_LIST, $"hex: {cfByte1List.ToHex()}");
        }

        public CaseMutation MutateTpSfByte1(byte sfByte1)
        {
            return Mutate(MutationFieldType.TP_SF_Byte1, $"0x{sfByte1:X}");
        }

        public CaseMutation MutateTpSfByte2(byte sfByte2)
        {
            return Mutate(MutationFieldType.TP_SF_Byte2, $"0x{sfByte2:X}");
        }


        public CaseMutation MutateTpFfHighNibble(byte ffHighNibble)
        {
            return Mutate(MutationFieldType.TP_FF_HighNibble, $"0x{ffHighNibble:X}");
        }

        public CaseMutation MutateTpFfDl12b(int tpFfDl12b)
        {
            return Mutate(MutationFieldType.TP_FF_DL_12b, $"0x{tpFfDl12b:X}");
        }

        public CaseMutation MutateTpFfByte1(byte byte1)
        {
            return Mutate(MutationFieldType.TP_FF_Byte1, $"0x{byte1:X}");
        }

        public CaseMutation MutateTpFfByte2(byte byte2)
        {
            return Mutate(MutationFieldType.TP_FF_Byte2, $"0x{byte2:X}");
        }

        public CaseMutation MutateTpFfDl32b(uint tpFfDl12b)
        {
            return Mutate(MutationFieldType.TP_FF_DL_32b, $"0x{tpFfDl12b:X}");
        }

        public CaseMutation MutateTpParameters(byte[] parameters)
        {
            return Mutate(MutationFieldType.TP_Parameters, $"hex: {parameters.ToHex()}");
        }

        public CaseMutation MutateTpParameters(byte[] parameters, int repeat)
        {
            return Mutate(MutationFieldType.TP_Parameters, $"hex: {parameters.ToHex()} repeat: {repeat}");
        }

        public CaseMutation MutateTpParameters(byte[] parameters, int repeat, int take)
        {
            return Mutate(MutationFieldType.TP_Parameters, $"hex: {parameters.ToHex()} repeat: {repeat} take: {take}");
        }

        public CaseMutation MutateTpParameters(byte parameter, int repeat)
        {
            return Mutate(MutationFieldType.TP_Parameters, $"0x{parameter:X} repeat: {repeat}");
        }

        public CaseMutation MutateTpParameters(byte parameter, int repeat, int take)
        {
            return Mutate(MutationFieldType.TP_Parameters, $"0x{parameter:X} repeat: {repeat} take: {take}");
        }

        public CaseMutation MutateTpFc(byte[] bytes)
        {
            return Mutate(MutationFieldType.TP_FC, $"hex: {bytes.ToHex()}");
        }

        private CaseMutation Append(MutationField mutationField)
        {
            var list = Fields.ToList();
            list.Add(mutationField);
            Fields = list.ToArray();
            return this;
        }

        public string Serialize()
        {
            return string.Join("; ", Fields.OrderBy(x => x.FieldType).Select(x => x.Serialize()));
        }

        public static CaseMutation Deserialize(string content)
        {
            var fields = new List<MutationField>();
            foreach (var item in content.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries))
            {
                var keyValue = item.Split(new[] { '=' }, StringSplitOptions.RemoveEmptyEntries);
                if (keyValue.Length >= 1)
                {
                    var key = keyValue[0];
                    var value = keyValue.Length > 1 ? keyValue[1] : string.Empty;
                    if (Enum.TryParse<MutationFieldType>(key.Trim(), true, out var fieldType))
                    {
                        var mutationField = new MutationField(fieldType, value);
                        fields.Add(mutationField);
                    }
                }
            }

            return new CaseMutation(fields.ToArray());
        }

        public void ApplyToDiagFrame(ref byte[] payload, ref int payloadLength)
        {
            foreach (var field in Fields)
            {
                switch (field.FieldType)
                {
                    case MutationFieldType.Payload:
                        payload = SequenceUtils.ParseBytes(field.Value);
                        payloadLength = payload.Length;
                        break;
                }
            }

            foreach (var field in Fields)
            {
                switch (field.FieldType)
                {
                    case MutationFieldType.PayloadLength:
                        payloadLength = SequenceUtils.ParseInt(field.Value);
                        break;
                }
            }
        }

        public void ApplyToFrame(CanFrame frame)
        {
            foreach (var field in Fields)
            {
                switch (field.FieldType)
                {
                    case MutationFieldType.Id:
                        frame.Id = SequenceUtils.ParseInt(field.Value);
                        break;
                    case MutationFieldType.Dlc:
                        frame.Dlc = SequenceUtils.ParseByte(field.Value);
                        break;
                    case MutationFieldType.Rtr:
                        frame.Rtr = SequenceUtils.ParseByte(field.Value) != 0;
                        break;
                    case MutationFieldType.Data:
                        frame.Data = SequenceUtils.ParseBytes(field.Value);
                        break;
                    case MutationFieldType.Ext:
                        frame.IsExt = SequenceUtils.ParseByte(field.Value) != 0;
                        break;
                    default:
                        throw new Exception($"Unknown mutation field type: {field.FieldType}");
                }
            }

        }
    }
}
