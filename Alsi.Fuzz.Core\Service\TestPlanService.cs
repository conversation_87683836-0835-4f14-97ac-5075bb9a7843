using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Storage;
using System;
using System.Threading.Tasks;

namespace Alsi.Fuzz.Core.Service
{
    public class TestPlanService
    {
        private readonly TestPlanStorage _storage;
        private readonly ITestPlanHistoryService _historyService;

        public TestPlanSequenceService SequenceService { get; }

        public TestPlanService(TestPlanStorage storage, ITestPlanHistoryService historyService)
        {
            _storage = storage ?? throw new ArgumentNullException(nameof(storage));
            _historyService = historyService ?? throw new ArgumentNullException(nameof(historyService));

            SequenceService = new TestPlanSequenceService(_storage);
        }

        public Task<TestPlan> CreateAsync(string description)
        {
            var now = DateTime.Now;
            var plan = new TestPlan
            {
                Manifest = new TestPlanManifest
                {
                    Description = description,
                    Created = now,
                    Modified = now
                },
                Config = new TestPlanConfig()
            };

            return Task.FromResult(plan);
        }

        public async Task<TestPlan> LoadAsync(string path)
        {
            var testPlan = _storage.Load(path);
            await _historyService.RecordAccessAsync(testPlan, path);
            return testPlan;
        }

        public Task SaveAsync(TestPlan plan, string path)
        {
            _storage.Save(path, plan);
            return Task.CompletedTask;
        }

        public TestPlan UpdateBasicInfo(string path, string description)
        {
            var testPlan = _storage.Load(path);

            testPlan.Manifest.Description = description;
            testPlan.Manifest.Modified = DateTime.Now;

            _storage.Save(path, testPlan);

            return testPlan;
        }

        public Task<TestPlan> UpdateHardwareConfigAsync(string path, HardwareConfig hardwareConfig)
        {
            var testPlan = _storage.Load(path);

            testPlan.Config.HardwareConfig = hardwareConfig;
            testPlan.Manifest.Modified = DateTime.Now;

            _storage.Save(path, testPlan);

            return Task.FromResult(testPlan);
        }

        public Task<TestPlan> UpdateSecurityConfigAsync(string path, SecurityConfig securityConfig)
        {
            var testPlan = _storage.Load(path);

            testPlan.Config.SecurityConfig = securityConfig;
            testPlan.Manifest.Modified = DateTime.Now;

            _storage.Save(path, testPlan);

            return Task.FromResult(testPlan);
        }

        public Task<TestPlan> UpdateCaseConfigAsync(string path, CaseConfig caseConfig)
        {
            var testPlan = _storage.Load(path);

            testPlan.Config.CaseConfig = caseConfig;
            testPlan.Manifest.Modified = DateTime.Now;

            _storage.Save(path, testPlan);

            return Task.FromResult(testPlan);
        }

        public CaseCollection LoadCaseCollection(string path)
        {
            return _storage.LoadCaseCollection(path);
        }

        public void SaveCaseCollection(string path, CaseCollection caseCollection)
        {
            _storage.SaveCaseCollection(path, caseCollection);
        }
    }
}
