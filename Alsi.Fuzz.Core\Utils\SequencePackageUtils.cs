using Alsi.Fuzz.Core.Models.TestSuites;
using System;
using System.IO;
using System.Text;
using System.Xml;
using System.Xml.Serialization;

namespace Alsi.Fuzz.Core.Utils
{
    public static class SequencePackageUtils
    {
        public static SequencePackage LoadFromString(string xmlContent)
        {
            if (string.IsNullOrWhiteSpace(xmlContent))
            {
                throw new ApplicationException("Failed to parse sequence XML, the XML is empty.");
            }

            try
            {
                using (var stream = new StringReader(xmlContent))
                {
                    var serializer = new XmlSerializer(typeof(SequencePackage));
                    var reader = new XmlTextReader(stream);
                    reader.Namespaces = false;  // 忽略命名空间
                    return serializer.Deserialize(reader) as SequencePackage;
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to parse XML as sequence package", ex);
            }
        }

        public static string SerializeToString(SequencePackage sequencePackage)
        {
            if (sequencePackage == null)
            {
                throw new ArgumentNullException(nameof(sequencePackage));
            }

            try
            {
                var xmlSerializerNamespaces = new XmlSerializerNamespaces();
                xmlSerializerNamespaces.Add(string.Empty, string.Empty); // 添加默认命名空间  

                var serializer = new XmlSerializer(typeof(SequencePackage));

                var settings = new XmlWriterSettings
                {
                    Indent = true,                // 启用缩进  
                    IndentChars = "  ",           // 设置缩进字符（可以是空格或制表符）  
                    NewLineChars = "\n",          // 设置换行字符  
                    NewLineHandling = NewLineHandling.Replace // 替换换行符
                };

                using (var stringWriter = new StringWriter())
                using (var xmlWriter = XmlWriter.Create(stringWriter, settings))
                {
                    serializer.Serialize(xmlWriter, sequencePackage, xmlSerializerNamespaces);
                    return stringWriter.ToString();
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Can't serialize sequence package to XML: {ex.Message}", ex);
            }
        }
    }
}
