import { ErrorData, AppInfo, TesterSnapshot, TestResult, ExecutionState, CaseStep, isTesterCompleted } from '@/api/appApi';
import { mockSuccess } from '../mockApi';
import { errorLogs } from '../mockData';
import { AxiosResponse } from 'axios';
import { CaseResult, MyGroupResponse, GroupTreeNode } from '@/api/interoperationApi';
import { CoverageType, GenerateCasesRequest } from '@/api/appApi';

// 生成模拟的互操作测试结果
function generateMockInteroperationResults(): CaseResult[] {
  const results: CaseResult[] = [];

  // 预定义的序列名称数组 - 确保唯一性
  const uniqueSequences = [
    'DiagnosticSessionControl',
    'ECUReset',
    'SecurityAccess',
    'CommunicationControl',
    'ReadDataByIdentifier',
    'WriteDataByIdentifier',
    'ClearDiagnosticInformation',
    'ReadDTCInformation',
    'InputOutputControlByIdentifier',
    'RoutineControl',
    'RequestDownload',
    'RequestUpload',
    'TransferData',
    'RequestTransferExit',
    'AccessTimingParameter',
    'SecuredDataTransmission',
    'ControlDTCSetting',
    'ResponseOnEvent',
    'LinkControl',
    'can-frames'
  ];

  // 生成随机的执行状态分布
  const successCount = Math.floor(Math.random() * 10) + 5; // 5-15个成功结果

  // 随机选择一些序列作为测试结果
  const selectedSequences = [...uniqueSequences]
    .sort(() => Math.random() - 0.5) // 随机打乱顺序
    .slice(0, successCount + 5); // 多取5个以便有一些非成功状态的结果

  // 为每个选定的序列创建一个结果
  selectedSequences.forEach((sequenceName, index) => {
    // 决定状态 - 前successCount个为Success，其余随机
    let state: ExecutionState;
    if (index < successCount) {
      state = ExecutionState.Success;
    } else {
      // 随机选择非Success状态
      const otherStates: ExecutionState[] = [ExecutionState.Pending, ExecutionState.Running, ExecutionState.Failure];
      state = otherStates[Math.floor(Math.random() * otherStates.length)];
    }

    // 创建结果对象
    const begin = new Date(Date.now() - Math.random() * 60000);
    const end = new Date(begin.getTime() + Math.random() * 30000);

    results.push({
      id: index + 1,
      testResultId: `test-${Math.random().toString(36).substring(2)}`,
      sequenceId: `seq-${Math.random().toString(36).substring(2)}`,
      sequenceName: sequenceName, // 使用唯一的序列名称
      parameter: `param-${index}`,
      name: `name-${index}`,
      state: state,
      begin: begin.toISOString(),
      end: end.toISOString(),
      detail: state === 'Success' ? 'Test passed' : `Test ${state.toLowerCase()}`,
      groupPath: ''
    });
  });

  return results;
}

// 生成模拟的测试用例
function generateMockTestCases(coverage: CoverageType, sequenceNames: string[]): CaseResult[] {
  const results: CaseResult[] = [];
  // 提高用例数量 - High覆盖模式生成100000个用例，Normal模式生成5000个
  const caseCount = (coverage === CoverageType.High ? 100000 : 5000) / 5;

  // 定义一些可能的前缀组
  const prefixGroups = ['G100', 'G200', 'G300', 'G400', 'G500'];

  // 仅为选定的序列生成测试用例
  for (const sequenceName of sequenceNames) {
    // 为每个选定的序列生成测试用例
    for (let i = 0; i < caseCount; i++) {
      // 随机选择一个前缀组
      const prefixIndex = Math.floor(Math.random() * prefixGroups.length);
      const prefix = prefixGroups[prefixIndex];

      results.push({
        id: results.length + 1,
        testResultId: `test-${Math.random().toString(36).substring(2)}`,
        sequenceId: `seq-${Math.random().toString(36).substring(2)}`,
        sequenceName: sequenceName,
        parameter: `id=0x${(Math.floor(Math.random() * 4095) + 1).toString(16).padStart(3, '0')};dlc=8;data=hex:${Array(16).fill(0).map(() => Math.floor(Math.random() * 255).toString(16).padStart(2, '0')).join('')}`,
        state: ExecutionState.Pending,
        begin: null,
        end: null,
        detail: '',
        name: `${prefix}-TestCase${i + 1}`,
        groupPath: ''
      });
    }
  }

  return results;
}
// 生成模拟的测试用例
function generateMockTestCasesWithGroup(coverage: CoverageType, sequenceNames: string[]): MyGroupResponse {
  const results: CaseResult[] = [];
  // 提高用例数量 - High覆盖模式生成100000个用例，Normal模式生成5000个
  const caseCount = (coverage === CoverageType.High ? 100000 : 5000) / 5;

  // 定义一些可能的前缀组
  const prefixGroups = ['G100', 'G200', 'G300', 'G400', 'G500'];

  // 仅为选定的序列生成测试用例
  for (const sequenceName of sequenceNames) {
    // 为每个选定的序列生成测试用例
    for (let i = 0; i < caseCount; i++) {
      // 随机选择一个前缀组
      const prefixIndex = Math.floor(Math.random() * prefixGroups.length);
      const prefix = prefixGroups[prefixIndex];

      results.push({
        id: results.length + 1,
        testResultId: `test-${Math.random().toString(36).substring(2)}`,
        sequenceId: `seq-${Math.random().toString(36).substring(2)}`,
        sequenceName: sequenceName,
        parameter: `id=0x${(Math.floor(Math.random() * 4095) + 1).toString(16).padStart(3, '0')};dlc=8;data=hex:${Array(16).fill(0).map(() => Math.floor(Math.random() * 255).toString(16).padStart(2, '0')).join('')}`,
        state: ExecutionState.Pending,
        begin: null,
        end: null,
        detail: '',
        name: `${prefix}-TestCase${i + 1}`,
        groupPath: `root/${prefix}`
      });
    }
  }
  const groupTree: GroupTreeNode = { name: 'root', id: 'root', children: [], count: 0 };
  //const groupTree: GroupTreeNode = { children: [] };

  // 构建分组树
  // for (const result of results) {
  //   const groupPath = result.groupPath.split('/');
  //   const currentNode = groupTree;

  // for (const part of groupPath) {
  //   let existingNode = currentNode.find(node => node.Name === part);
  //   if (!existingNode) {
  //     existingNode = { Name: part, Children: [] };
  //     currentNode.push(existingNode);
  //   }
  //   currentNode = existingNode.Children;
  // }

  // currentNode.push(caseNode);
  // }
  // var root = new GroupTreeNode { Name = "Root" };
  for (const result of results) {
    //  console.log("groupPath", result.groupPath);
    let currentNode = groupTree;
    const parts = result.groupPath.split('/');
    // console.log("parts", parts);
    for (const part of parts) {
      if (part !== "root") {
        let existingNode = currentNode.children.find(node => node.name === part);
        if (!existingNode) {
          console.log("part ", part, "currentNode", currentNode);
          // existingNode = { id: result.groupPath, name: part, children: [], parent: currentNode };
          // currentNode.children.push(existingNode);
          existingNode = { id: result.groupPath, name: part, children: [], count: 0 };
          currentNode.children.push(existingNode);
          currentNode.count = currentNode.children.length;
          console.log("existingNode is null new one", existingNode);
        }

        currentNode = existingNode;
      }
    }
  }

  // new
  return {
    cases: results,
    groupTree: groupTree
  };
  //return (results, groupTree);
}

// 生成模拟的测试结果列表
function generateMockTestResults(): TestResult[] {
  const results: TestResult[] = [];

  // 生成5个模拟测试结果
  for (let i = 0; i < 5; i++) {
    const creationTime = new Date(Date.now() - i * 86400000); // 每个相差一天
    const totalCount = 10 + Math.floor(Math.random() * 20);
    const successCount = Math.floor(totalCount * (0.7 + Math.random() * 0.2)); // 70-90% 成功率
    const failureCount = totalCount - successCount;

    results.push({
      id: `test-result-${i}-${Date.now()}`,
      resultFolderName: `测试运行 ${i + 1}`,
      testType: 'Case',
      creationTime: creationTime.toISOString(),
      completionTime: new Date(creationTime.getTime() + 3600000).toISOString(), // 一小时后完成
      totalCount,
      successCount,
      failureCount
    });
  }

  return results;
}

// 生成模拟的用例步骤
function generateMockCaseSteps(caseResultId: number): CaseStep[] {
  const steps: CaseStep[] = [];
  const stepCount = 5 + Math.floor(Math.random() * 10); // 5-15个步骤

  for (let i = 0; i < stepCount; i++) {
    const timestamp = Date.now() - (stepCount - i) * 1000; // 每步相差1秒
    const stateOptions = ['Success', 'Failure', 'Completed'];
    const state = Math.random() > 0.5 ? stateOptions[2] : (Math.random() > 0.25 ? stateOptions[1] : stateOptions[2]);

    steps.push({
      id: i + 1,
      name: `Step ${i + 1}`,
      caseResultId: caseResultId,
      timestamp: timestamp,
      frameTimestamp: timestamp,
      state: state,
      begin: new Date(timestamp - 500).toISOString(),
      end: new Date(timestamp).toISOString(),
      detail: state === 'Success' ? 'Step executed successfully' : (state === 'Completed' ? 'Step executed complete' : 'Step failed with error')
    });
  }

  return steps;
}

// 模拟测试状态
let mockProcessState: ExecutionState = ExecutionState.Pending;
let mockCurrentOperation = '';
let mockTestResult: TestResult = {
  id: `test-${Date.now()}`,
  resultFolderName: 'Mock Test Run',
  testType: 'Case',
  creationTime: new Date().toISOString(),
  totalCount: 0,
  successCount: 0,
  failureCount: 0
};
let mockCaseResults: CaseResult[] = [];

// 增加全局变量记录当前正在执行的定时器
let progressInterval: number | undefined = undefined;

// 启动进度更新的函数
function startProgressUpdate() {
  // 先清除可能存在的定时器
  if (progressInterval !== undefined) {
    clearInterval(progressInterval);
    progressInterval = undefined;
  }

  // 创建新的定时器
  progressInterval = window.setInterval(() => {
    if (mockProcessState === ExecutionState.Running && mockTestResult.successCount + mockTestResult.failureCount < mockTestResult.totalCount) {
      const currentIndex = mockTestResult.successCount + mockTestResult.failureCount;

      // 将当前用例标记为已完成
      const isSuccess = Math.random() > 0.2;
      mockCaseResults[currentIndex].state = isSuccess ? ExecutionState.Success : ExecutionState.Failure;
      mockCaseResults[currentIndex].begin = new Date(Date.now() - 3000).toISOString();
      mockCaseResults[currentIndex].end = new Date().toISOString();

      // 更新测试结果统计
      if (isSuccess) {
        mockTestResult.successCount++;
      } else {
        mockTestResult.failureCount++;
      }

      // 更新当前正在执行的用例
      const nextIndex = currentIndex + 1;
      if (nextIndex < mockTestResult.totalCount) {
        mockCaseResults[nextIndex].state = ExecutionState.Running;
        mockCurrentOperation = `Running test case: ${mockCaseResults[nextIndex].sequenceName}`;
      } else {
        mockProcessState = ExecutionState.Success;
        mockCurrentOperation = 'Test execution completed';
        mockTestResult.completionTime = new Date().toISOString();
        if (progressInterval !== undefined) {
          clearInterval(progressInterval);
          progressInterval = undefined;
        }
      }
    } else if (mockProcessState !== ExecutionState.Running) {
      // 如果不是运行状态，暂停执行但不清除定时器
      // 这里不做任何操作，定时器继续存在但不产生效果
    } else {
      if (progressInterval !== undefined) {
        clearInterval(progressInterval);
        progressInterval = undefined;
      }
    }
  }, 500);
}

// 保存模拟状态
export const mockAppApi = {
  // 获取应用信息
  getAppInfo(): Promise<AxiosResponse<AppInfo>> {
    return mockSuccess({
      dataFolder: 'D:\\mock\\data\\folder',
      logFolder: 'D:\\mock\\logs\\folder'
    });
  },

  // 记录错误
  logError: (data: ErrorData): Promise<AxiosResponse<void>> => {
    // 将错误添加到内存中
    errorLogs.push(data);
    return mockSuccess(undefined);
  },

  // 退出应用程序
  exit: (): Promise<AxiosResponse<void>> => {
    console.log("模拟应用程序退出");
    // 在模拟环境中，我们不能真正退出应用，只打印日志
    return mockSuccess(undefined);
  },

  // 获取最新互操作测试结果 - 修改确保序列名称唯一
  getLatestInteroperationCaseResults(): Promise<AxiosResponse<CaseResult[]>> {
    return mockSuccess(generateMockInteroperationResults());
  },

  // 生成测试用例 - 更新为支持序列筛选
  generateCases(coverageType: CoverageType | GenerateCasesRequest, sequenceNames?: string[]): Promise<AxiosResponse<CaseResult[]>> {
    // 处理两种不同的参数传递方式
    let actualCoverageType: CoverageType;
    let actualSequenceNames: string[];

    if (typeof coverageType === 'object') {
      // 如果传入的是GenerateCasesRequest对象
      actualCoverageType = coverageType.coverage;
      actualSequenceNames = coverageType.sequenceNames || [];
    } else {
      // 如果分别传入了两个参数
      actualCoverageType = coverageType;
      actualSequenceNames = sequenceNames || [];
    }

    return mockSuccess(generateMockTestCases(actualCoverageType, actualSequenceNames));
  },

  // 获取已保存的测试用例和分组树
  getCasesWithGroup(): Promise<AxiosResponse<MyGroupResponse>> {
    console.log('模拟获取已保存的测试用例');
    // 返回一些模拟的已保存数据
    const mockData = generateMockTestCasesWithGroup(CoverageType.Normal, ['TestSequence1', 'TestSequence2']);
    return mockSuccess(mockData);
  },

  // 生成测试用例 - 更新为支持序列筛选，增加2秒延迟用于调试进度条
  generateCasesWithGroup(coverageType: CoverageType | GenerateCasesRequest, sequenceNames?: string[]): Promise<AxiosResponse<MyGroupResponse>> {
    // 处理两种不同的参数传递方式
    let actualCoverageType: CoverageType;
    let actualSequenceNames: string[];

    if (typeof coverageType === 'object') {
      // 如果传入的是GenerateCasesRequest对象
      actualCoverageType = coverageType.coverage;
      actualSequenceNames = coverageType.sequenceNames || [];
    } else {
      // 如果分别传入了两个参数
      actualCoverageType = coverageType;
      actualSequenceNames = sequenceNames || [];
    }

    // 增加2秒延迟，方便调试进度条显示
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(mockSuccess(generateMockTestCasesWithGroup(actualCoverageType, actualSequenceNames)));
      }, 2000);
    });
  },

  // 获取保存的测试用例
  getSavedCases(): Promise<AxiosResponse<CaseResult[]>> {
    // 模拟已保存的测试用例，使用相同的测试用例生成函数但固定测试结果ID
    const savedCases = generateMockTestCases(CoverageType.Normal, ['can-frames', 'DiagnosticSessionControl']);
    const testResultId = 'saved-test-result-id';
    savedCases.forEach(c => {
      c.testResultId = testResultId;
      // 随机分配一些不同的状态
      const states: ExecutionState[] = [ExecutionState.Pending, ExecutionState.Running, ExecutionState.Success, ExecutionState.Failure];
      c.state = states[Math.floor(Math.random() * states.length)];
    });

    return mockSuccess(savedCases);
  },
  getSavedCaseCount(): Promise<AxiosResponse<number>> {
    return mockSuccess(9527);
  },
  // 开始测试
  startTest(): Promise<AxiosResponse<void>> {
    console.log("模拟开始测试");
    mockProcessState = ExecutionState.Running;
    mockCurrentOperation = 'Starting test execution';

    // 重置测试进度
    const savedCases = generateMockTestCases(CoverageType.Normal, ['can-frames', 'DiagnosticSessionControl']);
    mockCaseResults = savedCases;
    mockTestResult = {
      id: `test-${Date.now()}`,
      resultFolderName: 'Mock Test Run',
      testType: 'Case',
      creationTime: new Date().toISOString(),
      totalCount: savedCases.length,
      successCount: 0,
      failureCount: 0
    };

    // 启动模拟测试进度更新
    if (mockCaseResults.length > 0) {
      mockCaseResults[0].state = ExecutionState.Running;
      mockCurrentOperation = `Running test case: ${mockCaseResults[0].sequenceName}`;

      // 启动进度更新
      startProgressUpdate();
    }

    return mockSuccess(undefined);
  },

  // 停止测试
  stopTest(): Promise<AxiosResponse<void>> {
    console.log("模拟停止测试");
    mockProcessState = ExecutionState.Failure;
    mockCurrentOperation = 'Test execution stopped by user';
    mockTestResult.completionTime = new Date().toISOString();
    return mockSuccess(undefined);
  },

  // 暂停测试
  pauseTest(): Promise<AxiosResponse<void>> {
    console.log("模拟暂停测试");
    mockProcessState = ExecutionState.Paused;
    mockCurrentOperation = 'Test execution paused by user';
    return mockSuccess(undefined);
  },

  // 恢复测试
  resumeTest(): Promise<AxiosResponse<void>> {
    console.log("模拟恢复测试");
    mockProcessState = ExecutionState.Running;
    mockCurrentOperation = 'Test execution resumed by user';

    // 如果没有活动的定时器，重新启动进度更新
    if (progressInterval === undefined) {
      startProgressUpdate();
    }

    return mockSuccess(undefined);
  },

  // 获取测试状态
  getTestStatus(): Promise<AxiosResponse<TesterSnapshot>> {
    // 移除方法实现，保持与接口一致
    const snapshot: TesterSnapshot = {
      processState: mockProcessState,
      currentOperation: mockCurrentOperation,
      testResult: mockTestResult,
      caseResults: mockCaseResults
    };

    return mockSuccess(snapshot);
  },

  // 获取测试结果列表
  getTestResults(): Promise<AxiosResponse<TestResult[]>> {
    return mockSuccess(generateMockTestResults());
  },

  // 获取测试用例列表
  getCases(testResultId: string): Promise<AxiosResponse<CaseResult[]>> {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 针对特定测试结果ID生成用例
        const caseResults = generateMockTestCases(CoverageType.Normal, ['can-frames', 'DiagnosticSessionControl']);

        // 确保所有用例都关联到这个测试结果ID
        caseResults.forEach(c => {
          c.testResultId = testResultId;
          // 随机分配状态
          const states: ExecutionState[] = [ExecutionState.Success, ExecutionState.Failure];
          c.state = states[Math.floor(Math.random() * states.length)];
          c.begin = new Date(Date.now() - 3600000).toISOString();
          c.end = new Date(Date.now() - 3500000).toISOString();
        });

        resolve(mockSuccess(caseResults));
      }, 1000);
    });
  },

  // 获取用例步骤列表
  getCaseSteps(testResultId: string, caseResultId: number): Promise<AxiosResponse<CaseStep[]>> {
    return mockSuccess(generateMockCaseSteps(caseResultId));
  },

  // 删除测试结果
  deleteTestResult(testResultId: string): Promise<AxiosResponse<void>> {
    console.log(`模拟删除测试结果: ${testResultId}`);
    // 在模拟环境中，我们从生成的测试结果中过滤掉指定ID的结果
    return mockSuccess(undefined);
  },

  // 添加下载HTML报告的mock方法
  downloadHtmlReport(testResultId: string): Promise<void> {
    console.log(`Mock downloading HTML report for test result: ${testResultId}`);
    // 在mock模式下，我们只需返回一个成功的Promise
    return Promise.resolve();
  }
};
