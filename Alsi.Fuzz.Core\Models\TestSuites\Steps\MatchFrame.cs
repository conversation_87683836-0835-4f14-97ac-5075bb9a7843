using Alsi.App.Devices.Core;
using Alsi.Fuzz.Core.Models.Tester;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Collections.Generic;
using System.Xml.Serialization;

namespace Alsi.Fuzz.Core.Models.TestSuites.Steps
{
    public class MatchFrame
    {
        [XmlAttribute("dlc")]
        public string Dlc { get; set; }

        [XmlElement("hex-data")]
        public string Data { get; set; }

        [XmlAttribute("type")]
        public string Type { get; set; }

        public bool IsMatch(CanFrame frame, List<EnvVar> envVars)
        {
            var dlc = Dlc;
            if (!string.IsNullOrWhiteSpace(dlc))
            {
                envVars.Eval(ref dlc);
                if (SequenceUtils.ParseByte(dlc) != frame.Dlc)
                {
                    return false;
                }
            }

            var data = Data;
            if (!string.IsNullOrWhiteSpace(data))
            {
                envVars.Eval(ref data);
                var matchBytes = SequenceUtils.ParseMatchBytes(data);
                if (frame.Data.Length != matchBytes.Length)
                {
                    return false;

                }
                for (var i = 0; i < matchBytes.Length; i++)
                {
                    if (matchBytes[i].HasValue
                        && matchBytes[i].Value != frame.Data[i])
                    {
                        return false;
                    }
                }
            }

            var type = Type;
            if (string.IsNullOrWhiteSpace(type))
            {
                type = type.Trim();
                envVars.Eval(ref type);
                if (type.Equals("can", StringComparison.OrdinalIgnoreCase) && frame.IsCanFd)
                {
                    return false;
                }
                else if (type.Equals("canfd", StringComparison.OrdinalIgnoreCase) && !frame.IsCanFd)
                {
                    return false;
                }
            }

            return true;
        }

        public static MatchFrame Empty = new MatchFrame();
    }
}
