using Alsi.App.Devices.Core;
using Alsi.App.Devices.Core.TransportLayer;
using System;
using System.Linq;

namespace Alsi.Fuzz.Tester.Testers
{
    public class DiagRetryTester
    {
        public Response RetryResponse { get; set; }

        public void Run(CaseContext caseContext)
        {
            if (!caseContext.CaseConfig.EnableDiagRetryRequest)
            {
                return;
            }

            var payloadBytes = caseContext.CaseConfig.DiagRetryRequestPayload
                .Select(x => (byte)x)
                .ToArray();

            var request = new Request
            {
                IsCanfd = caseContext.HardwareConfig.CommunicationType == CommunicationType.CanFd,
                Payload = payloadBytes,
                PayloadLength = payloadBytes.Length,
                RequestId = caseContext.CaseConfig.RequestId,
                RequestIsExt = caseContext.CaseConfig.RequestIsExt,
                FlowControlId = caseContext.CaseConfig.RequestId,
                ResponseId = caseContext.CaseConfig.ResponseId
            };

            RetryResponse = null;

            var tpService = new TpService(new DiagParams());
            var response = tpService.Request(request, TimeSpan.FromMilliseconds(caseContext.CaseConfig.TimeoutMs), true);
            if (response?.Payload != null)
            {
                RetryResponse = response;
            }
        }
    }
}
