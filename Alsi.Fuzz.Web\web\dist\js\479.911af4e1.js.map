{"version": 3, "file": "js/479.911af4e1.js", "mappings": "uXAGA,MAAMA,EAAa,CAAEC,MAAO,gBAM5B,OAA4BC,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,cACRC,MAAO,CACLC,MAAO,CAAC,EACRC,IAAK,CAAC,EACNC,aAAc,CAAEC,KAAMC,UAExBC,KAAAA,CAAMC,GCAR,MAAMP,EAAQO,EAMRC,EAAcC,IAClB,MAAMC,EAAOD,EAAKE,cACZC,EAAQC,OAAOJ,EAAKK,WAAa,GAAGC,SAAS,EAAG,KAChDC,EAAMH,OAAOJ,EAAKQ,WAAWF,SAAS,EAAG,KACzCG,EAAQL,OAAOJ,EAAKU,YAAYJ,SAAS,EAAG,KAC5CK,EAAUP,OAAOJ,EAAKY,cAAcN,SAAS,EAAG,KAChDO,EAAUT,OAAOJ,EAAKc,cAAcR,SAAS,EAAG,KAChDS,EAAeX,OAAOJ,EAAKgB,mBAAmBV,SAAS,EAAG,KAEhE,MAAO,GAAGL,KAAQE,KAASI,KAAOE,KAASE,KAAWE,KAAWE,GAAc,EAG3EE,EAAkBjB,IACtB,MAAMS,EAAQL,OAAOJ,EAAKU,YAAYJ,SAAS,EAAG,KAC5CK,EAAUP,OAAOJ,EAAKY,cAAcN,SAAS,EAAG,KAChDO,EAAUT,OAAOJ,EAAKc,cAAcR,SAAS,EAAG,KACtD,MAAO,GAAGG,KAASE,KAAWE,GAAS,EAGnCK,GAAiBC,EAAAA,EAAAA,KAAS,KAC9B,IAAK5B,EAAMC,MAAO,MAAO,GAEzB,MAAM4B,EAAY,IAAIC,KAAK9B,EAAMC,OAC3B8B,EAAYvB,EAAWqB,GAE7B,IAAK7B,EAAME,IAAK,MAAO,UAAU6B,IAEjC,MAAMC,EAAU,IAAIF,KAAK9B,EAAME,KACzB+B,EAAUzB,EAAWwB,GACrBE,EAAaF,EAAQG,UAAYN,EAAUM,UAEjD,IAAIC,EAAe,GACnB,GAAIF,EAAa,IACfE,EAAe,GAAGF,WACb,CACL,MAAMZ,EAAUe,KAAKC,MAAMJ,EAAa,KAClCd,EAAUiB,KAAKC,MAAMhB,EAAU,IAC/BJ,EAAQmB,KAAKC,MAAMlB,EAAU,IAGjCgB,EADElB,EAAQ,EACK,GAAGA,MAAUE,EAAU,OAAOE,EAAU,MAC9CF,EAAU,EACJ,GAAGA,MAAYE,EAAU,MAEzB,GAAGA,I,CAItB,MAAO,aAAac,eAA0BL,aAAqBE,GAAS,IAGxEM,GAAgBX,EAAAA,EAAAA,KAAS,KAC7B,IAAK5B,EAAMC,MAAO,MAAO,IAEzB,MAAM4B,EAAY,IAAIC,KAAK9B,EAAMC,OAC3BuC,EAAWd,EAAeG,GAEhC,IAAK7B,EAAME,MAAQF,EAAMG,aAAc,OAAOqC,EAE9C,MAAMR,EAAU,IAAIF,KAAK9B,EAAME,KACzBgC,EAAaF,EAAQG,UAAYN,EAAUM,UAEjD,GAAID,EAAa,IACf,MAAO,GAAGA,MACL,CACL,MAAMZ,EAAUe,KAAKI,MAAMP,EAAa,KACxC,MAAO,GAAGZ,I,KDEd,MAAO,CAACoB,EAAUC,IACRD,EAAKzC,QACR2C,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,KAAaC,EAAAA,EAAAA,IAAOC,EAAAA,IAAY,CAC7CC,IAAK,EACLC,QAAStB,EAAeuB,MACxBC,UAAW,MACXC,OAAQ,OACR,cAAe,IACd,CACDC,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBC,EAAAA,EAAAA,IAAoB,OAAQ3D,GAAY4D,EAAAA,EAAAA,IAAiBjB,EAAcW,OAAQ,MAEjFO,EAAG,GACF,EAAG,CAAC,cACPC,EAAAA,EAAAA,IAAoB,IAAI,EAE9B,I,UEpGA,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,Q,UCLA,MAAM/D,EAAa,CAAEC,MAAO,gBACtB+D,EAAa,CACjBZ,IAAK,EACLnD,MAAO,qBAEHgE,EAAa,CACjBb,IAAK,EACLnD,MAAO,qBAEHiE,EAAa,CACjBd,IAAK,EACLnD,MAAO,iBAEHkE,EAAa,CACjBf,IAAK,EACLnD,MAAO,gBAEHmE,EAAa,CAAEnE,MAAO,kBACtBoE,EAAa,CACjBjB,IAAK,EACLnD,MAAO,gBAEHqE,EAAa,CAAErE,MAAO,eACtBsE,EAAa,CAAC,WACdC,EAAc,CAAEvE,MAAO,iBACvBwE,EAAc,CAAExE,MAAO,aACvByE,EAAc,CAAEzE,MAAO,eACvB0E,EAAc,CAAE1E,MAAO,iBAY7B,OAA4BC,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,4BACRC,MAAO,CACLwE,QAAS,CAAC,EACVC,QAAS,CAAErE,KAAMC,SACjBqE,kBAAmB,CAAC,EACpBC,YAAa,CAAC,GAEhBC,MAAO,CAAC,4BACRtE,KAAAA,CAAMC,GAAgBsE,KAAMC,IC6B9B,MAAM9E,EAAQO,EAORsE,EAAOC,EAKPC,GAAgBC,EAAAA,EAAAA,IAAe,IAsBrC,SAASC,IACHjF,EAAMwE,QAAQU,OAAS,IAEzBH,EAAc7B,MAAQlD,EAAMwE,QAAQW,KAAIC,GAAUA,EAAOC,QAAUC,EAAAA,GAAeC,UAClFC,IAEJ,EAzBAC,EAAAA,EAAAA,KAAU,KACRR,GAAyB,KAI3BS,EAAAA,EAAAA,KAAM,IAAM1F,EAAMwE,UAAS,KACzBS,GAAyB,GACxB,CAAEU,WAAW,KAGhBD,EAAAA,EAAAA,KAAM,IAAM1F,EAAM0E,oBAAoBkB,IAChCA,GAAe5F,EAAMwE,QAAQU,OAAS,IACxCH,EAAc7B,MAAQlD,EAAMwE,QAAQW,KAAIU,GACtCD,EAAYE,SAASD,EAAKE,gB,GAG7B,CAAEC,MAAM,IAYX,MAAMC,GAAgBrE,EAAAA,EAAAA,KAAS,IACtBmD,EAAc7B,MAAMgC,OAAS,GAAKH,EAAc7B,MAAMgD,OAAMC,GAAYA,MAI3EC,GAAkBxE,EAAAA,EAAAA,KAAS,IACxBmD,EAAc7B,MAAMmD,MAAKF,GAAYA,MAAcF,EAAc/C,QAIpEoD,GAAgB1E,EAAAA,EAAAA,KAAS,IACtBmD,EAAc7B,MAAMqD,QAAOJ,GAAYA,IAAUjB,SAIpDsB,EAAyBC,IAC7B1B,EAAc7B,MAAQ,IAAIwD,MAAM1G,EAAMwE,QAAQU,QAAQyB,KAAKF,GAC3DjB,GAAyB,EAIrBoB,EAAmBC,IACvB9B,EAAc7B,MAAM2D,IAAU9B,EAAc7B,MAAM2D,GAClDrB,GAAyB,EAIrBsB,EAAmBA,CAACL,EAAcI,EAAehB,KACrDd,EAAc7B,MAAM2D,GAASJ,EAC7BjB,GAAyB,EAIrBA,EAA0BA,KAE9B,MAAMW,EAAWnG,EAAMwE,QACpB+B,QAAO,CAAC9C,EAAGoD,IAAU9B,EAAc7B,MAAM2D,KACzC1B,KAAIU,GAAQA,EAAKE,eAGdgB,EAAiB,IAAI,IAAIC,IAAIb,IAEnCtB,EAAK,2BAA4BkC,EAAe,EAI5CE,GAAqBrF,EAAAA,EAAAA,KAAS,KAClC,IAAK5B,EAAMwE,SAAoC,IAAzBxE,EAAMwE,QAAQU,OAAc,MAAO,GAEzD,MAAMgC,EAAa,IAAIC,IACjBC,EAAa,IAAIJ,IAavB,OAVAhH,EAAMwE,QAAQ6C,SAAQjC,IACpB,MAAMkC,EAAQJ,EAAWK,IAAInC,EAAOW,eAAiB,EACrDmB,EAAWM,IAAIpC,EAAOW,aAAcuB,EAAQ,GAGxCA,EAAQ,GACVF,EAAWK,IAAIrC,EAAOW,a,IAInBW,MAAMgB,KAAKN,EAAW,IAIzBO,EAAeC,GACZX,EAAmB/D,MAAM4C,SAAS8B,GD/B3C,MAAO,CAAClF,EAAUC,KAChB,MAAMkF,GAAqBC,EAAAA,EAAAA,IAAkB,WACvCC,GAAyBD,EAAAA,EAAAA,IAAkB,eAC3CE,GAAoBF,EAAAA,EAAAA,IAAkB,UAE5C,OAAQlF,EAAAA,EAAAA,OAAcqF,EAAAA,EAAAA,IAAoB,MAAOrI,EAAY,CAC3D+C,EAAO,KAAOA,EAAO,IAAKY,EAAAA,EAAAA,IAAoB,MAAO,CAAE1D,MAAO,gBAAkB,EAC9E0D,EAAAA,EAAAA,IAAoB,KAAM,KAAM,4BAC9B,IACH0D,EAAmB/D,MAAMgC,OAAS,IAC9BtC,EAAAA,EAAAA,OAAcqF,EAAAA,EAAAA,IAAoB,MAAOrE,EAAY,EACpDsE,EAAAA,EAAAA,KAAapF,EAAAA,EAAAA,IAAOqF,EAAAA,IAAU,CAC5BC,MAAO,6CACPhI,KAAM,UACNiI,UAAU,EACV,YAAa,IACZ,CACDhF,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBgF,EAAAA,EAAAA,IAAiB,4BAA6B9E,EAAAA,EAAAA,IAAiByD,EAAmB/D,MAAMqF,KAAK,OAAS,IAAK,GAC3G5F,EAAO,KAAOA,EAAO,IAAKY,EAAAA,EAAAA,IAAoB,KAAM,KAAM,MAAO,IACjEZ,EAAO,KAAOA,EAAO,IAAK2F,EAAAA,EAAAA,IAAiB,0CAE7C7E,EAAG,QAGPC,EAAAA,EAAAA,IAAoB,IAAI,GAC3BhB,EAAK+B,UACD7B,EAAAA,EAAAA,OAAcqF,EAAAA,EAAAA,IAAoB,MAAOpE,EAAY,EACpDqE,EAAAA,EAAAA,IAAaL,EAAoB,CAAEhI,MAAO,cAAgB,CACxDwD,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtB4E,EAAAA,EAAAA,KAAapF,EAAAA,EAAAA,IAAO0F,EAAAA,aAEtB/E,EAAG,IAELd,EAAO,KAAOA,EAAO,IAAK2F,EAAAA,EAAAA,IAAiB,oBAEpB,IAAxB5F,EAAK8B,QAAQU,SACXtC,EAAAA,EAAAA,OAAcqF,EAAAA,EAAAA,IAAoB,MAAOnE,EAAY,4CACrDlB,EAAAA,EAAAA,OAAcqF,EAAAA,EAAAA,IAAoB,MAAOlE,EAAY,EACpDR,EAAAA,EAAAA,IAAoB,MAAOS,EAAY,EACrCkE,EAAAA,EAAAA,IAAaH,EAAwB,CACnCU,WAAYxC,EAAc/C,MAC1B,sBAAuBP,EAAO,KAAOA,EAAO,GAAM+F,GAAkBzC,EAAe/C,MAAQwF,GAC3FC,cAAevC,EAAgBlD,MAC/B0F,SAAUpC,EACVqC,KAAM,SACL,CACDxF,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBgF,EAAAA,EAAAA,IAAiB,iBAAkB9E,EAAAA,EAAAA,IAAiB8C,EAAcpD,OAAS,KAAMM,EAAAA,EAAAA,IAAiBd,EAAK8B,QAAQU,QAAU,KAAM,MAEjIzB,EAAG,GACF,EAAG,CAAC,aAAc,kBACpBf,EAAKiC,aAAejC,EAAKiC,YAAc,IACnC/B,EAAAA,EAAAA,OAAcqF,EAAAA,EAAAA,IAAoB,MAAOhE,EAAY,sBAAuBT,EAAAA,EAAAA,KAAiBV,EAAAA,EAAAA,IAAOgG,EAAAA,EAAPhG,CAAuBJ,EAAKiC,cAAe,KACzIjB,EAAAA,EAAAA,IAAoB,IAAI,MAE9BH,EAAAA,EAAAA,IAAoB,MAAOW,EAAY,GACpCtB,EAAAA,EAAAA,KAAW,IAAOqF,EAAAA,EAAAA,IAAoBc,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAYtG,EAAK8B,SAAS,CAACqB,EAAMgB,MAC/EjE,EAAAA,EAAAA,OAAcqF,EAAAA,EAAAA,IAAoB,MAAO,CAC/CjF,IAAK6C,EAAKoD,IAAM,QAAQpC,IACxBhH,OAAOqJ,EAAAA,EAAAA,IAAgB,CAAC,YAAa,CAAE,QAAWrC,EAAQ,IAAM,EAAG,UAAac,EAAY9B,EAAKE,iBACjGoD,SAASC,EAAAA,EAAAA,KAAgBV,GAAiB9B,EAAgBC,IAAS,CAAC,UACnE,EACDtD,EAAAA,EAAAA,IAAoB,MAAOa,EAAa,EACtC8D,EAAAA,EAAAA,IAAaH,EAAwB,CACnCU,WAAY1D,EAAc7B,MAAM2D,GAChC,sBAAwB6B,GAAkB3D,EAAc7B,MAAM2D,GAAU6B,EACxEG,KAAM,QACNM,QAASxG,EAAO,KAAOA,EAAO,IAAKyG,EAAAA,EAAAA,KAAe,QAAU,CAAC,UAC7DR,SAAWnC,GAAQK,EAAiBL,EAAKI,EAAOhB,IAC/C,KAAM,EAAG,CAAC,aAAc,sBAAuB,gBAEpDtC,EAAAA,EAAAA,IAAoB,MAAOc,EAAa,EACtCiE,EAAAA,EAAAA,KAAiB9E,EAAAA,EAAAA,IAAiBqC,EAAKE,cAAgB,IAAK,GAC3D4B,EAAY9B,EAAKE,gBACbnD,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAamF,EAAmB,CAC7ChF,IAAK,EACL6F,KAAM,QACNzI,KAAM,UACNgD,OAAQ,OACRvD,MAAO,iBACN,CACDwD,SAASC,EAAAA,EAAAA,KAAS,IAAMX,EAAO,KAAOA,EAAO,GAAK,EAChD2F,EAAAA,EAAAA,IAAiB,mBAEnB7E,EAAG,MAELC,EAAAA,EAAAA,IAAoB,IAAI,MAE9BH,EAAAA,EAAAA,IAAoB,MAAOe,EAAa,EACtC4D,EAAAA,EAAAA,IAAamB,EAAAA,EAAc,CACzBhE,MAAOQ,EAAKR,OACX,KAAM,EAAG,CAAC,aAEf9B,EAAAA,EAAAA,IAAoB,MAAOgB,EAAa,EACtC2D,EAAAA,EAAAA,IAAaoB,EAAa,CACxBrJ,MAAO4F,EAAK5F,MACZC,IAAK2F,EAAK3F,IACVC,cAAc,GACb,KAAM,EAAG,CAAC,QAAS,WAEvB,GAAIgE,MACL,YAGd,CAEJ,IErQA,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,QCLA,MAAMvE,EAAa,CAAC,wBACdgE,EAAa,CAAE/D,MAAO,cACtBgE,EAAa,CAAEhE,MAAO,0BACtBiE,EAAa,CAAEjE,MAAO,iBACtBkE,EAAa,CAAElE,MAAO,gBAS5B,OAA4BC,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,sBACRC,MAAO,CACLuJ,QAAS,CAAEnJ,KAAMC,SACjBmJ,SAAU,CAAC,EACX9E,kBAAmB,CAAC,EACpB+E,oBAAqB,CAAC,GAExB7E,MAAO,CAAC,iBAAkB,aAC1BtE,KAAAA,CAAMC,GAAgBsE,KAAMC,ICiB9B,MAAM9E,EAAQO,EAORsE,EAAOC,EAMP4E,GAAa1E,EAAAA,EAAAA,KAAI,GACjB2E,GAAwB3E,EAAAA,EAAAA,IAAkB,IAC1C4E,GAAwB5E,EAAAA,EAAAA,KAAI,GAC5B6E,GAAyB7E,EAAAA,EAAAA,IAAI,IACnC,IAAI8E,EAAuC,KAG3C,MAAMC,GAAW/E,EAAAA,EAAAA,IAAI,CACjBwE,SAAUQ,EAAAA,GAAaC,OACvBvF,kBAAmB,KAGjBwF,GAAgBtI,EAAAA,EAAAA,IAAS,CAC3B2F,IAAKA,IAAMvH,EAAMuJ,QACjB/B,IAAMtE,GAAU2B,EAAK,iBAAkB3B,KAIrCiH,GAAiBvI,EAAAA,EAAAA,KAAS,IACxBiI,EAAuB3G,MAChB2G,EAAuB3G,MAE3B,6BAILkH,GAAiCxI,EAAAA,EAAAA,KAAS,KAC5C,GAAgD,IAA5CmI,EAAS7G,MAAMwB,kBAAkBQ,QAAuD,IAAvCyE,EAAsBzG,MAAMgC,OAC7E,OAAO,EAIX,IAAImF,EAAqB,EACrBC,EAAsB,EAU1B,OARAX,EAAsBzG,MAAMmE,SAAQjC,IAChC,GAAIA,EAAOnF,OAASmF,EAAOlF,KAAO6J,EAAS7G,MAAMwB,kBAAkBoB,SAASV,EAAOW,cAAe,CAC9F,MAAMwE,EAAgB,IAAIzI,KAAKsD,EAAOlF,KAAKiC,UAAY,IAAIL,KAAKsD,EAAOnF,OAAOkC,UAC9EkI,GAAsBE,EACtBD,GAAuB,C,KAIxBA,EAAsB,EAAID,EAAqBC,EAAsB,CAAC,IAI3EE,EAA6BC,UAC/Bb,EAAsB1G,OAAQ,EAC9B,IACI,MAAMwH,QAAiBC,EAAAA,GAAOC,qCAC9BjB,EAAsBzG,MAAQwH,EAASG,KAGvC,MAAMC,EAAsBJ,EAASG,KAChCtE,QAAOnB,GAAUA,EAAOC,QAAUC,EAAAA,GAAeC,UACjDJ,KAAIC,GAAUA,EAAOW,eAC1BgE,EAAS7G,MAAMwB,kBAAoBoG,C,CACrC,MAAOC,GACLC,QAAQD,MAAM,YAAaA,GAC3BE,EAAAA,GAAUF,MAAM,yC,CAClB,QACEnB,EAAsB1G,OAAQ,C,GAKhCgI,EAAiBT,UACnB,GAAgD,IAA5CV,EAAS7G,MAAMwB,kBAAkBQ,OAArC,CAKAwE,EAAWxG,OAAQ,EACnB2G,EAAuB3G,MAAQ,GAG/BiI,IAEA,IACI,MAAMT,QAAiBC,EAAAA,GAAOS,uBAAuBrB,EAAS7G,MAAMsG,SAAUO,EAAS7G,MAAMwB,mBAG7FG,EAAK,YAAa6F,EAASG,KAAKQ,MAAOX,EAASG,KAAKS,WACrDpB,EAAchH,OAAQ,EAEtB+H,EAAAA,GAAUM,QAAQ,0BAA0Bb,EAASG,KAAKQ,MAAMnG,oB,CAClE,MAAO6F,GACLC,QAAQD,MAAM,YAAaA,GAC3BE,EAAAA,GAAUF,MAAM,gC,CAClB,QACErB,EAAWxG,OAAQ,EAEnBsI,G,OAxBAP,EAAAA,GAAUQ,QAAQ,sC,EA6BpBN,EAA+BA,KACjC,MAAMO,EAAejB,UACjB,MAAMC,QAAiBC,EAAAA,GAAOgB,wBAC9B9B,EAAuB3G,MAAQwH,EAASG,KAGpCnB,EAAWxG,QACX4G,EAAwB8B,OAAOC,WAAWH,EAAc,K,EAKhE5B,EAAwB8B,OAAOC,WAAWH,EAAc,IAAI,EAI1DF,EAA8BA,KAC5B1B,IACA8B,OAAOE,aAAahC,GACpBA,EAAwB,K,EAK1BiC,EAAeA,KACjB7B,EAAchH,OAAQ,CAAK,EAIzB8I,EAAoBA,KAEtBR,IAGAzB,EAAS7G,MAAMsG,SAAWxJ,EAAMwJ,SAChCO,EAAS7G,MAAMwB,kBAAoB,IAAI1E,EAAM0E,kBAAkB,EDCnE,OCKAgB,EAAAA,EAAAA,KAAM,IAAM1F,EAAMuJ,UAAUA,IACpBA,IAEAQ,EAAS7G,MAAMsG,SAAWxJ,EAAMwJ,SAChCO,EAAS7G,MAAMwB,kBAAoB,IAAI1E,EAAM0E,mBAG7C8F,I,KAKR/E,EAAAA,EAAAA,KAAU,KACFzF,EAAMuJ,SACNiB,G,IDnBD,CAAC9H,EAAUC,KAChB,MAAMsJ,GAAsBnE,EAAAA,EAAAA,IAAkB,YACxCoE,GAA4BpE,EAAAA,EAAAA,IAAkB,kBAC9CqE,GAAuBrE,EAAAA,EAAAA,IAAkB,aACzCsE,GAAuBtE,EAAAA,EAAAA,IAAkB,aACzCuE,GAAqBC,EAAAA,EAAAA,IAAkB,WAE7C,OAAQ1J,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAauJ,EAAsB,CACvD3D,WAAYyB,EAAchH,MAC1B,sBAAuBP,EAAO,KAAOA,EAAO,GAAM+F,GAAkBwB,EAAehH,MAAQwF,GAC3FN,MAAO,sBACPvI,MAAO,qBACP,wBAAwB,EACxB,yBAAyB,EACzB0M,QAASP,GACR,CACDQ,QAAQlJ,EAAAA,EAAAA,KAAS,IAAM,EACrBC,EAAAA,EAAAA,IAAoB,MAAOO,EAAY,EACrCP,EAAAA,EAAAA,IAAoB,MAAOQ,EAAY,EACrCmE,EAAAA,EAAAA,IAAaiE,EAAsB,CACjChD,QAAS4C,EACTU,SAAU/C,EAAWxG,OACpB,CACDG,SAASC,EAAAA,EAAAA,KAAS,IAAMX,EAAO,KAAOA,EAAO,GAAK,EAChD2F,EAAAA,EAAAA,IAAiB,cAEnB7E,EAAG,GACF,EAAG,CAAC,cACPyE,EAAAA,EAAAA,IAAaiE,EAAsB,CACjC/L,KAAM,UACN+I,QAAS+B,EACTzG,QAASiF,EAAWxG,MACpBuJ,SAAsD,IAA5C1C,EAAS7G,MAAMwB,kBAAkBQ,QAC1C,CACD7B,SAASC,EAAAA,EAAAA,KAAS,IAAMX,EAAO,KAAOA,EAAO,GAAK,EAChD2F,EAAAA,EAAAA,IAAiB,kBAEnB7E,EAAG,GACF,EAAG,CAAC,UAAW,oBAIxBJ,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBoJ,EAAAA,EAAAA,MAAiB9J,EAAAA,EAAAA,OAAcqF,EAAAA,EAAAA,IAAoB,MAAO,CACxDpI,MAAO,iBACP,uBAAwBsK,EAAejH,OACtC,EACDK,EAAAA,EAAAA,IAAoB,MAAOK,EAAY,CACrCjB,EAAO,KAAOA,EAAO,IAAKY,EAAAA,EAAAA,IAAoB,MAAO,KAAM,aAAc,KACzE2E,EAAAA,EAAAA,IAAagE,EAA2B,CACtCzD,WAAYsB,EAAS7G,MAAMsG,SAC3B,sBAAuB7G,EAAO,KAAOA,EAAO,GAAM+F,GAAkBqB,EAAS7G,MAAMsG,SAAYd,GAC/FG,KAAM,SACL,CACDxF,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtB4E,EAAAA,EAAAA,IAAa+D,EAAqB,CAChCU,OAAO7J,EAAAA,EAAAA,IAAOkH,EAAAA,IAAcC,QAC3B,CACD5G,SAASC,EAAAA,EAAAA,KAAS,IAAMX,EAAO,KAAOA,EAAO,GAAK,EAChD2F,EAAAA,EAAAA,IAAiB,cAEnB7E,EAAG,GACF,EAAG,CAAC,WACPyE,EAAAA,EAAAA,IAAa+D,EAAqB,CAChCU,OAAO7J,EAAAA,EAAAA,IAAOkH,EAAAA,IAAc4C,MAC3B,CACDvJ,SAASC,EAAAA,EAAAA,KAAS,IAAMX,EAAO,KAAOA,EAAO,GAAK,EAChD2F,EAAAA,EAAAA,IAAiB,YAEnB7E,EAAG,GACF,EAAG,CAAC,aAETA,EAAG,GACF,EAAG,CAAC,kBAETF,EAAAA,EAAAA,IAAoB,MAAOM,EAAY,EACrCqE,EAAAA,EAAAA,IAAa2E,EAA2B,CACtCrI,QAASmF,EAAsBzG,MAC/BuB,QAASmF,EAAsB1G,MAC/B,eAAgBkH,EAA+BlH,MAC/CwB,kBAAmBqF,EAAS7G,MAAMwB,kBAClC,6BAA8B/B,EAAO,KAAOA,EAAO,GAAM+F,GAAkBqB,EAAS7G,MAAMwB,kBAAqBgE,IAC9G,KAAM,EAAG,CAAC,UAAW,UAAW,eAAgB,yBAEpD,EAAG9I,IAAc,CAClB,CAACyM,EAAoB3C,EAAWxG,YAGpCO,EAAG,GACF,EAAG,CAAC,cAAc,CAEvB,IElRA,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,Q,UCLA,MAAM7D,EAAa,CAAEkN,MAAO,CAAC,QAAU,OAAO,KAAO,IAAI,iBAAiB,WACpElJ,EAAa,CAAE/D,MAAO,aACtBgE,EAAa,CAAEhE,MAAO,cACtBiE,EAAa,CACjBd,IAAK,EACLnD,MAAO,iBAEHkE,EAAa,CAAElE,MAAO,oBACtBmE,EAAa,CACjBhB,IAAK,EACLnD,MAAO,qBAEHoE,EAAa,CACjBjB,IAAK,EACLnD,MAAO,iBAEHqE,EAAa,CACjBlB,IAAK,EACLnD,MAAO,0BCqCHkN,EAAc,GACdC,GAAc,GACdC,GAAgB,GDxBtB,QAA4BnN,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,sBACRC,MAAO,CACLqL,MAAO,CAAC,EACR3B,WAAY,CAAEtJ,KAAMC,SACpB6M,OAAQ,CAAE9M,KAAMC,SAChB8M,aAAc,CAAC,EACfC,iBAAkB,CAAEhN,KAAMC,UAE5BC,KAAAA,CAAMC,GCCR,MAAMP,EAAQO,EASR8M,GAAiBrI,EAAAA,EAAAA,IAAwB,MACzCsI,GAAkBtI,EAAAA,EAAAA,IAAwB,MAChD,IAAIuI,EAAqB,KAMzB,MAAMC,EAAoBA,KACxB,IAAKF,EAAgBpK,MAAO,OAG5BoK,EAAgBpK,MAAMuK,UAAY,GAGlC,MAAMC,EAASC,SAASC,cAAc,OACtCF,EAAOG,UAAY,aACnBH,EAAOZ,MAAMgB,OAAS,GAAGb,OAGzB,MAAMc,EAAWJ,SAASC,cAAc,OACxCG,EAASC,YAAc,KACvBD,EAASF,UAAY,iBACrBH,EAAOO,YAAYF,GAGnB,MAAMG,EAAaP,SAASC,cAAc,OAC1CM,EAAWF,YAAc,OACzBE,EAAWL,UAAY,mBACvBH,EAAOO,YAAYC,GAGnB,MAAMC,EAAcR,SAASC,cAAc,OAC3CO,EAAYH,YAAc,YAC1BG,EAAYN,UAAY,oBACxBH,EAAOO,YAAYE,GAGnBb,EAAgBpK,MAAM+K,YAAYP,EAAO,EAIrCU,EAAoBA,KACnBf,EAAenK,OAAUlD,EAAMqL,MAAMnG,SAG1CsI,IAGID,GACFA,EAAcc,UAGhBd,EAAgB,IAAIe,EAAAA,EAAc,CAChCC,UAAWlB,EAAenK,MAC1BsL,MAAOxO,EAAMqL,MACboD,WAAY1B,EACZ2B,WAAY1B,GACZ2B,WAAYA,CAAC9I,EAAkBgB,KAE7B,MAAM+H,EAAMjB,SAASC,cAAc,OACnCgB,EAAIf,UAAY,WAGhBe,EAAI9B,MAAMgB,OAAS,GAAGf,MACtB6B,EAAI9B,MAAM+B,WAAa,GAAG9B,MAC1B6B,EAAI9B,MAAMgC,aAAejI,IAAU7G,EAAMqL,MAAMnG,OAAS,EAAI,OAAS,oBACrE0J,EAAI9B,MAAMiC,gBAAkBlI,EAAQ,IAAM,EAAI,OAAS,UAGvD+H,EAAII,YAAc,KAChBJ,EAAI9B,MAAMiC,gBAAkB,SAAS,EAEvCH,EAAIK,WAAa,KACfL,EAAI9B,MAAMiC,gBAAkBlI,EAAQ,IAAM,EAAI,OAAS,UACvD+H,EAAI9B,MAAMgC,aAAejI,IAAU7G,EAAMqL,MAAMnG,OAAS,EAAI,OAAS,mBAAmB,EAI1F,MAAMgK,EAASvB,SAASC,cAAc,OACtCsB,EAAOrB,UAAY,mBAGnB,MAAMsB,EAAQxB,SAASC,cAAc,OACrCuB,EAAMnB,YAAc,GAAGnI,EAAKoD,IAAMpC,EAAQ,IAC1CsI,EAAMtB,UAAY,eAClBqB,EAAOjB,YAAYkB,GAGnB,MAAMC,EAAUzB,SAASC,cAAc,OACvCwB,EAAQpB,YAAcnI,EAAK+B,MAAQ,IACnCwH,EAAQhH,MAAQvC,EAAK+B,MAAQ,GAC7BwH,EAAQvB,UAAY,iBACpBqB,EAAOjB,YAAYmB,GAGnB,MAAMC,EAAW1B,SAASC,cAAc,OAQxC,OAPAyB,EAASrB,YAAcnI,EAAKyJ,WAAa,IACzCD,EAASjH,MAAQvC,EAAKyJ,WAAa,GACnCD,EAASxB,UAAY,kBACrBqB,EAAOjB,YAAYoB,GAEnBT,EAAIX,YAAYiB,GAETN,CAAG,IAEZ,EAIEW,EAAsBA,MAC1BC,EAAAA,EAAAA,KAAS,KACPpB,GAAmB,GACnB,EDeJ,OCXA1I,EAAAA,EAAAA,KAAM,IAAM1F,EAAMqL,QAAO,MACvBmE,EAAAA,EAAAA,KAAS,KACPD,GAAqB,GACrB,GACD,CAAEvJ,MAAM,KAGXP,EAAAA,EAAAA,KAAU,MACR+J,EAAAA,EAAAA,KAAS,KACPpB,GAAmB,GACnB,KAIJqB,EAAAA,EAAAA,KAAY,KACNlC,IACFA,EAAcc,UACdd,EAAgB,K,IDNb,CAAC7K,EAAUC,KAChB,MAAM+M,GAAyB5H,EAAAA,EAAAA,IAAkB,eAC3CD,GAAqBC,EAAAA,EAAAA,IAAkB,WAE7C,OAAQlF,EAAAA,EAAAA,OAAcqF,EAAAA,EAAAA,IAAoB,MAAOrI,EAAY,EAC3D2D,EAAAA,EAAAA,IAAoB,MAAOK,EAAY,EACrCL,EAAAA,EAAAA,IAAoB,OAAQ,KAAM,aAAcC,EAAAA,EAAAA,IAAiBd,EAAK2I,MAAMnG,QAAU,UAAW,MAEnG3B,EAAAA,EAAAA,IAAoB,MAAOM,EAAY,CACpCnB,EAAKwK,QAAUxK,EAAKyK,eAChBvK,EAAAA,EAAAA,OAAcqF,EAAAA,EAAAA,IAAoB,MAAOnE,EAAY,CACpDnB,EAAO,KAAOA,EAAO,IAAKY,EAAAA,EAAAA,IAAoB,MAAO,CAAE1D,MAAO,mBAAqB,EACjF0D,EAAAA,EAAAA,IAAoB,OAAQ,KAAM,0BAChC,KACJ2E,EAAAA,EAAAA,IAAawH,EAAwB,CACnCC,WAAYjN,EAAKyK,aAAayC,SAC9BC,OAAQnN,EAAKyK,aAAapC,MAAQ,iBAAc+E,EAChD,eAAgB,GACf,KAAM,EAAG,CAAC,aAAc,YAC3BvM,EAAAA,EAAAA,IAAoB,MAAOQ,GAAYP,EAAAA,EAAAA,IAAiBd,EAAKyK,aAAa4C,SAAU,OAEtFrM,EAAAA,EAAAA,IAAoB,IAAI,GAC3BhB,EAAKgH,aACD9G,EAAAA,EAAAA,OAAcqF,EAAAA,EAAAA,IAAoB,MAAOjE,EAAY,EACpDkE,EAAAA,EAAAA,IAAaL,EAAoB,CAAEhI,MAAO,cAAgB,CACxDwD,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtB4E,EAAAA,EAAAA,KAAapF,EAAAA,EAAAA,IAAO0F,EAAAA,aAEtB/E,EAAG,IAELd,EAAO,KAAOA,EAAO,IAAK2F,EAAAA,EAAAA,IAAiB,uBAEtB,IAAtB5F,EAAK2I,MAAMnG,QAAgBxC,EAAK0K,mBAC9BxK,EAAAA,EAAAA,OAAcqF,EAAAA,EAAAA,IAAoB,MAAOhE,EAAY,0CACrDvB,EAAK2I,MAAMnG,OAAS,IAClBtC,EAAAA,EAAAA,OAAcqF,EAAAA,EAAAA,IAAoB,MAAO/D,EAAY,EACpDX,EAAAA,EAAAA,IAAoB,MAAO,CACzB1D,MAAO,eACPmQ,QAAS,kBACThL,IAAKsI,GACJ,KAAM,MACT/J,EAAAA,EAAAA,IAAoB,MAAO,CACzByM,QAAS,iBACThL,IAAKqI,EACLxN,MAAO,iBACN,KAAM,SAEX6D,EAAAA,EAAAA,IAAoB,IAAI,MAElC,CAEJ,IEpOA,MAAM,IAA2B,OAAgB,GAAQ,CAAC,CAAC,YAAY,qBAEvE,UCLA,MAAM9D,GAAa,CAAC,wBACdgE,GAAa,CAAE/D,MAAO,WACtBgE,GAAa,CAAEhE,MAAO,gBACtBiE,GAAa,CAAEjE,MAAO,kBACtBkE,GAAa,CAAElE,MAAO,gBACtBmE,GAAa,CACjBhB,IAAK,EACLnD,MAAO,eAEHoE,GAAa,CAAEpE,MAAO,cACtBqE,GAAa,CAAErE,MAAO,gBACtBsE,GAAa,CAAEtE,MAAO,aACtBuE,GAAc,CAAEvE,MAAO,aACvBwE,GAAc,CAAExE,MAAO,cACvByE,GAAc,CAClBtB,IAAK,EACLnD,MAAO,gBAEH0E,GAAc,CAAE1E,MAAO,0BAY7B,QAA4BC,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,YACRO,KAAAA,CAAMC,GCuCR,MAAMkE,GAAUO,EAAAA,EAAAA,KAAI,GACdiL,GAAcjL,EAAAA,EAAAA,IAAI,IAClBkL,GAAmBlL,EAAAA,EAAAA,IAAIgF,EAAAA,GAAaC,QACpCvF,GAAoBM,EAAAA,EAAAA,IAAc,IAClCyE,GAAsBzE,EAAAA,EAAAA,IAAY,GAClCmL,GAAqBnL,EAAAA,EAAAA,KAAI,GAGzBoL,GAAWpL,EAAAA,EAAAA,IAAkB,IAC7BqL,GAAiBrL,EAAAA,EAAAA,IAAkB,IACnCsG,GAAYtG,EAAAA,EAAAA,IAA0B,MAGtCsL,GAAUtL,EAAAA,EAAAA,MACVuL,GAAmBvL,EAAAA,EAAAA,KAAI,GACvBwL,GAAkBxL,EAAAA,EAAAA,IAAqB,IACvCyL,GAAUzL,EAAAA,EAAAA,IAAI,IAAIlD,MAGlB4O,EAAY,CAChBC,SAAU,WACVhE,MAAO,QAIHiE,GAAgBhP,EAAAA,EAAAA,KAAS,IACtB0J,EAAUpI,MAAQ,CAACoI,EAAUpI,OAAS,KAGzC2N,GAAejP,EAAAA,EAAAA,KAAS,IACrBwO,EAASlN,MAAMgC,SAGlBkI,GAAmBxL,EAAAA,EAAAA,KAAS,IACC,IAA1BwO,EAASlN,MAAMgC,SAGlB4L,GAAUlP,EAAAA,EAAAA,KAAS,IAChBwO,EAASlN,MAAMgC,OAAS,IAG3B6L,GAAcnP,EAAAA,EAAAA,KAAS,IACpB4O,EAAgBtN,MAAMgC,OAAS,IAAMT,EAAQvB,QAIhD8N,EAAkBvG,UACtBwF,EAAY/M,MAAQ,wBACpBuB,EAAQvB,OAAQ,EAChB,IACE,MAAMwH,QAAiBC,EAAAA,GAAOsG,oBAC9Bb,EAASlN,MAAQwH,EAASG,KAAKQ,MAC/BC,EAAUpI,MAAQwH,EAASG,KAAKS,UAGhCkF,EAAgBtN,MAAQ0N,EAAc1N,MAGlCoI,EAAUpI,OACZgO,EAAgB5F,EAAUpI,M,CAE5B,MAAO6H,GACPC,QAAQD,MAAM,YAAaA,GAC3BE,EAAAA,GAAUF,MAAM,6B,CAChB,QACAtG,EAAQvB,OAAQ,C,GAIdiO,EAAYA,KAChBZ,EAAiBrN,OAAQ,EACzBuN,EAAQvN,MAAQ,IAAIpB,IAAM,EAGtBsP,EAAcA,KAClBb,EAAiBrN,OAAQ,EACzBuN,EAAQvN,MAAQ,IAAIpB,IAAM,EAItBoP,EAAmBG,IACvB,MAAMC,EAAOD,EAAKpI,GAClB+B,QAAQuG,IAAI,mBAAoBF,EAAM,OAAQC,GAC9C,MAAME,GAAcF,EAAKxL,SAAS,KAClCuK,EAAenN,MAAQkN,EAASlN,MAAMqD,QAAOkL,GAAKD,GAAeC,EAAEC,WAAaD,EAAEC,UAAU5L,SAASwL,KACrGtG,QAAQuG,IAAI,iBAAkBnB,EAASlN,MAAM,EAIzCyO,EAAqBA,KAEzBxB,EAAmBjN,OAAQ,CAAI,EAI3B0O,EAAmBA,CAACC,EAAwBC,KAEhD1B,EAASlN,MAAQ2O,EACjBvG,EAAUpI,MAAQ4O,EAGlBtB,EAAgBtN,MAAQ0N,EAAc1N,MAGlCoI,EAAUpI,OACZgO,EAAgB5F,EAAUpI,M,EAKxB6O,EAAatH,UACjB,IACEwF,EAAY/M,MAAQ,uBACpBuB,EAAQvB,OAAQ,QACVyH,EAAAA,GAAOqH,UAAU5B,EAASlN,OAChC+H,EAAAA,GAAUM,QAAQ,GAAG6E,EAASlN,MAAMgC,uC,CACpC,MAAO6F,GACPC,QAAQD,MAAM,YAAaA,GAC3BE,EAAAA,GAAUF,MAAM,4B,CAChB,QACAtG,EAAQvB,OAAQ,C,GD5BpB,OCiCAuC,EAAAA,EAAAA,KAAU,KACRuL,GAAiB,IDlCZ,CAACtO,EAAUC,KAChB,MAAMwJ,GAAuBrE,EAAAA,EAAAA,IAAkB,aACzCmK,GAAqBnK,EAAAA,EAAAA,IAAkB,WACvCuE,GAAqBC,EAAAA,EAAAA,IAAkB,WAE7C,OAAOI,EAAAA,EAAAA,MAAiB9J,EAAAA,EAAAA,OAAcqF,EAAAA,EAAAA,IAAoB,MAAO,CAC/DpI,MAAO,uBACP,uBAAwBoQ,EAAY/M,OACnC,EACDK,EAAAA,EAAAA,IAAoB,MAAOK,GAAY,EACrCL,EAAAA,EAAAA,IAAoB,MAAOM,GAAY,EACrCqE,EAAAA,EAAAA,IAAaiE,EAAsB,CACjChD,QAASgI,EACT/Q,KAAM,UACNyI,KAAM,QACNhJ,MAAO,gBACP4M,UAAWsE,EAAY7N,OACtB,CACDG,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtB4E,EAAAA,EAAAA,KAAapF,EAAAA,EAAAA,IAAOoP,EAAAA,IAAkB,CAAEC,KAAM,uCAC9CxP,EAAO,KAAOA,EAAO,IAAKY,EAAAA,EAAAA,IAAoB,OAAQ,CAAE1D,MAAO,eAAiB,cAAe,OAEjG4D,EAAG,GACF,EAAG,CAAC,cACPyE,EAAAA,EAAAA,IAAaiE,EAAsB,CACjChD,QAASiI,EACThR,KAAM,UACNyI,KAAM,QACNhJ,MAAO,kBACP4M,UAAWsE,EAAY7N,OACtB,CACDG,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtB4E,EAAAA,EAAAA,KAAapF,EAAAA,EAAAA,IAAOoP,EAAAA,IAAkB,CAAEC,KAAM,qCAC9CxP,EAAO,KAAOA,EAAO,IAAKY,EAAAA,EAAAA,IAAoB,OAAQ,CAAE1D,MAAO,eAAiB,gBAAiB,OAEnG4D,EAAG,GACF,EAAG,CAAC,gBAETF,EAAAA,EAAAA,IAAoB,MAAOO,GAAY,EACrCoE,EAAAA,EAAAA,IAAaiE,EAAsB,CACjC/L,KAAM,UACN+I,QAASwI,EACT9I,KAAM,SACL,CACDxF,SAASC,EAAAA,EAAAA,KAAS,IAAMX,EAAO,KAAOA,EAAO,GAAK,EAChD2F,EAAAA,EAAAA,IAAiB,wBAEnB7E,EAAG,SAITF,EAAAA,EAAAA,IAAoB,MAAOQ,GAAY,CACnCU,EAAQvB,OAgCNQ,EAAAA,EAAAA,IAAoB,IAAI,KA/BvBd,EAAAA,EAAAA,OAAcqF,EAAAA,EAAAA,IAAoB,MAAOjE,GAAY,EACpDT,EAAAA,EAAAA,IAAoB,MAAOU,GAAY,EACrCV,EAAAA,EAAAA,IAAoB,OAAQ,KAAM,UAAWC,EAAAA,EAAAA,IAAiBqN,EAAa3N,OAAS,SAAU,MAEhGK,EAAAA,EAAAA,IAAoB,MAAOW,GAAY,CACrCvB,EAAO,KAAOA,EAAO,IAAKY,EAAAA,EAAAA,IAAoB,MAAO,CAAE1D,MAAO,eAAiB,EAC7E0D,EAAAA,EAAAA,IAAoB,KAAM,KAAM,iBAC9B,MACHX,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAaoP,EAAoB,CAC9CjC,QAAS,UACThL,IAAKsL,EACLzF,KAAM2F,EAAgBtN,MACtBlD,MAAO0Q,EACP0B,YAAalB,EACblO,IAAKyN,EAAQvN,MACbrD,MAAO,aACP,wBAAwB,EACxB,oBAAqB,GACrB,WAAY,KACZ,qBAAsB0Q,EAAiBrN,OACtC,CACDG,SAASC,EAAAA,EAAAA,KAAS,EAAGuH,UAAW,EAC9BtH,EAAAA,EAAAA,IAAoB,MAAOY,GAAY,EACrCZ,EAAAA,EAAAA,IAAoB,MAAOa,IAAaZ,EAAAA,EAAAA,IAAiBqH,EAAKjD,MAAO,IACrErE,EAAAA,EAAAA,IAAoB,MAAOc,IAAab,EAAAA,EAAAA,IAAiBqH,EAAKvD,OAAS,SAAU,QAGrF7D,EAAG,GACF,EAAG,CAAC,OAAQ,6BAIrBgB,EAAQvB,OAUNQ,EAAAA,EAAAA,IAAoB,IAAI,KATvBd,EAAAA,EAAAA,OAAcqF,EAAAA,EAAAA,IAAoB,MAAO3D,GAAa,EACrD4D,EAAAA,EAAAA,IAAamK,GAAqB,CAChChH,MAAOgF,EAAenN,MACtBwG,YAAY,EACZwD,QAAQ,EACR,gBAAiB,KACjB,qBAAsBE,EAAiBlK,OACtC,KAAM,EAAG,CAAC,QAAS,6BAI9BK,EAAAA,EAAAA,IAAoB,MAAOgB,GAAa,EACtC2D,EAAAA,EAAAA,IAAaiE,EAAsB,CACjC/L,KAAM,UACN+I,QAAS4I,EACTtF,UAAWqE,EAAQ5N,OAClB,CACDG,SAASC,EAAAA,EAAAA,KAAS,IAAMX,EAAO,KAAOA,EAAO,GAAK,EAChD2F,EAAAA,EAAAA,IAAiB,YAEnB7E,EAAG,GACF,EAAG,CAAC,gBAETyE,EAAAA,EAAAA,IAAaoK,EAAqB,CAChC/I,QAAS4G,EAAmBjN,MAC5B,mBAAoBP,EAAO,KAAOA,EAAO,GAAM+F,GAAkByH,EAAoBjN,MAAQwF,GAC7Fc,SAAU0G,EAAiBhN,MAC3B,qBAAsBwB,EAAkBxB,MACxC,wBAAyBuG,EAAoBvG,MAC7CqP,YAAaX,GACZ,KAAM,EAAG,CAAC,UAAW,WAAY,qBAAsB,2BACzD,EAAGhS,KAAc,CAClB,CAACyM,EAAoB5H,EAAQvB,QAC7B,CAEJ,IEzRA,MAAM,IAA2B,OAAgB,GAAQ,CAAC,CAAC,YAAY,qBAEvE,S,yFCDA,GAA4BpD,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,eACRC,MAAO,CACLqF,MAAO,CAAC,GAEV/E,KAAAA,CAAMC,GCFR,MAAMP,EAAQO,EAIRiS,GAAU5Q,EAAAA,EAAAA,KAAoD,KAClE,OAAQ5B,EAAMqF,OACZ,KAAKC,EAAAA,GAAeC,QAClB,MAAO,UACT,KAAKD,EAAAA,GAAemN,QAClB,MAAO,UACT,KAAKnN,EAAAA,GAAeoN,QAClB,MAAO,SACT,KAAKpN,EAAAA,GAAeqN,QACpB,QACE,MAAO,O,IAIPC,EAAoBvN,IACxB,OAAQA,GACN,KAAKC,EAAAA,GAAemN,QAClB,MAAO,UACT,KAAKnN,EAAAA,GAAeqN,QAClB,MAAO,UACT,KAAKrN,EAAAA,GAAeC,QAClB,MAAO,SACT,KAAKD,EAAAA,GAAeoN,QAClB,MAAO,SACT,QACE,MAAO,U,EAIPG,GAAYjR,EAAAA,EAAAA,KAAS,IAClBgR,EAAiB5S,EAAMqF,SDKhC,MAAO,CAAC3C,EAAUC,KAChB,MAAMqF,GAAoBF,EAAAA,EAAAA,IAAkB,UAE5C,OAAQlF,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAamF,EAAmB,CACpD5H,KAAMoS,EAAQtP,MACd2F,KAAM,QACNiE,MAAO,CAAC,YAAY,SACnB,CACDzJ,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBgF,EAAAA,EAAAA,KAAiB9E,EAAAA,EAAAA,IAAiBqP,EAAU3P,OAAQ,MAEtDO,EAAG,GACF,EAAG,CAAC,QAAQ,CAEjB,IE5DA,MAAME,EAAc,EAEpB,O;;;;;;;;ACGA,MAAM2K,EAYJwE,WAAAA,CAAYC,GACVC,KAAKzE,UAAYwE,EAAQxE,UACzByE,KAAKxE,MAAQuE,EAAQvE,OAAS,GAC9BwE,KAAKvE,WAAasE,EAAQtE,YAAc,GACxCuE,KAAKtE,WAAaqE,EAAQrE,YAAc,GACxCsE,KAAKC,iBAAmBF,EAAQpE,WAChCqE,KAAKE,mBAAqBH,EAAQI,aAClCH,KAAKI,UAAYL,EAAQK,WAAa,OAEtCJ,KAAKK,kBAAoB,EACzBL,KAAKM,gBAAkB,EACvBN,KAAKO,gBAAkB,KACvBP,KAAKQ,eAAiB,KACtBR,KAAKS,iBAAmB,KACxBT,KAAKU,YAAcV,KAAKxE,MAAMtJ,OAAS8N,KAAKvE,WAC5CuE,KAAKW,YAAc,EAGfX,KAAKU,YAAcV,KAAKI,YAC1BJ,KAAKW,YAAcX,KAAKI,UAAYJ,KAAKU,aAG3CV,KAAKY,YACT,CAMEA,UAAAA,GAiBE,GAfAZ,KAAKzE,UAAUd,UAAY,GAG3BuF,KAAKO,gBAAkB5F,SAASC,cAAc,OAE9CiG,OAAOC,OAAOd,KAAKO,gBAAgBzG,MAAO,CACxCiH,KAAM,IACNC,SAAU,OACVC,SAAU,WACVC,UAAW,IACXpG,OAAQ,OACRqG,UAAW,eAITnB,KAAKE,mBAAoB,CAC3B,MAAMxF,EAASsF,KAAKE,qBAChBxF,GACFsF,KAAKO,gBAAgBtF,YAAYP,EAEzC,CAGIsF,KAAKQ,eAAiB7F,SAASC,cAAc,OAE7CiG,OAAOC,OAAOd,KAAKQ,eAAe1G,MAAO,CACvCmH,SAAU,WACVG,MAAO,SAIT,MAAMC,EAAerB,KAAKU,YAAcV,KAAKW,YAC7CX,KAAKQ,eAAe1G,MAAMgB,OAAS,GAAGuG,MAGtCrB,KAAKS,iBAAmB9F,SAASC,cAAc,OAE/CiG,OAAOC,OAAOd,KAAKS,iBAAiB3G,MAAO,CACzCmH,SAAU,WACVG,MAAO,OACPE,KAAM,MAIRtB,KAAKO,gBAAgBgB,iBAAiB,SAAUvB,KAAKwB,aAAaC,KAAKzB,OAGvEA,KAAKQ,eAAevF,YAAY+E,KAAKS,kBACrCT,KAAKO,gBAAgBtF,YAAY+E,KAAKQ,gBACtCR,KAAKzE,UAAUN,YAAY+E,KAAKO,iBAGhCP,KAAK0B,mBAAmB,EAAGrS,KAAKsS,IAAI,IAAK3B,KAAKxE,MAAMtJ,QACxD,CAMEsP,YAAAA,GACE,MAAMI,EAAY5B,KAAKO,gBAAgBqB,UACjCC,EAAkB7B,KAAKO,gBAAgBuB,aAGvCC,EAAgBH,EAAY5B,KAAKW,YAGjCqB,EAAa3S,KAAK4S,IAAI,EAAG5S,KAAKC,MAAMyS,EAAgB/B,KAAKvE,YAAcuE,KAAKtE,YAC5EwG,EAAW7S,KAAKsS,IACpB3B,KAAKxE,MAAMtJ,OACX7C,KAAK8S,MAAMJ,EAAgBF,EAAkB7B,KAAKW,aAAeX,KAAKvE,YAAcuE,KAAKtE,YAIvFsG,IAAehC,KAAKK,mBAClB6B,IAAalC,KAAKM,iBACL,IAAb4B,IACJlC,KAAK0B,mBAAmBM,EAAYE,GAEpClC,KAAKK,kBAAoB2B,EACzBhC,KAAKM,gBAAkB4B,EAE7B,CAQER,kBAAAA,CAAmBM,EAAYE,GAE7BlC,KAAKS,iBAAiBhG,UAAY,GAGlCuF,KAAKS,iBAAiB3G,MAAMsI,UAAY,cAAcJ,EAAahC,KAAKvE,WAAauE,KAAKW,iBAG1F,IAAK,IAAI0B,EAAIL,EAAYK,EAAIH,EAAUG,IAAK,CAC1C,MAAMxP,EAAOmN,KAAKxE,MAAM6G,GAExB,GAAIrC,KAAKC,iBAAkB,CAEzB,MAAMqC,EAActC,KAAKC,iBAAiBpN,EAAMwP,GAC5CC,IAEFA,EAAYxI,MAAMgB,OAAYkF,KAAKvE,WAAauE,KAAKW,YAA1B,KAC3B2B,EAAYxI,MAAMqH,UAAY,aAC9BmB,EAAYxI,MAAMsH,MAAQ,OAE1BpB,KAAKS,iBAAiBxF,YAAYqH,GAE5C,KAAa,CAEL,MAAMC,EAAM5H,SAASC,cAAc,OACnCiG,OAAOC,OAAOyB,EAAIzI,MAAO,CACvBgB,OAAWkF,KAAKvE,WAAauE,KAAKW,YAA1B,KACRS,MAAO,OACPD,UAAW,aACXqB,QAAS,MACT1G,aAAc,mBAEhByG,EAAIvH,YAAcyH,KAAKC,UAAU7P,GACjCmN,KAAKS,iBAAiBxF,YAAYsH,EAC1C,CACA,CACA,CAOEI,WAAAA,CAAYnH,GACVwE,KAAKxE,MAAQA,GAAS,GACtBwE,KAAKU,YAAcV,KAAKxE,MAAMtJ,OAAS8N,KAAKvE,WAG5CuE,KAAKW,YAAc,EACfX,KAAKU,YAAcV,KAAKI,YAC1BJ,KAAKW,YAAcX,KAAKI,UAAYJ,KAAKU,aAIvCV,KAAKQ,iBACPR,KAAKQ,eAAe1G,MAAMgB,OAAYkF,KAAKU,YAAcV,KAAKW,YAA3B,MAGrCX,KAAKK,kBAAoB,EACzBL,KAAKM,gBAAkB,EAGvBN,KAAKwB,cACT,CAOEoB,aAAAA,CAAc/O,GACRA,GAAS,GAAKA,EAAQmM,KAAKxE,MAAMtJ,SAEnC8N,KAAKO,gBAAgBqB,UAAY/N,EAAQmM,KAAKvE,WAAauE,KAAKW,YAEtE,CAMEtF,OAAAA,GACM2E,KAAKO,iBACPP,KAAKO,gBAAgBsC,oBAAoB,SAAU7C,KAAKwB,cAEtDxB,KAAKzE,YACPyE,KAAKzE,UAAUd,UAAY,IAE7BuF,KAAKxE,MAAQ,KACbwE,KAAKzE,UAAY,KACjByE,KAAKO,gBAAkB,KACvBP,KAAKQ,eAAiB,KACtBR,KAAKS,iBAAmB,IAC5B,CAMEqC,OAAAA,GACE9C,KAAKwB,cACT,CAOEuB,kBAAAA,GACE,OAAO/C,KAAKO,eAChB;;;;;;GC9OsB,qBAAX3H,SACTA,OAAOoK,UAAY,CACjB1H,iB", "sources": ["webpack://fuzz-web/./src/components/common/TimeDisplay.vue?10ba", "webpack://fuzz-web/./src/components/common/TimeDisplay.vue", "webpack://fuzz-web/./src/components/common/TimeDisplay.vue?9136", "webpack://fuzz-web/./src/components/TestCases/InteroperationResultPanel.vue?6c02", "webpack://fuzz-web/./src/components/TestCases/InteroperationResultPanel.vue", "webpack://fuzz-web/./src/components/TestCases/InteroperationResultPanel.vue?d74e", "webpack://fuzz-web/./src/components/TestCases/GenerateCasesDialog.vue?fde1", "webpack://fuzz-web/./src/components/TestCases/GenerateCasesDialog.vue", "webpack://fuzz-web/./src/components/TestCases/GenerateCasesDialog.vue?f470", "webpack://fuzz-web/./src/components/TestCases/GeneratedCasesPanel.vue?27d5", "webpack://fuzz-web/./src/components/TestCases/GeneratedCasesPanel.vue", "webpack://fuzz-web/./src/components/TestCases/GeneratedCasesPanel.vue?dba8", "webpack://fuzz-web/./src/views/testplan/TestCases.vue?36a3", "webpack://fuzz-web/./src/views/testplan/TestCases.vue", "webpack://fuzz-web/./src/views/testplan/TestCases.vue?b18a", "webpack://fuzz-web/./src/components/common/CaseStateTag.vue?c0c7", "webpack://fuzz-web/./src/components/common/CaseStateTag.vue", "webpack://fuzz-web/./src/components/common/CaseStateTag.vue?4980", "webpack://fuzz-web/../src/virtual-scroll.js", "webpack://fuzz-web/../src/index.js"], "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, unref as _unref, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode } from \"vue\"\n\nconst _hoisted_1 = { class: \"time-display\" }\n\nimport { computed } from 'vue';\r\nimport { ElTooltip } from 'element-plus';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TimeDisplay',\n  props: {\n    begin: {},\n    end: {},\n    showDuration: { type: <PERSON>olean }\n  },\n  setup(__props: any) {\n\r\nconst props = __props;\r\n\r\nconst formatDate = (date: Date): string => {\r\n  const year = date.getFullYear();\r\n  const month = String(date.getMonth() + 1).padStart(2, '0');\r\n  const day = String(date.getDate()).padStart(2, '0');\r\n  const hours = String(date.getHours()).padStart(2, '0');\r\n  const minutes = String(date.getMinutes()).padStart(2, '0');\r\n  const seconds = String(date.getSeconds()).padStart(2, '0');\r\n  const milliseconds = String(date.getMilliseconds()).padStart(3, '0');\r\n  \r\n  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;\r\n};\r\n\r\nconst formatTimeOnly = (date: Date): string => {\r\n  const hours = String(date.getHours()).padStart(2, '0');\r\n  const minutes = String(date.getMinutes()).padStart(2, '0');\r\n  const seconds = String(date.getSeconds()).padStart(2, '0');\r\n  return `${hours}:${minutes}:${seconds}`;\r\n};\r\n\r\nconst tooltipContent = computed((): string => {\r\n  if (!props.begin) return '';\r\n  \r\n  const beginDate = new Date(props.begin);\r\n  const beginFull = formatDate(beginDate);\r\n  \r\n  if (!props.end) return `Start: ${beginFull}`;\r\n  \r\n  const endDate = new Date(props.end);\r\n  const endFull = formatDate(endDate);\r\n  const durationMs = endDate.getTime() - beginDate.getTime();\r\n  \r\n  let durationText = '';\r\n  if (durationMs < 1000) {\r\n    durationText = `${durationMs} ms`;\r\n  } else {\r\n    const seconds = Math.floor(durationMs / 1000);\r\n    const minutes = Math.floor(seconds / 60);\r\n    const hours = Math.floor(minutes / 60);\r\n    \r\n    if (hours > 0) {\r\n      durationText = `${hours}h ${minutes % 60}m ${seconds % 60}s`;\r\n    } else if (minutes > 0) {\r\n      durationText = `${minutes}m ${seconds % 60}s`;\r\n    } else {\r\n      durationText = `${seconds}s`;\r\n    }\r\n  }\r\n  \r\n  return `Duration: ${durationText}<br>Start: ${beginFull}<br>End: ${endFull}`;\r\n});\r\n\r\nconst formattedTime = computed((): string => {\r\n  if (!props.begin) return '-';\r\n  \r\n  const beginDate = new Date(props.begin);\r\n  const beginStr = formatTimeOnly(beginDate);\r\n  \r\n  if (!props.end || !props.showDuration) return beginStr;\r\n  \r\n  const endDate = new Date(props.end);\r\n  const durationMs = endDate.getTime() - beginDate.getTime();\r\n  \r\n  if (durationMs < 1000) {\r\n    return `${durationMs}ms`;\r\n  } else {\r\n    const seconds = Math.round(durationMs / 1000);\r\n    return `${seconds}s`;\r\n  }\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  return (_ctx.begin)\n    ? (_openBlock(), _createBlock(_unref(ElTooltip), {\n        key: 0,\n        content: tooltipContent.value,\n        placement: \"top\",\n        effect: \"dark\",\n        \"raw-content\": \"\"\n      }, {\n        default: _withCtx(() => [\n          _createElementVNode(\"span\", _hoisted_1, _toDisplayString(formattedTime.value), 1)\n        ]),\n        _: 1\n      }, 8, [\"content\"]))\n    : _createCommentVNode(\"\", true)\n}\n}\n\n})", "<template>\r\n  <el-tooltip \r\n    v-if=\"begin\" \r\n    :content=\"tooltipContent\" \r\n    placement=\"top\" \r\n    effect=\"dark\"\r\n    raw-content\r\n  >\r\n    <span class=\"time-display\">{{ formattedTime }}</span>\r\n  </el-tooltip>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, defineProps } from 'vue';\r\nimport { ElTooltip } from 'element-plus';\r\n\r\nconst props = defineProps<{\r\n  begin: string | null;\r\n  end?: string | null;\r\n  showDuration?: boolean;\r\n}>();\r\n\r\nconst formatDate = (date: Date): string => {\r\n  const year = date.getFullYear();\r\n  const month = String(date.getMonth() + 1).padStart(2, '0');\r\n  const day = String(date.getDate()).padStart(2, '0');\r\n  const hours = String(date.getHours()).padStart(2, '0');\r\n  const minutes = String(date.getMinutes()).padStart(2, '0');\r\n  const seconds = String(date.getSeconds()).padStart(2, '0');\r\n  const milliseconds = String(date.getMilliseconds()).padStart(3, '0');\r\n  \r\n  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;\r\n};\r\n\r\nconst formatTimeOnly = (date: Date): string => {\r\n  const hours = String(date.getHours()).padStart(2, '0');\r\n  const minutes = String(date.getMinutes()).padStart(2, '0');\r\n  const seconds = String(date.getSeconds()).padStart(2, '0');\r\n  return `${hours}:${minutes}:${seconds}`;\r\n};\r\n\r\nconst tooltipContent = computed((): string => {\r\n  if (!props.begin) return '';\r\n  \r\n  const beginDate = new Date(props.begin);\r\n  const beginFull = formatDate(beginDate);\r\n  \r\n  if (!props.end) return `Start: ${beginFull}`;\r\n  \r\n  const endDate = new Date(props.end);\r\n  const endFull = formatDate(endDate);\r\n  const durationMs = endDate.getTime() - beginDate.getTime();\r\n  \r\n  let durationText = '';\r\n  if (durationMs < 1000) {\r\n    durationText = `${durationMs} ms`;\r\n  } else {\r\n    const seconds = Math.floor(durationMs / 1000);\r\n    const minutes = Math.floor(seconds / 60);\r\n    const hours = Math.floor(minutes / 60);\r\n    \r\n    if (hours > 0) {\r\n      durationText = `${hours}h ${minutes % 60}m ${seconds % 60}s`;\r\n    } else if (minutes > 0) {\r\n      durationText = `${minutes}m ${seconds % 60}s`;\r\n    } else {\r\n      durationText = `${seconds}s`;\r\n    }\r\n  }\r\n  \r\n  return `Duration: ${durationText}<br>Start: ${beginFull}<br>End: ${endFull}`;\r\n});\r\n\r\nconst formattedTime = computed((): string => {\r\n  if (!props.begin) return '-';\r\n  \r\n  const beginDate = new Date(props.begin);\r\n  const beginStr = formatTimeOnly(beginDate);\r\n  \r\n  if (!props.end || !props.showDuration) return beginStr;\r\n  \r\n  const endDate = new Date(props.end);\r\n  const durationMs = endDate.getTime() - beginDate.getTime();\r\n  \r\n  if (durationMs < 1000) {\r\n    return `${durationMs}ms`;\r\n  } else {\r\n    const seconds = Math.round(durationMs / 1000);\r\n    return `${seconds}s`;\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.time-display {\r\n  font-size: 11px;\r\n  color: var(--el-text-color-secondary);\r\n}\r\n</style>\r\n", "import script from \"./TimeDisplay.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TimeDisplay.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./TimeDisplay.vue?vue&type=style&index=0&id=5494a8fa&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-5494a8fa\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, unref as _unref, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, renderList as _renderList, Fragment as _Fragment, withModifiers as _withModifiers, createBlock as _createBlock, normalizeClass as _normalizeClass } from \"vue\"\n\nconst _hoisted_1 = { class: \"result-panel\" }\nconst _hoisted_2 = {\n  key: 0,\n  class: \"duplicate-warning\"\n}\nconst _hoisted_3 = {\n  key: 1,\n  class: \"loading-indicator\"\n}\nconst _hoisted_4 = {\n  key: 2,\n  class: \"empty-message\"\n}\nconst _hoisted_5 = {\n  key: 3,\n  class: \"results-list\"\n}\nconst _hoisted_6 = { class: \"select-all-row\" }\nconst _hoisted_7 = {\n  key: 0,\n  class: \"average-time\"\n}\nconst _hoisted_8 = { class: \"result-rows\" }\nconst _hoisted_9 = [\"onClick\"]\nconst _hoisted_10 = { class: \"item-checkbox\" }\nconst _hoisted_11 = { class: \"item-name\" }\nconst _hoisted_12 = { class: \"item-status\" }\nconst _hoisted_13 = { class: \"item-duration\" }\n\nimport { ref, computed, watch, onMounted } from 'vue';\r\nimport { Loading } from '@element-plus/icons-vue';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport { ExecutionState } from '@/api/appApi';\r\nimport { ElAlert } from 'element-plus';\r\nimport CaseStateTag from '@/components/common/CaseStateTag.vue';\r\nimport TimeDisplay from '@/components/common/TimeDisplay.vue';\r\nimport { formatDuration } from '@/utils/timeUtils';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'InteroperationResultPanel',\n  props: {\n    results: {},\n    loading: { type: Boolean },\n    selectedSequences: {},\n    averageTime: {}\n  },\n  emits: [\"update:selectedSequences\"],\n  setup(__props: any, { emit: __emit }) {\n\r\nconst props = __props;\r\n\r\nconst emit = __emit;\r\n\r\n// 选中的项目数组\r\nconst selectedItems = ref<boolean[]>([]);\r\n\r\n// 初始化选中项，在 onMounted 中处理\r\nonMounted(() => {\r\n  initializeSelectedItems();\r\n});\r\n\r\n// 监听结果变化，更新选中状态\r\nwatch(() => props.results, () => {\r\n  initializeSelectedItems();\r\n}, { immediate: false });\r\n\r\n// 监听外部传入的已选中序列\r\nwatch(() => props.selectedSequences, (newSelected) => {\r\n  if (newSelected && props.results.length > 0) {\r\n    selectedItems.value = props.results.map(item =>\r\n      newSelected.includes(item.sequenceName)\r\n    );\r\n  }\r\n}, { deep: true });\r\n\r\n// 初始化选中状态函数 - 默认勾选成功的结果\r\nfunction initializeSelectedItems() {\r\n  if (props.results.length > 0) {\r\n    // 默认勾选成功的结果\r\n    selectedItems.value = props.results.map(result => result.state === ExecutionState.Success);\r\n    updateSelectedSequences();\r\n  }\r\n}\r\n\r\n// 计算全选状态\r\nconst isAllSelected = computed(() => {\r\n  return selectedItems.value.length > 0 && selectedItems.value.every(selected => selected);\r\n});\r\n\r\n// 计算部分选中状态\r\nconst isIndeterminate = computed(() => {\r\n  return selectedItems.value.some(selected => selected) && !isAllSelected.value;\r\n});\r\n\r\n// 计算选中的数量\r\nconst selectedCount = computed(() => {\r\n  return selectedItems.value.filter(selected => selected).length;\r\n});\r\n\r\n// 处理全选/取消全选\r\nconst handleSelectAllChange = (val: boolean) => {\r\n  selectedItems.value = new Array(props.results.length).fill(val);\r\n  updateSelectedSequences();\r\n};\r\n\r\n// 新增: 点击项目时切换对应的复选框状态\r\nconst handleItemClick = (index: number) => {\r\n  selectedItems.value[index] = !selectedItems.value[index];\r\n  updateSelectedSequences();\r\n};\r\n\r\n// 处理单个项目选中/取消选中\r\nconst handleItemSelect = (val: boolean, index: number, item: CaseResult) => {\r\n  selectedItems.value[index] = val;\r\n  updateSelectedSequences();\r\n};\r\n\r\n// 更新选中的序列列表并触发事件 - 修正为使用索引跟踪\r\nconst updateSelectedSequences = () => {\r\n  // 根据索引获取选中的结果项，然后提取其序列名称\r\n  const selected = props.results\r\n    .filter((_, index) => selectedItems.value[index])\r\n    .map(item => item.sequenceName);\r\n\r\n  // 使用Set去重，确保不会因为重复的sequenceName导致问题\r\n  const uniqueSelected = [...new Set(selected)];\r\n\r\n  emit('update:selectedSequences', uniqueSelected);\r\n};\r\n\r\n// 检测重复的序列名称\r\nconst duplicateSequences = computed(() => {\r\n  if (!props.results || props.results.length === 0) return [];\r\n\r\n  const nameCounts = new Map<string, number>();\r\n  const duplicates = new Set<string>();\r\n\r\n  // 统计每个序列名称出现的次数\r\n  props.results.forEach(result => {\r\n    const count = nameCounts.get(result.sequenceName) || 0;\r\n    nameCounts.set(result.sequenceName, count + 1);\r\n\r\n    // 如果出现次数超过1，则是重复的\r\n    if (count > 0) {\r\n      duplicates.add(result.sequenceName);\r\n    }\r\n  });\r\n\r\n  return Array.from(duplicates);\r\n});\r\n\r\n// 检查特定的序列名称是否重复\r\nconst isDuplicate = (name: string) => {\r\n  return duplicateSequences.value.includes(name);\r\n};\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_checkbox = _resolveComponent(\"el-checkbox\")!\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _cache[6] || (_cache[6] = _createElementVNode(\"div\", { class: \"panel-header\" }, [\n      _createElementVNode(\"h3\", null, \"Interoperation Results\")\n    ], -1)),\n    (duplicateSequences.value.length > 0)\n      ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [\n          _createVNode(_unref(ElAlert), {\n            title: \"Warning: Duplicate sequence names detected\",\n            type: \"warning\",\n            closable: false,\n            \"show-icon\": \"\"\n          }, {\n            default: _withCtx(() => [\n              _createTextVNode(\" Found duplicate names: \" + _toDisplayString(duplicateSequences.value.join(', ')) + \".\", 1),\n              _cache[2] || (_cache[2] = _createElementVNode(\"br\", null, null, -1)),\n              _cache[3] || (_cache[3] = _createTextVNode(\" This may cause selection issues. \"))\n            ]),\n            _: 1\n          })\n        ]))\n      : _createCommentVNode(\"\", true),\n    (_ctx.loading)\n      ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [\n          _createVNode(_component_el_icon, { class: \"is-loading\" }, {\n            default: _withCtx(() => [\n              _createVNode(_unref(Loading))\n            ]),\n            _: 1\n          }),\n          _cache[4] || (_cache[4] = _createTextVNode(\" Loading... \"))\n        ]))\n      : (_ctx.results.length === 0)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, \" No interoperation results available \"))\n        : (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [\n            _createElementVNode(\"div\", _hoisted_6, [\n              _createVNode(_component_el_checkbox, {\n                modelValue: isAllSelected.value,\n                \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((isAllSelected).value = $event)),\n                indeterminate: isIndeterminate.value,\n                onChange: handleSelectAllChange,\n                size: \"small\"\n              }, {\n                default: _withCtx(() => [\n                  _createTextVNode(\" Select All (\" + _toDisplayString(selectedCount.value) + \"/\" + _toDisplayString(_ctx.results.length) + \") \", 1)\n                ]),\n                _: 1\n              }, 8, [\"modelValue\", \"indeterminate\"]),\n              (_ctx.averageTime && _ctx.averageTime > 0)\n                ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, \" Average elapsed: \" + _toDisplayString(_unref(formatDuration)(_ctx.averageTime)), 1))\n                : _createCommentVNode(\"\", true)\n            ]),\n            _createElementVNode(\"div\", _hoisted_8, [\n              (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.results, (item, index) => {\n                return (_openBlock(), _createElementBlock(\"div\", {\n                  key: item.id || `item-${index}`,\n                  class: _normalizeClass([\"list-item\", { 'striped': index % 2 === 1, 'duplicate': isDuplicate(item.sequenceName) }]),\n                  onClick: _withModifiers(($event: any) => (handleItemClick(index)), [\"stop\"])\n                }, [\n                  _createElementVNode(\"div\", _hoisted_10, [\n                    _createVNode(_component_el_checkbox, {\n                      modelValue: selectedItems.value[index],\n                      \"onUpdate:modelValue\": ($event: any) => ((selectedItems.value[index]) = $event),\n                      size: \"small\",\n                      onClick: _cache[1] || (_cache[1] = _withModifiers(() => {}, [\"stop\"])),\n                      onChange: (val) => handleItemSelect(val, index, item)\n                    }, null, 8, [\"modelValue\", \"onUpdate:modelValue\", \"onChange\"])\n                  ]),\n                  _createElementVNode(\"div\", _hoisted_11, [\n                    _createTextVNode(_toDisplayString(item.sequenceName) + \" \", 1),\n                    (isDuplicate(item.sequenceName))\n                      ? (_openBlock(), _createBlock(_component_el_tag, {\n                          key: 0,\n                          size: \"small\",\n                          type: \"warning\",\n                          effect: \"dark\",\n                          class: \"duplicate-tag\"\n                        }, {\n                          default: _withCtx(() => _cache[5] || (_cache[5] = [\n                            _createTextVNode(\" Duplicate \")\n                          ])),\n                          _: 1\n                        }))\n                      : _createCommentVNode(\"\", true)\n                  ]),\n                  _createElementVNode(\"div\", _hoisted_12, [\n                    _createVNode(CaseStateTag, {\n                      state: item.state\n                    }, null, 8, [\"state\"])\n                  ]),\n                  _createElementVNode(\"div\", _hoisted_13, [\n                    _createVNode(TimeDisplay, {\n                      begin: item.begin,\n                      end: item.end,\n                      showDuration: true\n                    }, null, 8, [\"begin\", \"end\"])\n                  ])\n                ], 10, _hoisted_9))\n              }), 128))\n            ])\n          ]))\n  ]))\n}\n}\n\n})", "<template>\r\n  <div class=\"result-panel\">\r\n    <div class=\"panel-header\">\r\n      <h3>Interoperation Results</h3>\r\n    </div>\r\n\r\n    <!-- 新增: 显示重复序列名称警告 -->\r\n    <div v-if=\"duplicateSequences.length > 0\" class=\"duplicate-warning\">\r\n      <el-alert title=\"Warning: Duplicate sequence names detected\" type=\"warning\" :closable=\"false\" show-icon>\r\n        <template #default>\r\n          Found duplicate names: {{ duplicateSequences.join(', ') }}.<br />\r\n          This may cause selection issues.\r\n        </template>\r\n      </el-alert>\r\n    </div>\r\n\r\n    <div v-if=\"loading\" class=\"loading-indicator\">\r\n      <el-icon class=\"is-loading\">\r\n        <Loading />\r\n      </el-icon> Loading...\r\n    </div>\r\n\r\n    <div v-else-if=\"results.length === 0\" class=\"empty-message\">\r\n      No interoperation results available\r\n    </div>\r\n\r\n    <div v-else class=\"results-list\">\r\n      <!-- 全选复选框 -->\r\n      <div class=\"select-all-row\">\r\n        <el-checkbox v-model=\"isAllSelected\" :indeterminate=\"isIndeterminate\" @change=\"handleSelectAllChange\"\r\n          size=\"small\">\r\n          Select All ({{ selectedCount }}/{{ results.length }})\r\n        </el-checkbox>\r\n        <div v-if=\"averageTime && averageTime > 0\" class=\"average-time\">\r\n          Average elapsed: {{ formatDuration(averageTime) }}\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"result-rows\">\r\n        <!-- 优化后的单行项布局 -->\r\n        <div v-for=\"(item, index) in results\" :key=\"item.id || `item-${index}`\" class=\"list-item\"\r\n          :class=\"{ 'striped': index % 2 === 1, 'duplicate': isDuplicate(item.sequenceName) }\"\r\n          @click.stop=\"handleItemClick(index)\">\r\n          <!-- 使用直接的勾选元素 -->\r\n          <div class=\"item-checkbox\">\r\n            <el-checkbox v-model=\"selectedItems[index]\" size=\"small\" @click.stop\r\n              @change=\"(val: boolean) => handleItemSelect(val, index, item)\" />\r\n          </div>\r\n\r\n          <div class=\"item-name\">\r\n            {{ item.sequenceName }}\r\n            <el-tag v-if=\"isDuplicate(item.sequenceName)\" size=\"small\" type=\"warning\" effect=\"dark\"\r\n              class=\"duplicate-tag\">\r\n              Duplicate\r\n            </el-tag>\r\n          </div>\r\n\r\n          <div class=\"item-status\">\r\n            <CaseStateTag :state=\"item.state\" />\r\n          </div>\r\n\r\n          <div class=\"item-duration\">\r\n            <TimeDisplay :begin=\"item.begin\" :end=\"item.end\" :showDuration=\"true\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { defineProps, defineEmits, ref, computed, watch, onMounted } from 'vue';\r\nimport { Loading } from '@element-plus/icons-vue';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport { ExecutionState } from '@/api/appApi';\r\nimport { ElAlert } from 'element-plus';\r\nimport CaseStateTag from '@/components/common/CaseStateTag.vue';\r\nimport TimeDisplay from '@/components/common/TimeDisplay.vue';\r\nimport { formatDuration } from '@/utils/timeUtils';\r\n\r\nconst props = defineProps<{\r\n  results: CaseResult[];\r\n  loading: boolean;\r\n  selectedSequences?: string[];\r\n  averageTime?: number; // 平均时间(毫秒)\r\n}>();\r\n\r\nconst emit = defineEmits<{\r\n  (e: 'update:selectedSequences', value: string[]): void;\r\n}>();\r\n\r\n// 选中的项目数组\r\nconst selectedItems = ref<boolean[]>([]);\r\n\r\n// 初始化选中项，在 onMounted 中处理\r\nonMounted(() => {\r\n  initializeSelectedItems();\r\n});\r\n\r\n// 监听结果变化，更新选中状态\r\nwatch(() => props.results, () => {\r\n  initializeSelectedItems();\r\n}, { immediate: false });\r\n\r\n// 监听外部传入的已选中序列\r\nwatch(() => props.selectedSequences, (newSelected) => {\r\n  if (newSelected && props.results.length > 0) {\r\n    selectedItems.value = props.results.map(item =>\r\n      newSelected.includes(item.sequenceName)\r\n    );\r\n  }\r\n}, { deep: true });\r\n\r\n// 初始化选中状态函数 - 默认勾选成功的结果\r\nfunction initializeSelectedItems() {\r\n  if (props.results.length > 0) {\r\n    // 默认勾选成功的结果\r\n    selectedItems.value = props.results.map(result => result.state === ExecutionState.Success);\r\n    updateSelectedSequences();\r\n  }\r\n}\r\n\r\n// 计算全选状态\r\nconst isAllSelected = computed(() => {\r\n  return selectedItems.value.length > 0 && selectedItems.value.every(selected => selected);\r\n});\r\n\r\n// 计算部分选中状态\r\nconst isIndeterminate = computed(() => {\r\n  return selectedItems.value.some(selected => selected) && !isAllSelected.value;\r\n});\r\n\r\n// 计算选中的数量\r\nconst selectedCount = computed(() => {\r\n  return selectedItems.value.filter(selected => selected).length;\r\n});\r\n\r\n// 处理全选/取消全选\r\nconst handleSelectAllChange = (val: boolean) => {\r\n  selectedItems.value = new Array(props.results.length).fill(val);\r\n  updateSelectedSequences();\r\n};\r\n\r\n// 新增: 点击项目时切换对应的复选框状态\r\nconst handleItemClick = (index: number) => {\r\n  selectedItems.value[index] = !selectedItems.value[index];\r\n  updateSelectedSequences();\r\n};\r\n\r\n// 处理单个项目选中/取消选中\r\nconst handleItemSelect = (val: boolean, index: number, item: CaseResult) => {\r\n  selectedItems.value[index] = val;\r\n  updateSelectedSequences();\r\n};\r\n\r\n// 更新选中的序列列表并触发事件 - 修正为使用索引跟踪\r\nconst updateSelectedSequences = () => {\r\n  // 根据索引获取选中的结果项，然后提取其序列名称\r\n  const selected = props.results\r\n    .filter((_, index) => selectedItems.value[index])\r\n    .map(item => item.sequenceName);\r\n\r\n  // 使用Set去重，确保不会因为重复的sequenceName导致问题\r\n  const uniqueSelected = [...new Set(selected)];\r\n\r\n  emit('update:selectedSequences', uniqueSelected);\r\n};\r\n\r\n// 检测重复的序列名称\r\nconst duplicateSequences = computed(() => {\r\n  if (!props.results || props.results.length === 0) return [];\r\n\r\n  const nameCounts = new Map<string, number>();\r\n  const duplicates = new Set<string>();\r\n\r\n  // 统计每个序列名称出现的次数\r\n  props.results.forEach(result => {\r\n    const count = nameCounts.get(result.sequenceName) || 0;\r\n    nameCounts.set(result.sequenceName, count + 1);\r\n\r\n    // 如果出现次数超过1，则是重复的\r\n    if (count > 0) {\r\n      duplicates.add(result.sequenceName);\r\n    }\r\n  });\r\n\r\n  return Array.from(duplicates);\r\n});\r\n\r\n// 检查特定的序列名称是否重复\r\nconst isDuplicate = (name: string) => {\r\n  return duplicateSequences.value.includes(name);\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.result-panel {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n}\r\n\r\n.panel-header {\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #ebeef5;\r\n\r\n  h3 {\r\n    margin: 0;\r\n    font-size: 15px;\r\n    font-weight: 500;\r\n    color: #303133;\r\n  }\r\n\r\n  .panel-subtitle {\r\n    font-size: 12px;\r\n    color: #909399;\r\n    margin-top: 2px;\r\n  }\r\n}\r\n\r\n.select-all-row {\r\n  padding: 8px 20px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  background-color: var(--el-fill-color-light);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n\r\n  .average-time {\r\n    font-size: 12px;\r\n    color: #909399;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.results-list {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.result-rows {\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: auto;\r\n  flex: 1;\r\n  flex-basis: 0;\r\n}\r\n\r\n.list-item {\r\n  font-size: 13px;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 20px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n\r\n  &.striped {\r\n    background-color: #fafafa;\r\n  }\r\n\r\n  &:hover {\r\n    background-color: #f5f7fa;\r\n  }\r\n\r\n  /* 单行布局样式 */\r\n  .item-checkbox {\r\n    flex: 0 0 auto;\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .item-name {\r\n    flex: 1;\r\n    font-weight: 500;\r\n    color: #303133;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n  }\r\n\r\n  .item-status {\r\n    flex: 0 0 80px;\r\n    text-align: right;\r\n  }\r\n\r\n  .item-duration {\r\n    text-align: right;\r\n    color: #909399;\r\n    font-size: 12px;\r\n    min-width: 60px;\r\n  }\r\n}\r\n\r\n.loading-indicator,\r\n.empty-message {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  flex: 1;\r\n  color: #909399;\r\n  font-size: 13px;\r\n\r\n  .el-icon {\r\n    margin-right: 6px;\r\n  }\r\n}\r\n\r\n.duplicate-warning {\r\n  margin: 0 12px;\r\n  padding-top: 12px;\r\n}\r\n\r\n.duplicate-tag {\r\n  margin-left: 8px;\r\n  font-size: 10px;\r\n  line-height: 1;\r\n  padding: 2px 4px;\r\n  border-radius: 2px;\r\n}\r\n\r\n.list-item {\r\n  // ...existing code...\r\n\r\n  &.duplicate {\r\n    background-color: rgba(255, 229, 100, 0.1);\r\n\r\n    &:hover {\r\n      background-color: rgba(255, 229, 100, 0.2);\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./InteroperationResultPanel.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./InteroperationResultPanel.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./InteroperationResultPanel.vue?vue&type=style&index=0&id=97d37b30&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-97d37b30\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { createElementVNode as _createElementVNode, unref as _unref, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, resolveDirective as _resolveDirective, openBlock as _openBlock, createElementBlock as _createElementBlock, withDirectives as _withDirectives, createBlock as _createBlock } from \"vue\"\n\nconst _hoisted_1 = [\"element-loading-text\"]\nconst _hoisted_2 = { class: \"option-row\" }\nconst _hoisted_3 = { class: \"interoperation-results\" }\nconst _hoisted_4 = { class: \"dialog-footer\" }\nconst _hoisted_5 = { class: \"button-group\" }\n\nimport { ref, computed, watch, onMounted } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { CaseResult, GroupTreeNode } from '@/api/interoperationApi';\r\nimport { CoverageType, appApi, ExecutionState } from '@/api/appApi';\r\nimport InteroperationResultPanel from './InteroperationResultPanel.vue';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'GenerateCasesDialog',\n  props: {\n    visible: { type: Boolean },\n    coverage: {},\n    selectedSequences: {},\n    baselineAverageTime: {}\n  },\n  emits: [\"update:visible\", \"generated\"],\n  setup(__props: any, { emit: __emit }) {\n\r\nconst props = __props;\r\n\r\nconst emit = __emit;\r\n\r\n// 内部状态\r\nconst generating = ref(false);\r\nconst interoperationResults = ref<CaseResult[]>([]);\r\nconst interoperationLoading = ref(false);\r\nconst generatingProgressText = ref('');\r\nlet generateProgressTimer: number | null = null;\r\n\r\n// 表单数据\r\nconst formData = ref({\r\n    coverage: CoverageType.Normal,\r\n    selectedSequences: [] as string[]\r\n});\r\n\r\nconst dialogVisible = computed({\r\n    get: () => props.visible,\r\n    set: (value) => emit('update:visible', value)\r\n});\r\n\r\n// 动态的生成进度文本\r\nconst generatingText = computed(() => {\r\n    if (generatingProgressText.value) {\r\n        return generatingProgressText.value;\r\n    }\r\n    return 'Generating Test Cases...';\r\n});\r\n\r\n// 计算选中用例的平均时间(毫秒)\r\nconst averageTimeOfSelectedSequences = computed(() => {\r\n    if (formData.value.selectedSequences.length === 0 || interoperationResults.value.length === 0) {\r\n        return 0;\r\n    }\r\n\r\n    // 计算选中序列的平均执行时间\r\n    let totalExecutionTime = 0;\r\n    let totalExecutionCount = 0;\r\n\r\n    interoperationResults.value.forEach(result => {\r\n        if (result.begin && result.end && formData.value.selectedSequences.includes(result.sequenceName)) {\r\n            const executionTime = new Date(result.end).getTime() - new Date(result.begin).getTime();\r\n            totalExecutionTime += executionTime;\r\n            totalExecutionCount += 1;\r\n        }\r\n    });\r\n\r\n    return totalExecutionCount > 0 ? totalExecutionTime / totalExecutionCount : 0;\r\n});\r\n\r\n// 获取最新互操作测试结果\r\nconst fetchInteroperationResults = async () => {\r\n    interoperationLoading.value = true;\r\n    try {\r\n        const response = await appApi.getLatestInteroperationCaseResults();\r\n        interoperationResults.value = response.data;\r\n\r\n        // 默认勾选成功的结果\r\n        const successfulSequences = response.data\r\n            .filter(result => result.state === ExecutionState.Success)\r\n            .map(result => result.sequenceName);\r\n        formData.value.selectedSequences = successfulSequences;\r\n    } catch (error) {\r\n        console.error('获取测试结果失败:', error);\r\n        ElMessage.error('Failed to fetch interoperation results');\r\n    } finally {\r\n        interoperationLoading.value = false;\r\n    }\r\n};\r\n\r\n// 生成测试用例\r\nconst handleGenerate = async () => {\r\n    if (formData.value.selectedSequences.length === 0) {\r\n        ElMessage.warning('Please select at least one sequence');\r\n        return;\r\n    }\r\n\r\n    generating.value = true;\r\n    generatingProgressText.value = '';\r\n\r\n    // 开始轮询生成进度\r\n    startGenerateProgressPolling();\r\n\r\n    try {\r\n        const response = await appApi.generateCasesWithGroup(formData.value.coverage, formData.value.selectedSequences);\r\n\r\n        // 生成成功后，发送数据给父组件并关闭对话框\r\n        emit('generated', response.data.cases, response.data.groupTree);\r\n        dialogVisible.value = false;\r\n\r\n        ElMessage.success(`Successfully generated ${response.data.cases.length} test cases`);\r\n    } catch (error) {\r\n        console.error('生成测试用例失败:', error);\r\n        ElMessage.error('Failed to generate test cases');\r\n    } finally {\r\n        generating.value = false;\r\n        // 停止轮询生成进度\r\n        stopGenerateProgressPolling();\r\n    }\r\n};\r\n\r\n// 开始轮询生成进度\r\nconst startGenerateProgressPolling = () => {\r\n    const pollProgress = async () => {\r\n        const response = await appApi.getGeneratingProgress();\r\n        generatingProgressText.value = response.data;\r\n\r\n        // 如果还在生成中，继续轮询\r\n        if (generating.value) {\r\n            generateProgressTimer = window.setTimeout(pollProgress, 100);\r\n        }\r\n    };\r\n\r\n    // 延迟开始轮询，给后端一点启动时间\r\n    generateProgressTimer = window.setTimeout(pollProgress, 200);\r\n};\r\n\r\n// 停止轮询生成进度\r\nconst stopGenerateProgressPolling = () => {\r\n    if (generateProgressTimer) {\r\n        window.clearTimeout(generateProgressTimer);\r\n        generateProgressTimer = null;\r\n    }\r\n};\r\n\r\n// 取消对话框\r\nconst handleCancel = () => {\r\n    dialogVisible.value = false;\r\n};\r\n\r\n// 对话框关闭时的处理\r\nconst handleDialogClose = () => {\r\n    // 停止轮询\r\n    stopGenerateProgressPolling();\r\n\r\n    // 重置表单数据\r\n    formData.value.coverage = props.coverage;\r\n    formData.value.selectedSequences = [...props.selectedSequences];\r\n};\r\n\r\n\r\n\r\n// 监听对话框显示状态，获取互操作结果\r\nwatch(() => props.visible, (visible) => {\r\n    if (visible) {\r\n        // 初始化表单数据\r\n        formData.value.coverage = props.coverage;\r\n        formData.value.selectedSequences = [...props.selectedSequences];\r\n\r\n        // 获取互操作结果\r\n        fetchInteroperationResults();\r\n    }\r\n});\r\n\r\n// 组件挂载时获取互操作结果\r\nonMounted(() => {\r\n    if (props.visible) {\r\n        fetchInteroperationResults();\r\n    }\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_radio = _resolveComponent(\"el-radio\")!\n  const _component_el_radio_group = _resolveComponent(\"el-radio-group\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_dialog = _resolveComponent(\"el-dialog\")!\n  const _directive_loading = _resolveDirective(\"loading\")!\n\n  return (_openBlock(), _createBlock(_component_el_dialog, {\n    modelValue: dialogVisible.value,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((dialogVisible).value = $event)),\n    title: \"Generate Test Cases\",\n    class: \"app-dialog-full-70\",\n    \"close-on-click-modal\": false,\n    \"close-on-press-escape\": false,\n    onClose: handleDialogClose\n  }, {\n    footer: _withCtx(() => [\n      _createElementVNode(\"div\", _hoisted_4, [\n        _createElementVNode(\"div\", _hoisted_5, [\n          _createVNode(_component_el_button, {\n            onClick: handleCancel,\n            disabled: generating.value\n          }, {\n            default: _withCtx(() => _cache[6] || (_cache[6] = [\n              _createTextVNode(\"Cancel\")\n            ])),\n            _: 1\n          }, 8, [\"disabled\"]),\n          _createVNode(_component_el_button, {\n            type: \"primary\",\n            onClick: handleGenerate,\n            loading: generating.value,\n            disabled: formData.value.selectedSequences.length === 0\n          }, {\n            default: _withCtx(() => _cache[7] || (_cache[7] = [\n              _createTextVNode(\" Generate \")\n            ])),\n            _: 1\n          }, 8, [\"loading\", \"disabled\"])\n        ])\n      ])\n    ]),\n    default: _withCtx(() => [\n      _withDirectives((_openBlock(), _createElementBlock(\"div\", {\n        class: \"dialog-content\",\n        \"element-loading-text\": generatingText.value\n      }, [\n        _createElementVNode(\"div\", _hoisted_2, [\n          _cache[5] || (_cache[5] = _createElementVNode(\"div\", null, \"Coverage:\", -1)),\n          _createVNode(_component_el_radio_group, {\n            modelValue: formData.value.coverage,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((formData.value.coverage) = $event)),\n            size: \"small\"\n          }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_radio, {\n                label: _unref(CoverageType).Normal\n              }, {\n                default: _withCtx(() => _cache[3] || (_cache[3] = [\n                  _createTextVNode(\"Normal\")\n                ])),\n                _: 1\n              }, 8, [\"label\"]),\n              _createVNode(_component_el_radio, {\n                label: _unref(CoverageType).High\n              }, {\n                default: _withCtx(() => _cache[4] || (_cache[4] = [\n                  _createTextVNode(\"High\")\n                ])),\n                _: 1\n              }, 8, [\"label\"])\n            ]),\n            _: 1\n          }, 8, [\"modelValue\"])\n        ]),\n        _createElementVNode(\"div\", _hoisted_3, [\n          _createVNode(InteroperationResultPanel, {\n            results: interoperationResults.value,\n            loading: interoperationLoading.value,\n            \"average-time\": averageTimeOfSelectedSequences.value,\n            selectedSequences: formData.value.selectedSequences,\n            \"onUpdate:selectedSequences\": _cache[1] || (_cache[1] = ($event: any) => ((formData.value.selectedSequences) = $event))\n          }, null, 8, [\"results\", \"loading\", \"average-time\", \"selectedSequences\"])\n        ])\n      ], 8, _hoisted_1)), [\n        [_directive_loading, generating.value]\n      ])\n    ]),\n    _: 1\n  }, 8, [\"modelValue\"]))\n}\n}\n\n})", "<template>\r\n    <el-dialog v-model=\"dialogVisible\" title=\"Generate Test Cases\" class=\"app-dialog-full-70\"\r\n        :close-on-click-modal=\"false\" :close-on-press-escape=\"false\" @close=\"handleDialogClose\">\r\n\r\n        <!-- 对话框主要内容 -->\r\n        <div class=\"dialog-content\" v-loading=\"generating\" :element-loading-text=\"generatingText\">\r\n            <div class=\"option-row\">\r\n                <div>Coverage:</div>\r\n                <el-radio-group v-model=\"formData.coverage\" size=\"small\">\r\n                    <el-radio :label=\"CoverageType.Normal\">Normal</el-radio>\r\n                    <el-radio :label=\"CoverageType.High\">High</el-radio>\r\n                </el-radio-group>\r\n            </div>\r\n            <div class=\"interoperation-results\">\r\n                <InteroperationResultPanel :results=\"interoperationResults\" :loading=\"interoperationLoading\"\r\n                    :average-time=\"averageTimeOfSelectedSequences\"\r\n                    v-model:selectedSequences=\"formData.selectedSequences\" />\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 对话框底部按钮 -->\r\n        <template #footer>\r\n            <div class=\"dialog-footer\">\r\n                <div class=\"button-group\">\r\n                    <el-button @click=\"handleCancel\" :disabled=\"generating\">Cancel</el-button>\r\n                    <el-button type=\"primary\" @click=\"handleGenerate\" :loading=\"generating\"\r\n                        :disabled=\"formData.selectedSequences.length === 0\">\r\n                        Generate\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n        </template>\r\n    </el-dialog>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { defineProps, defineEmits, ref, computed, watch, onMounted } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { CaseResult, GroupTreeNode } from '@/api/interoperationApi';\r\nimport { CoverageType, appApi, ExecutionState } from '@/api/appApi';\r\nimport InteroperationResultPanel from './InteroperationResultPanel.vue';\r\n\r\nconst props = defineProps<{\r\n    visible: boolean;\r\n    coverage: CoverageType;\r\n    selectedSequences: string[];\r\n    baselineAverageTime: number;\r\n}>();\r\n\r\nconst emit = defineEmits<{\r\n    (e: 'update:visible', value: boolean): void;\r\n    (e: 'generated', cases: CaseResult[], groupTree: GroupTreeNode): void;\r\n}>();\r\n\r\n// 内部状态\r\nconst generating = ref(false);\r\nconst interoperationResults = ref<CaseResult[]>([]);\r\nconst interoperationLoading = ref(false);\r\nconst generatingProgressText = ref('');\r\nlet generateProgressTimer: number | null = null;\r\n\r\n// 表单数据\r\nconst formData = ref({\r\n    coverage: CoverageType.Normal,\r\n    selectedSequences: [] as string[]\r\n});\r\n\r\nconst dialogVisible = computed({\r\n    get: () => props.visible,\r\n    set: (value) => emit('update:visible', value)\r\n});\r\n\r\n// 动态的生成进度文本\r\nconst generatingText = computed(() => {\r\n    if (generatingProgressText.value) {\r\n        return generatingProgressText.value;\r\n    }\r\n    return 'Generating Test Cases...';\r\n});\r\n\r\n// 计算选中用例的平均时间(毫秒)\r\nconst averageTimeOfSelectedSequences = computed(() => {\r\n    if (formData.value.selectedSequences.length === 0 || interoperationResults.value.length === 0) {\r\n        return 0;\r\n    }\r\n\r\n    // 计算选中序列的平均执行时间\r\n    let totalExecutionTime = 0;\r\n    let totalExecutionCount = 0;\r\n\r\n    interoperationResults.value.forEach(result => {\r\n        if (result.begin && result.end && formData.value.selectedSequences.includes(result.sequenceName)) {\r\n            const executionTime = new Date(result.end).getTime() - new Date(result.begin).getTime();\r\n            totalExecutionTime += executionTime;\r\n            totalExecutionCount += 1;\r\n        }\r\n    });\r\n\r\n    return totalExecutionCount > 0 ? totalExecutionTime / totalExecutionCount : 0;\r\n});\r\n\r\n// 获取最新互操作测试结果\r\nconst fetchInteroperationResults = async () => {\r\n    interoperationLoading.value = true;\r\n    try {\r\n        const response = await appApi.getLatestInteroperationCaseResults();\r\n        interoperationResults.value = response.data;\r\n\r\n        // 默认勾选成功的结果\r\n        const successfulSequences = response.data\r\n            .filter(result => result.state === ExecutionState.Success)\r\n            .map(result => result.sequenceName);\r\n        formData.value.selectedSequences = successfulSequences;\r\n    } catch (error) {\r\n        console.error('获取测试结果失败:', error);\r\n        ElMessage.error('Failed to fetch interoperation results');\r\n    } finally {\r\n        interoperationLoading.value = false;\r\n    }\r\n};\r\n\r\n// 生成测试用例\r\nconst handleGenerate = async () => {\r\n    if (formData.value.selectedSequences.length === 0) {\r\n        ElMessage.warning('Please select at least one sequence');\r\n        return;\r\n    }\r\n\r\n    generating.value = true;\r\n    generatingProgressText.value = '';\r\n\r\n    // 开始轮询生成进度\r\n    startGenerateProgressPolling();\r\n\r\n    try {\r\n        const response = await appApi.generateCasesWithGroup(formData.value.coverage, formData.value.selectedSequences);\r\n\r\n        // 生成成功后，发送数据给父组件并关闭对话框\r\n        emit('generated', response.data.cases, response.data.groupTree);\r\n        dialogVisible.value = false;\r\n\r\n        ElMessage.success(`Successfully generated ${response.data.cases.length} test cases`);\r\n    } catch (error) {\r\n        console.error('生成测试用例失败:', error);\r\n        ElMessage.error('Failed to generate test cases');\r\n    } finally {\r\n        generating.value = false;\r\n        // 停止轮询生成进度\r\n        stopGenerateProgressPolling();\r\n    }\r\n};\r\n\r\n// 开始轮询生成进度\r\nconst startGenerateProgressPolling = () => {\r\n    const pollProgress = async () => {\r\n        const response = await appApi.getGeneratingProgress();\r\n        generatingProgressText.value = response.data;\r\n\r\n        // 如果还在生成中，继续轮询\r\n        if (generating.value) {\r\n            generateProgressTimer = window.setTimeout(pollProgress, 100);\r\n        }\r\n    };\r\n\r\n    // 延迟开始轮询，给后端一点启动时间\r\n    generateProgressTimer = window.setTimeout(pollProgress, 200);\r\n};\r\n\r\n// 停止轮询生成进度\r\nconst stopGenerateProgressPolling = () => {\r\n    if (generateProgressTimer) {\r\n        window.clearTimeout(generateProgressTimer);\r\n        generateProgressTimer = null;\r\n    }\r\n};\r\n\r\n// 取消对话框\r\nconst handleCancel = () => {\r\n    dialogVisible.value = false;\r\n};\r\n\r\n// 对话框关闭时的处理\r\nconst handleDialogClose = () => {\r\n    // 停止轮询\r\n    stopGenerateProgressPolling();\r\n\r\n    // 重置表单数据\r\n    formData.value.coverage = props.coverage;\r\n    formData.value.selectedSequences = [...props.selectedSequences];\r\n};\r\n\r\n\r\n\r\n// 监听对话框显示状态，获取互操作结果\r\nwatch(() => props.visible, (visible) => {\r\n    if (visible) {\r\n        // 初始化表单数据\r\n        formData.value.coverage = props.coverage;\r\n        formData.value.selectedSequences = [...props.selectedSequences];\r\n\r\n        // 获取互操作结果\r\n        fetchInteroperationResults();\r\n    }\r\n});\r\n\r\n// 组件挂载时获取互操作结果\r\nonMounted(() => {\r\n    if (props.visible) {\r\n        fetchInteroperationResults();\r\n    }\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.dialog-content {\r\n    padding: 0;\r\n    display: flex;\r\n    flex: 1;\r\n    flex-direction: column;\r\n}\r\n\r\n.interoperation-results {\r\n    overflow-y: auto;\r\n    border: 1px solid #e4e7ed;\r\n    border-radius: 4px;\r\n    background: #fff;\r\n    display: flex;\r\n    flex: 1;\r\n    flex-basis: 0;\r\n}\r\n\r\n.option-row {\r\n    display: flex;\r\n    flex-direction: row;\r\n    gap: 10px;\r\n    align-items: center;\r\n    margin-bottom: 10px;\r\n}\r\n</style>\r\n", "import script from \"./GenerateCasesDialog.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./GenerateCasesDialog.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./GenerateCasesDialog.vue?vue&type=style&index=0&id=badc4246&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-badc4246\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, unref as _unref, withCtx as _withCtx, createTextVNode as _createTextVNode } from \"vue\"\n\nconst _hoisted_1 = { style: {\"display\":\"flex\",\"flex\":\"1\",\"flex-direction\":\"column\"} }\nconst _hoisted_2 = { class: \"case-info\" }\nconst _hoisted_3 = { class: \"case-panel\" }\nconst _hoisted_4 = {\n  key: 0,\n  class: \"save-progress\"\n}\nconst _hoisted_5 = { class: \"progress-message\" }\nconst _hoisted_6 = {\n  key: 1,\n  class: \"loading-indicator\"\n}\nconst _hoisted_7 = {\n  key: 2,\n  class: \"empty-message\"\n}\nconst _hoisted_8 = {\n  key: 3,\n  class: \"results-list case-list\"\n}\n\nimport { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';\r\nimport { Loading } from '@element-plus/icons-vue';\r\nimport { VirtualScroll } from 'js-booster';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport type { SaveProgressResponse } from '@/api/appApi';\r\n\r\nconst ITEM_HEIGHT = 36; // 行高\r\nconst BUFFER_SIZE = 20; // 缓冲区大小\r\nconst HEADER_HEIGHT = 40; // 表头高度\r\n\r\n// 渲染固定表头\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'GeneratedCasesPanel',\n  props: {\n    cases: {},\n    generating: { type: Boolean },\n    saving: { type: Boolean },\n    saveProgress: {},\n    showEmptyMessage: { type: Boolean }\n  },\n  setup(__props: any) {\n\r\nconst props = __props;\r\n\r\n// 虚拟滚动相关\r\nconst casesContainer = ref<HTMLElement | null>(null);\r\nconst headerContainer = ref<HTMLElement | null>(null);\r\nlet virtualScroll: any = null;\r\nconst renderFixedHeader = () => {\r\n  if (!headerContainer.value) return;\r\n\r\n  // 清空表头容器\r\n  headerContainer.value.innerHTML = '';\r\n\r\n  // 创建表头\r\n  const header = document.createElement('div');\r\n  header.className = 'header-row';\r\n  header.style.height = `${HEADER_HEIGHT}px`;\r\n\r\n  // ID 列\r\n  const idHeader = document.createElement('div');\r\n  idHeader.textContent = 'ID';\r\n  idHeader.className = 'header-cell-id';\r\n  header.appendChild(idHeader);\r\n\r\n  // 名称列\r\n  const nameHeader = document.createElement('div');\r\n  nameHeader.textContent = 'Name';\r\n  nameHeader.className = 'header-cell-name';\r\n  header.appendChild(nameHeader);\r\n\r\n  // 参数列\r\n  const paramHeader = document.createElement('div');\r\n  paramHeader.textContent = 'Parameter';\r\n  paramHeader.className = 'header-cell-param';\r\n  header.appendChild(paramHeader);\r\n\r\n  // 添加到表头容器\r\n  headerContainer.value.appendChild(header);\r\n};\r\n\r\n// 初始化虚拟滚动\r\nconst initVirtualScroll = () => {\r\n  if (!casesContainer.value || !props.cases.length) return;\r\n\r\n  // 渲染固定表头\r\n  renderFixedHeader();\r\n\r\n  // 如果已经存在虚拟滚动实例，先销毁\r\n  if (virtualScroll) {\r\n    virtualScroll.destroy();\r\n  }\r\n\r\n  virtualScroll = new VirtualScroll({\r\n    container: casesContainer.value,\r\n    items: props.cases,\r\n    itemHeight: ITEM_HEIGHT,\r\n    bufferSize: BUFFER_SIZE,\r\n    renderItem: (item: CaseResult, index: number) => {\r\n      // 创建主容器\r\n      const div = document.createElement('div');\r\n      div.className = 'case-row';\r\n\r\n      // 设置动态样式\r\n      div.style.height = `${ITEM_HEIGHT}px`;\r\n      div.style.lineHeight = `${ITEM_HEIGHT}px`;\r\n      div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';\r\n      div.style.backgroundColor = index % 2 === 0 ? '#fff' : '#fafafa';\r\n\r\n      // 添加悬停效果\r\n      div.onmouseover = () => {\r\n        div.style.backgroundColor = '#f5f7fa';\r\n      };\r\n      div.onmouseout = () => {\r\n        div.style.backgroundColor = index % 2 === 0 ? '#fff' : '#fafafa';\r\n        div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';\r\n      };\r\n\r\n      // 创建行内容\r\n      const rowDiv = document.createElement('div');\r\n      rowDiv.className = 'case-row-content';\r\n\r\n      // ID\r\n      const idDiv = document.createElement('div');\r\n      idDiv.textContent = `${item.id || index + 1}`;\r\n      idDiv.className = 'case-cell-id';\r\n      rowDiv.appendChild(idDiv);\r\n\r\n      // 名称\r\n      const nameDiv = document.createElement('div');\r\n      nameDiv.textContent = item.name || '-';\r\n      nameDiv.title = item.name || '';\r\n      nameDiv.className = 'case-cell-name';\r\n      rowDiv.appendChild(nameDiv);\r\n\r\n      // 参数\r\n      const paramDiv = document.createElement('div');\r\n      paramDiv.textContent = item.parameter || '-';\r\n      paramDiv.title = item.parameter || '';\r\n      paramDiv.className = 'case-cell-param';\r\n      rowDiv.appendChild(paramDiv);\r\n\r\n      div.appendChild(rowDiv);\r\n\r\n      return div;\r\n    }\r\n  });\r\n};\r\n\r\n// 更新虚拟滚动数据\r\nconst updateVirtualScroll = () => {\r\n  nextTick(() => {\r\n    initVirtualScroll();\r\n  });\r\n};\r\n\r\n// 监听 cases 变化，更新虚拟滚动\r\nwatch(() => props.cases, () => {\r\n  nextTick(() => {\r\n    updateVirtualScroll();\r\n  });\r\n}, { deep: true });\r\n\r\n// 组件挂载时初始化虚拟滚动\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    initVirtualScroll();\r\n  });\r\n});\r\n\r\n// 组件卸载时销毁虚拟滚动\r\nonUnmounted(() => {\r\n  if (virtualScroll) {\r\n    virtualScroll.destroy();\r\n    virtualScroll = null;\r\n  }\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_progress = _resolveComponent(\"el-progress\")!\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createElementVNode(\"span\", null, \" Showing \" + _toDisplayString(_ctx.cases.length) + \" cases \", 1)\n    ]),\n    _createElementVNode(\"div\", _hoisted_3, [\n      (_ctx.saving && _ctx.saveProgress)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [\n            _cache[0] || (_cache[0] = _createElementVNode(\"div\", { class: \"progress-header\" }, [\n              _createElementVNode(\"span\", null, \"Saving test cases...\")\n            ], -1)),\n            _createVNode(_component_el_progress, {\n              percentage: _ctx.saveProgress.progress,\n              status: _ctx.saveProgress.error ? 'exception' : undefined,\n              \"stroke-width\": 6\n            }, null, 8, [\"percentage\", \"status\"]),\n            _createElementVNode(\"div\", _hoisted_5, _toDisplayString(_ctx.saveProgress.message), 1)\n          ]))\n        : _createCommentVNode(\"\", true),\n      (_ctx.generating)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [\n            _createVNode(_component_el_icon, { class: \"is-loading\" }, {\n              default: _withCtx(() => [\n                _createVNode(_unref(Loading))\n              ]),\n              _: 1\n            }),\n            _cache[1] || (_cache[1] = _createTextVNode(\" Generating... \"))\n          ]))\n        : (_ctx.cases.length === 0 && _ctx.showEmptyMessage)\n          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, \" Click Generate to create test cases \"))\n          : (_ctx.cases.length > 0)\n            ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [\n                _createElementVNode(\"div\", {\n                  class: \"cases-header\",\n                  ref_key: \"headerContainer\",\n                  ref: headerContainer\n                }, null, 512),\n                _createElementVNode(\"div\", {\n                  ref_key: \"casesContainer\",\n                  ref: casesContainer,\n                  class: \"cases-content\"\n                }, null, 512)\n              ]))\n            : _createCommentVNode(\"\", true)\n    ])\n  ]))\n}\n}\n\n})", "<template>\r\n  <div style=\"display: flex;flex:1; flex-direction: column;\">\r\n    <div class=\"case-info\">\r\n      <span>\r\n        Showing {{ cases.length }} cases\r\n      </span>\r\n    </div>\r\n    <div class=\"case-panel\">\r\n      <!-- 保存进度显示 -->\r\n      <div v-if=\"saving && saveProgress\" class=\"save-progress\">\r\n        <div class=\"progress-header\">\r\n          <span>Saving test cases...</span>\r\n        </div>\r\n        <el-progress :percentage=\"saveProgress.progress\" :status=\"saveProgress.error ? 'exception' : undefined\"\r\n          :stroke-width=\"6\">\r\n        </el-progress>\r\n        <div class=\"progress-message\">{{ saveProgress.message }}</div>\r\n      </div>\r\n\r\n      <div v-if=\"generating\" class=\"loading-indicator\">\r\n        <el-icon class=\"is-loading\">\r\n          <Loading />\r\n        </el-icon> Generating...\r\n      </div>\r\n\r\n      <div v-else-if=\"cases.length === 0 && showEmptyMessage\" class=\"empty-message\">\r\n        Click Generate to create test cases\r\n      </div>\r\n\r\n      <div v-else-if=\"cases.length > 0\" class=\"results-list case-list\">\r\n        <!-- 固定表头 -->\r\n        <div class=\"cases-header\" ref=\"headerContainer\"></div>\r\n        <!-- 虚拟滚动内容容器 -->\r\n        <div ref=\"casesContainer\" class=\"cases-content\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { defineProps, ref, onMounted, onUnmounted, watch, nextTick } from 'vue';\r\nimport { Loading } from '@element-plus/icons-vue';\r\nimport { VirtualScroll } from 'js-booster';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport type { SaveProgressResponse } from '@/api/appApi';\r\n\r\nconst props = defineProps<{\r\n  cases: CaseResult[];\r\n  generating: boolean;\r\n  saving?: boolean;\r\n  saveProgress?: SaveProgressResponse | null;\r\n  showEmptyMessage: boolean;\r\n}>();\r\n\r\n// 虚拟滚动相关\r\nconst casesContainer = ref<HTMLElement | null>(null);\r\nconst headerContainer = ref<HTMLElement | null>(null);\r\nlet virtualScroll: any = null;\r\nconst ITEM_HEIGHT = 36; // 行高\r\nconst BUFFER_SIZE = 20; // 缓冲区大小\r\nconst HEADER_HEIGHT = 40; // 表头高度\r\n\r\n// 渲染固定表头\r\nconst renderFixedHeader = () => {\r\n  if (!headerContainer.value) return;\r\n\r\n  // 清空表头容器\r\n  headerContainer.value.innerHTML = '';\r\n\r\n  // 创建表头\r\n  const header = document.createElement('div');\r\n  header.className = 'header-row';\r\n  header.style.height = `${HEADER_HEIGHT}px`;\r\n\r\n  // ID 列\r\n  const idHeader = document.createElement('div');\r\n  idHeader.textContent = 'ID';\r\n  idHeader.className = 'header-cell-id';\r\n  header.appendChild(idHeader);\r\n\r\n  // 名称列\r\n  const nameHeader = document.createElement('div');\r\n  nameHeader.textContent = 'Name';\r\n  nameHeader.className = 'header-cell-name';\r\n  header.appendChild(nameHeader);\r\n\r\n  // 参数列\r\n  const paramHeader = document.createElement('div');\r\n  paramHeader.textContent = 'Parameter';\r\n  paramHeader.className = 'header-cell-param';\r\n  header.appendChild(paramHeader);\r\n\r\n  // 添加到表头容器\r\n  headerContainer.value.appendChild(header);\r\n};\r\n\r\n// 初始化虚拟滚动\r\nconst initVirtualScroll = () => {\r\n  if (!casesContainer.value || !props.cases.length) return;\r\n\r\n  // 渲染固定表头\r\n  renderFixedHeader();\r\n\r\n  // 如果已经存在虚拟滚动实例，先销毁\r\n  if (virtualScroll) {\r\n    virtualScroll.destroy();\r\n  }\r\n\r\n  virtualScroll = new VirtualScroll({\r\n    container: casesContainer.value,\r\n    items: props.cases,\r\n    itemHeight: ITEM_HEIGHT,\r\n    bufferSize: BUFFER_SIZE,\r\n    renderItem: (item: CaseResult, index: number) => {\r\n      // 创建主容器\r\n      const div = document.createElement('div');\r\n      div.className = 'case-row';\r\n\r\n      // 设置动态样式\r\n      div.style.height = `${ITEM_HEIGHT}px`;\r\n      div.style.lineHeight = `${ITEM_HEIGHT}px`;\r\n      div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';\r\n      div.style.backgroundColor = index % 2 === 0 ? '#fff' : '#fafafa';\r\n\r\n      // 添加悬停效果\r\n      div.onmouseover = () => {\r\n        div.style.backgroundColor = '#f5f7fa';\r\n      };\r\n      div.onmouseout = () => {\r\n        div.style.backgroundColor = index % 2 === 0 ? '#fff' : '#fafafa';\r\n        div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';\r\n      };\r\n\r\n      // 创建行内容\r\n      const rowDiv = document.createElement('div');\r\n      rowDiv.className = 'case-row-content';\r\n\r\n      // ID\r\n      const idDiv = document.createElement('div');\r\n      idDiv.textContent = `${item.id || index + 1}`;\r\n      idDiv.className = 'case-cell-id';\r\n      rowDiv.appendChild(idDiv);\r\n\r\n      // 名称\r\n      const nameDiv = document.createElement('div');\r\n      nameDiv.textContent = item.name || '-';\r\n      nameDiv.title = item.name || '';\r\n      nameDiv.className = 'case-cell-name';\r\n      rowDiv.appendChild(nameDiv);\r\n\r\n      // 参数\r\n      const paramDiv = document.createElement('div');\r\n      paramDiv.textContent = item.parameter || '-';\r\n      paramDiv.title = item.parameter || '';\r\n      paramDiv.className = 'case-cell-param';\r\n      rowDiv.appendChild(paramDiv);\r\n\r\n      div.appendChild(rowDiv);\r\n\r\n      return div;\r\n    }\r\n  });\r\n};\r\n\r\n// 更新虚拟滚动数据\r\nconst updateVirtualScroll = () => {\r\n  nextTick(() => {\r\n    initVirtualScroll();\r\n  });\r\n};\r\n\r\n// 监听 cases 变化，更新虚拟滚动\r\nwatch(() => props.cases, () => {\r\n  nextTick(() => {\r\n    updateVirtualScroll();\r\n  });\r\n}, { deep: true });\r\n\r\n// 组件挂载时初始化虚拟滚动\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    initVirtualScroll();\r\n  });\r\n});\r\n\r\n// 组件卸载时销毁虚拟滚动\r\nonUnmounted(() => {\r\n  if (virtualScroll) {\r\n    virtualScroll.destroy();\r\n    virtualScroll = null;\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.case-panel {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  border-top: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n}\r\n\r\n.case-info {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  display: flex;\r\n  padding: 10px 15px;\r\n}\r\n\r\n.save-progress {\r\n  padding: 12px;\r\n  background-color: #f0f9ff;\r\n  border-bottom: 1px solid #ebeef5;\r\n\r\n  .progress-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 8px;\r\n    font-size: 13px;\r\n    font-weight: 500;\r\n    color: #303133;\r\n  }\r\n\r\n  .progress-message {\r\n    margin-top: 4px;\r\n    font-size: 12px;\r\n    color: #606266;\r\n  }\r\n}\r\n\r\n.results-list {\r\n  flex: 1;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n  font-size: 13px;\r\n  min-height: 0;\r\n  flex-basis: 0;\r\n}\r\n\r\n.cases-header {\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 10;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.cases-content {\r\n  flex: 1;\r\n  overflow: auto;\r\n  position: relative;\r\n}\r\n\r\n/* 使用全局样式中的表格样式 */\r\n.case-list {\r\n  .cases-header {\r\n    .header-row {\r\n      display: flex;\r\n      align-items: center;\r\n      width: 100%;\r\n      height: 40px;\r\n      background-color: #f5f7fa;\r\n      border-bottom: 1px solid #dcdfe6;\r\n      font-weight: bold;\r\n      font-size: 12px;\r\n      color: #606266;\r\n      padding: 0;\r\n    }\r\n\r\n    .header-cell-id {\r\n      width: 60px;\r\n      flex-shrink: 0;\r\n      padding-left: 12px;\r\n    }\r\n\r\n    .header-cell-name {\r\n      flex: 1;\r\n      padding-left: 12px;\r\n    }\r\n\r\n    .header-cell-param {\r\n      flex: 1;\r\n      padding: 0 10px;\r\n    }\r\n  }\r\n\r\n  .case-row {\r\n    border: none;\r\n    padding: 0;\r\n    height: 36px;\r\n    line-height: 36px;\r\n    font-size: 12px;\r\n    transition: all 0.3s;\r\n  }\r\n\r\n  .case-row-content {\r\n    display: flex;\r\n    align-items: center;\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n\r\n  .case-cell-id {\r\n    font-weight: bold;\r\n    color: var(--el-color-primary);\r\n    width: 60px;\r\n    flex-shrink: 0;\r\n    padding-left: 12px;\r\n  }\r\n\r\n  .case-cell-name {\r\n    font-weight: 500;\r\n    color: #303133;\r\n    flex: 1;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n    padding: 0 0 0 10px;\r\n  }\r\n\r\n  .case-cell-param {\r\n    flex: 1;\r\n    color: #606266;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n    padding: 0 0 0 10px;\r\n  }\r\n}\r\n\r\n.loading-indicator,\r\n.empty-message {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  flex: 1;\r\n  color: #909399;\r\n  font-size: 13px;\r\n\r\n  .el-icon {\r\n    margin-right: 6px;\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./GeneratedCasesPanel.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./GeneratedCasesPanel.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./GeneratedCasesPanel.vue?vue&type=style&index=0&id=b7cb50e4&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-b7cb50e4\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { unref as _unref, createVNode as _createVNode, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createBlock as _createBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\"\n\nconst _hoisted_1 = [\"element-loading-text\"]\nconst _hoisted_2 = { class: \"toolbar\" }\nconst _hoisted_3 = { class: \"toolbar-left\" }\nconst _hoisted_4 = { class: \"action-buttons\" }\nconst _hoisted_5 = { class: \"content-area\" }\nconst _hoisted_6 = {\n  key: 0,\n  class: \"dialog-left\"\n}\nconst _hoisted_7 = { class: \"group-info\" }\nconst _hoisted_8 = { class: \"group-border\" }\nconst _hoisted_9 = { class: \"tree-node\" }\nconst _hoisted_10 = { class: \"case-name\" }\nconst _hoisted_11 = { class: \"case-count\" }\nconst _hoisted_12 = {\n  key: 1,\n  class: \"dialog-right\"\n}\nconst _hoisted_13 = { class: \"toolbar bottom-toolbar\" }\n\nimport { ref, computed, onMounted } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';\r\nimport { appApi, CoverageType } from '@/api/appApi';\r\nimport { CaseResult, GroupTreeNode } from '@/api/interoperationApi';\r\nimport GenerateCasesDialog from '@/components/TestCases/GenerateCasesDialog.vue';\r\nimport GeneratedCasesPanel from '@/components/TestCases/GeneratedCasesPanel.vue';\r\n\r\n// 状态变量\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TestCases',\n  setup(__props) {\n\r\nconst loading = ref(true);\r\nconst loadingText = ref('');\r\nconst selectedCoverage = ref(CoverageType.Normal);\r\nconst selectedSequences = ref<string[]>([]);\r\nconst baselineAverageTime = ref<number>(0); // 生成时的基准平均时间\r\nconst showGenerateDialog = ref(false); // 控制生成用例对话框显示\r\n\r\n// 测试用例相关状态\r\nconst allCases = ref<CaseResult[]>([]);\r\nconst displayedCases = ref<CaseResult[]>([]);\r\nconst groupTree = ref<GroupTreeNode | null>(null);\r\n\r\n// 树形组件引用和状态\r\nconst treeRef = ref();\r\nconst defaultExpandAll = ref(true); // 控制树的默认展开状态\r\nconst currentTreeData = ref<GroupTreeNode[]>([]); // 当前显示的树数据\r\nconst treeKey = ref(new Date())\r\n\r\n// 树形组件配置\r\nconst treeProps = {\r\n  children: 'children',\r\n  label: 'name'\r\n};\r\n\r\n// 计算属性\r\nconst groupTreeData = computed(() => {\r\n  return groupTree.value ? [groupTree.value] : [];\r\n});\r\n\r\nconst allCaseCount = computed(() => {\r\n  return allCases.value.length;\r\n});\r\n\r\nconst showEmptyMessage = computed(() => {\r\n  return allCases.value.length === 0;\r\n});\r\n\r\nconst canSave = computed(() => {\r\n  return allCases.value.length > 0;\r\n});\r\n\r\nconst hasTreeData = computed(() => {\r\n  return currentTreeData.value.length > 0 && !loading.value;\r\n});\r\n\r\n// 获取已保存的测试用例\r\nconst fetchSavedCases = async () => {\r\n  loadingText.value = \"Loading test cases...\";\r\n  loading.value = true;\r\n  try {\r\n    const response = await appApi.getCasesWithGroup();\r\n    allCases.value = response.data.cases;\r\n    groupTree.value = response.data.groupTree;\r\n\r\n    // 初始化当前树数据\r\n    currentTreeData.value = groupTreeData.value;\r\n\r\n    // 默认点击根节点显示所有用例\r\n    if (groupTree.value) {\r\n      handleNodeClick(groupTree.value);\r\n    }\r\n  } catch (error) {\r\n    console.error('获取测试用例失败:', error);\r\n    ElMessage.error('Failed to fetch test cases');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst expandAll = () => {\r\n  defaultExpandAll.value = true;\r\n  treeKey.value = new Date();\r\n};\r\n\r\nconst collapseAll = () => {\r\n  defaultExpandAll.value = false;\r\n  treeKey.value = new Date();\r\n};\r\n\r\n// 节点点击时筛选用例\r\nconst handleNodeClick = (node: any) => {\r\n  const path = node.id;\r\n  console.log(\"getNodePath node\", node, \"path\", path);\r\n  const isRootNode = !path.includes('/');\r\n  displayedCases.value = allCases.value.filter(c => isRootNode || (c.groupPath && c.groupPath.includes(path)));\r\n  console.log(\"allCases.value\", allCases.value);\r\n};\r\n\r\n// 打开生成用例对话框\r\nconst openGenerateDialog = () => {\r\n  // 打开对话框\r\n  showGenerateDialog.value = true;\r\n};\r\n\r\n// 用例生成成功的回调\r\nconst onCasesGenerated = (newCases: CaseResult[], newGroupTree: GroupTreeNode) => {\r\n  // 更新主页面的数据\r\n  allCases.value = newCases;\r\n  groupTree.value = newGroupTree;\r\n\r\n  // 更新当前树数据\r\n  currentTreeData.value = groupTreeData.value;\r\n\r\n  // 默认点击根节点显示所有用例\r\n  if (groupTree.value) {\r\n    handleNodeClick(groupTree.value);\r\n  }\r\n};\r\n\r\n// 保存测试用例\r\nconst handleSave = async () => {\r\n  try {\r\n    loadingText.value = \"Saving test cases...\";\r\n    loading.value = true;\r\n    await appApi.saveCases(allCases.value);\r\n    ElMessage.success(`${allCases.value.length} test cases saved successfully`);\r\n  } catch (error) {\r\n    console.error('保存测试用例失败:', error);\r\n    ElMessage.error('Failed to save test cases');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 组件挂载时获取已保存的测试用例\r\nonMounted(() => {\r\n  fetchSavedCases();\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_tree = _resolveComponent(\"el-tree\")!\n  const _directive_loading = _resolveDirective(\"loading\")!\n\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", {\n    class: \"test-cases-container\",\n    \"element-loading-text\": loadingText.value\n  }, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createElementVNode(\"div\", _hoisted_3, [\n        _createVNode(_component_el_button, {\n          onClick: expandAll,\n          type: \"primary\",\n          size: \"small\",\n          class: \"expand-button\",\n          disabled: !hasTreeData.value\n        }, {\n          default: _withCtx(() => [\n            _createVNode(_unref(FontAwesomeIcon), { icon: \"up-right-and-down-left-from-center\" }),\n            _cache[1] || (_cache[1] = _createElementVNode(\"span\", { class: \"button-text\" }, \"Expand All\", -1))\n          ]),\n          _: 1\n        }, 8, [\"disabled\"]),\n        _createVNode(_component_el_button, {\n          onClick: collapseAll,\n          type: \"primary\",\n          size: \"small\",\n          class: \"collapse-button\",\n          disabled: !hasTreeData.value\n        }, {\n          default: _withCtx(() => [\n            _createVNode(_unref(FontAwesomeIcon), { icon: \"down-left-and-up-right-to-center\" }),\n            _cache[2] || (_cache[2] = _createElementVNode(\"span\", { class: \"button-text\" }, \"Collapse All\", -1))\n          ]),\n          _: 1\n        }, 8, [\"disabled\"])\n      ]),\n      _createElementVNode(\"div\", _hoisted_4, [\n        _createVNode(_component_el_button, {\n          type: \"success\",\n          onClick: openGenerateDialog,\n          size: \"small\"\n        }, {\n          default: _withCtx(() => _cache[3] || (_cache[3] = [\n            _createTextVNode(\" Generate Cases \")\n          ])),\n          _: 1\n        })\n      ])\n    ]),\n    _createElementVNode(\"div\", _hoisted_5, [\n      (!loading.value)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [\n            _createElementVNode(\"div\", _hoisted_7, [\n              _createElementVNode(\"span\", null, \"Total \" + _toDisplayString(allCaseCount.value) + \" cases\", 1)\n            ]),\n            _createElementVNode(\"div\", _hoisted_8, [\n              _cache[4] || (_cache[4] = _createElementVNode(\"div\", { class: \"tree-header\" }, [\n                _createElementVNode(\"h4\", null, \"Case Groups\")\n              ], -1)),\n              (_openBlock(), _createBlock(_component_el_tree, {\n                ref_key: \"treeRef\",\n                ref: treeRef,\n                data: currentTreeData.value,\n                props: treeProps,\n                onNodeClick: handleNodeClick,\n                key: treeKey.value,\n                class: \"group-tree\",\n                \"expand-on-click-node\": false,\n                \"highlight-current\": \"\",\n                \"node-key\": \"id\",\n                \"default-expand-all\": defaultExpandAll.value\n              }, {\n                default: _withCtx(({ data }) => [\n                  _createElementVNode(\"div\", _hoisted_9, [\n                    _createElementVNode(\"div\", _hoisted_10, _toDisplayString(data.name), 1),\n                    _createElementVNode(\"div\", _hoisted_11, _toDisplayString(data.count) + \" cases\", 1)\n                  ])\n                ]),\n                _: 1\n              }, 8, [\"data\", \"default-expand-all\"]))\n            ])\n          ]))\n        : _createCommentVNode(\"\", true),\n      (!loading.value)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [\n            _createVNode(GeneratedCasesPanel, {\n              cases: displayedCases.value,\n              generating: false,\n              saving: false,\n              \"save-progress\": null,\n              \"show-empty-message\": showEmptyMessage.value\n            }, null, 8, [\"cases\", \"show-empty-message\"])\n          ]))\n        : _createCommentVNode(\"\", true)\n    ]),\n    _createElementVNode(\"div\", _hoisted_13, [\n      _createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: handleSave,\n        disabled: !canSave.value\n      }, {\n        default: _withCtx(() => _cache[5] || (_cache[5] = [\n          _createTextVNode(\"Save\")\n        ])),\n        _: 1\n      }, 8, [\"disabled\"])\n    ]),\n    _createVNode(GenerateCasesDialog, {\n      visible: showGenerateDialog.value,\n      \"onUpdate:visible\": _cache[0] || (_cache[0] = ($event: any) => ((showGenerateDialog).value = $event)),\n      coverage: selectedCoverage.value,\n      \"selected-sequences\": selectedSequences.value,\n      \"baseline-average-time\": baselineAverageTime.value,\n      onGenerated: onCasesGenerated\n    }, null, 8, [\"visible\", \"coverage\", \"selected-sequences\", \"baseline-average-time\"])\n  ], 8, _hoisted_1)), [\n    [_directive_loading, loading.value]\n  ])\n}\n}\n\n})", "<template>\r\n  <div class=\"test-cases-container\" v-loading=\"loading\" :element-loading-text=\"loadingText\">\r\n    <!-- 顶部工具栏 -->\r\n    <div class=\"toolbar\">\r\n      <div class=\"toolbar-left\">\r\n        <!-- 展开/收起全部按钮 -->\r\n        <el-button @click=\"expandAll\" type=\"primary\" size=\"small\" class=\"expand-button\" :disabled=\"!hasTreeData\">\r\n          <font-awesome-icon icon=\"up-right-and-down-left-from-center\" /><span class=\"button-text\">Expand All</span>\r\n        </el-button>\r\n        <el-button @click=\"collapseAll\" type=\"primary\" size=\"small\" class=\"collapse-button\" :disabled=\"!hasTreeData\">\r\n          <font-awesome-icon icon=\"down-left-and-up-right-to-center\" /><span class=\"button-text\">Collapse All</span>\r\n        </el-button>\r\n      </div>\r\n      <div class=\"action-buttons\">\r\n        <el-button type=\"success\" @click=\"openGenerateDialog\" size=\"small\">\r\n          Generate Cases\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 - 显示 Case Groups 和用例列表 -->\r\n    <div class=\"content-area\">\r\n      <!-- 左侧：分组树 -->\r\n      <div class=\"dialog-left\" v-if=\"!loading\">\r\n        <!-- 分组树上方的总览信息 -->\r\n        <div class=\"group-info\">\r\n          <span>Total {{ allCaseCount }} cases</span>\r\n        </div>\r\n        <div class=\"group-border\">\r\n          <div class=\"tree-header\">\r\n            <h4>Case Groups</h4>\r\n          </div>\r\n          <el-tree ref=\"treeRef\" :data=\"currentTreeData\" :props=\"treeProps\" @node-click=\"handleNodeClick\" :key=\"treeKey\"\r\n            class=\"group-tree\" :expand-on-click-node=\"false\" highlight-current node-key=\"id\"\r\n            :default-expand-all=\"defaultExpandAll\">\r\n            <template #default=\"{ data }\">\r\n              <div class=\"tree-node\">\r\n                <div class=\"case-name\">{{ data.name }}</div>\r\n                <div class=\"case-count\">{{ data.count }} cases</div>\r\n              </div>\r\n            </template>\r\n          </el-tree>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧：用例列表 -->\r\n      <div class=\"dialog-right\" v-if=\"!loading\">\r\n        <GeneratedCasesPanel :cases=\"displayedCases\" :generating=\"false\" :saving=\"false\" :save-progress=\"null\"\r\n          :show-empty-message=\"showEmptyMessage\" />\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 底部工具栏 -->\r\n    <div class=\"toolbar bottom-toolbar\">\r\n      <el-button type=\"primary\" @click=\"handleSave\" :disabled=\"!canSave\">Save</el-button>\r\n    </div>\r\n\r\n    <!-- 生成用例对话框 -->\r\n    <GenerateCasesDialog v-model:visible=\"showGenerateDialog\" :coverage=\"selectedCoverage\"\r\n      :selected-sequences=\"selectedSequences\" :baseline-average-time=\"baselineAverageTime\"\r\n      @generated=\"onCasesGenerated\" />\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, computed, onMounted } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';\r\nimport { appApi, CoverageType } from '@/api/appApi';\r\nimport { CaseResult, GroupTreeNode } from '@/api/interoperationApi';\r\nimport GenerateCasesDialog from '@/components/TestCases/GenerateCasesDialog.vue';\r\nimport GeneratedCasesPanel from '@/components/TestCases/GeneratedCasesPanel.vue';\r\n\r\n// 状态变量\r\nconst loading = ref(true);\r\nconst loadingText = ref('');\r\nconst selectedCoverage = ref(CoverageType.Normal);\r\nconst selectedSequences = ref<string[]>([]);\r\nconst baselineAverageTime = ref<number>(0); // 生成时的基准平均时间\r\nconst showGenerateDialog = ref(false); // 控制生成用例对话框显示\r\n\r\n// 测试用例相关状态\r\nconst allCases = ref<CaseResult[]>([]);\r\nconst displayedCases = ref<CaseResult[]>([]);\r\nconst groupTree = ref<GroupTreeNode | null>(null);\r\n\r\n// 树形组件引用和状态\r\nconst treeRef = ref();\r\nconst defaultExpandAll = ref(true); // 控制树的默认展开状态\r\nconst currentTreeData = ref<GroupTreeNode[]>([]); // 当前显示的树数据\r\nconst treeKey = ref(new Date())\r\n\r\n// 树形组件配置\r\nconst treeProps = {\r\n  children: 'children',\r\n  label: 'name'\r\n};\r\n\r\n// 计算属性\r\nconst groupTreeData = computed(() => {\r\n  return groupTree.value ? [groupTree.value] : [];\r\n});\r\n\r\nconst allCaseCount = computed(() => {\r\n  return allCases.value.length;\r\n});\r\n\r\nconst showEmptyMessage = computed(() => {\r\n  return allCases.value.length === 0;\r\n});\r\n\r\nconst canSave = computed(() => {\r\n  return allCases.value.length > 0;\r\n});\r\n\r\nconst hasTreeData = computed(() => {\r\n  return currentTreeData.value.length > 0 && !loading.value;\r\n});\r\n\r\n// 获取已保存的测试用例\r\nconst fetchSavedCases = async () => {\r\n  loadingText.value = \"Loading test cases...\";\r\n  loading.value = true;\r\n  try {\r\n    const response = await appApi.getCasesWithGroup();\r\n    allCases.value = response.data.cases;\r\n    groupTree.value = response.data.groupTree;\r\n\r\n    // 初始化当前树数据\r\n    currentTreeData.value = groupTreeData.value;\r\n\r\n    // 默认点击根节点显示所有用例\r\n    if (groupTree.value) {\r\n      handleNodeClick(groupTree.value);\r\n    }\r\n  } catch (error) {\r\n    console.error('获取测试用例失败:', error);\r\n    ElMessage.error('Failed to fetch test cases');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst expandAll = () => {\r\n  defaultExpandAll.value = true;\r\n  treeKey.value = new Date();\r\n};\r\n\r\nconst collapseAll = () => {\r\n  defaultExpandAll.value = false;\r\n  treeKey.value = new Date();\r\n};\r\n\r\n// 节点点击时筛选用例\r\nconst handleNodeClick = (node: any) => {\r\n  const path = node.id;\r\n  console.log(\"getNodePath node\", node, \"path\", path);\r\n  const isRootNode = !path.includes('/');\r\n  displayedCases.value = allCases.value.filter(c => isRootNode || (c.groupPath && c.groupPath.includes(path)));\r\n  console.log(\"allCases.value\", allCases.value);\r\n};\r\n\r\n// 打开生成用例对话框\r\nconst openGenerateDialog = () => {\r\n  // 打开对话框\r\n  showGenerateDialog.value = true;\r\n};\r\n\r\n// 用例生成成功的回调\r\nconst onCasesGenerated = (newCases: CaseResult[], newGroupTree: GroupTreeNode) => {\r\n  // 更新主页面的数据\r\n  allCases.value = newCases;\r\n  groupTree.value = newGroupTree;\r\n\r\n  // 更新当前树数据\r\n  currentTreeData.value = groupTreeData.value;\r\n\r\n  // 默认点击根节点显示所有用例\r\n  if (groupTree.value) {\r\n    handleNodeClick(groupTree.value);\r\n  }\r\n};\r\n\r\n// 保存测试用例\r\nconst handleSave = async () => {\r\n  try {\r\n    loadingText.value = \"Saving test cases...\";\r\n    loading.value = true;\r\n    await appApi.saveCases(allCases.value);\r\n    ElMessage.success(`${allCases.value.length} test cases saved successfully`);\r\n  } catch (error) {\r\n    console.error('保存测试用例失败:', error);\r\n    ElMessage.error('Failed to save test cases');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 组件挂载时获取已保存的测试用例\r\nonMounted(() => {\r\n  fetchSavedCases();\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.test-cases-container {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 15px 20px;\r\n}\r\n\r\n/* 顶部工具栏样式 */\r\n.toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n\r\n  .toolbar-left {\r\n    display: flex;\r\n  }\r\n\r\n  .action-buttons {\r\n    display: flex;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n/* 底部工具栏样式 */\r\n.bottom-toolbar {\r\n  margin-top: 15px;\r\n  margin-bottom: 0;\r\n  justify-content: flex-start;\r\n}\r\n\r\n/* 主要内容区域样式 */\r\n.content-area {\r\n  display: flex;\r\n  flex: 1;\r\n  min-height: 0;\r\n  gap: 15px;\r\n}\r\n\r\n/* 左侧分组树样式 */\r\n.dialog-left {\r\n  display: flex;\r\n  flex-direction: column;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  background: #fff;\r\n  flex: 0.5;\r\n  min-height: 0;\r\n  min-width: 0;\r\n}\r\n\r\n.group-info {\r\n  padding: 10px 15px;\r\n  border-bottom: 1px solid #e4e7ed;\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.group-border {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex-basis: 0;\r\n  min-height: 0;\r\n}\r\n\r\n.tree-header {\r\n  padding: 10px 15px;\r\n  border-bottom: 1px solid #e4e7ed;\r\n  background: #f8f9fa;\r\n\r\n  h4 {\r\n    margin: 0;\r\n    font-size: 14px;\r\n    color: #303133;\r\n  }\r\n}\r\n\r\n.group-tree {\r\n  flex: 1;\r\n  padding: 10px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.tree-node {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n\r\n  .case-name {\r\n    flex: 1;\r\n    font-size: 13px;\r\n  }\r\n\r\n  .case-count {\r\n    font-size: 12px;\r\n    color: #909399;\r\n    margin-left: 10px;\r\n  }\r\n}\r\n\r\n/* 右侧用例列表样式 */\r\n.dialog-right {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  background: #fff;\r\n}\r\n\r\n.button-text {\r\n  margin-left: 8px;\r\n}\r\n</style>\r\n", "import script from \"./TestCases.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TestCases.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./TestCases.vue?vue&type=style&index=0&id=54337949&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-54337949\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from \"vue\"\n\nimport { computed } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'CaseStateTag',\n  props: {\n    state: {}\n  },\n  setup(__props: any) {\n\r\nconst props = __props;\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getCaseStateName = (state: string): 'Not Run' | 'Running' | 'Passed' | 'Failed' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Passed';\r\n    case ExecutionState.Failure:\r\n      return 'Failed';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getCaseStateName(props.state);\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n\n  return (_openBlock(), _createBlock(_component_el_tag, {\n    type: tagType.value,\n    size: \"small\",\n    style: {\"min-width\":\"60px\"}\n  }, {\n    default: _withCtx(() => [\n      _createTextVNode(_toDisplayString(stateName.value), 1)\n    ]),\n    _: 1\n  }, 8, [\"type\"]))\n}\n}\n\n})", "<template>\r\n  <el-tag :type=\"tagType\" size=\"small\" style=\"min-width: 60px;\">\r\n    {{ stateName }}\r\n  </el-tag>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, defineProps } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\nconst props = defineProps<{\r\n  state: string;\r\n}>();\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getCaseStateName = (state: string): 'Not Run' | 'Running' | 'Passed' | 'Failed' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Passed';\r\n    case ExecutionState.Failure:\r\n      return 'Failed';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getCaseStateName(props.state);\r\n});\r\n</script>\r\n", "import script from \"./CaseStateTag.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./CaseStateTag.vue?vue&type=script&setup=true&lang=ts\"\n\nconst __exports__ = script;\n\nexport default __exports__", "/**\r\n * js-booster - High-performance frontend library\r\n * VirtualScroll - Virtual scrolling implementation\r\n * @version 1.1.3\r\n * <AUTHOR>\r\n * @license MIT\r\n */\r\n\r\nclass VirtualScroll {\r\n  /**\r\n   * Create a virtual scroll instance\r\n   * @param {Object} options Configuration options\r\n   * @param {HTMLElement} options.container Scroll container element\r\n   * @param {Array} options.items Data items to display\r\n   * @param {number} [options.itemHeight=20] Height of each list item (pixels)\r\n   * @param {number} [options.bufferSize=10] Number of buffer items outside the visible area\r\n   * @param {Function} [options.renderItem] Custom item rendering function\r\n   * @param {Function} [options.renderHeader] Custom header rendering function\r\n   * @param {number} [options.maxHeight=26840000] Maximum height in pixels for the content wrapper\r\n   */\r\n  constructor(options) {\r\n    this.container = options.container;\r\n    this.items = options.items || [];\r\n    this.itemHeight = options.itemHeight || 20;\r\n    this.bufferSize = options.bufferSize || 10;\r\n    this.customRenderItem = options.renderItem;\r\n    this.customRenderHeader = options.renderHeader;\r\n    this.maxHeight = options.maxHeight || 26840000; // Add maximum height limit to prevent DOM height overflow\r\n\r\n    this.visibleStartIndex = 0;\r\n    this.visibleEndIndex = 0;\r\n    this.scrollContainer = null;\r\n    this.contentWrapper = null;\r\n    this.contentContainer = null;\r\n    this.totalHeight = this.items.length * this.itemHeight;\r\n    this.heightScale = 1; // Height scaling factor\r\n\r\n    // If total height exceeds maximum height, calculate scaling factor\r\n    if (this.totalHeight > this.maxHeight) {\r\n      this.heightScale = this.maxHeight / this.totalHeight;\r\n    }\r\n\r\n    this.initialize();\r\n  }\r\n\r\n  /**\r\n   * Initialize virtual scroll component\r\n   * @private\r\n   */\r\n  initialize() {\r\n    // Clear container\r\n    this.container.innerHTML = '';\r\n\r\n    // Create scroll container\r\n    this.scrollContainer = document.createElement('div');\r\n    // Add inline styles\r\n    Object.assign(this.scrollContainer.style, {\r\n      flex: '1',\r\n      overflow: 'auto',\r\n      position: 'relative',\r\n      minHeight: '0',\r\n      height: '100%',\r\n      boxSizing: 'border-box'\r\n    });\r\n\r\n    // If there's a custom header render function, render the header\r\n    if (this.customRenderHeader) {\r\n      const header = this.customRenderHeader();\r\n      if (header) {\r\n        this.scrollContainer.appendChild(header);\r\n      }\r\n    }\r\n\r\n    // Create content wrapper\r\n    this.contentWrapper = document.createElement('div');\r\n    // Add inline styles\r\n    Object.assign(this.contentWrapper.style, {\r\n      position: 'relative',\r\n      width: '100%'\r\n    });\r\n\r\n    // Use scaled height to ensure it doesn't exceed browser limits\r\n    const scaledHeight = this.totalHeight * this.heightScale;\r\n    this.contentWrapper.style.height = `${scaledHeight}px`;\r\n\r\n    // Create content container\r\n    this.contentContainer = document.createElement('div');\r\n    // Add inline styles\r\n    Object.assign(this.contentContainer.style, {\r\n      position: 'absolute',\r\n      width: '100%',\r\n      left: '0'\r\n    });\r\n\r\n    // Add scroll event listener\r\n    this.scrollContainer.addEventListener('scroll', this.handleScroll.bind(this));\r\n\r\n    // Assemble DOM\r\n    this.contentWrapper.appendChild(this.contentContainer);\r\n    this.scrollContainer.appendChild(this.contentWrapper);\r\n    this.container.appendChild(this.scrollContainer);\r\n\r\n    // Render initial visible items\r\n    this.renderVisibleItems(0, Math.min(100, this.items.length));\r\n  }\r\n\r\n  /**\r\n   * Handle scroll event\r\n   * @private\r\n   */\r\n  handleScroll() {\r\n    const scrollTop = this.scrollContainer.scrollTop;\r\n    const containerHeight = this.scrollContainer.clientHeight;\r\n\r\n    // Consider scaling factor in calculations\r\n    const realScrollTop = scrollTop / this.heightScale;\r\n\r\n    // Calculate visible range\r\n    const startIndex = Math.max(0, Math.floor(realScrollTop / this.itemHeight) - this.bufferSize);\r\n    const endIndex = Math.min(\r\n      this.items.length,\r\n      Math.ceil((realScrollTop + containerHeight / this.heightScale) / this.itemHeight) + this.bufferSize\r\n    );\r\n\r\n    // Only update when visible range changes\r\n    if (startIndex !== this.visibleStartIndex\r\n       || endIndex !== this.visibleEndIndex\r\n       || endIndex === 0) {\r\n      this.renderVisibleItems(startIndex, endIndex);\r\n\r\n      this.visibleStartIndex = startIndex;\r\n      this.visibleEndIndex = endIndex;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Render visible items\r\n   * @param {number} startIndex Start index\r\n   * @param {number} endIndex End index\r\n   * @private\r\n   */\r\n  renderVisibleItems(startIndex, endIndex) {\r\n    // Clear content container\r\n    this.contentContainer.innerHTML = '';\r\n\r\n    // Set position considering scaling factor\r\n    this.contentContainer.style.transform = `translateY(${startIndex * this.itemHeight * this.heightScale}px)`;\r\n\r\n    // Render visible items\r\n    for (let i = startIndex; i < endIndex; i++) {\r\n      const item = this.items[i];\r\n\r\n      if (this.customRenderItem) {\r\n        // Use custom render function\r\n        const itemElement = this.customRenderItem(item, i);\r\n        if (itemElement) {\r\n          // Only set necessary height styles, other styles are determined by the caller\r\n          itemElement.style.height = `${this.itemHeight * this.heightScale}px`;\r\n          itemElement.style.boxSizing = 'border-box';\r\n          itemElement.style.width = '100%';\r\n\r\n          this.contentContainer.appendChild(itemElement);\r\n        }\r\n      } else {\r\n        // Use default rendering - very simple default implementation\r\n        const row = document.createElement('div');\r\n        Object.assign(row.style, {\r\n          height: `${this.itemHeight * this.heightScale}px`,\r\n          width: '100%',\r\n          boxSizing: 'border-box',\r\n          padding: '8px',\r\n          borderBottom: '1px solid #eee'\r\n        });\r\n        row.textContent = JSON.stringify(item);\r\n        this.contentContainer.appendChild(row);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update data items and re-render\r\n   * @param {Array} items New data items array\r\n   * @public\r\n   */\r\n  updateItems(items) {\r\n    this.items = items || [];\r\n    this.totalHeight = this.items.length * this.itemHeight;\r\n\r\n    // Recalculate scaling factor\r\n    this.heightScale = 1;\r\n    if (this.totalHeight > this.maxHeight) {\r\n      this.heightScale = this.maxHeight / this.totalHeight;\r\n    }\r\n\r\n    // Ensure height is set correctly\r\n    if (this.contentWrapper) {\r\n      this.contentWrapper.style.height = `${this.totalHeight * this.heightScale}px`;\r\n    }\r\n\r\n    this.visibleStartIndex = 0;\r\n    this.visibleEndIndex = 0;\r\n\r\n    // Force recalculation of visible items\r\n    this.handleScroll();\r\n  }\r\n\r\n  /**\r\n   * Scroll to specified index\r\n   * @param {number} index Index of the item to scroll to\r\n   * @public\r\n   */\r\n  scrollToIndex(index) {\r\n    if (index >= 0 && index < this.items.length) {\r\n      // Apply scaling factor when scrolling\r\n      this.scrollContainer.scrollTop = index * this.itemHeight * this.heightScale;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Destroy component, remove event listeners, etc.\r\n   * @public\r\n   */\r\n  destroy() {\r\n    if (this.scrollContainer) {\r\n      this.scrollContainer.removeEventListener('scroll', this.handleScroll);\r\n    }\r\n    if (this.container) {\r\n      this.container.innerHTML = '';\r\n    }\r\n    this.items = null;\r\n    this.container = null;\r\n    this.scrollContainer = null;\r\n    this.contentWrapper = null;\r\n    this.contentContainer = null;\r\n  }\r\n\r\n  /**\r\n   * Refresh virtual scroll, re-render current visible items\r\n   * @public\r\n   */\r\n  refresh() {\r\n    this.handleScroll();\r\n  }\r\n\r\n  /**\r\n   * Get scroll container element\r\n   * @returns {HTMLElement} Scroll container element\r\n   * @public\r\n   */\r\n  getScrollContainer() {\r\n    return this.scrollContainer;\r\n  }\r\n}\r\n\r\n// Export VirtualScroll class\r\nexport { VirtualScroll };\r\n", "/**\r\n * js-booster - High-performance frontend library\r\n * @version 1.1.3\r\n * <AUTHOR>\r\n * @license MIT\r\n */\r\n\r\nimport { VirtualScroll } from './virtual-scroll';\r\n\r\n// Export all components\r\nexport { VirtualScroll };\r\n\r\n// If in browser environment, add to global object\r\nif (typeof window !== 'undefined') {\r\n  window.JsBooster = {\r\n    VirtualScroll\r\n  };\r\n}\r\n"], "names": ["_hoisted_1", "class", "_defineComponent", "__name", "props", "begin", "end", "showDuration", "type", "Boolean", "setup", "__props", "formatDate", "date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "milliseconds", "getMilliseconds", "formatTimeOnly", "tooltipContent", "computed", "beginDate", "Date", "beginFull", "endDate", "endFull", "durationMs", "getTime", "durationText", "Math", "floor", "formattedTime", "beginStr", "round", "_ctx", "_cache", "_openBlock", "_createBlock", "_unref", "ElTooltip", "key", "content", "value", "placement", "effect", "default", "_withCtx", "_createElementVNode", "_toDisplayString", "_", "_createCommentVNode", "__exports__", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "results", "loading", "selectedSequences", "averageTime", "emits", "emit", "__emit", "selectedItems", "ref", "initializeSelectedItems", "length", "map", "result", "state", "ExecutionState", "Success", "updateSelectedSequences", "onMounted", "watch", "immediate", "newSelected", "item", "includes", "sequenceName", "deep", "isAllSelected", "every", "selected", "isIndeterminate", "some", "selectedCount", "filter", "handleSelectAllChange", "val", "Array", "fill", "handleItemClick", "index", "handleItemSelect", "uniqueSelected", "Set", "duplicateSequences", "nameCounts", "Map", "duplicates", "for<PERSON>ach", "count", "get", "set", "add", "from", "isDuplicate", "name", "_component_el_icon", "_resolveComponent", "_component_el_checkbox", "_component_el_tag", "_createElementBlock", "_createVNode", "<PERSON><PERSON><PERSON><PERSON>", "title", "closable", "_createTextVNode", "join", "Loading", "modelValue", "$event", "indeterminate", "onChange", "size", "formatDuration", "_Fragment", "_renderList", "id", "_normalizeClass", "onClick", "_withModifiers", "CaseStateTag", "TimeDisplay", "visible", "coverage", "baselineAverageTime", "generating", "interoperationResults", "interoperationLoading", "generatingProgressText", "generateProgressTimer", "formData", "CoverageType", "Normal", "dialogVisible", "generatingText", "averageTimeOfSelectedSequences", "totalExecutionTime", "totalExecutionCount", "executionTime", "fetchInteroperationResults", "async", "response", "appApi", "getLatestInteroperationCaseResults", "data", "successfulSequences", "error", "console", "ElMessage", "handleGenerate", "startGenerateProgressPolling", "generateCasesWithGroup", "cases", "groupTree", "success", "stopGenerateProgressPolling", "warning", "pollProgress", "getGeneratingProgress", "window", "setTimeout", "clearTimeout", "handleCancel", "handleDialogClose", "_component_el_radio", "_component_el_radio_group", "_component_el_button", "_component_el_dialog", "_directive_loading", "_resolveDirective", "onClose", "footer", "disabled", "_withDirectives", "label", "High", "InteroperationResultPanel", "style", "ITEM_HEIGHT", "BUFFER_SIZE", "HEADER_HEIGHT", "saving", "saveProgress", "showEmptyMessage", "casesContainer", "headerContainer", "virtualScroll", "renderFixedHeader", "innerHTML", "header", "document", "createElement", "className", "height", "idHeader", "textContent", "append<PERSON><PERSON><PERSON>", "nameHeader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initVirtualScroll", "destroy", "VirtualScroll", "container", "items", "itemHeight", "bufferSize", "renderItem", "div", "lineHeight", "borderBottom", "backgroundColor", "on<PERSON><PERSON>ver", "onmouseout", "rowDiv", "idDiv", "nameDiv", "paramDiv", "parameter", "updateVirtualScroll", "nextTick", "onUnmounted", "_component_el_progress", "percentage", "progress", "status", "undefined", "message", "ref_key", "loadingText", "selectedCoverage", "showGenerateDialog", "allCases", "displayedCases", "treeRef", "defaultExpandAll", "currentTreeData", "<PERSON><PERSON><PERSON>", "treeProps", "children", "groupTreeData", "allCaseCount", "canSave", "hasTreeData", "fetchSavedCases", "getCasesWithGroup", "handleNodeClick", "expandAll", "collapseAll", "node", "path", "log", "isRootNode", "c", "groupPath", "openGenerateDialog", "onCasesGenerated", "newCases", "newGroupTree", "handleSave", "saveCases", "_component_el_tree", "FontAwesomeIcon", "icon", "onNodeClick", "GeneratedCasesPanel", "GenerateCasesDialog", "onGenerated", "tagType", "Running", "Failure", "Pending", "getCaseStateName", "stateName", "constructor", "options", "this", "customRenderItem", "customRenderHeader", "renderHeader", "maxHeight", "visibleStartIndex", "visibleEndIndex", "scrollContainer", "contentWrapper", "contentContainer", "totalHeight", "heightScale", "initialize", "Object", "assign", "flex", "overflow", "position", "minHeight", "boxSizing", "width", "scaledHeight", "left", "addEventListener", "handleScroll", "bind", "renderVisibleItems", "min", "scrollTop", "containerHeight", "clientHeight", "realScrollTop", "startIndex", "max", "endIndex", "ceil", "transform", "i", "itemElement", "row", "padding", "JSON", "stringify", "updateItems", "scrollToIndex", "removeEventListener", "refresh", "getScrollContainer", "JsBooster"], "sourceRoot": ""}