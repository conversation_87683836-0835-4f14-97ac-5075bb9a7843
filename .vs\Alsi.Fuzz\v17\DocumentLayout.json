{"Version": 1, "WorkspaceRootPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{BB7CB51F-8FE5-473F-B731-C6BC3AE32BE7}|Alsi.Fuzz.UnitTests\\Alsi.Fuzz.UnitTests.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.fuzz\\alsi.fuzz.unittests\\testplans\\testplantests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BB7CB51F-8FE5-473F-B731-C6BC3AE32BE7}|Alsi.Fuzz.UnitTests\\Alsi.Fuzz.UnitTests.csproj|solutionrelative:alsi.fuzz.unittests\\testplans\\testplantests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6579CAA3-008C-4FC2-B6B3-F3D228DAFB03}|Alsi.Fuzz.Web\\Alsi.Fuzz.Web.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.fuzz\\alsi.fuzz.web\\controllers\\casecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6579CAA3-008C-4FC2-B6B3-F3D228DAFB03}|Alsi.Fuzz.Web\\Alsi.Fuzz.Web.csproj|solutionrelative:alsi.fuzz.web\\controllers\\casecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9C5C66B4-B1FA-4F41-9DAF-3779C5940C53}|Alsi.Fuzz.Core\\Alsi.Fuzz.Core.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.fuzz\\alsi.fuzz.core\\service\\testplanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9C5C66B4-B1FA-4F41-9DAF-3779C5940C53}|Alsi.Fuzz.Core\\Alsi.Fuzz.Core.csproj|solutionrelative:alsi.fuzz.core\\service\\testplanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9C5C66B4-B1FA-4F41-9DAF-3779C5940C53}|Alsi.Fuzz.Core\\Alsi.Fuzz.Core.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.fuzz\\alsi.fuzz.core\\storage\\testplanstorage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9C5C66B4-B1FA-4F41-9DAF-3779C5940C53}|Alsi.Fuzz.Core\\Alsi.Fuzz.Core.csproj|solutionrelative:alsi.fuzz.core\\storage\\testplanstorage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9C5C66B4-B1FA-4F41-9DAF-3779C5940C53}|Alsi.Fuzz.Core\\Alsi.Fuzz.Core.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.fuzz\\alsi.fuzz.core\\service\\testplanmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9C5C66B4-B1FA-4F41-9DAF-3779C5940C53}|Alsi.Fuzz.Core\\Alsi.Fuzz.Core.csproj|solutionrelative:alsi.fuzz.core\\service\\testplanmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9C5C66B4-B1FA-4F41-9DAF-3779C5940C53}|Alsi.Fuzz.Core\\Alsi.Fuzz.Core.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.fuzz\\alsi.fuzz.core\\service\\results\\caseresult.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9C5C66B4-B1FA-4F41-9DAF-3779C5940C53}|Alsi.Fuzz.Core\\Alsi.Fuzz.Core.csproj|solutionrelative:alsi.fuzz.core\\service\\results\\caseresult.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "TestPlanTests.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.UnitTests\\TestPlans\\TestPlanTests.cs", "RelativeDocumentMoniker": "Alsi.Fuzz.UnitTests\\TestPlans\\TestPlanTests.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.UnitTests\\TestPlans\\TestPlanTests.cs", "RelativeToolTip": "Alsi.Fuzz.UnitTests\\TestPlans\\TestPlanTests.cs", "ViewState": "AgIAAH0AAAAAAAAAAAAIwIoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-21T05:57:57.864Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "TestPlanStorage.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Core\\Storage\\TestPlanStorage.cs", "RelativeDocumentMoniker": "Alsi.Fuzz.Core\\Storage\\TestPlanStorage.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Core\\Storage\\TestPlanStorage.cs", "RelativeToolTip": "Alsi.Fuzz.Core\\Storage\\TestPlanStorage.cs", "ViewState": "AgIAACoAAAAAAAAAAIAwwDoAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-21T05:54:53.535Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "TestPlanService.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Core\\Service\\TestPlanService.cs", "RelativeDocumentMoniker": "Alsi.Fuzz.Core\\Service\\TestPlanService.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Core\\Service\\TestPlanService.cs", "RelativeToolTip": "Alsi.Fuzz.Core\\Service\\TestPlanService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAABTAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-21T05:54:01.915Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "TestPlanManager.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Core\\Service\\TestPlanManager.cs", "RelativeDocumentMoniker": "Alsi.Fuzz.Core\\Service\\TestPlanManager.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Core\\Service\\TestPlanManager.cs", "RelativeToolTip": "Alsi.Fuzz.Core\\Service\\TestPlanManager.cs", "ViewState": "AgIAACEAAAAAAAAAAIAwwDcAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-21T05:53:50.435Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{6324226f-61b6-4f28-92ee-18d4b5fe1e48}"}, {"$type": "Document", "DocumentIndex": 5, "Title": "CaseResult.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Core\\Service\\Results\\CaseResult.cs", "RelativeDocumentMoniker": "Alsi.Fuzz.Core\\Service\\Results\\CaseResult.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Core\\Service\\Results\\CaseResult.cs", "RelativeToolTip": "Alsi.Fuzz.Core\\Service\\Results\\CaseResult.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAuwA8AAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-21T05:43:39.732Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "CaseController.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\Controllers\\CaseController.cs", "RelativeDocumentMoniker": "Alsi.Fuzz.Web\\Controllers\\CaseController.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\Controllers\\CaseController.cs", "RelativeToolTip": "Alsi.Fuzz.Web\\Controllers\\CaseController.cs", "ViewState": "AgIAAE4DAAAAAAAAAAAAAF0DAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-21T05:05:13.674Z", "EditorCaption": ""}]}]}]}