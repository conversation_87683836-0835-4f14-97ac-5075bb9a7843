<template>
  <el-dialog v-model="dialogVisible" :title="`${caseData?.name || caseData?.sequenceName || ''}`" width="60%"
    destroy-on-close>
    <div v-if="loading" class="loading">
      <el-skeleton :rows="10" animated />
    </div>

    <div v-else class="case-detail-content">
      <!-- 基本信息区域 -->
      <div class="basic-info">
        <h4>Information</h4>
        <div class="info-grid">
          <div class="info-item" v-if="caseData?.name">
            <div class="label">Case Name:</div>
            <div class="value">{{ caseData?.name }}</div>
          </div>
          <div class="info-item" v-if="caseData?.name">
            <div class="label">Sequence Name:</div>
            <div class="value">{{ caseData?.sequenceName }}</div>
          </div>
          <div class="info-item">
            <div class="label">Start Time:</div>
            <div class="value">{{ formatDateTime(caseData?.begin) }}</div>
          </div>
          <div class="info-item">
            <div class="label">End Time / Status:</div>
            <div class="value status-combined">
              {{ formatDateTime(caseData?.end) }}
              <CaseStateTag :state="caseData?.state || ''" class="status-tag" />
            </div>
          </div>
          <div v-if="caseData?.parameter" class="info-item full-width">
            <div class="label">Parameter:</div>
            <div class="value" :title="caseData?.parameter">{{ caseData?.parameter }}</div>
          </div>
          <div v-if="caseData?.detail" class="info-item full-width">
            <div class="label">Detail:</div>
            <div class="value" :title="caseData?.detail">{{ caseData.detail }}</div>
          </div>
        </div>
      </div>

      <!-- 步骤列表区域 -->
      <div class="steps-section">
        <h4>Steps</h4>

        <div v-if="steps.length === 0" class="no-steps">
          <el-empty description="No steps available" />
        </div>

        <el-timeline v-else>
          <el-timeline-item v-for="step in steps" :key="step.id" :type="getTimelineItemType(step.state)"
            :hollow="step.state !== 'Success'">
            <div class="step-content">
              <div class="step-row">
                <div class="step-left">
                  <span class="step-timestamp">{{ formatMicroseconds(step.timestamp) }}</span>
                  <span class="step-name" :title="step.name" >{{ step.name }}</span>
                  <span v-if="step.detail" :title="step.detail" class="step-detail-inline">{{ step.detail }}</span>
                </div>
                <div class="step-right">
                  <CaseStateTag v-if="step.state != ExecutionState.Completed" :state="step.state" />
                </div>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">Close</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { CaseResult } from '@/api/interoperationApi';
import { appApi, CaseStep, ExecutionState } from '@/api/appApi';
import { getTimelineItemType } from '@/utils/status';
import CaseStateTag from '@/components/common/CaseStateTag.vue';
import { formatDateTime } from '@/utils/timeUtils';

const props = defineProps<{
  visible: boolean;
  testResultId?: string | null;
  caseResultId?: number | null;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'close'): void;
}>();

const dialogVisible = ref(props.visible);
const caseData = ref<CaseResult | null>(null);
const steps = ref<CaseStep[]>([]);
const loading = ref(false);

watch(() => props.visible, (newValue) => {
  dialogVisible.value = newValue;
  if (newValue && props.testResultId && props.caseResultId) {
    loadCaseData();
  }
});

watch(() => dialogVisible.value, (newValue) => {
  emit('update:visible', newValue);
  if (!newValue) emit('close');
});

const loadCaseData = async () => {
  if (!props.testResultId || !props.caseResultId) {
    ElMessage.warning('Missing required parameters');
    return;
  }

  loading.value = true;
  try {
    // 并行加载用例数据和步骤数据
    const [caseResponse, stepsResponse] = await Promise.all([
      appApi.getCaseResult(props.testResultId, props.caseResultId),
      appApi.getCaseSteps(props.testResultId, props.caseResultId)
    ]);

    caseData.value = caseResponse.data;
    steps.value = stepsResponse.data;
  } catch (error) {
    console.error('Failed to load case data:', error);
    ElMessage.error('Failed to load case details');
  } finally {
    loading.value = false;
  }
};

const closeDialog = () => {
  dialogVisible.value = false;
  // 清空数据
  caseData.value = null;
  steps.value = [];
};

// 添加新的微秒格式化方法
const formatMicroseconds = (microseconds: number): string => {
  if (!microseconds && microseconds !== 0) return 'N/A';

  // 转换为秒并保留6位小数
  const seconds = microseconds / 1000000;
  // 使用toFixed(6)确保始终有6位小数
  return `${seconds.toFixed(6)}`;
};

// 组件挂载时，如果对话框是可见的且有必要参数，则加载数据
onMounted(() => {
  if (dialogVisible.value && props.testResultId && props.caseResultId) {
    loadCaseData();
  }
});
</script>

<style scoped lang="scss">
.loading {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.case-detail-content {
  padding: 0 20px;
}

.basic-info {
  margin-bottom: 20px;

  h4 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 16px;
    color: #303133;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 8px;
  }

  .info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px 24px;

    .info-item {
      .label {
        font-size: 13px;
        color: #909399;
        margin-bottom: 4px;
      }

      .value {
        font-size: 14px;
        color: #303133;
        word-break: break-word;

        &.status-combined {
          display: flex;
          align-items: center;
          
          .status-tag {
            margin-left: 12px;
          }
        }
      }

      &.full-width {
        grid-column: span 2;
      }
    }
  }
}

.steps-section {
  h4 {
    margin-top: 20px;
    margin-bottom: 16px;
    font-size: 16px;
    color: #303133;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 8px;
  }

  .no-steps {
    padding: 20px 0;
  }

  .step-content {
    font-size: 13px;

    .step-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      padding: 8px 4px;
      transition: background-color 0.2s;
    }

    .step-left {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0; // 防止flex子元素溢出
    }

    .step-right {
      margin-left: 10px;
      flex-shrink: 0;
    }

    .step-timestamp {
      min-width: 80px;
      display: inline-block;
      color: #606266;
      margin-right: 12px;
      flex-shrink: 0;
    }

    .step-name {
      min-width: 80px;
      font-weight: 500;
      display: inline-block;
      color: #303133;
      margin-right: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 200px; /* 限制最大宽度，可根据实际情况调整 */
    }

    .step-detail-inline {
      color: #606266;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .el-timeline-item:nth-child(odd) .step-row {
    background-color: #f5f5fa;
  }

  .el-timeline-item:nth-child(even) .step-row {
    background-color: #f5f5fa;
  }

  :deep(.el-timeline-item__node) {
    margin-top: 10px;
  }

  :deep(.el-timeline-item__tail) {
    margin-top: 10px;
  }
}
</style>
