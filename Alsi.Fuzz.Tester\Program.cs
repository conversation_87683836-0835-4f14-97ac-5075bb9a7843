using Alsi.App;
using Alsi.App.Devices;
using System;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Alsi.Fuzz.Tester
{
    internal static class Program
    {
        public static bool IsPaused { get; set; }
        public static bool IsRunning { get; set; }

        static void Main(string[] args)
        {
            // 解析命令行参数
            var arguments = args.Select(arg => arg.Split('='))
                .ToDictionary(
                    arg => arg[0].TrimStart('-'),
                    arg => arg.Length > 1 ? arg[1] : null
                );

            int? customPort = null;
            if (arguments.ContainsKey("port") && int.TryParse(arguments["port"], out int port))
            {
                customPort = port;
            }

            if (arguments.ContainsKey("quick-debug")
                && bool.TryParse(arguments["quick-debug"], out var quickDebug))
            {
                if (!Debugger.IsAttached)
                {
                    Debugger.Launch();
                }
            }

            if (arguments.ContainsKey("parent-process-mutex")
                && !string.IsNullOrWhiteSpace(arguments["parent-process-mutex"]))
            {
                var parentProcessMutexName = arguments["parent-process-mutex"];
                MonitorParentProcessWithMutex(parentProcessMutexName);
            }

            var testerAssembly = new FuzzTesterAssembly();

            var options = new AppOptions
            {
                CustomPort = customPort,
                ApiLogOptions = new ApiLogOptions
                {
                    SilentApiPaths = { "api/tester/snapshot" }
                }
            };

            var app = WebHostApp
                .Create(appName: "FuzzTester", appFolderName: "Alsi.Atts", productFolderName: "Fuzz")
                .UseOptions(options)
                .UseSerilog("FuzzTester")
                .UseApiHost(testerAssembly)
                .UseDevice()
                .Build();

            IsRunning = true;
            while (IsRunning)
            {
                Thread.Sleep(100);
            }

            // 等待测试执行器释放资源，保存数据日志
            if (TesterEnv.PackageTester != null && TesterEnv.PackageTester.NeedRelease)
            {
                for (var i = 0; i < 300; i++)
                {
                    Thread.Sleep(100);

                    if (TesterEnv.PackageTester.IsReleased)
                    {
                        break;
                    }
                }
            }

            app.Exit();
        }

        private static void MonitorParentProcessWithMutex(string mutexName)
        {
            Task.Run(() =>
            {
                try
                {
                    using (var mutex = Mutex.OpenExisting(mutexName))
                    {
                        mutex.WaitOne();
                        AppEnv.Logger.Warning($"Parent process mutex [{mutexName}] has been released, exiting tester");
                        IsRunning = false;
                    }
                }
                catch (Exception e)
                {
                    AppEnv.Logger.Error(e, $"Failed to monitor parent mutex [{mutexName}], exiting tester");
                    IsRunning = false;
                }
            });
        }
    }
}
