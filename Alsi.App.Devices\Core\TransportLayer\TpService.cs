using Alsi.App.Devices.Core.TransportLayer.Frames;
using Alsi.Common.Utils;
using System;
using System.Linq;
using System.Threading;

namespace Alsi.App.Devices.Core.TransportLayer
{
    public partial class TpService
    {
        public TpService(DiagParams diagParams)
        {
            DiagParams = diagParams;
            DataBus.OnReceived += DataBus_OnReceived;
        }

        private readonly ManualResetEvent waitFlowControlEvent = new ManualResetEvent(false);

        private Response Response { get; set; }
        private TpContext Context { get; set; }
        private Exception Exception { get; set; }
        private DiagParams DiagParams { get; set; }
        public bool IsRunning { get; private set; } = false;

        private readonly object LockObj = new object();

        public Response Request(
            Request request, TimeSpan? timeout = null, bool acceptNoResponse = false)
        {
            lock (LockObj)
            {
                return InternalRequest(request, timeout, acceptNoResponse);
            }
        }

        private Response InternalRequest(Request request, TimeSpan? timeout, bool acceptNoResponse)
        {
            try
            {
                IsRunning = true;

                Exception = null;
                timeout = timeout == null ? TimeSpan.FromMilliseconds(DiagParams.P2ExtTime) : timeout;
                Response = new Response();

                Context = new TpContext(request, DiagParams);

                if (!SendSingleFrame(request)
                    && !SendMultipleFrame(request))
                {
                    throw new Exception("Can't parse payload as Single Frame or Multiple Frame");
                }

                var isReceived = Context.Wait((int)timeout.Value.TotalMilliseconds);

                if (!isReceived)
                {
                    if (acceptNoResponse)
                    {
                        // 调用 TP 接口时，如果配置了接受无响应，当接口超时，返回空白数据
                        return new Response();
                    }
                    else
                    {
                        Exception = new Exception($"Request failed: no response within timeout ({timeout.Value.TotalMilliseconds}ms)");
                    }
                }

                if (Exception != null)
                {
                    throw Exception;
                }

                return Response;
            }
            finally
            {
                IsRunning = false;
            }
        }

        private void DataBus_OnReceived(object sender, CanFrame frame)
        {
            if (!IsRunning || Context == null)
            {
                return;
            }

            var id = frame.Id;
            var data = frame.Data;

            var responseContext = Context.ResponseContext;

            if (id != Context.Request.ResponseId)
            {
                return;
            }

            if (FlowControl.TryMatch(frame.Data, false, out var receivedFlowControl))
            {
                // [接收方] 接收流控帧（FC）
                Context.Recorder.Add(receivedFlowControl);
                waitFlowControlEvent.Set();
                return;
            }

            if (SingleFrame.TryMatch(frame.Data, false, frame.IsCanFd, out var singleFrame))
            {
                // [接收方] 接收单帧（SF）
                responseContext.SingleFrame = singleFrame;

                Context.Recorder.Add(singleFrame);
            }
            else if (FirstFrame.TryMatch(frame.Data, false, out var firstFrame))
            {
                // [接收方] 多帧 -> 接收首帧（FF）
                responseContext.FirstFrame = firstFrame;

                Context.Recorder.Add(firstFrame);

                // [接收方] 多帧 -> 发送流控制帧（FC）
                var flowControl = FlowControl.Build(FlowState.ContinueToSend, DiagParams.Bs, DiagParams.StMin);
                HandleRequest(Context.Request.FlowControlId, 8, flowControl.Data,
                    Context.Request.IsCanfd, Context.Request.RequestIsExt);

                Context.Recorder.Add(flowControl);
            }
            else if (ConsecutiveFrame.TryMatch(frame.Data, false, out var consecutiveFrame))
            {
                // [接收方] 多帧 -> 接收连续帧（CF）
                responseContext.AddConsecutiveFrame(consecutiveFrame);
                Context.Recorder.Add(consecutiveFrame);

                if (DiagParams.Bs != 0)
                {
                    // 如果 BS 不为 0，发送方最多能连续发送 BS 帧 CF 就会继续受到接收方的流控帧
                    var receivedCfCount = responseContext.GetConsecutiveFrames().Length;
                    if (receivedCfCount != 0 && receivedCfCount % DiagParams.Bs == 0)
                    {
                        // [接收方] 多帧 -> 发送流控制帧（FC）
                        var flowControl = FlowControl.Build(FlowState.ContinueToSend, DiagParams.Bs, DiagParams.StMin);
                        HandleRequest(Context.Request.FlowControlId, 8, flowControl.Data,
                            Context.Request.IsCanfd, Context.Request.RequestIsExt);
                        Context.Recorder.Add(flowControl);
                    }
                }
            }
            else
            {
                Exception = new Exception(
                    $"Handle data failed, unknow TP frame type: [{id.ToHex()}] {data.Take(10).ToHex()}...");

                Context.OnResponse(false);
                return;
            }

            // 是否所有数据都已接收完成（单帧或多帧）
            if (responseContext.IsFinished(out var payload))
            {
                // 参考 ISO14229-1_2013_03-en 1.pdf 第 337 页 0x78(requestCorrectlyReceived-ResponsePending)
                // 如果收到了 0x78，则继续等待结果消息
                if (payload.Length >= 3 && payload[0] == 0x7F && payload[2] == 0x78)
                {
                    responseContext.SingleFrame = null;
                    responseContext.FirstFrame = null;
                    responseContext.ClearConsecutiveFrame();
                    AppEnv.Logger.Info($"0x78(requestCorrectlyReceived-ResponsePending) was received: {payload.ToHex()}, continue to receive");

                    Context.OnResponse(true);
                    return;
                }

                Response = new Response
                {
                    Payload = payload
                };

                Context.OnResponse(false);
            }
        }

        private void HandleRequest(int id, byte dlc, byte[] data, bool isCanfd, bool isExt)
        {
            var frame = new CanFrame
            {
                Id = id,
                Data = data,
                Dlc = dlc,
                IsCanFd = isCanfd,
                IsExt = isExt
            };
            DataBus.Send(frame);
        }
    }
}
