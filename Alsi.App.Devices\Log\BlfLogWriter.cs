using Alsi.App;
using Alsi.App.Devices.Core;
using System;
using System.IO;
using System.Runtime.InteropServices;

namespace Alsi.Common.Log
{
    public class BlfLogWriter
    {
        [DllImport("OperateBlf.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern IntPtr CreateBlfFile(string blfPath);

        [DllImport("OperateBlf.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern int CloseBlfFile(IntPtr handleFile);

        [DllImport("OperateBlf.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern int WriteBlfCanData(IntPtr handleFile, VBLCANMessage canMessage);

        [DllImport("OperateBlf.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern int WriteBlfCanfdData(IntPtr handleFile, VBLCANFDMessage64 canfdMessage);

        [DllImport("OperateBlf.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern int WriteBlfLinData(IntPtr handleFile, VBLLINMessage linMessage);

        [DllImport("OperateBlf.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern int WriteBlfCanErrorFrameData(IntPtr handleFile, VBLCANErrorFrame errorFrame);

        [DllImport("OperateBlf.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern int WriteBlfCanfdErrorFrameData(IntPtr handleFile, VBLCANFDErrorFrame64 errorFrame);

        public static IntPtr HandleFile { get; set; } = IntPtr.Zero;

        public static string LogPath { get; set; } = string.Empty;

        public static bool IsCreateFile { get; set; } = false;

        public void Initialize(string logPath, DateTime? timestamp = null)
        {
            // 创建文件  
            using (var file = File.Create(logPath))
            {
                file.Close();
            }

            HandleFile = CreateBlfFile(logPath);

            AppEnv.Logger.Info($"Open data log file [{HandleFile.ToString()}]: {logPath}");

            IsCreateFile = true;
            LogPath = logPath;
        }

        public static void WriteLog(CanFrame frame)
        {
            if (frame.IsCanFd)
            {
                if (frame.IsErrorFrame)
                {
                    var canfdErrorFrame = new VBLCANFDErrorFrame64();
                    canfdErrorFrame.mChannel = frame.Channel;
                    canfdErrorFrame.mDLC = frame.Dlc;
                    if (frame.IsBrs && frame.IsEsi)
                    {
                        canfdErrorFrame.mFlags = 0x7000;
                    }
                    else
                    {
                        if (frame.IsBrs && !frame.IsEsi)
                        {
                            canfdErrorFrame.mFlags = 0x3000;
                        }
                        else if (!frame.IsBrs && frame.IsEsi)
                        {
                            canfdErrorFrame.mFlags = 0x5000;
                        }
                        else
                        {
                            canfdErrorFrame.mFlags = 0x1000;
                        }
                    }
                    canfdErrorFrame.mID = (uint)frame.Id;
                    canfdErrorFrame.mHeader.mObjectTimeStamp = frame.TimeUS * 1000;
                    WriteBlfCanfdErrorFrameData(HandleFile, canfdErrorFrame);
                }
                else
                {
                    var canfdBlfFrame = new VBLCANFDMessage64();
                    canfdBlfFrame.mHeader.mObjectTimeStamp = frame.TimeUS * 1000;
                    var id = (uint)frame.Id;
                    if (frame.IsExt)
                    {
                        id |= 0x8000_0000;
                    }
                    canfdBlfFrame.mID = id;
                    canfdBlfFrame.mChannel = frame.Channel;
                    canfdBlfFrame.mDLC = frame.Dlc;
                    canfdBlfFrame.mDir = frame.IsTx ? (byte)1 : (byte)0; // 0 is Rx , 1 is Tx
                    if (frame.IsBrs && frame.IsEsi)
                    {
                        canfdBlfFrame.mFlags = 0x7000;
                    }
                    else
                    {
                        if (frame.IsBrs && !frame.IsEsi)
                        {
                            canfdBlfFrame.mFlags = 0x3000;
                        }
                        else if (!frame.IsBrs && frame.IsEsi)
                        {
                            canfdBlfFrame.mFlags = 0x5000;
                        }
                        else
                        {
                            canfdBlfFrame.mFlags = 0x1000;
                        }
                    }
                    if (frame.Data.Length < 64)
                    {
                        var newData = new byte[64];
                        frame.Data.CopyTo(newData, 0);
                        canfdBlfFrame.mData = newData;
                    }
                    else
                    {
                        canfdBlfFrame.mData = frame.Data;
                    }
                    canfdBlfFrame.mFrameLength = (uint)frame.Data.Length;

                    canfdBlfFrame.mCRC = 0;
                    canfdBlfFrame.mBtrCfgArb = 0;
                    canfdBlfFrame.mBtrCfgData = 0;
                    canfdBlfFrame.mBitCount = 0;
                    canfdBlfFrame.mExtDataOffset = 0;
                    canfdBlfFrame.mTimeOffsetBrsNs = 0;
                    canfdBlfFrame.mTimeOffsetCrcDelNs = 0;
                    canfdBlfFrame.mTxCount = 0;
                    canfdBlfFrame.mValidDataBytes = 0;
                    WriteBlfCanfdData(HandleFile, canfdBlfFrame);
                }
            }
            else
            {
                if (frame.IsErrorFrame) //ErrorFrame
                {
                    var canErrorFrame = new VBLCANErrorFrame();
                    canErrorFrame.mChannel = frame.Channel;
                    canErrorFrame.mHeader.mObjectTimeStamp = frame.TimeUS * 1000;
                    canErrorFrame.mLength = (ushort)frame.Data.Length;
                    WriteBlfCanErrorFrameData(HandleFile, canErrorFrame);
                }
                else
                {
                    var canBlfFrame = new VBLCANMessage();
                    var id = (uint)frame.Id;
                    if (frame.IsExt)
                    {
                        id |= 0x8000_0000;
                    }
                    canBlfFrame.mID = id;

                    canBlfFrame.mHeader.mObjectTimeStamp = frame.TimeUS * 1000;
                    canBlfFrame.mChannel = frame.Channel;
                    canBlfFrame.mDLC = frame.Dlc;
                    canBlfFrame.mFlags = frame.IsTx ? (byte)1 : (byte)0; // 0 is Rx , 1 is Tx
                    if (frame.Data.Length < 8)
                    {
                        var newData = new byte[8];
                        frame.Data.CopyTo(newData, 0);
                        canBlfFrame.mData = newData;
                    }
                    else
                    {
                        canBlfFrame.mData = frame.Data;
                    }
                    WriteBlfCanData(HandleFile, canBlfFrame);
                }
            }
        }

        public void Log(CanFrame[] frames)
        {
            foreach (var frame in frames)
            {
                WriteLog(frame);
            }
        }

        public void Release()
        {
            AppEnv.Logger.Info($"Close data log file {HandleFile.ToString()}: {LogPath}");

            CloseBlfFile(HandleFile);
            IsCreateFile = false;
        }
    }
}
