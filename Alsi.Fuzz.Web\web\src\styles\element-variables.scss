:root {
  /* ===== 主颜色 - 皇家蓝 #27408B ===== */
  --el-color-primary: #27408B !important;
  --el-color-primary-light-1: #3e55a1 !important;
  /* 轻微变亮 */
  --el-color-primary-light-2: #556bb7 !important;
  --el-color-primary-light-3: #6c80cd !important;
  --el-color-primary-light-4: #8394d8 !important;
  --el-color-primary-light-5: #99a7e3 !important;
  --el-color-primary-light-6: #b0bbee !important;
  --el-color-primary-light-7: #c6cef8 !important;
  --el-color-primary-light-8: #dde2ff !important;
  --el-color-primary-light-9: #eef0ff !important;
  /* 最浅 */
  --el-color-primary-dark-1: #213678 !important;
  /* 轻微变深 */
  --el-color-primary-dark-2: #1b2c65 !important;
  /* 更深 */

  /* ===== 成功色 - 翠绿 ===== */
  /* 选择一个偏青的翠绿色，与蓝色主题形成补充 */
  --el-color-success: #10864D !important;
  --el-color-success-light-1: #269661 !important;
  --el-color-success-light-2: #3ca775 !important;
  --el-color-success-light-3: #52b789 !important;
  --el-color-success-light-4: #68c89d !important;
  --el-color-success-light-5: #7ed9b1 !important;
  --el-color-success-light-6: #94eac5 !important;
  --el-color-success-light-7: #aafbd9 !important;
  --el-color-success-light-8: #c7fcea !important;
  --el-color-success-light-9: #e3fef6 !important;
  --el-color-success-dark-1: #0c6b3e !important;
  --el-color-success-dark-2: #09512F !important;

  /* ===== 警告色 - 琥珀金 ===== */
  /* 选择一个温暖的琥珀色，与蓝色形成良好对比 */
  --el-color-warning: #E67E22 !important;
  --el-color-warning-light-1: #eb8d3a !important;
  --el-color-warning-light-2: #ef9c51 !important;
  --el-color-warning-light-3: #f3ab68 !important;
  --el-color-warning-light-4: #f6ba80 !important;
  --el-color-warning-light-5: #f9c997 !important;
  --el-color-warning-light-6: #fcd8af !important;
  --el-color-warning-light-7: #fee7c6 !important;
  --el-color-warning-light-8: #fff2de !important;
  --el-color-warning-light-9: #fffaf5 !important;
  --el-color-warning-dark-1: #cc6d18 !important;
  --el-color-warning-dark-2: #b35C0F !important;

  /* ===== 危险色 - 深樱桃红 ===== */
  /* 偏暗的红色，更专业、不过于明亮刺眼 */
  --el-color-danger: #C0392B !important;
  --el-color-danger-light-1: #cd4d40 !important;
  --el-color-danger-light-2: #da6154 !important;
  --el-color-danger-light-3: #e77568 !important;
  --el-color-danger-light-4: #f0897d !important;
  --el-color-danger-light-5: #f69d91 !important;
  --el-color-danger-light-6: #fbb1a6 !important;
  --el-color-danger-light-7: #ffc6bb !important;
  --el-color-danger-light-8: #ffdad0 !important;
  --el-color-danger-light-9: #ffeee8 !important;
  --el-color-danger-dark-1: #a3301F !important;
  --el-color-danger-dark-2: #862714 !important;

  /* ===== 信息色 - 靛蓝 ===== */
  /* 与主题色相近但更为中性的蓝色，不抢主题色的风头 */
  --el-color-info: #3498DB !important;
  --el-color-info-light-1: #4ba4e1 !important;
  --el-color-info-light-2: #62b1e7 !important;
  --el-color-info-light-3: #79bdec !important;
  --el-color-info-light-4: #90caf2 !important;
  --el-color-info-light-5: #a6d7f7 !important;
  --el-color-info-light-6: #bde3fc !important;
  --el-color-info-light-7: #d3f0ff !important;
  --el-color-info-light-8: #e8f7ff !important;
  --el-color-info-light-9: #f5fcff !important;
  --el-color-info-dark-1: #2980b9 !important;
  --el-color-info-dark-2: #1f6897 !important;

  /* ===== 中性色 - 灰色 ===== */
  --el-text-color-primary: #303133 !important;
  --el-text-color-regular: #606266 !important;
  --el-text-color-secondary: #909399 !important;
  --el-text-color-placeholder: #A8ABB2 !important;
  --el-text-color-disabled: #C0C4CC !important;

  --el-border-color-base: #DCDFE6 !important;
  --el-border-color-light: #E4E7ED !important;
  --el-border-color-lighter: #EBEEF5 !important;
  --el-border-color-extra-light: #F2F6FC !important;

  --el-fill-color-base: #F5F7FA !important;
  --el-fill-color-light: #F5F7FA !important;
  --el-fill-color-lighter: #FAFAFA !important;
  --el-fill-color-extra-light: #FAFCFF !important;
  --el-fill-color-blank: #FFFFFF !important;

  /* ===== 背景色 ===== */
  --el-bg-color: #FFFFFF !important;
  --el-bg-color-overlay: #FFFFFF !important;

  /* ===== 全局字体设置 ===== */
  --app-font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif, "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB";
  --app-font-smoothing-webkit: antialiased;
  --app-font-smoothing-moz: grayscale;
}

/* 全局应用字体样式 */
body {
  font-family: var(--app-font-family);
  -webkit-font-smoothing: var(--app-font-smoothing-webkit);
  -moz-osx-font-smoothing: var(--app-font-smoothing-moz);
}

/* 确保Element Plus组件也使用相同字体 */
.el-menu,
.el-menu-item,
.el-submenu__title,
.el-dropdown-menu,
.el-dropdown-item,
.el-button,
.el-input,
.el-dialog,
.el-card,
.el-message,
.el-notification,
.el-popover,
.el-tooltip,
.el-tabs,
.el-tab-pane,
.el-pagination {
  font-family: var(--app-font-family) !important;
  -webkit-font-smoothing: var(--app-font-smoothing-webkit) !important;
  -moz-osx-font-smoothing: var(--app-font-smoothing-moz) !important;
}

/* 特别处理弹出菜单的字体，解决CefSharp中字体不一致问题 */
.el-menu--horizontal .el-menu.el-menu--popup .el-menu-item {
  font-family: var(--app-font-family) !important;
  font-size: inherit !important;
  -webkit-font-smoothing: var(--app-font-smoothing-webkit) !important;
  -moz-osx-font-smoothing: var(--app-font-smoothing-moz) !important;
}

/* 修复Element Plus卡片组件滚动条的问题 */
.el-card__body {
  overflow: auto;
}

/* 修复Element Plus菜单分割线的问题 */
.el-divider--horizontal {
  margin: 0;
  padding: 0 10;
}

.el-menu--horizontal .el-menu.el-menu--popup .el-menu-item {
  font-size: 14px !important;
}

.el-sub-menu__title {
  font-size: 14px !important;
}

.el-menu-item.is-active {
  background-color: var(--el-border-color-extra-light);
}

.el-menu-item:hover {
  background-color: #ecf5ff;
}

/* 用例列表组件样式 */
.case-list {
  /* 表头样式 */
  .cases-header {
    .header-row {
      display: flex;
      align-items: center;
      width: 100%;
      height: 40px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #dcdfe6;
      font-weight: bold;
      font-size: 12px;
      color: #606266;
      padding: 0;
    }

    .header-cell-id {
      width: 60px;
      flex-shrink: 0;
      padding-left: 12px;
    }

    .header-cell-name {
      width: 180px;
      flex-shrink: 0;
      padding-left: 12px;
    }

    .header-cell-param {
      flex: 1;
      padding: 0 10px;
    }

    .header-cell-detail {
      flex: 1;
      padding: 0 10px;
    }

    .header-cell-status {
      width: 80px;
      flex-shrink: 0;
      text-align: center;
      padding-right: 12px;
    }
  }

  /* 用例行样式 */
  .case-row {
    border: none;
    padding: 0;
    cursor: pointer;
    height: 36px;
    line-height: 36px;
    font-size: 12px;
    transition: all 0.3s;
  }

  .case-row-content {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
  }

  .case-cell-id {
    font-weight: bold;
    color: var(--el-color-primary);
    width: 60px;
    flex-shrink: 0;
    padding-left: 12px;
  }

  .case-cell-name {
    font-weight: 500;
    color: #303133;
    width: 180px;
    flex-shrink: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 0 0 10px;
  }

  .case-cell-param {
    flex: 1;
    color: #606266;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 0 0 10px;
  }

  .case-cell-detail {
    flex: 1;
    padding: 0 0 0 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #909399;
  }

  .case-cell-status {
    width: 80px;
    flex-shrink: 0;
    text-align: center;
    padding: 0 0 0 10px;
  }

  .case-status-tag {
    min-width: 60px;
    font-size: 12px;
    padding: 0 6px;
    height: 22px;
    line-height: 20px;
  }
}

.el-collapse-item__header {
  border-top: 1px solid #8080802b;
  padding: 0 10px;
  width: auto;
}

.el-collapse-item__content {
  margin: 10px;
  padding-bottom: 0;
}

.el-collapse-item__wrap {
  box-shadow: inset 1px 3px 5px rgba(0, 0, 0, .08);
}

.el-timeline-item {
  padding-bottom: 4px;
}

::-webkit-scrollbar {
  width: 10px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

@each $size in 50, 55, 60, 65, 70, 75, 80, 85, 90 {
  $margin: (100 - $size) / 2;
  
  .app-dialog-full-#{$size}.el-dialog {
    height: #{$size}vh !important;
    width: #{$size}vw !important;
    margin-top: #{$margin}vh !important;
    margin-left: #{$margin}vw !important;
    display: flex !important;
    flex-direction: column !important;

    .el-dialog__body {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
}

.app-editor-readonly {
  .cm-editor {
    background-color: #EEE5 !important;
  }
}
