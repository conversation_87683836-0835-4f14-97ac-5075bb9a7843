using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service;
using System.Linq;
using System.Web.Http;

namespace Alsi.Fuzz.Web.Controllers
{
    public class TestSuiteController : WebControllerBase
    {
        private readonly BuiltInTestSuiteService _testSuiteService;

        public TestSuiteController()
        {
            _testSuiteService = new BuiltInTestSuiteService();
        }

        [HttpGet]
        [ActionName("builtin")]
        public IHttpActionResult GetBuiltInTestSuites()
        {
            var testSuites = _testSuiteService.Get();
            return Ok(testSuites);
        }

        public class RequestSequenceContentDto
        {
            public string SuiteName { get; set; } = string.Empty;
            public string PackageName { get; set; } = string.Empty;
            public string CustomName { get; set; } = string.Empty;
        }

        [HttpGet]
        [ActionName("xml")]
        public IHttpActionResult GetSequenceContent([FromUri] RequestSequenceContentDto request)
        {
            var suiteName = request.SuiteName;
            var packageName = request.PackageName;
            var customName = request.CustomName;

            var currentPlan = TestPlanManager.Instance.GetCurrentPlan();
            if (currentPlan == null)
            {
                return GetBuiltInSequenceContent(suiteName, packageName);
            }

            SequenceConfig sequenceConfig = null;

            if (!string.IsNullOrWhiteSpace(customName))
            {
                // 查找自定义包
                sequenceConfig = currentPlan.Config.SequenceConfigList.FirstOrDefault(
                    x => x.TestSuiteName == suiteName
                    && x.CustomName == customName
                    && !x.IsBuiltIn);
            }
            else
            {
                // 查找内建包配置（但内建包始终返回最新的内建XML）
                sequenceConfig = currentPlan.Config.SequenceConfigList.FirstOrDefault(
                    x => x.TestSuiteName == suiteName
                    && x.SequencePackageName == packageName
                    && x.IsBuiltIn);
            }

            // 如果是自定义包且找到了配置，返回用户修改的XML
            if (sequenceConfig != null && !sequenceConfig.IsBuiltIn && !string.IsNullOrWhiteSpace(sequenceConfig.SequencePackageXml))
            {
                return Ok(sequenceConfig.SequencePackageXml);
            }

            // 否则返回内建XML（包括内建包和未找到自定义包的情况）
            return GetBuiltInSequenceContent(suiteName, packageName);
        }

        [HttpGet]
        [ActionName("builtin-xml")]
        public IHttpActionResult GetBuiltInSequenceContent(string suiteName, string packageName)
        {
            var content = _testSuiteService.GetBuiltInXml(suiteName, packageName);
            return Ok(content);
        }
    }
}
