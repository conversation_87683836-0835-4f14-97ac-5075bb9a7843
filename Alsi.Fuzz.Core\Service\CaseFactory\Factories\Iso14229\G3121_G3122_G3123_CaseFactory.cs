using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts;
using Alsi.Fuzz.Core.Service.CaseFactory.Iso14229;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G3121_G3122_G3123_CaseFactory : CaseFactoryBase
    {
        public override void Generate(MutationOptions options, Action<CreateCaseInfo> createCase)
        {
            var xmlServices = options.XmlServices;
            var supportedXmlServicesWithoutSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => !x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历没有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == false)
                .ToArray();
            var groupPath = "";
            var name = "";
            foreach (var xmlService in supportedXmlServicesWithoutSubfunction)
            {
                var sid = xmlService.Id;
                var serviceName = xmlService.IsoUdsServiceDisplayName;
                groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                    .GetService(serviceName)
                    .Valid()
                    .Path;
                name = $"Sid{sid:X2} Correct -{groupPath}";
                var payload = new List<byte> { sid, };
                payload.AddRange(xmlService.Parameter2k);

                var caseMutation = CaseMutation.Create(name, groupPath)
                    .MutatePayload(payload.ToArray())
                    .MutatePayloadLength(payload.Count);
                createCase(new CreateCaseInfo($"G3121-Sid{sid:X2}", caseMutation, sid, xmlService.SubfunctionId));

                groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                    .GetService(serviceName)
                    .Invalid()
                    .Datalength()
                    .Path;
                for (var i = 1; i < payload.Count - 1; i++)
                {
                    name = $"Sid{sid:X2} Less: payloadLength={i} -{groupPath}";
                    caseMutation = CaseMutation.Create(name, groupPath)
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(i);
                    createCase(new CreateCaseInfo($"G3122-Sid{sid:X2}", caseMutation, sid, xmlService.SubfunctionId));
                }

                var randomByte = new byte[] { 0, 0x55, 0xAA, 0xCC, 0xFF };
                {
                    var payloadBytes = payload.ToList();
                    payloadBytes.Insert(0, randomByte[options.Random.Next(0, 5)]);
                    name = $"Sid{sid:X2} Large: head -{groupPath}";
                    caseMutation = CaseMutation.Create(name, groupPath)
                       .MutatePayload(payloadBytes.ToArray())
                        .MutatePayloadLength(payloadBytes.Count + 1);
                    createCase(new CreateCaseInfo($"G3123-Sid{sid:X2}", caseMutation, sid, xmlService.SubfunctionId));
                }

                {
                    var payloadBytes = payload.ToList();
                    payloadBytes.Add(randomByte[options.Random.Next(0, 5)]);
                    name = $"Sid{sid:X2} Large: tail -{groupPath}";
                    caseMutation = CaseMutation.Create(name, groupPath)
                       .MutatePayload(payloadBytes.ToArray())
                        .MutatePayloadLength(payloadBytes.Count + 1);
                    createCase(new CreateCaseInfo($"G3123-Sid{sid:X2}", caseMutation, sid, xmlService.SubfunctionId));
                }
            }
        }
    }
}
