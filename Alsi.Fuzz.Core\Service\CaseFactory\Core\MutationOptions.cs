using Alsi.App.Devices.Core;
using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service.Results;
using System;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Core
{
    public class MutationOptions
    {
        public IsoType IsoType { get; set; }
        public CoverageType Coverage { get; set; }
        public CaseResult[] InteroperationResults { get; set; } = Array.Empty<CaseResult>();
        public string[] SelectedSequenceNames { get; set; } = Array.Empty<string>();

        public WhiteListFrame[] WhiteListFrames { get; set; } = Array.Empty<WhiteListFrame>();
        public string SelectedNodeName { get; set; } = string.Empty;

        public CommunicationType CommunicationType { get; set; }
        public XmlService[] XmlServices { get; set; } = Array.Empty<XmlService>();

        public byte[] TpParameters { get; set; } = Array.Empty<byte>();
        public byte[] TpParametersBigDataPayload { get; set; } = Array.Empty<byte>();
        public int TpParametersBigDataRepeat { get; set; } = 1;

        public bool IsDutMtuLessThan4096 { get; set; }

        public Random Random { get; } = new Random();
    }

    public class XmlService
    {
        public bool IsSupported { get; set; }
        public bool HasMultipleFrameRequest { get; set; }
        public string SequenceName { get; set; }
        public string IsoUdsServiceDisplayName { get; set; }
        public byte Id { get; set; }
        public byte? SubfunctionId { get; set; }
        public string IsoUdsSubfunctionDisplayName { get; set; }
        public byte[] Parameter2k { get; set; } = Array.Empty<byte>();

        public override string ToString()
        {
            return $"Sid{Id:X}-Subfunc{SubfunctionId:X}-Supported-{IsSupported}";
        }
    }

    public enum CoverageType
    {
        Normal = 0,
        High = 1
    }
}
