"use strict";(self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[]).push([[479],{2479:function(e,t,s){s.r(t),s.d(t,{default:function(){return Ce}});s(8111),s(2489);var a=s(6768),l=s(144),n=s(4232),i=s(1219),r=s(292),o=s(1021),c=(s(7588),s(1701),s(1148),s(3579),s(7642),s(8004),s(3853),s(5876),s(2475),s(5024),s(1698),s(5130)),u=s(7477),d=s(9559),h=s(2971),v=s(3321);const g={class:"time-display"};var p=(0,a.pM)({__name:"TimeDisplay",props:{begin:{},end:{},showDuration:{type:<PERSON><PERSON><PERSON>}},setup(e){const t=e,s=e=>{const t=e.getFullYear(),s=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0"),l=String(e.getHours()).padStart(2,"0"),n=String(e.getMinutes()).padStart(2,"0"),i=String(e.getSeconds()).padStart(2,"0"),r=String(e.getMilliseconds()).padStart(3,"0");return`${t}-${s}-${a} ${l}:${n}:${i}.${r}`},i=e=>{const t=String(e.getHours()).padStart(2,"0"),s=String(e.getMinutes()).padStart(2,"0"),a=String(e.getSeconds()).padStart(2,"0");return`${t}:${s}:${a}`},r=(0,a.EW)((()=>{if(!t.begin)return"";const e=new Date(t.begin),a=s(e);if(!t.end)return`Start: ${a}`;const l=new Date(t.end),n=s(l),i=l.getTime()-e.getTime();let r="";if(i<1e3)r=`${i} ms`;else{const e=Math.floor(i/1e3),t=Math.floor(e/60),s=Math.floor(t/60);r=s>0?`${s}h ${t%60}m ${e%60}s`:t>0?`${t}m ${e%60}s`:`${e}s`}return`Duration: ${r}<br>Start: ${a}<br>End: ${n}`})),o=(0,a.EW)((()=>{if(!t.begin)return"-";const e=new Date(t.begin),s=i(e);if(!t.end||!t.showDuration)return s;const a=new Date(t.end),l=a.getTime()-e.getTime();if(l<1e3)return`${l}ms`;{const e=Math.round(l/1e3);return`${e}s`}}));return(e,t)=>e.begin?((0,a.uX)(),(0,a.Wv)((0,l.R1)(v.R7),{key:0,content:r.value,placement:"top",effect:"dark","raw-content":""},{default:(0,a.k6)((()=>[(0,a.Lk)("span",g,(0,n.v_)(o.value),1)])),_:1},8,["content"])):(0,a.Q3)("",!0)}}),m=s(1241);const b=(0,m.A)(p,[["__scopeId","data-v-5494a8fa"]]);var f=b,k=s(4441);const C={class:"result-panel"},y={key:0,class:"duplicate-warning"},S={key:1,class:"loading-indicator"},w={key:2,class:"empty-message"},x={key:3,class:"results-list"},E={class:"select-all-row"},_={key:0,class:"average-time"},L={class:"result-rows"},W=["onClick"],R={class:"item-checkbox"},H={class:"item-name"},F={class:"item-status"},$={class:"item-duration"};var q=(0,a.pM)({__name:"InteroperationResultPanel",props:{results:{},loading:{type:Boolean},selectedSequences:{},averageTime:{}},emits:["update:selectedSequences"],setup(e,{emit:t}){const s=e,i=t,r=(0,l.KR)([]);function v(){s.results.length>0&&(r.value=s.results.map((e=>e.state===o.si.Success)),T())}(0,a.sV)((()=>{v()})),(0,a.wB)((()=>s.results),(()=>{v()}),{immediate:!1}),(0,a.wB)((()=>s.selectedSequences),(e=>{e&&s.results.length>0&&(r.value=s.results.map((t=>e.includes(t.sequenceName))))}),{deep:!0});const g=(0,a.EW)((()=>r.value.length>0&&r.value.every((e=>e)))),p=(0,a.EW)((()=>r.value.some((e=>e))&&!g.value)),m=(0,a.EW)((()=>r.value.filter((e=>e)).length)),b=e=>{r.value=new Array(s.results.length).fill(e),T()},q=e=>{r.value[e]=!r.value[e],T()},N=(e,t,s)=>{r.value[t]=e,T()},T=()=>{const e=s.results.filter(((e,t)=>r.value[t])).map((e=>e.sequenceName)),t=[...new Set(e)];i("update:selectedSequences",t)},I=(0,a.EW)((()=>{if(!s.results||0===s.results.length)return[];const e=new Map,t=new Set;return s.results.forEach((s=>{const a=e.get(s.sequenceName)||0;e.set(s.sequenceName,a+1),a>0&&t.add(s.sequenceName)})),Array.from(t)})),M=e=>I.value.includes(e);return(e,t)=>{const s=(0,a.g2)("el-icon"),i=(0,a.g2)("el-checkbox"),o=(0,a.g2)("el-tag");return(0,a.uX)(),(0,a.CE)("div",C,[t[6]||(t[6]=(0,a.Lk)("div",{class:"panel-header"},[(0,a.Lk)("h3",null,"Interoperation Results")],-1)),I.value.length>0?((0,a.uX)(),(0,a.CE)("div",y,[(0,a.bF)((0,l.R1)(d.KR),{title:"Warning: Duplicate sequence names detected",type:"warning",closable:!1,"show-icon":""},{default:(0,a.k6)((()=>[(0,a.eW)(" Found duplicate names: "+(0,n.v_)(I.value.join(", "))+".",1),t[2]||(t[2]=(0,a.Lk)("br",null,null,-1)),t[3]||(t[3]=(0,a.eW)(" This may cause selection issues. "))])),_:1})])):(0,a.Q3)("",!0),e.loading?((0,a.uX)(),(0,a.CE)("div",S,[(0,a.bF)(s,{class:"is-loading"},{default:(0,a.k6)((()=>[(0,a.bF)((0,l.R1)(u.Loading))])),_:1}),t[4]||(t[4]=(0,a.eW)(" Loading... "))])):0===e.results.length?((0,a.uX)(),(0,a.CE)("div",w," No interoperation results available ")):((0,a.uX)(),(0,a.CE)("div",x,[(0,a.Lk)("div",E,[(0,a.bF)(i,{modelValue:g.value,"onUpdate:modelValue":t[0]||(t[0]=e=>g.value=e),indeterminate:p.value,onChange:b,size:"small"},{default:(0,a.k6)((()=>[(0,a.eW)(" Select All ("+(0,n.v_)(m.value)+"/"+(0,n.v_)(e.results.length)+") ",1)])),_:1},8,["modelValue","indeterminate"]),e.averageTime&&e.averageTime>0?((0,a.uX)(),(0,a.CE)("div",_," Average elapsed: "+(0,n.v_)((0,l.R1)(k.a)(e.averageTime)),1)):(0,a.Q3)("",!0)]),(0,a.Lk)("div",L,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(e.results,((e,s)=>((0,a.uX)(),(0,a.CE)("div",{key:e.id||`item-${s}`,class:(0,n.C4)(["list-item",{striped:s%2===1,duplicate:M(e.sequenceName)}]),onClick:(0,c.D$)((e=>q(s)),["stop"])},[(0,a.Lk)("div",R,[(0,a.bF)(i,{modelValue:r.value[s],"onUpdate:modelValue":e=>r.value[s]=e,size:"small",onClick:t[1]||(t[1]=(0,c.D$)((()=>{}),["stop"])),onChange:t=>N(t,s,e)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),(0,a.Lk)("div",H,[(0,a.eW)((0,n.v_)(e.sequenceName)+" ",1),M(e.sequenceName)?((0,a.uX)(),(0,a.Wv)(o,{key:0,size:"small",type:"warning",effect:"dark",class:"duplicate-tag"},{default:(0,a.k6)((()=>t[5]||(t[5]=[(0,a.eW)(" Duplicate ")]))),_:1})):(0,a.Q3)("",!0)]),(0,a.Lk)("div",F,[(0,a.bF)(h.A,{state:e.state},null,8,["state"])]),(0,a.Lk)("div",$,[(0,a.bF)(f,{begin:e.begin,end:e.end,showDuration:!0},null,8,["begin","end"])])],10,W)))),128))])]))])}}});const N=(0,m.A)(q,[["__scopeId","data-v-97d37b30"]]);var T=N;const I=["element-loading-text"],M={class:"option-row"},K={class:"interoperation-results"},z={class:"dialog-footer"},X={class:"button-group"};var D=(0,a.pM)({__name:"GenerateCasesDialog",props:{visible:{type:Boolean},coverage:{},selectedSequences:{},baselineAverageTime:{}},emits:["update:visible","generated"],setup(e,{emit:t}){const s=e,n=t,r=(0,l.KR)(!1),c=(0,l.KR)([]),u=(0,l.KR)(!1),d=(0,l.KR)("");let h=null;const v=(0,l.KR)({coverage:o.Fp.Normal,selectedSequences:[]}),g=(0,a.EW)({get:()=>s.visible,set:e=>n("update:visible",e)}),p=(0,a.EW)((()=>d.value?d.value:"Generating Test Cases...")),m=(0,a.EW)((()=>{if(0===v.value.selectedSequences.length||0===c.value.length)return 0;let e=0,t=0;return c.value.forEach((s=>{if(s.begin&&s.end&&v.value.selectedSequences.includes(s.sequenceName)){const a=new Date(s.end).getTime()-new Date(s.begin).getTime();e+=a,t+=1}})),t>0?e/t:0})),b=async()=>{u.value=!0;try{const e=await o.GQ.getLatestInteroperationCaseResults();c.value=e.data;const t=e.data.filter((e=>e.state===o.si.Success)).map((e=>e.sequenceName));v.value.selectedSequences=t}catch(e){console.error("获取测试结果失败:",e),i.nk.error("Failed to fetch interoperation results")}finally{u.value=!1}},f=async()=>{if(0!==v.value.selectedSequences.length){r.value=!0,d.value="",k();try{const e=await o.GQ.generateCasesWithGroup(v.value.coverage,v.value.selectedSequences);n("generated",e.data.cases,e.data.groupTree),g.value=!1,i.nk.success(`Successfully generated ${e.data.cases.length} test cases`)}catch(e){console.error("生成测试用例失败:",e),i.nk.error("Failed to generate test cases")}finally{r.value=!1,C()}}else i.nk.warning("Please select at least one sequence")},k=()=>{const e=async()=>{const t=await o.GQ.getGeneratingProgress();d.value=t.data,r.value&&(h=window.setTimeout(e,100))};h=window.setTimeout(e,200)},C=()=>{h&&(window.clearTimeout(h),h=null)},y=()=>{g.value=!1},S=()=>{C(),v.value.coverage=s.coverage,v.value.selectedSequences=[...s.selectedSequences]};return(0,a.wB)((()=>s.visible),(e=>{e&&(v.value.coverage=s.coverage,v.value.selectedSequences=[...s.selectedSequences],b())})),(0,a.sV)((()=>{s.visible&&b()})),(e,t)=>{const s=(0,a.g2)("el-radio"),n=(0,a.g2)("el-radio-group"),i=(0,a.g2)("el-button"),d=(0,a.g2)("el-dialog"),h=(0,a.gN)("loading");return(0,a.uX)(),(0,a.Wv)(d,{modelValue:g.value,"onUpdate:modelValue":t[2]||(t[2]=e=>g.value=e),title:"Generate Test Cases",class:"app-dialog-full-70","close-on-click-modal":!1,"close-on-press-escape":!1,onClose:S},{footer:(0,a.k6)((()=>[(0,a.Lk)("div",z,[(0,a.Lk)("div",X,[(0,a.bF)(i,{onClick:y,disabled:r.value},{default:(0,a.k6)((()=>t[6]||(t[6]=[(0,a.eW)("Cancel")]))),_:1},8,["disabled"]),(0,a.bF)(i,{type:"primary",onClick:f,loading:r.value,disabled:0===v.value.selectedSequences.length},{default:(0,a.k6)((()=>t[7]||(t[7]=[(0,a.eW)(" Generate ")]))),_:1},8,["loading","disabled"])])])])),default:(0,a.k6)((()=>[(0,a.bo)(((0,a.uX)(),(0,a.CE)("div",{class:"dialog-content","element-loading-text":p.value},[(0,a.Lk)("div",M,[t[5]||(t[5]=(0,a.Lk)("div",null,"Coverage:",-1)),(0,a.bF)(n,{modelValue:v.value.coverage,"onUpdate:modelValue":t[0]||(t[0]=e=>v.value.coverage=e),size:"small"},{default:(0,a.k6)((()=>[(0,a.bF)(s,{label:(0,l.R1)(o.Fp).Normal},{default:(0,a.k6)((()=>t[3]||(t[3]=[(0,a.eW)("Normal")]))),_:1},8,["label"]),(0,a.bF)(s,{label:(0,l.R1)(o.Fp).High},{default:(0,a.k6)((()=>t[4]||(t[4]=[(0,a.eW)("High")]))),_:1},8,["label"])])),_:1},8,["modelValue"])]),(0,a.Lk)("div",K,[(0,a.bF)(T,{results:c.value,loading:u.value,"average-time":m.value,selectedSequences:v.value.selectedSequences,"onUpdate:selectedSequences":t[1]||(t[1]=e=>v.value.selectedSequences=e)},null,8,["results","loading","average-time","selectedSequences"])])],8,I)),[[h,r.value]])])),_:1},8,["modelValue"])}}});const V=(0,m.A)(D,[["__scopeId","data-v-badc4246"]]);var G=V,P=s(7489);const A={style:{display:"flex",flex:"1","flex-direction":"column"}},B={class:"case-info"},Q={class:"case-panel"},U={key:0,class:"save-progress"},j={class:"progress-message"},O={key:1,class:"loading-indicator"},Y={key:2,class:"empty-message"},J={key:3,class:"results-list case-list"},Z=36,ee=20,te=40;var se=(0,a.pM)({__name:"GeneratedCasesPanel",props:{cases:{},generating:{type:Boolean},saving:{type:Boolean},saveProgress:{},showEmptyMessage:{type:Boolean}},setup(e){const t=e,s=(0,l.KR)(null),i=(0,l.KR)(null);let r=null;const o=()=>{if(!i.value)return;i.value.innerHTML="";const e=document.createElement("div");e.className="header-row",e.style.height=`${te}px`;const t=document.createElement("div");t.textContent="ID",t.className="header-cell-id",e.appendChild(t);const s=document.createElement("div");s.textContent="Name",s.className="header-cell-name",e.appendChild(s);const a=document.createElement("div");a.textContent="Parameter",a.className="header-cell-param",e.appendChild(a),i.value.appendChild(e)},c=()=>{s.value&&t.cases.length&&(o(),r&&r.destroy(),r=new P.$({container:s.value,items:t.cases,itemHeight:Z,bufferSize:ee,renderItem:(e,s)=>{const a=document.createElement("div");a.className="case-row",a.style.height=`${Z}px`,a.style.lineHeight=`${Z}px`,a.style.borderBottom=s===t.cases.length-1?"none":"1px solid #ebeef5",a.style.backgroundColor=s%2===0?"#fff":"#fafafa",a.onmouseover=()=>{a.style.backgroundColor="#f5f7fa"},a.onmouseout=()=>{a.style.backgroundColor=s%2===0?"#fff":"#fafafa",a.style.borderBottom=s===t.cases.length-1?"none":"1px solid #ebeef5"};const l=document.createElement("div");l.className="case-row-content";const n=document.createElement("div");n.textContent=`${e.id||s+1}`,n.className="case-cell-id",l.appendChild(n);const i=document.createElement("div");i.textContent=e.name||"-",i.title=e.name||"",i.className="case-cell-name",l.appendChild(i);const r=document.createElement("div");return r.textContent=e.parameter||"-",r.title=e.parameter||"",r.className="case-cell-param",l.appendChild(r),a.appendChild(l),a}}))},d=()=>{(0,a.dY)((()=>{c()}))};return(0,a.wB)((()=>t.cases),(()=>{(0,a.dY)((()=>{d()}))}),{deep:!0}),(0,a.sV)((()=>{(0,a.dY)((()=>{c()}))})),(0,a.hi)((()=>{r&&(r.destroy(),r=null)})),(e,t)=>{const r=(0,a.g2)("el-progress"),o=(0,a.g2)("el-icon");return(0,a.uX)(),(0,a.CE)("div",A,[(0,a.Lk)("div",B,[(0,a.Lk)("span",null," Showing "+(0,n.v_)(e.cases.length)+" cases ",1)]),(0,a.Lk)("div",Q,[e.saving&&e.saveProgress?((0,a.uX)(),(0,a.CE)("div",U,[t[0]||(t[0]=(0,a.Lk)("div",{class:"progress-header"},[(0,a.Lk)("span",null,"Saving test cases...")],-1)),(0,a.bF)(r,{percentage:e.saveProgress.progress,status:e.saveProgress.error?"exception":void 0,"stroke-width":6},null,8,["percentage","status"]),(0,a.Lk)("div",j,(0,n.v_)(e.saveProgress.message),1)])):(0,a.Q3)("",!0),e.generating?((0,a.uX)(),(0,a.CE)("div",O,[(0,a.bF)(o,{class:"is-loading"},{default:(0,a.k6)((()=>[(0,a.bF)((0,l.R1)(u.Loading))])),_:1}),t[1]||(t[1]=(0,a.eW)(" Generating... "))])):0===e.cases.length&&e.showEmptyMessage?((0,a.uX)(),(0,a.CE)("div",Y," Click Generate to create test cases ")):e.cases.length>0?((0,a.uX)(),(0,a.CE)("div",J,[(0,a.Lk)("div",{class:"cases-header",ref_key:"headerContainer",ref:i},null,512),(0,a.Lk)("div",{ref_key:"casesContainer",ref:s,class:"cases-content"},null,512)])):(0,a.Q3)("",!0)])])}}});const ae=(0,m.A)(se,[["__scopeId","data-v-b7cb50e4"]]);var le=ae;const ne=["element-loading-text"],ie={class:"toolbar"},re={class:"toolbar-left"},oe={class:"action-buttons"},ce={class:"content-area"},ue={key:0,class:"dialog-left"},de={class:"group-info"},he={class:"group-border"},ve={class:"tree-node"},ge={class:"case-name"},pe={class:"case-count"},me={key:1,class:"dialog-right"},be={class:"toolbar bottom-toolbar"};var fe=(0,a.pM)({__name:"TestCases",setup(e){const t=(0,l.KR)(!0),s=(0,l.KR)(""),c=(0,l.KR)(o.Fp.Normal),u=(0,l.KR)([]),d=(0,l.KR)(0),h=(0,l.KR)(!1),v=(0,l.KR)([]),g=(0,l.KR)([]),p=(0,l.KR)(null),m=(0,l.KR)(),b=(0,l.KR)(!0),f=(0,l.KR)([]),k=(0,l.KR)(new Date),C={children:"children",label:"name"},y=(0,a.EW)((()=>p.value?[p.value]:[])),S=(0,a.EW)((()=>v.value.length)),w=(0,a.EW)((()=>0===v.value.length)),x=(0,a.EW)((()=>v.value.length>0)),E=(0,a.EW)((()=>f.value.length>0&&!t.value)),_=async()=>{s.value="Loading test cases...",t.value=!0;try{const e=await o.GQ.getCasesWithGroup();v.value=e.data.cases,p.value=e.data.groupTree,f.value=y.value,p.value&&R(p.value)}catch(e){console.error("获取测试用例失败:",e),i.nk.error("Failed to fetch test cases")}finally{t.value=!1}},L=()=>{b.value=!0,k.value=new Date},W=()=>{b.value=!1,k.value=new Date},R=e=>{const t=e.id;console.log("getNodePath node",e,"path",t);const s=!t.includes("/");g.value=v.value.filter((e=>s||e.groupPath&&e.groupPath.includes(t))),console.log("allCases.value",v.value)},H=()=>{h.value=!0},F=(e,t)=>{v.value=e,p.value=t,f.value=y.value,p.value&&R(p.value)},$=async()=>{try{s.value="Saving test cases...",t.value=!0,await o.GQ.saveCases(v.value),i.nk.success(`${v.value.length} test cases saved successfully`)}catch(e){console.error("保存测试用例失败:",e),i.nk.error("Failed to save test cases")}finally{t.value=!1}};return(0,a.sV)((()=>{_()})),(e,i)=>{const o=(0,a.g2)("el-button"),v=(0,a.g2)("el-tree"),p=(0,a.gN)("loading");return(0,a.bo)(((0,a.uX)(),(0,a.CE)("div",{class:"test-cases-container","element-loading-text":s.value},[(0,a.Lk)("div",ie,[(0,a.Lk)("div",re,[(0,a.bF)(o,{onClick:L,type:"primary",size:"small",class:"expand-button",disabled:!E.value},{default:(0,a.k6)((()=>[(0,a.bF)((0,l.R1)(r.gc),{icon:"up-right-and-down-left-from-center"}),i[1]||(i[1]=(0,a.Lk)("span",{class:"button-text"},"Expand All",-1))])),_:1},8,["disabled"]),(0,a.bF)(o,{onClick:W,type:"primary",size:"small",class:"collapse-button",disabled:!E.value},{default:(0,a.k6)((()=>[(0,a.bF)((0,l.R1)(r.gc),{icon:"down-left-and-up-right-to-center"}),i[2]||(i[2]=(0,a.Lk)("span",{class:"button-text"},"Collapse All",-1))])),_:1},8,["disabled"])]),(0,a.Lk)("div",oe,[(0,a.bF)(o,{type:"success",onClick:H,size:"small"},{default:(0,a.k6)((()=>i[3]||(i[3]=[(0,a.eW)(" Generate Cases ")]))),_:1})])]),(0,a.Lk)("div",ce,[t.value?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.CE)("div",ue,[(0,a.Lk)("div",de,[(0,a.Lk)("span",null,"Total "+(0,n.v_)(S.value)+" cases",1)]),(0,a.Lk)("div",he,[i[4]||(i[4]=(0,a.Lk)("div",{class:"tree-header"},[(0,a.Lk)("h4",null,"Case Groups")],-1)),((0,a.uX)(),(0,a.Wv)(v,{ref_key:"treeRef",ref:m,data:f.value,props:C,onNodeClick:R,key:k.value,class:"group-tree","expand-on-click-node":!1,"highlight-current":"","node-key":"id","default-expand-all":b.value},{default:(0,a.k6)((({data:e})=>[(0,a.Lk)("div",ve,[(0,a.Lk)("div",ge,(0,n.v_)(e.name),1),(0,a.Lk)("div",pe,(0,n.v_)(e.count)+" cases",1)])])),_:1},8,["data","default-expand-all"]))])])),t.value?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.CE)("div",me,[(0,a.bF)(le,{cases:g.value,generating:!1,saving:!1,"save-progress":null,"show-empty-message":w.value},null,8,["cases","show-empty-message"])]))]),(0,a.Lk)("div",be,[(0,a.bF)(o,{type:"primary",onClick:$,disabled:!x.value},{default:(0,a.k6)((()=>i[5]||(i[5]=[(0,a.eW)("Save")]))),_:1},8,["disabled"])]),(0,a.bF)(G,{visible:h.value,"onUpdate:visible":i[0]||(i[0]=e=>h.value=e),coverage:c.value,"selected-sequences":u.value,"baseline-average-time":d.value,onGenerated:F},null,8,["visible","coverage","selected-sequences","baseline-average-time"])],8,ne)),[[p,t.value]])}}});const ke=(0,m.A)(fe,[["__scopeId","data-v-54337949"]]);var Ce=ke},2971:function(e,t,s){s.d(t,{A:function(){return o}});var a=s(6768),l=s(4232),n=s(1021),i=(0,a.pM)({__name:"CaseStateTag",props:{state:{}},setup(e){const t=e,s=(0,a.EW)((()=>{switch(t.state){case n.si.Success:return"success";case n.si.Running:return"warning";case n.si.Failure:return"danger";case n.si.Pending:default:return"info"}})),i=e=>{switch(e){case n.si.Running:return"Running";case n.si.Pending:return"Not Run";case n.si.Success:return"Passed";case n.si.Failure:return"Failed";default:return"Unknown"}},r=(0,a.EW)((()=>i(t.state)));return(e,t)=>{const n=(0,a.g2)("el-tag");return(0,a.uX)(),(0,a.Wv)(n,{type:s.value,size:"small",style:{"min-width":"60px"}},{default:(0,a.k6)((()=>[(0,a.eW)((0,l.v_)(r.value),1)])),_:1},8,["type"])}}});const r=i;var o=r},7489:function(e,t,s){s.d(t,{$:function(){return a}});
/**
 * js-booster - High-performance frontend library
 * VirtualScroll - Virtual scrolling implementation
 * @version 1.1.3
 * <AUTHOR>
 * @license MIT
 */
class a{constructor(e){this.container=e.container,this.items=e.items||[],this.itemHeight=e.itemHeight||20,this.bufferSize=e.bufferSize||10,this.customRenderItem=e.renderItem,this.customRenderHeader=e.renderHeader,this.maxHeight=e.maxHeight||2684e4,this.visibleStartIndex=0,this.visibleEndIndex=0,this.scrollContainer=null,this.contentWrapper=null,this.contentContainer=null,this.totalHeight=this.items.length*this.itemHeight,this.heightScale=1,this.totalHeight>this.maxHeight&&(this.heightScale=this.maxHeight/this.totalHeight),this.initialize()}initialize(){if(this.container.innerHTML="",this.scrollContainer=document.createElement("div"),Object.assign(this.scrollContainer.style,{flex:"1",overflow:"auto",position:"relative",minHeight:"0",height:"100%",boxSizing:"border-box"}),this.customRenderHeader){const e=this.customRenderHeader();e&&this.scrollContainer.appendChild(e)}this.contentWrapper=document.createElement("div"),Object.assign(this.contentWrapper.style,{position:"relative",width:"100%"});const e=this.totalHeight*this.heightScale;this.contentWrapper.style.height=`${e}px`,this.contentContainer=document.createElement("div"),Object.assign(this.contentContainer.style,{position:"absolute",width:"100%",left:"0"}),this.scrollContainer.addEventListener("scroll",this.handleScroll.bind(this)),this.contentWrapper.appendChild(this.contentContainer),this.scrollContainer.appendChild(this.contentWrapper),this.container.appendChild(this.scrollContainer),this.renderVisibleItems(0,Math.min(100,this.items.length))}handleScroll(){const e=this.scrollContainer.scrollTop,t=this.scrollContainer.clientHeight,s=e/this.heightScale,a=Math.max(0,Math.floor(s/this.itemHeight)-this.bufferSize),l=Math.min(this.items.length,Math.ceil((s+t/this.heightScale)/this.itemHeight)+this.bufferSize);a===this.visibleStartIndex&&l===this.visibleEndIndex&&0!==l||(this.renderVisibleItems(a,l),this.visibleStartIndex=a,this.visibleEndIndex=l)}renderVisibleItems(e,t){this.contentContainer.innerHTML="",this.contentContainer.style.transform=`translateY(${e*this.itemHeight*this.heightScale}px)`;for(let s=e;s<t;s++){const e=this.items[s];if(this.customRenderItem){const t=this.customRenderItem(e,s);t&&(t.style.height=this.itemHeight*this.heightScale+"px",t.style.boxSizing="border-box",t.style.width="100%",this.contentContainer.appendChild(t))}else{const t=document.createElement("div");Object.assign(t.style,{height:this.itemHeight*this.heightScale+"px",width:"100%",boxSizing:"border-box",padding:"8px",borderBottom:"1px solid #eee"}),t.textContent=JSON.stringify(e),this.contentContainer.appendChild(t)}}}updateItems(e){this.items=e||[],this.totalHeight=this.items.length*this.itemHeight,this.heightScale=1,this.totalHeight>this.maxHeight&&(this.heightScale=this.maxHeight/this.totalHeight),this.contentWrapper&&(this.contentWrapper.style.height=this.totalHeight*this.heightScale+"px"),this.visibleStartIndex=0,this.visibleEndIndex=0,this.handleScroll()}scrollToIndex(e){e>=0&&e<this.items.length&&(this.scrollContainer.scrollTop=e*this.itemHeight*this.heightScale)}destroy(){this.scrollContainer&&this.scrollContainer.removeEventListener("scroll",this.handleScroll),this.container&&(this.container.innerHTML=""),this.items=null,this.container=null,this.scrollContainer=null,this.contentWrapper=null,this.contentContainer=null}refresh(){this.handleScroll()}getScrollContainer(){return this.scrollContainer}}
/**
 * js-booster - High-performance frontend library
 * @version 1.1.3
 * <AUTHOR>
 * @license MIT
 */"undefined"!==typeof window&&(window.JsBooster={VirtualScroll:a})}}]);
//# sourceMappingURL=479.911af4e1.js.map