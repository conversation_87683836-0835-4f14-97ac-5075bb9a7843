using Alsi.App;
using Alsi.Common.Utils;
using Alsi.Common.Utils.Autosar;
using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Iso11898;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso11898
{
    public class G12_CaseFactory
    {
        public CaseMutation[] Generate(MutationOptions options, WhiteListFrame whiteListFrame)
        {
            var list = new List<CaseMutation>();
            InternalGenerate(list, whiteListFrame, options);
            return list.ToArray();
        }

        public (CaseMutation[], List<string> groupPaths) GenerateWithGroup(MutationOptions options, WhiteListFrame whiteListFrame)
        {
            var list = new List<CaseMutation>();
            var groupPaths = new List<string>();
            InternalGenerateWithGroup(list, whiteListFrame, options, groupPaths);
            return (list.ToArray(), groupPaths);
        }

        private void InternalGenerate(
            List<CaseMutation> list, WhiteListFrame whiteListFrame, MutationOptions options)
        {
            var id = whiteListFrame.Id;
            var dlc = whiteListFrame.Dlc;
            var length = DlcUtils.GetDataLength(dlc);

            var random = options.Random;

            // G121
            foreach (var payloadByte in new byte[] { 0, 0x7F, 0x80, 0xFF })
            {
                var payload = Enumerable.Range(0, length).Select(x => payloadByte).ToArray();
                var mutation = CaseMutation.Create($"G121-ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(dlc)
                    .MutateRtr(0)
                    .MutateExt(whiteListFrame.IsExt)
                    .MutateData(payload);
                list.Add(mutation);
            }

            // G122
            var dataList = new List<byte[]>();
            if (options.Coverage == CoverageType.High)
            {
                dataList = new LhsSampler().GenerateByteBasedSamples(length);
            }
            else if (options.Coverage == CoverageType.Normal)
            {
                dataList = new LhsSampler().GenerateBitBasedSamples(length, dlc * 16);
            }

            dataList = dataList.OrderBy(x => x.ToHex()).ToList();
            foreach (var data in dataList)
            {
                var mutation = CaseMutation.Create($"G122-ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(dlc)
                    .MutateRtr(0)
                    .MutateExt(whiteListFrame.IsExt)
                    .MutateData(data);
                list.Add(mutation);
            }

            // G123 
            var mutationG123 = CaseMutation.Create($"G123-ID_{id:X}")
                .MutateId(id)
                .MutateDlc(0)
                .MutateRtr(0)
                .MutateExt(whiteListFrame.IsExt)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG123);

            // G124
            var mutationG124 = CaseMutation.Create($"G124-ID_{id:X}")
                .MutateId(id)
                .MutateDlc(dlc)
                .MutateRtr(1)
                .MutateExt(whiteListFrame.IsExt)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG124);

            // G125  Q = G123
            var mutationG125 = CaseMutation.Create($"G125-ID_{id:X}")
                .MutateId(id)
                .MutateDlc(0)
                .MutateRtr(0)
                .MutateExt(whiteListFrame.IsExt)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG125);

            // G126
            var mutationG126 = CaseMutation.Create($"G126-ID_{id:X}")
                .MutateId(id)
                .MutateDlc(0)
                .MutateRtr(1)
                .MutateExt(whiteListFrame.IsExt)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG126);

            // G127
            for (var mutateDlc = 0; mutateDlc <= 15; mutateDlc++)
            {
                if (mutateDlc != dlc)
                {
                    var bytes = new byte[DlcUtils.GetDataLength(mutateDlc)];
                    random.NextBytes(bytes);
                    var mutationG127 = CaseMutation.Create($"G127-ID_{id:X}")
                        .MutateId(id)
                        .MutateDlc(mutateDlc)
                        .MutateRtr(0)
                        .MutateExt(whiteListFrame.IsExt)
                        .MutateData(bytes);
                    list.Add(mutationG127);
                }
            }

            // G128
            for (var mutateDlc = 0; mutateDlc <= 15; mutateDlc++)
            {
                if (mutateDlc != dlc)
                {
                    var bytes = new byte[0];
                    var mutationG128 = CaseMutation.Create($"G128-ID_{id:X}")
                        .MutateId(id)
                        .MutateDlc(mutateDlc)
                        .MutateRtr(1)
                        .MutateExt(whiteListFrame.IsExt)
                        .MutateData(bytes);
                    list.Add(mutationG128);
                }
            }


            //var maxBitNumber = 128;
            //int maxLength = maxBitNumber / 8;
            var maxBitNumber = length * 8;
            int maxLength = length;

            // G12A  N=1 C(64,1) = 64 分别0/1遍历  
            for (var i = 0; i < maxBitNumber; i++)
            {
                BigInteger value = BigInteger.One << i;
                var bytes = ConvertToByteArray(value, maxLength);

                var mutationG11A = CaseMutation.Create($"G12A-ID_{i}-1")
                    .MutateId(id)
                    .MutateDlc(maxLength)
                    .MutateRtr(0)
                    .MutateExt(whiteListFrame.IsExt)
                    .MutateData(bytes);
                list.Add(mutationG11A);

                // 追加补码用例（每个bit反转）（reset bit = 1）
                var complement = ComplementByteArray(bytes, maxLength);

                var mutationG11AComplement = CaseMutation.Create($"G12A-ID_{i}-0")
                    .MutateId(id)
                    .MutateDlc(maxLength)
                    .MutateRtr(0)
                    .MutateExt(whiteListFrame.IsExt)
                    .MutateData(complement);
                list.Add(mutationG11AComplement);
            }

            //G12B N = 2 C(64, 2) = 2016 分别0 / 1遍历
            int sampleCount = options.Coverage == CoverageType.High ? 128 : 32;
            var totalCombination = maxBitNumber * (maxBitNumber - 1) / 2;
            if (sampleCount > totalCombination)
            {
                sampleCount = totalCombination;
            }

            var sampled = new HashSet<(int, int)>();
            random = options.Random ?? new Random();

            for (int k = 0; k < sampleCount; k++)
            {
                int i = 0, j = 1; // Initialize to safe defaults
                int idx;
                bool assigned = false;
                // 均匀抽样：等间隔采样
                if (sampleCount == totalCombination)
                {
                    // 全量遍历
                    idx = k;
                    i = 0;
                    while (i < maxBitNumber - 1)
                    {
                        int group = maxBitNumber - i - 1;
                        if (idx < group)
                        {
                            j = i + 1 + idx;
                            assigned = true;
                            break;
                        }
                        idx -= group;
                        i++;
                    }
                }
                else
                {
                    // 均匀采样索引
                    idx = (int)((long)k * totalCombination / sampleCount);
                    i = 0;
                    while (i < maxBitNumber - 1)
                    {
                        int group = maxBitNumber - i - 1;
                        if (idx < group)
                        {
                            j = i + 1 + idx;
                            assigned = true;
                            break;
                        }
                        idx -= group;
                        i++;
                    }
                    // 防止重复
                    while (!sampled.Add((i, j)))
                    {
                        idx = random.Next(totalCombination);
                        AppEnv.Logger.Debug($"sampled duplicate, random as {idx}");
                        i = 0;
                        int tmp = idx;
                        while (i < maxBitNumber - 1)
                        {
                            int group = maxBitNumber - i - 1;
                            if (tmp < group)
                            {
                                j = i + 1 + tmp;
                                assigned = true;
                                break;
                            }
                            tmp -= group;
                            i++;
                        }
                    }
                }
                if (!assigned)
                {
                    // fallback in case assignment did not happen
                    i = 0;
                    j = 1;
                }

                BigInteger value = (BigInteger.One << i) | (BigInteger.One << j);
                var bytes = ConvertToByteArray(value, maxLength);

                var mutationG11B = CaseMutation.Create($"G12B-ID_{k}_{idx}_{i}_{j}-1")
                    .MutateId(id)
                    .MutateDlc(maxLength)
                    .MutateRtr(0)
                    .MutateExt(whiteListFrame.IsExt)
                    .MutateData(bytes);
                list.Add(mutationG11B);

                // 追加补码用例（每个bit反转）
                var complement = ComplementByteArray(bytes, maxLength);

                var mutationG11BComplement = CaseMutation.Create($"G12B-ID_{k}_{idx}_{i}_{j}-0")
                    .MutateId(id)
                    .MutateDlc(maxLength)
                    .MutateRtr(0)
                    .MutateExt(whiteListFrame.IsExt)
                    .MutateData(complement);
                list.Add(mutationG11BComplement);
            }

            //G12C
            //G12C: 8字节数据，最右侧groupsize个1，其余为0，左移stepSize位生成下一个case，避免左移后groupsize的1超出64位
            var stepSize = 8;
            foreach (var groupsize in GenerateGroupSizes(8 * length))
            {
                //计算最大可左移次数，保证groupsize的1不会超出64位
                int maxShift = 8 * length - groupsize;
                for (int shift = 0; shift <= maxShift; shift += stepSize)
                {
                    // 构造payload: groupsize个1，左移shift位
                    BigInteger value = ((BigInteger.One << groupsize) - 1) << shift;
                    var payload = ConvertToByteArray(value, length);
                    var mutation = CaseMutation.Create($"G12C-ID_{id:X}-GSZ_{groupsize}-SHIFT_{shift}")
                        .MutateId(id)
                        .MutateDlc(length)
                        .MutateRtr(0)
                        .MutateExt(whiteListFrame.IsExt)
                        .MutateData(payload);
                    list.Add(mutation);
                }
            }

            //G12D
            if (whiteListFrame != null && whiteListFrame.WhiteListFrameSignals != null)
            {
                int totalBits = length * 8;
                bool[] usedBits = new bool[totalBits];

                // 标记已被 signal 使用的 bit
                foreach (var WhiteListFrameSignal in whiteListFrame.WhiteListFrameSignals)
                {
                    var bitLength = WhiteListFrameSignal.Length;
                    var startBit = WhiteListFrameSignal.StartBit;
                    int startBitFixed = (startBit / 8 * 8) + (7 - startBit % 8);
                    for (int b = 0; b < bitLength; b++)
                    {
                        int bitIdx = startBitFixed + b;
                        if (bitIdx >= 0 && bitIdx < totalBits)
                        {
                            usedBits[bitIdx] = true;
                        }
                    }
                }

                // 检查是否有未被signal 使用的 bit
                bool hasUnused = usedBits.Any(u => !u);
                if (hasUnused)
                {
                    for (var bit = 0; bit < totalBits; bit++)
                    {
                        if (!usedBits[bit])
                        {
                            int byteIdx = bit / 8;
                            int bitInByte = bit % 8;
                            byte[] bytes = new byte[length];

                            //确认过需要测试为0的情况，避免原本signal默认值不为0
                            var mutationUnusedBits = CaseMutation.Create($"G12D-ID_{id}_{bit}-UnusedBitsRandomFill-0")
                                .MutateId(id)
                                .MutateDlc(dlc)
                                .MutateRtr(0)
                                .MutateExt(whiteListFrame.IsExt)
                                .MutateData(bytes);
                            list.Add(mutationUnusedBits);

                            bytes[byteIdx] |= (byte)(1 << bitInByte);

                            var mutationUnusedBits2 = CaseMutation.Create($"G12D-ID_{id}_{bit}-UnusedBitsRandomFill-1")
                                .MutateId(id)
                                .MutateDlc(dlc)
                                .MutateRtr(0)
                                .MutateExt(whiteListFrame.IsExt)
                                .MutateData(bytes);
                            list.Add(mutationUnusedBits2);
                        }
                    }
                }
            }
        }

        private void AddCaseMutationWithGroupPath(MutationOptions options, List<CaseMutation> list, string name, int id, byte dlc, byte rtr, bool isExt, byte[] payload)
        {
            var frame = options.WhiteListFrames.FirstOrDefault(x => x.Id == id);
            var isTx = frame.Transmitter == options.SelectedNodeName;
            var isRx = frame.Receivers.Contains(options.SelectedNodeName);

            var groupPath = $"ISO11898/WhiteListMessages/{(isTx ? "GetTxOrRxMessages(isTx)" : "RxMessages")}/{frame.Name}/RTR={rtr}/{(DlcUtils.GetDlc(payload.Length) == dlc ? "CorrectDLC" : "xx")}/Payload";
            var mutation = CaseMutation.Create(name, string.Empty)
                .MutateId(id)
                .MutateDlc(dlc)
                .MutateRtr(rtr)
                .MutateExt(isExt)
                .MutateData(payload);
            list.Add(mutation);
        }
        //private void AddCaseMutationWithGroupPath(MutationOptions options, List<CaseMutation> list, CaseMutation mutation)
        //{
        //    var id = mutation.MutateId;
        //    var frame = options.WhiteListFrames.FirstOrDefault(x => x.Id == mutation.MutateId);
        //    var isTx = frame.Transmitter == options.SelectedNodeName;
        //    var isRx = frame.Receivers.Contains(options.SelectedNodeName);

        //    var groupPath = $"ISO11898/WhiteListMessages/{(isTx ? "GetTxOrRxMessages(isTx)" : "RxMessages")}/{frame.Name}/RTR={rtr}/{(DlcUtils.GetDlc(payload.Length) == dlc ? "CorrectDLC" : "xx")}/Payload";
        //    var mutationWithGroup = CaseMutation.Create(name, string.Empty)
        //        .MutateId(id)
        //        .MutateDlc(dlc)
        //        .MutateRtr(rtr)
        //        .MutateExt(isExt)
        //        .MutateData(payload);
        //    list.Add(mutation);
        //}

        private void InternalGenerateWithGroup(
            List<CaseMutation> list, WhiteListFrame whiteListFrame, MutationOptions options, List<string> groupPaths)
        {
            var id = whiteListFrame.Id;
            var dlc = whiteListFrame.Dlc;
            var length = DlcUtils.GetDataLength(dlc);
            byte rtr = 0;

            var random = options.Random;
            var groupPath = "";
            var name = "";
            var isTx = whiteListFrame.Transmitter == options.SelectedNodeName;
            var isRx = whiteListFrame.Receivers.Contains(options.SelectedNodeName);

           // var groupPath = $"ISO11898/WhiteListMessages/{(isTx ? "GetTxOrRxMessages(isTx)" : "RxMessages")}/{frame.Name}/RTR={rtr}/{(DlcUtils.GetDlc(payload.Length) == dlc ? "CorrectDLC" : "xx")}/Payload";
            //G121
            // WhiteListMessages - RxMessages / GetTxOrRxMessages(isTx) - whiteListFrame.Id - RTR = 0 / RTR = 1 - CorrectDLC / IncorrectDLC / Signal / -Paload / ReservedBit / SignaName1
            // UnknownMessages - GetTxOrRxMessages(isTx) - 11bitCANIdentifier / 29bitCANIdentifier - RTR = 0 / RTR = 1 - CorrectDLC / IncorrectDLC
            groupPath = Iso11898CaseGroupConsts.WhiteListMessages.GetTxOrRxMessages(isTx).PayloadPath(whiteListFrame.Name, rtr, true);
			groupPaths.Add(groupPath);
		    foreach (var payloadByte in new byte[] { 0, 0x7F, 0x80, 0xFF })
            {
                var payload = Enumerable.Range(0, length).Select(x => payloadByte).ToArray();
                rtr = 0;
                //groupPath = CaseGroupConsts.WhiteListMessages.RxMessage.Payload;
                //CaseGroupConsts.WhiteListMessages.RxMessage.CustomPayload("x");
               
                //groupPath = $"ISO11898/WhiteListMessages/RxMessages/{whiteListFrame.Name}/RTR=0/CorrectDLC/Payload";
                name = $"Data:BoundaryValue:{payloadByte:X}";
                var mutation = CaseMutation.Create(name, groupPath)
                    .MutateId(id)
                    .MutateDlc(dlc)
                    .MutateRtr(rtr)
                    .MutateExt(whiteListFrame.IsExt)
                    .MutateData(payload);
                list.Add(mutation);
                //mutation.GroupPath = groupPath;
            }

            // G122
            // UnknownMessages - GetTxOrRxMessages(isTx) - 11bitCANIdentifier / 29bitCANIdentifier - RTR = 0 / RTR = 1 - CorrectDLC / IncorrectDLC
            var dataList = new List<byte[]>();
            if (options.Coverage == CoverageType.High)
            {
                dataList = new LhsSampler().GenerateByteBasedSamples(length);
            }
            else if (options.Coverage == CoverageType.Normal)
            {
                dataList = new LhsSampler().GenerateBitBasedSamples(length, dlc * 16);
            }

            dataList = dataList.OrderBy(x => x.ToHex()).ToList();
			groupPath = Iso11898CaseGroupConsts.WhiteListMessages.GetTxOrRxMessages(isTx).PayloadPath(whiteListFrame.Name, rtr, true);
			groupPaths.Add(groupPath);
            foreach (var data in dataList)
            {
                rtr = 0;
                //groupPath = $"ISO11898/WhiteListMessages/RxMessages/{whiteListFrame.Name}/RTR=0/CorrectDLC/Payload";
                
                name = $"Data:LHS:{data.ToHex()}";
                var mutation = CaseMutation.Create(name, groupPath)
                    .MutateId(id)
                    .MutateDlc(dlc)
                    .MutateRtr(rtr)
                    .MutateExt(whiteListFrame.IsExt)
                    .MutateData(data);
                list.Add(mutation);
                //AddCaseMutationWithGroupPath( options,  list,  name,  id,  dlc,  rtr, whiteListFrame.IsExt, data);
            }

            // G123  IncorrectDLC
            //groupPath = $"ISO11898/WhiteListMessages/RxMessages/{whiteListFrame.Name}/RTR=0/CorrectDLC/Payload";
            groupPath = Iso11898CaseGroupConsts.WhiteListMessages.GetTxOrRxMessages(isTx).PayloadPath(whiteListFrame.Name, rtr, true);
			groupPaths.Add(groupPath);
            name = $"Data:NULL";
            var mutationG123 = CaseMutation.Create(name, groupPath)
                .MutateId(id)
                .MutateDlc(0)
                .MutateRtr(0)
                .MutateExt(whiteListFrame.IsExt)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG123);

            // G124
            groupPath = $"ISO11898/WhiteListMessages/RxMessages/{whiteListFrame.Name}/RTR=1";
            rtr = 1;
            //groupPath = Iso11898CaseGroupConsts.WhiteListMessages.GetTxOrRxMessages(isTx).RtrPath(whiteListFrame.Name, rtr);
            groupPath = Iso11898CaseGroupConsts.WhiteListMessages.GetTxOrRxMessages(isTx).RtrPath(whiteListFrame.Name, rtr);
            groupPaths.Add(groupPath);
			name = $"{groupPath}";
            var mutationG124 = CaseMutation.Create(name, groupPath)
                .MutateId(id)
                .MutateDlc(dlc)
                .MutateRtr(rtr)
                .MutateExt(whiteListFrame.IsExt)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG124);

            // G125  Q = G123
            //groupPath = $"ISO11898/WhiteListMessages/RxMessages/{whiteListFrame.Name}/RTR=0/IncorrectDLC";
            rtr = 0;
            groupPath = Iso11898CaseGroupConsts.WhiteListMessages.GetTxOrRxMessages(isTx).DlcPath(whiteListFrame.Name, rtr, false);
            groupPaths.Add(groupPath);
			name = $"QA:IncorrectDLC = 0 actually is true";
            var mutationG125 = CaseMutation.Create(name, groupPath)
                .MutateId(id)
                .MutateDlc(0)
                .MutateRtr(rtr)
                .MutateExt(whiteListFrame.IsExt)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG125);

            // G126
            rtr = 1;
            groupPath = Iso11898CaseGroupConsts.WhiteListMessages.GetTxOrRxMessages(isTx).RtrPath(whiteListFrame.Name, rtr);
           	groupPaths.Add(groupPath);
		    name = $"QA:IncorrectDLC = 0 actually is true";
            var mutationG126 = CaseMutation.Create(name, groupPath)
                .MutateId(id)
                .MutateDlc(0)
                .MutateRtr(rtr)
                .MutateExt(whiteListFrame.IsExt)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG126);

            // G127
            rtr = 0;
            groupPath = Iso11898CaseGroupConsts.WhiteListMessages.GetTxOrRxMessages(isTx).DlcPath(whiteListFrame.Name, rtr, false);
            groupPaths.Add(groupPath);
			for (var mutateDlc = 0; mutateDlc <= 15; mutateDlc++)
            {
                if (mutateDlc != dlc)
                {
                    name = $"IncorrectDLC = {mutateDlc}";
                    var bytes = new byte[DlcUtils.GetDataLength(mutateDlc)];
                    random.NextBytes(bytes);
                    var mutationG127 = CaseMutation.Create(name, groupPath)
                        .MutateId(id)
                        .MutateDlc(mutateDlc)
                        .MutateRtr(rtr)
                        .MutateExt(whiteListFrame.IsExt)
                        .MutateData(bytes);
                    list.Add(mutationG127);
                }
            }

            // G128
            rtr = 1;
            groupPath = Iso11898CaseGroupConsts.WhiteListMessages.GetTxOrRxMessages(isTx).RtrPath(whiteListFrame.Name, rtr);
            groupPaths.Add(groupPath);
			
			for (var mutateDlc = 0; mutateDlc <= 15; mutateDlc++)
            {
                if (mutateDlc != dlc)
                {
                    name = $"IncorrectDLC = {mutateDlc}";
                    var bytes = new byte[0];
                    var mutationG128 = CaseMutation.Create(name, groupPath)
                        .MutateId(id)
                        .MutateDlc(mutateDlc)
                        .MutateRtr(rtr)
                        .MutateExt(whiteListFrame.IsExt)
                        .MutateData(bytes);
                    list.Add(mutationG128);
                }
            }

            // G129  N=1 C(64,1) = 64 分别0/1遍历
            //var maxBitNumber = 128;
            //int maxLength = maxBitNumber / 8;
            var maxBitNumber = length * 8;
            int maxLength = length;
            rtr = 0;
            groupPath = Iso11898CaseGroupConsts.WhiteListMessages.GetTxOrRxMessages(isTx).PayloadPath(whiteListFrame.Name, rtr, true);
            groupPaths.Add(groupPath);
			  
            for (var i = 0; i < maxBitNumber; i++)
            {
                name = $"C(64,1):0 = {i}";
                BigInteger value = BigInteger.One << i;
                var bytes = ConvertToByteArray(value, maxLength);

                var mutationG12A = CaseMutation.Create(name, groupPath)
                    .MutateId(id)
                    .MutateDlc(maxLength)
                    .MutateRtr(rtr)
                    .MutateExt(whiteListFrame.IsExt)
                    .MutateData(bytes);
                list.Add(mutationG12A);

                // 追加补码用例（每个bit反转）（reset bit = 1）
                name = $"C(64,1):1 = {i}";
                var complement = ComplementByteArray(bytes, maxLength);
                var mutationG11AComplement = CaseMutation.Create(name, groupPath)
                    .MutateId(id)
                    .MutateDlc(maxLength)
                    .MutateRtr(0)
                    .MutateExt(whiteListFrame.IsExt)
                    .MutateData(complement);
                list.Add(mutationG11AComplement);
            }

            //N = 2 C(64, 2) = 2016 分别0 / 1遍历
            int sampleCount = options.Coverage == CoverageType.High ? 128 : 32;
            var totalCombination = maxBitNumber * (maxBitNumber - 1) / 2;
            if (sampleCount > totalCombination)
            {
                sampleCount = totalCombination;
            }

            var sampled = new HashSet<(int, int)>();
            random = options.Random ?? new Random();

            for (int k = 0; k < sampleCount; k++)
            {
                int i = 0, j = 1; // Initialize to safe defaults
                int idx;
                bool assigned = false;
                // 均匀抽样：等间隔采样
                if (sampleCount == totalCombination)
                {
                    // 全量遍历
                    idx = k;
                    i = 0;
                    while (i < maxBitNumber - 1)
                    {
                        int group = maxBitNumber - i - 1;
                        if (idx < group)
                        {
                            j = i + 1 + idx;
                            assigned = true;
                            break;
                        }
                        idx -= group;
                        i++;
                    }
                }
                else
                {
                    // 均匀采样索引
                    idx = (int)((long)k * totalCombination / sampleCount);
                    i = 0;
                    while (i < maxBitNumber - 1)
                    {
                        int group = maxBitNumber - i - 1;
                        if (idx < group)
                        {
                            j = i + 1 + idx;
                            assigned = true;
                            break;
                        }
                        idx -= group;
                        i++;
                    }
                    // 防止重复
                    while (!sampled.Add((i, j)))
                    {
                        idx = random.Next(totalCombination);
                        AppEnv.Logger.Debug($"sampled duplicate, random as {idx}");
                        i = 0;
                        int tmp = idx;
                        while (i < maxBitNumber - 1)
                        {
                            int group = maxBitNumber - i - 1;
                            if (tmp < group)
                            {
                                j = i + 1 + tmp;
                                assigned = true;
                                break;
                            }
                            tmp -= group;
                            i++;
                        }
                    }
                }
                if (!assigned)
                {
                    // fallback in case assignment did not happen
                    i = 0;
                    j = 1;
                }

                groupPath = Iso11898CaseGroupConsts.WhiteListMessages.GetTxOrRxMessages(isTx).PayloadPath(whiteListFrame.Name, rtr, true);
                groupPaths.Add(groupPath);
				BigInteger value = (BigInteger.One << i) | (BigInteger.One << j);
                var bytes = ConvertToByteArray(value, maxLength);
                name = $"C(64,2):0 = {i}+{j}";
                var mutationG11B = CaseMutation.Create(name, groupPath)
                    .MutateId(id)
                    .MutateDlc(maxLength)
                    .MutateRtr(0)
                    .MutateExt(whiteListFrame.IsExt)
                    .MutateData(bytes);
                list.Add(mutationG11B);

                // 追加补码用例（每个bit反转）
                var complement = ComplementByteArray(bytes, maxLength);
                name = $"C(64,2):1 = {i}+{j}";
                var mutationG11BComplement = CaseMutation.Create(name, groupPath)
                    .MutateId(id)
                    .MutateDlc(maxLength)
                    .MutateRtr(0)
                    .MutateExt(whiteListFrame.IsExt)
                    .MutateData(complement);
                list.Add(mutationG11BComplement);
            }

            //G12A
            //G12A: 8字节数据，最右侧groupsize个1，其余为0，左移stepSize位生成下一个case，避免左移后groupsize的1超出64位
            groupPath = Iso11898CaseGroupConsts.WhiteListMessages.GetTxOrRxMessages(isTx).PayloadPath(whiteListFrame.Name, rtr, true);
            groupPaths.Add(groupPath);
			var stepSize = 8;
            foreach (var groupsize in GenerateGroupSizes(8 * length))
            {
                //计算最大可左移次数，保证groupsize的1不会超出64位
                var maxShift = 8 * length - groupsize;
                for (var shift = 0; shift <= maxShift; shift += stepSize)
                {
                    // 构造payload: groupsize个1，左移shift位
                    BigInteger value = ((BigInteger.One << groupsize) - 1) << shift;
                    var payload = ConvertToByteArray(value, length);
                    name = $"groupsize = {groupsize} shift = {shift}";
                    var mutation = CaseMutation.Create(name, groupPath)
                        .MutateId(id)
                        .MutateDlc(length)
                        .MutateRtr(0)
                        .MutateExt(whiteListFrame.IsExt)
                        .MutateData(payload);
                    list.Add(mutation);
                }
            }

            //G12B
            groupPath = Iso11898CaseGroupConsts.WhiteListMessages.GetTxOrRxMessages(isTx).SignalPath(whiteListFrame.Name, 0, "Reserved Bit");
            groupPaths.Add(groupPath);
			if (whiteListFrame != null && whiteListFrame.WhiteListFrameSignals != null)
            {
                int totalBits = length * 8;
                bool[] usedBits = new bool[totalBits];

                // 标记已被 signal 使用的 bit
                foreach (var WhiteListFrameSignal in whiteListFrame.WhiteListFrameSignals)
                {
                    var bitLength = WhiteListFrameSignal.Length;
                    var startBit = WhiteListFrameSignal.StartBit;
                    int startBitFixed = (startBit / 8 * 8) + (7 - startBit % 8);
                    for (int b = 0; b < bitLength; b++)
                    {
                        int bitIdx = startBitFixed + b;
                        if (bitIdx >= 0 && bitIdx < totalBits)
                        {
                            usedBits[bitIdx] = true;
                        }
                    }
                }

                // 检查是否有未被signal 使用的 bit
                bool hasUnused = usedBits.Any(u => !u);
                if (hasUnused)
                {
                    for (var bit = 0; bit < totalBits; bit++)
                    {
                        if (!usedBits[bit])
                        {
                            int byteIdx = bit / 8;
                            int bitInByte = bit % 8;
                            byte[] bytes = new byte[length];

                            //确认过需要测试为0的情况，避免原本signal默认值不为0
                            name = $"UnusedBitsRandomFill-0 bit = {bit}";
                            var mutationUnusedBits = CaseMutation.Create(name, groupPath)
                                .MutateId(id)
                                .MutateDlc(dlc)
                                .MutateRtr(0)
                                .MutateExt(whiteListFrame.IsExt)
                                .MutateData(bytes);
                            list.Add(mutationUnusedBits);

                            bytes[byteIdx] |= (byte)(1 << bitInByte);

                            name = $"UnusedBitsRandomFill-1 bit = {bit}";
                            var mutationUnusedBits2 = CaseMutation.Create(name, groupPath)
                                .MutateId(id)
                                .MutateDlc(dlc)
                                .MutateRtr(0)
                                .MutateExt(whiteListFrame.IsExt)
                                .MutateData(bytes);
                            list.Add(mutationUnusedBits2);
                        }
                    }
                }
            }
        }

        //生成groupSize的辅助方法2 8N（step = 8）
        private static IEnumerable<int> GenerateGroupSizes(int maxGroupSize)
        {
            for (int n = 1; n <= maxGroupSize / 8; n++)
            {
                var val1 = n * 8 - 1;
                var val2 = n * 8;
                if (val1 > 0 && val1 <= maxGroupSize)
                {
                    yield return val1;
                }
                if (val2 <= maxGroupSize)
                {
                    yield return val2;
                }
            }
        }

        // 将BigInteger转换为固定长度的字节数组（大端格式） 
        private static byte[] ConvertToByteArray(BigInteger value, int length)
        {
            // 获取BigInteger的字节表示（小端格式）
            byte[] bytes = value.ToByteArray();
            byte[] result = new byte[length];

            // 去除可能存在的符号位
            var sourceLength = bytes.Length;
            if (bytes.Length > 0 && bytes[bytes.Length - 1] == 0)
            {
                sourceLength--;
            }

            // 将字节复制到结果数组，同时反转字节顺序以获得大端格式
            var sourceToCopy = Math.Min(sourceLength, length);

            // 先截取需要的字节，逆序后复制到 result 的末尾
            var reversed = bytes.Take(sourceToCopy).Reverse().ToArray();
            Array.Copy(reversed, 0, result, length - sourceToCopy, sourceToCopy);

            return result;
        }

        private static byte[] ComplementByteArray(byte[] bytes, int length = 8)
        {
            var complement = new byte[length];
            for (var i = 0; i < length; i++)
            {
                complement[i] = (byte)~bytes[i];
            }
            return complement;
        }

    }
}
