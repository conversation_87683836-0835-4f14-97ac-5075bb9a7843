using Alsi.App.Devices.Core;
using Alsi.App.Devices.Core.TransportLayer;
using Alsi.App.Devices.Core.TransportLayer.Frames;
using Alsi.Common.Utils;
using Alsi.Common.Utils.Autosar;
using Alsi.Common.Utils.Timers;
using Alsi.Fuzz.Core.Models.Tester;
using Alsi.Fuzz.Core.Models.TestSuites.Steps.Diagnostic;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.Results;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Alsi.Fuzz.Tester.Testers
{
    public class DiagStepTester
    {
        public Response LatestUdsResponse { get; set; }
        private ErrorFrameChecker ErrorFrameChecker { get; set; }

        private bool IsNormalDiag(CaseContext caseContext)
        {
            var testerGroup = caseContext.CaseResult.TesterGroup;
            return caseContext.TestResult.TestType == TestType.Interoperation
                || (caseContext.TestResult.TestType == TestType.Case && testerGroup.StartsWith("G3"));
        }

        private Task SendDiagWithoutMutatationAsync(SendDiagStep sendDiagStep, CaseStep caseStep, CaseContext caseContext)
        {
            var payload = sendDiagStep.HexPayload?.Trim() ?? string.Empty;
            caseContext.EnvVars.Eval(ref payload);

            var payloadBytes = SequenceUtils.ParseBytes(payload);
            var payloadLength = payloadBytes.Length;

            var requestId = caseContext.CaseConfig.RequestId;
            var responseId = caseContext.CaseConfig.ResponseId;
            var requestIsExt = caseContext.CaseConfig.RequestIsExt;
            var diagTimeoutMs = caseContext.CaseConfig.TimeoutMs;
            var isCanFd = caseContext.HardwareConfig.CommunicationType == CommunicationType.CanFd;

            var request = new Request
            {
                IsCanfd = isCanFd,
                Payload = payloadBytes,
                PayloadLength = payloadLength,
                RequestId = requestId,
                RequestIsExt = requestIsExt,
                FlowControlId = requestId,
                ResponseId = responseId
            };

            LatestUdsResponse = null;

            caseStep.Detail = $"No Mutation, Payload={payloadBytes.ToHex()}, PayloadLength={payloadLength}";

            var tpService = new TpService(new DiagParams());
            var response = tpService.Request(request, TimeSpan.FromMilliseconds(diagTimeoutMs), acceptNoResponse: true);
            if (response?.Payload != null)
            {
                LatestUdsResponse = response;
            }

            // 仅在用例执行时，尝试启用 DiagRetryTester
            if (caseContext.TestResult.TestType == TestType.Case)
            {
                if (LatestUdsResponse == null || LatestUdsResponse.Payload.Length == 0)
                {
                    ErrorFrameChecker?.Reset();
                    DiagRetryTester.Run(caseContext);
                }
            }

            return Task.CompletedTask;
        }

        public Task SendDiagAsync(
            SendDiagStep sendDiagStep, CaseStep caseStep, CaseContext caseContext, ErrorFrameChecker errorFrameChecker)
        {
            ErrorFrameChecker = errorFrameChecker;

            if (!sendDiagStep.Mutate)
            {
                return SendDiagWithoutMutatationAsync(sendDiagStep, caseStep, caseContext);
            }

            var testerGroup = caseContext.CaseResult.TesterGroup;

            // 普遍诊断：互操作 + 正式执行用例时 G3 开头的用例
            // 特殊诊断：正式执行用例时非 G3 开头的用例，因为异变规则特殊，需要额外处理
            var isNormalDiag = IsNormalDiag(caseContext);

            var payload = sendDiagStep.HexPayload?.Trim() ?? string.Empty;
            caseContext.EnvVars.Eval(ref payload);

            var payloadBytes = SequenceUtils.ParseBytes(payload);
            var payloadLength = payloadBytes.Length;

            var caseMutation = CaseMutation.Deserialize(caseContext.CaseResult.Parameter);
            caseMutation.ApplyToDiagFrame(ref payloadBytes, ref payloadLength);

            if (isNormalDiag)
            {
                var requestId = caseContext.CaseConfig.RequestId;
                var responseId = caseContext.CaseConfig.ResponseId;
                var requestIsExt = caseContext.CaseConfig.RequestIsExt;
                var diagTimeoutMs = caseContext.CaseConfig.TimeoutMs;
                var isCanFd = caseContext.HardwareConfig.CommunicationType == CommunicationType.CanFd;

                var request = new Request
                {
                    IsCanfd = isCanFd,
                    Payload = payloadBytes,
                    PayloadLength = payloadLength,
                    RequestId = requestId,
                    RequestIsExt = requestIsExt,
                    FlowControlId = requestId,
                    ResponseId = responseId
                };

                LatestUdsResponse = null;

                caseStep.Detail = $"Payload={payloadBytes.ToHex()}, PayloadLength={payloadLength}";

                var tpService = new TpService(new DiagParams());
                var response = tpService.Request(request, TimeSpan.FromMilliseconds(diagTimeoutMs), acceptNoResponse: true);
                if (response?.Payload != null)
                {
                    LatestUdsResponse = response;
                }

                // 检测发送的请求中，是否包含多帧请求；检查多帧时，用 CAN 总线进行检查，以生成更多用例
                if (MultipleFrame.TryBuild(request.Payload, request.PayloadLength, false, out var multipleFrame))
                {
                    var resultProps = new ResultProps();
                    if (!string.IsNullOrWhiteSpace(caseContext.CaseResult.ResultProps))
                    {
                        resultProps = JsonUtils.Deserialize<ResultProps>(caseContext.CaseResult.ResultProps);
                    }
                    resultProps.HasMultipleFrameRequest = true;
                    caseContext.CaseResult.ResultProps = JsonUtils.Serialize(resultProps);
                }

                // 仅在用例执行时，尝试启用 DiagRetryTester
                if (caseContext.TestResult.TestType == TestType.Case)
                {
                    if (LatestUdsResponse == null || LatestUdsResponse.Payload.Length == 0)
                    {
                        ErrorFrameChecker?.Reset();
                        DiagRetryTester.Run(caseContext);
                    }
                }

                return Task.CompletedTask;
            }
            else if (testerGroup.StartsWith("G411")
                || testerGroup.StartsWith("G412")
                || testerGroup.StartsWith("G413")
                || testerGroup.StartsWith("G414")
                || testerGroup.StartsWith("G415"))
            {
                // ID 和 Ext 异变，发送一次或多次
                int? mutateId = null;
                var idField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.Id);
                if (idField != null)
                {
                    mutateId = SequenceUtils.ParseInt(idField.Value);
                }

                bool? mutateIsExt = null;
                var extField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.Ext);
                if (extField != null)
                {
                    mutateIsExt = SequenceUtils.ParseByte(extField.Value) != 0;
                }

                int? mutateRepeatFrame = null;
                var repeatField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.UDS_Repeat_Frame);
                if (repeatField != null)
                {
                    mutateRepeatFrame = SequenceUtils.ParseInt(repeatField.Value);
                }

                Execute(caseContext, payloadBytes, mutateId: mutateId, mutateIsExt: mutateIsExt, mutateRepeatFrame: mutateRepeatFrame);
                return Task.CompletedTask;
            }
            else if (testerGroup.StartsWith("G42"))
            {
                // 重复发送
                int? mutateRepeatFrame = null;
                var repeatField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.UDS_Repeat_Frame);
                if (repeatField != null)
                {
                    mutateRepeatFrame = SequenceUtils.ParseInt(repeatField.Value);
                }

                Execute(caseContext, payloadBytes, mutateRepeatFrame: mutateRepeatFrame);
                return Task.CompletedTask;
            }
            else if (testerGroup.StartsWith("G431"))
            {
                // 单帧按首帧或连续帧发送
                byte? insertByte = null;
                var repeatField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.UDS_Insert_Byte_Before_N_PCI);
                if (repeatField != null)
                {
                    insertByte = SequenceUtils.ParseByte(repeatField.Value);
                }

                Execute(caseContext, payloadBytes, mutateInsertByteBeforeN_PCI: insertByte);
                return Task.CompletedTask;
            }
            else if (testerGroup.StartsWith("G432"))
            {
                // 多帧按单帧发送（Payload 已经是截取后的单帧长度）
                Execute(caseContext, payloadBytes);
                return Task.CompletedTask;
            }
            else if (testerGroup.StartsWith("G433"))
            {
                // 异变 FF 的 N_PCI、HighNibble；异变 CF 的 HighNibble 和 SN

                byte? mutateFfHighNibble = null;
                var mutateFfHighNibbleField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.UDS_FF_HighNibble);
                if (mutateFfHighNibbleField != null)
                {
                    mutateFfHighNibble = SequenceUtils.ParseByte(mutateFfHighNibbleField.Value);
                }

                int? mutateFfDl12b = null;
                var mutateFfDl12bField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.UDS_FF_DL_12b);
                if (mutateFfDl12bField != null)
                {
                    mutateFfDl12b = SequenceUtils.ParseInt(mutateFfDl12bField.Value);
                }

                byte[] mutateCfByte1List = null;
                var mutateCfByte1ListField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.UDS_CF_BYTE1_LIST);
                if (mutateCfByte1ListField != null)
                {
                    mutateCfByte1List = SequenceUtils.ParseBytes(mutateCfByte1ListField.Value);
                }

                Execute(caseContext, payloadBytes,
                    mutateFfHighNibble: mutateFfHighNibble, mutateFfDl12b: mutateFfDl12b, mutateCfByte1List: mutateCfByte1List);
                return Task.CompletedTask;
            }
            else if (testerGroup.StartsWith("G434"))
            {
                // 多帧异变
                int? ParseInt(MutationFieldType mutationFieldType)
                {
                    var field = caseMutation.Fields.FirstOrDefault(x => x.FieldType == mutationFieldType);
                    return field == null ? (int?)null : SequenceUtils.ParseInt(field.Value);
                }

                int? mutateReplaceCfWithFfNum = ParseInt(MutationFieldType.UDS_Replace_CF_With_FF_NUM);
                int? mutateReplaceCfWithFfNpci = ParseInt(MutationFieldType.UDS_Replace_CF_With_FF_N_PCI);
                int? mutateReplaceCfWithFfDl12b = ParseInt(MutationFieldType.UDS_Replace_CF_With_FF_DL_12b);

                int? mutateRepeatIndex = ParseInt(MutationFieldType.UDS_Repeat_Index);
                int? mutateRepeatTimes = ParseInt(MutationFieldType.UDS_Repeat_Times);

                int? mutateSkipIndex = ParseInt(MutationFieldType.UDS_Skip_Index);

                int? mutateSwapA = ParseInt(MutationFieldType.UDS_Swap_A);
                int? mutateSwapB = ParseInt(MutationFieldType.UDS_Swap_B);

                int? mutateReverseFrom = ParseInt(MutationFieldType.UDS_Reverse_From);
                int? mutateReverseTo = ParseInt(MutationFieldType.UDS_Reverse_To);

                Execute(caseContext, payloadBytes,
                    mutateReplaceCfWithFfNum: mutateReplaceCfWithFfNum,
                    mutateReplaceCfWithFfNpci: mutateReplaceCfWithFfNpci,
                    mutateReplaceCfWithFfDl12b: mutateReplaceCfWithFfDl12b,

                    mutateRepeatIndex: mutateRepeatIndex,
                    mutateRepeatTimes: mutateRepeatTimes,

                    mutateSkipIndex: mutateSkipIndex,

                    mutateSwapA: mutateSwapA,
                    mutateSwapB: mutateSwapB,

                    mutateReverseFrom: mutateReverseFrom,
                    mutateReverseTo: mutateReverseTo);
                return Task.CompletedTask;
            }

            throw new Exception($"ISO 14229 execution, unknown tester group: {testerGroup}");
        }

        private DiagRetryTester DiagRetryTester { get; set; } = new DiagRetryTester();

        public Task RecvDiagAsync(RecvDiagStep recvDiagStep, CaseStep caseStep, CaseContext caseContext)
        {
            var message = string.Empty;

            var isNormalDiag = IsNormalDiag(caseContext);
            if (isNormalDiag)
            {
                var actualPayload = LatestUdsResponse?.Payload ?? Array.Empty<byte>();
                var payloadText = string.Empty;
                if (actualPayload.Length > 10)
                {
                    payloadText = actualPayload.ToHex().Substring(0, 10) + "...";
                }
                else
                {
                    payloadText = actualPayload.ToHex();
                }

                var actualPayloadText = $"Actual payload: {payloadText}";

                if (caseContext.TestResult.TestType == TestType.Interoperation)
                {
                    // 未定义任何数据匹配
                    if (!recvDiagStep.MatchDiags.Any())
                    {
                        // TODO: get timestamp
                        //caseStep.FrameTimestamp = rxFrame.TimeUS;
                        caseStep.State = CaseStepState.Success;
                        caseStep.Detail = actualPayloadText;
                        return Task.CompletedTask;
                    }

                    var expectText = string.Empty;
                    // 定义了数据匹配
                    foreach (var matchDiag in recvDiagStep.MatchDiags)
                    {
                        var expectedPayload = matchDiag.Payload;
                        caseContext.EnvVars.Eval(ref expectedPayload);
                        if (matchDiag.IsMatch(expectedPayload, actualPayload))
                        {
                            expectText = $"Expect payload: {expectedPayload}, ";

                            // TODO: get timestamp
                            //caseStep.FrameTimestamp = rxFrame.TimeUS;
                            caseStep.State = CaseStepState.Success;
                            caseStep.Detail = expectText + actualPayloadText;

                            caseContext.EnvVars.SetByCommand(matchDiag.SetVars, actualPayload);

                            return Task.CompletedTask;
                        }
                    }

                    message = $"Expect payload: {recvDiagStep.MatchDiags.First()?.Payload}, Actual payload: {payloadText}";
                }
                else if (caseContext.TestResult.TestType == TestType.Case)
                {
                    if (actualPayload.Length == 0)
                    {
                        // 当诊断没响应时，根据用户配置，发送其它诊断请求，并收到了诊断响应
                        var retryResponse = DiagRetryTester.RetryResponse;
                        if (retryResponse != null)
                        {
                            caseStep.State = CaseStepState.Success;
                            caseStep.Detail = $"No response, use [Retry] request and received: {retryResponse.Payload.ToHex()}";

                            return Task.CompletedTask;
                        }
                    }
                    else
                    {
                        // 收到了诊断响应
                        caseStep.State = CaseStepState.Success;
                        caseStep.Detail = actualPayloadText;

                        // 如果诊断响应和期待匹配，设置全局变量
                        foreach (var matchDiag in recvDiagStep.MatchDiags)
                        {
                            var expectedPayload = matchDiag.Payload;
                            caseContext.EnvVars.Eval(ref expectedPayload);
                            if (matchDiag.IsMatch(expectedPayload, actualPayload))
                            {
                                caseContext.EnvVars.SetByCommand(matchDiag.SetVars, actualPayload);
                            }
                        }

                        return Task.CompletedTask;
                    }

                    message = $"No response";
                }
                else
                {
                    message = $"Unknown test result type: {caseContext.TestResult.TestType}";
                }
            }
            else
            {
                if (lastReceivedFrame == null)
                {
                    // 当诊断没响应时，根据用户配置，发送其它诊断请求，并收到了诊断响应
                    if (retryResponse != null)
                    {
                        caseStep.State = CaseStepState.Success;
                        caseStep.Detail = $"No response, use [Retry] request and received: {retryResponse.Payload.ToHex()}";

                        return Task.CompletedTask;
                    }

                    message = $"No response, expect frame with ID={caseContext.CaseConfig.ResponseId:X}";
                    caseStep.State = CaseStepState.Failure;
                    caseStep.Detail = message;
                    throw new Exception(message);
                }
                else
                {
                    var detail = $"Received frame: Timestamp={TimestampUtils.GetString(lastReceivedFrame.TimeUS)} Id=0x{lastReceivedFrame.Id:X} Data={lastReceivedFrame.Data.ToHex()}";
                    caseStep.State = CaseStepState.Success;
                    caseStep.Detail = detail;
                }
            }

            caseStep.State = CaseStepState.Failure;
            caseStep.Detail = message;
            throw new Exception(message);
        }

        private void Execute(CaseContext caseContext, byte[] payloadBytes,
            int? mutateId = null,
            bool? mutateIsExt = null,
            int? mutateRepeatFrame = null,
            byte? mutateInsertByteBeforeN_PCI = null,

            // G432
            byte? mutateFfHighNibble = null,
            int? mutateFfDl12b = null,
            byte[] mutateCfByte1List = null,

            // G434
            int? mutateReplaceCfWithFfNum = null,
            int? mutateReplaceCfWithFfNpci = null,
            int? mutateReplaceCfWithFfDl12b = null,

            int? mutateRepeatIndex = null,
            int? mutateRepeatTimes = null,

            int? mutateSkipIndex = null,

            int? mutateSwapA = null,
            int? mutateSwapB = null,

            int? mutateReverseFrom = null,
            int? mutateReverseTo = null)
        {
            // 为支持更多用例以测试 TP，默认使用 CAN 总线
            var isCanFd = false;
            CanFrame CreateSendFrame(byte[] data)
            {
                var frame = new CanFrame
                {
                    Id = caseContext.CaseConfig.RequestId,
                    Data = data,
                    IsTx = true,
                    Dlc = (byte)DlcUtils.GetDlc(data.Length),
                    IsCanFd = isCanFd,
                    IsExt = caseContext.CaseConfig.RequestIsExt
                };

                if (mutateId.HasValue)
                {
                    frame.Id = mutateId.Value;
                }

                if (mutateIsExt.HasValue)
                {
                    frame.IsExt = mutateIsExt.Value;
                }
                return frame;
            }

            if (SingleFrame.TryBuild(payloadBytes, payloadBytes.Length, isCanFd, out var singleFrame))
            {
                var bytes = singleFrame.Data;
                if (mutateInsertByteBeforeN_PCI.HasValue)
                {
                    // 将单帧，按首帧发送
                    var list = bytes.ToList();
                    list.Insert(0, mutateInsertByteBeforeN_PCI.Value);
                    if (list.Count > 8)
                    {
                        list.RemoveAt(list.Count - 1);
                    }
                    bytes = list.ToArray();
                }

                var frame = CreateSendFrame(bytes);
                ExecuteSingleFrame(caseContext, frame, mutateRepeatFrame ?? 1);
                return;
            }

            if (MultipleFrame.TryBuild(payloadBytes, payloadBytes.Length, isCanFd, out var multipleFrame))
            {
                var firstFrame = CreateSendFrame(multipleFrame.FirstFrame.Data);
                if (mutateFfHighNibble.HasValue && firstFrame.Data.Length >= 1)
                {
                    firstFrame.Data[0] = (byte)((firstFrame.Data[0] & 0x0F) | ((mutateFfHighNibble.Value << 4) & 0xF0));
                }

                if (mutateFfDl12b.HasValue && firstFrame.Data.Length >= 2)
                {
                    var ffDl12b = mutateFfDl12b.Value & 0x0FFF;

                    // 设置 byte 0 的低半字节（长度的高 4 位）
                    firstFrame.Data[0] = (byte)((firstFrame.Data[0] & 0xF0) | ((ffDl12b >> 8) & 0x0F));

                    // 设置 byte 1（长度的低 8 位）
                    firstFrame.Data[1] = (byte)(ffDl12b & 0xFF);
                }

                var cfs = multipleFrame.ConsecutiveFrames.Select(x => CreateSendFrame(x.Data)).ToArray();
                if (mutateCfByte1List != null)
                {
                    for (var i = 0; i < cfs.Length; i++)
                    {
                        if (cfs[i].Data.Length >= 1 && i < mutateCfByte1List.Length)
                        {
                            cfs[i].Data[0] = mutateCfByte1List[i];
                        }
                    }
                }

                if (mutateReplaceCfWithFfNum.HasValue
                    && mutateReplaceCfWithFfNpci.HasValue
                    && mutateReplaceCfWithFfDl12b.HasValue)
                {
                    var cfIndex = mutateReplaceCfWithFfNum.Value - 1;
                    if (cfIndex < cfs.Length)
                    {
                        var data = firstFrame.Data.ToArray();

                        var ffDl12b = mutateReplaceCfWithFfDl12b.Value & 0x0FFF;
                        data[0] = (byte)((data[0] & 0x0F) | ((mutateReplaceCfWithFfNpci.Value << 4) & 0xF0));

                        data[0] = (byte)((data[0] & 0xF0) | ((ffDl12b >> 8) & 0x0F));
                        data[1] = (byte)(ffDl12b & 0xFF);

                        cfs[cfIndex].Data = data;
                    }
                }

                var allFrames = new List<CanFrame>
                {
                    firstFrame
                };
                allFrames.AddRange(cfs);

                if (mutateRepeatIndex.HasValue && mutateRepeatTimes.HasValue)
                {
                    if (mutateRepeatIndex.Value < allFrames.Count)
                    {
                        for (var i = 0; i < mutateRepeatTimes.Value - 1; i++)
                        {
                            allFrames.Insert(mutateRepeatIndex.Value, allFrames[mutateRepeatIndex.Value]);
                        }
                    }
                }

                if (mutateSkipIndex.HasValue)
                {
                    if (mutateSkipIndex.Value < allFrames.Count)
                    {
                        allFrames.RemoveAt(mutateSkipIndex.Value);
                    }
                }

                if (mutateSwapA.HasValue && mutateSwapB.HasValue)
                {
                    if (mutateSwapA.Value >= 0
                        && mutateSwapA.Value < allFrames.Count
                        && mutateSwapB.Value >= 0
                        && mutateSwapB.Value < allFrames.Count)
                    {
                        var temp = allFrames[mutateSwapA.Value];
                        allFrames[mutateSwapA.Value] = allFrames[mutateSwapB.Value];
                        allFrames[mutateSwapB.Value] = temp;
                    }
                }

                if (mutateReverseFrom.HasValue && mutateReverseTo.HasValue)
                {
                    var fromIndex = mutateReverseFrom.Value;
                    var toIndex = mutateReverseTo.Value;

                    if (fromIndex >= 0
                        && fromIndex < allFrames.Count
                        && toIndex >= 0
                        && toIndex < allFrames.Count)
                    {
                        int reverseCount = (toIndex - fromIndex + 1) / 2;
                        for (int i = 0; i < reverseCount; i++)
                        {
                            var temp = allFrames[fromIndex + i];
                            allFrames[fromIndex + i] = allFrames[toIndex - i];
                            allFrames[toIndex - i] = temp;
                        }
                    }
                }

                firstFrame = allFrames[0];
                cfs = allFrames.Skip(1).ToArray();

                ExecuteMultipleFrame(caseContext, firstFrame, cfs, false, mutateRepeatFrame ?? 1);
                return;
            }

            throw new Exception($"Failed to execute case with data: {payloadBytes.Take(10).ToHex()}...");
        }

        private CanFrame lastReceivedFrame;
        private Response retryResponse;

        private void ExecuteSingleFrame(CaseContext caseContext, CanFrame singleFrame, int repeatSfOrFf)
        {
            var responseId = caseContext.CaseConfig.ResponseId;
            var timeoutMs = caseContext.CaseConfig.TimeoutMs;

            var responseMonitor = FrameMonitor.CreateIdMatcher(responseId, timeoutMs);
            responseMonitor.StartListening();

            for (var i = 0; i < repeatSfOrFf; i++)
            {
                //AppEnv.Logger.Debug($"ExecuteSingleFrame -> Send: {singleFrame}");
                DataBus.Send(singleFrame);
            }

            if (responseMonitor.WaitForMatch() && responseMonitor.LastMatchedFrame != null)
            {
                lastReceivedFrame = responseMonitor.LastMatchedFrame;
                return;
            }

            // 如果异变条件是重复发送，休眠一段时间，以使总线恢复
            if (repeatSfOrFf > 1)
            {
                HighResolutionTimer.Sleep(repeatSfOrFf);
            }

            ErrorFrameChecker?.Reset();

            var diagRetryTester = new DiagRetryTester();
            diagRetryTester.Run(caseContext);
            retryResponse = diagRetryTester.RetryResponse;
        }

        private void ExecuteMultipleFrame(CaseContext caseContext, CanFrame firstFrame, CanFrame[] consecutiveFrames, bool waitFcInSendingCfs, int repeatSfOrFf)
        {
            var list = consecutiveFrames.ToList();
            for (var i = 0; i < repeatSfOrFf - 1; i++)
            {
                // 如果需要异变发送多次，把首帧 FF，放到 CF 里面进行发送
                list.Insert(0, firstFrame);
            }
            consecutiveFrames = list.ToArray();

            var responseId = caseContext.CaseConfig.ResponseId;
            var diagTimeoutMs = caseContext.CaseConfig.TimeoutMs;

            var mockFlowControl = FlowControl.Build(FlowState.ContinueToSend, 0, 10);
            var multipleFrameSender = new MultipleFrameSender(
                firstFrame,
                consecutiveFrames,
                responseId,
                diagTimeoutMs,
                waitFcInSendingCfs,
                () => mockFlowControl);

            var responseFrame = multipleFrameSender.Execute();
            if (responseFrame != null)
            {
                lastReceivedFrame = responseFrame;
                return;
            }

            ErrorFrameChecker?.Reset();

            var diagRetryTester = new DiagRetryTester();
            diagRetryTester.Run(caseContext);
            retryResponse = diagRetryTester.RetryResponse;
        }
    }
}
