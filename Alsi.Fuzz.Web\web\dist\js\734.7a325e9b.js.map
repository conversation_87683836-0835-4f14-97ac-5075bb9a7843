{"version": 3, "file": "js/734.7a325e9b.js", "mappings": "iLAOA,GAA4BA,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,eACRC,MAAO,CACLC,MAAO,CAAC,GAEVC,KAAAA,CAAMC,GCFR,MAAMH,EAAQG,EAIRC,GAAUC,EAAAA,EAAAA,KAAoD,KAClE,OAAQL,EAAMC,OACZ,KAAKK,EAAAA,GAAeC,QAClB,MAAO,UACT,KAAKD,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeG,QAClB,MAAO,SACT,KAAKH,EAAAA,GAAeI,QACpB,QACE,MAAO,O,IAIPC,EAAoBV,IACxB,OAAQA,GACN,KAAKK,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeI,QAClB,MAAO,UACT,KAAKJ,EAAAA,GAAeC,QAClB,MAAO,SACT,KAAKD,EAAAA,GAAeG,QAClB,MAAO,SACT,QACE,MAAO,U,EAIPG,GAAYP,EAAAA,EAAAA,KAAS,IAClBM,EAAiBX,EAAMC,SDKhC,MAAO,CAACY,EAAUC,KAChB,MAAMC,GAAoBC,EAAAA,EAAAA,IAAkB,UAE5C,OAAQC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAaH,EAAmB,CACpDI,KAAMf,EAAQgB,MACdC,KAAM,QACNC,MAAO,CAAC,YAAY,SACnB,CACDC,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBC,EAAAA,EAAAA,KAAiBC,EAAAA,EAAAA,IAAiBd,EAAUQ,OAAQ,MAEtDO,EAAG,GACF,EAAG,CAAC,QAAQ,CAEjB,IE5DA,MAAMC,EAAc,EAEpB,O,4GCEO,MAAMC,EAAuB5B,IAClC,OAAQA,GACN,KAAKK,EAAAA,GAAeC,QAClB,MAAO,UACT,KAAKD,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeG,QAClB,MAAO,SACT,KAAKH,EAAAA,GAAeI,QACpB,QACE,MAAO,O,0BCdb,MAAMoB,EAAa,CACjBC,IAAK,EACLC,MAAO,WAEHC,EAAa,CACjBF,IAAK,EACLC,MAAO,uBAEHE,EAAa,CAAEF,MAAO,cACtBG,EAAa,CAAEH,MAAO,aACtBI,EAAa,CACjBL,IAAK,EACLC,MAAO,aAEHK,EAAa,CAAEL,MAAO,SACtBM,EAAa,CACjBP,IAAK,EACLC,MAAO,aAEHO,EAAa,CAAEP,MAAO,SACtBQ,EAAa,CAAER,MAAO,aACtBS,EAAc,CAAET,MAAO,SACvBU,EAAc,CAAEV,MAAO,aACvBW,EAAc,CAAEX,MAAO,yBACvBY,EAAc,CAClBb,IAAK,EACLC,MAAO,wBAEHa,EAAc,CAAC,SACfC,EAAc,CAClBf,IAAK,EACLC,MAAO,wBAEHe,EAAc,CAAC,SACfC,EAAc,CAAEhB,MAAO,iBACvBiB,EAAc,CAClBlB,IAAK,EACLC,MAAO,YAEHkB,EAAc,CAAElB,MAAO,gBACvBmB,EAAc,CAAEnB,MAAO,YACvBoB,EAAc,CAAEpB,MAAO,aACvBqB,EAAc,CAAErB,MAAO,kBACvBsB,EAAc,CAAC,SACfC,EAAc,CAAC,SACfC,EAAc,CAAExB,MAAO,cACvByB,EAAc,CAAEzB,MAAO,iBAW7B,OAA4BlC,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,mBACRC,MAAO,CACL0D,QAAS,CAAEvC,KAAMwC,SACjBC,aAAc,CAAC,EACfC,aAAc,CAAC,GAEjBC,MAAO,CAAC,iBAAkB,SAC1B5D,KAAAA,CAAMC,GAAgB4D,KAAMC,ICmB9B,MAAMhE,EAAQG,EAMR4D,EAAOC,EAKPC,GAAgBC,EAAAA,EAAAA,IAAIlE,EAAM0D,SAC1BS,GAAWD,EAAAA,EAAAA,IAAuB,MAClCE,GAAQF,EAAAA,EAAAA,IAAgB,IACxBG,GAAUH,EAAAA,EAAAA,KAAI,IAEpBI,EAAAA,EAAAA,KAAM,IAAMtE,EAAM0D,UAAUa,IAC1BN,EAAc7C,MAAQmD,EAClBA,GAAYvE,EAAM4D,cAAgB5D,EAAM6D,cAC1CW,G,KAIJF,EAAAA,EAAAA,KAAM,IAAML,EAAc7C,QAAQmD,IAChCR,EAAK,iBAAkBQ,GAClBA,GAAUR,EAAK,QAAQ,IAG9B,MAAMS,EAAeC,UACnB,GAAKzE,EAAM4D,cAAiB5D,EAAM6D,aAAlC,CAKAQ,EAAQjD,OAAQ,EAChB,IAEE,MAAOsD,EAAcC,SAAuBC,QAAQC,IAAI,CACtDC,EAAAA,GAAOC,cAAc/E,EAAM4D,aAAc5D,EAAM6D,cAC/CiB,EAAAA,GAAOE,aAAahF,EAAM4D,aAAc5D,EAAM6D,gBAGhDM,EAAS/C,MAAQsD,EAAaO,KAC9Bb,EAAMhD,MAAQuD,EAAcM,I,CAC5B,MAAOC,GACPC,QAAQD,MAAM,4BAA6BA,GAC3CE,EAAAA,GAAUF,MAAM,8B,CAChB,QACAb,EAAQjD,OAAQ,C,OAlBhBgE,EAAAA,GAAUC,QAAQ,8B,EAsBhBC,EAAcA,KAClBrB,EAAc7C,OAAQ,EAEtB+C,EAAS/C,MAAQ,KACjBgD,EAAMhD,MAAQ,EAAE,EAIZmE,EAAsBC,IAC1B,IAAKA,GAAiC,IAAjBA,EAAoB,MAAO,MAGhD,MAAMC,EAAUD,EAAe,IAE/B,MAAO,GAAGC,EAAQC,QAAQ,IAAI,EDdhC,OCkBAC,EAAAA,EAAAA,KAAU,KACJ1B,EAAc7C,OAASpB,EAAM4D,cAAgB5D,EAAM6D,cACrDW,G,IDpBG,CAAC3D,EAAUC,KAChB,MAAM8E,GAAyB5E,EAAAA,EAAAA,IAAkB,eAC3C6E,GAAsB7E,EAAAA,EAAAA,IAAkB,YACxC8E,GAA8B9E,EAAAA,EAAAA,IAAkB,oBAChD+E,GAAyB/E,EAAAA,EAAAA,IAAkB,eAC3CgF,GAAuBhF,EAAAA,EAAAA,IAAkB,aACzCiF,GAAuBjF,EAAAA,EAAAA,IAAkB,aAE/C,OAAQC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAa+E,EAAsB,CACvDC,WAAYjC,EAAc7C,MAC1B,sBAAuBN,EAAO,KAAOA,EAAO,GAAMqF,GAAkBlC,EAAe7C,MAAQ+E,GAC3FC,MAAO,GAAGjC,EAAS/C,OAAOiF,MAAQlC,EAAS/C,OAAOkF,cAAgB,KAClEC,MAAO,MACP,mBAAoB,IACnB,CACDC,QAAQhF,EAAAA,EAAAA,KAAS,IAAM,EACrBiF,EAAAA,EAAAA,IAAoB,OAAQhD,EAAa,EACvCiD,EAAAA,EAAAA,IAAaV,EAAsB,CAAEW,QAASrB,GAAe,CAC3D/D,SAASC,EAAAA,EAAAA,KAAS,IAAMV,EAAO,KAAOA,EAAO,GAAK,EAChDW,EAAAA,EAAAA,IAAiB,aAEnBE,EAAG,SAITJ,SAASC,EAAAA,EAAAA,KAAS,IAAM,CACrB6C,EAAQjD,QACJH,EAAAA,EAAAA,OAAc2F,EAAAA,EAAAA,IAAoB,MAAO9E,EAAY,EACpD4E,EAAAA,EAAAA,IAAad,EAAwB,CACnCiB,KAAM,GACNC,SAAU,UAGb7F,EAAAA,EAAAA,OAAc2F,EAAAA,EAAAA,IAAoB,MAAO3E,EAAY,EACpDwE,EAAAA,EAAAA,IAAoB,MAAOvE,EAAY,CACrCpB,EAAO,KAAOA,EAAO,IAAK2F,EAAAA,EAAAA,IAAoB,KAAM,KAAM,eAAgB,KAC1EA,EAAAA,EAAAA,IAAoB,MAAOtE,EAAY,CACpCgC,EAAS/C,OAAOiF,OACZpF,EAAAA,EAAAA,OAAc2F,EAAAA,EAAAA,IAAoB,MAAOxE,EAAY,CACpDtB,EAAO,KAAOA,EAAO,IAAK2F,EAAAA,EAAAA,IAAoB,MAAO,CAAEzE,MAAO,SAAW,cAAe,KACxFyE,EAAAA,EAAAA,IAAoB,MAAOpE,GAAYX,EAAAA,EAAAA,IAAiByC,EAAS/C,OAAOiF,MAAO,OAEjFU,EAAAA,EAAAA,IAAoB,IAAI,GAC3B5C,EAAS/C,OAAOiF,OACZpF,EAAAA,EAAAA,OAAc2F,EAAAA,EAAAA,IAAoB,MAAOtE,EAAY,CACpDxB,EAAO,KAAOA,EAAO,IAAK2F,EAAAA,EAAAA,IAAoB,MAAO,CAAEzE,MAAO,SAAW,kBAAmB,KAC5FyE,EAAAA,EAAAA,IAAoB,MAAOlE,GAAYb,EAAAA,EAAAA,IAAiByC,EAAS/C,OAAOkF,cAAe,OAEzFS,EAAAA,EAAAA,IAAoB,IAAI,IAC5BN,EAAAA,EAAAA,IAAoB,MAAOjE,EAAY,CACrC1B,EAAO,KAAOA,EAAO,IAAK2F,EAAAA,EAAAA,IAAoB,MAAO,CAAEzE,MAAO,SAAW,eAAgB,KACzFyE,EAAAA,EAAAA,IAAoB,MAAOhE,GAAaf,EAAAA,EAAAA,KAAiBsF,EAAAA,EAAAA,IAAOC,EAAAA,EAAPD,CAAuB7C,EAAS/C,OAAO8F,QAAS,MAE3GT,EAAAA,EAAAA,IAAoB,MAAO/D,EAAa,CACtC5B,EAAO,KAAOA,EAAO,IAAK2F,EAAAA,EAAAA,IAAoB,MAAO,CAAEzE,MAAO,SAAW,sBAAuB,KAChGyE,EAAAA,EAAAA,IAAoB,MAAO9D,EAAa,EACtClB,EAAAA,EAAAA,KAAiBC,EAAAA,EAAAA,KAAiBsF,EAAAA,EAAAA,IAAOC,EAAAA,EAAPD,CAAuB7C,EAAS/C,OAAO+F,MAAQ,IAAK,IACtFT,EAAAA,EAAAA,IAAaU,EAAAA,EAAc,CACzBnH,MAAOkE,EAAS/C,OAAOnB,OAAS,GAChC+B,MAAO,cACN,KAAM,EAAG,CAAC,cAGhBmC,EAAS/C,OAAOiG,YACZpG,EAAAA,EAAAA,OAAc2F,EAAAA,EAAAA,IAAoB,MAAOhE,EAAa,CACrD9B,EAAO,KAAOA,EAAO,IAAK2F,EAAAA,EAAAA,IAAoB,MAAO,CAAEzE,MAAO,SAAW,cAAe,KACxFyE,EAAAA,EAAAA,IAAoB,MAAO,CACzBzE,MAAO,QACPoE,MAAOjC,EAAS/C,OAAOiG,YACtB3F,EAAAA,EAAAA,IAAiByC,EAAS/C,OAAOiG,WAAY,EAAGxE,OAErDkE,EAAAA,EAAAA,IAAoB,IAAI,GAC3B5C,EAAS/C,OAAOkG,SACZrG,EAAAA,EAAAA,OAAc2F,EAAAA,EAAAA,IAAoB,MAAO9D,EAAa,CACrDhC,EAAO,KAAOA,EAAO,IAAK2F,EAAAA,EAAAA,IAAoB,MAAO,CAAEzE,MAAO,SAAW,WAAY,KACrFyE,EAAAA,EAAAA,IAAoB,MAAO,CACzBzE,MAAO,QACPoE,MAAOjC,EAAS/C,OAAOkG,SACtB5F,EAAAA,EAAAA,IAAiByC,EAAS/C,MAAMkG,QAAS,EAAGvE,OAEjDgE,EAAAA,EAAAA,IAAoB,IAAI,QAGhCN,EAAAA,EAAAA,IAAoB,MAAOzD,EAAa,CACtClC,EAAO,KAAOA,EAAO,IAAK2F,EAAAA,EAAAA,IAAoB,KAAM,KAAM,SAAU,IAC5C,IAAvBrC,EAAMhD,MAAMmG,SACRtG,EAAAA,EAAAA,OAAc2F,EAAAA,EAAAA,IAAoB,MAAO3D,EAAa,EACrDyD,EAAAA,EAAAA,IAAab,EAAqB,CAAE2B,YAAa,4BAElDvG,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAa6E,EAAwB,CAAEhE,IAAK,GAAK,CAC9DR,SAASC,EAAAA,EAAAA,KAAS,IAAM,GACrBP,EAAAA,EAAAA,KAAW,IAAO2F,EAAAA,EAAAA,IAAoBa,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAYtD,EAAMhD,OAAQuG,KACxE1G,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAa4E,EAA6B,CAC9D/D,IAAK4F,EAAKC,GACVzG,MAAM6F,EAAAA,EAAAA,IAAOnF,EAAPmF,CAA4BW,EAAK1H,OACvC4H,OAAuB,YAAfF,EAAK1H,OACZ,CACDsB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBiF,EAAAA,EAAAA,IAAoB,MAAOvD,EAAa,EACtCuD,EAAAA,EAAAA,IAAoB,MAAOtD,EAAa,EACtCsD,EAAAA,EAAAA,IAAoB,MAAOrD,EAAa,EACtCqD,EAAAA,EAAAA,IAAoB,OAAQpD,GAAa3B,EAAAA,EAAAA,IAAiB6D,EAAmBoC,EAAKG,YAAa,IAC/FrB,EAAAA,EAAAA,IAAoB,OAAQ,CAC1BzE,MAAO,YACPoE,MAAOuB,EAAKtB,OACX3E,EAAAA,EAAAA,IAAiBiG,EAAKtB,MAAO,EAAG/C,GAClCqE,EAAKL,SACDrG,EAAAA,EAAAA,OAAc2F,EAAAA,EAAAA,IAAoB,OAAQ,CACzC7E,IAAK,EACLqE,MAAOuB,EAAKL,OACZtF,MAAO,uBACNN,EAAAA,EAAAA,IAAiBiG,EAAKL,QAAS,EAAG/D,KACrCwD,EAAAA,EAAAA,IAAoB,IAAI,MAE9BN,EAAAA,EAAAA,IAAoB,MAAOjD,EAAa,CACrCmE,EAAK1H,QAAS+G,EAAAA,EAAAA,IAAO1G,EAAAA,IAAgByH,YACjC9G,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAakG,EAAAA,EAAc,CACxCrF,IAAK,EACL9B,MAAO0H,EAAK1H,OACX,KAAM,EAAG,CAAC,YACb8G,EAAAA,EAAAA,IAAoB,IAAI,YAKpCpF,EAAG,GACF,KAAM,CAAC,OAAQ,cAChB,SAENA,EAAG,aAKnBA,EAAG,GACF,EAAG,CAAC,aAAc,SAAS,CAEhC,I,UE9QA,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O;;;;;;;;ACAA,MAAMoG,EAYJC,WAAAA,CAAYC,GACVC,KAAKC,UAAYF,EAAQE,UACzBD,KAAKE,MAAQH,EAAQG,OAAS,GAC9BF,KAAKG,WAAaJ,EAAQI,YAAc,GACxCH,KAAKI,WAAaL,EAAQK,YAAc,GACxCJ,KAAKK,iBAAmBN,EAAQO,WAChCN,KAAKO,mBAAqBR,EAAQS,aAClCR,KAAKS,UAAYV,EAAQU,WAAa,OAEtCT,KAAKU,kBAAoB,EACzBV,KAAKW,gBAAkB,EACvBX,KAAKY,gBAAkB,KACvBZ,KAAKa,eAAiB,KACtBb,KAAKc,iBAAmB,KACxBd,KAAKe,YAAcf,KAAKE,MAAMd,OAASY,KAAKG,WAC5CH,KAAKgB,YAAc,EAGfhB,KAAKe,YAAcf,KAAKS,YAC1BT,KAAKgB,YAAchB,KAAKS,UAAYT,KAAKe,aAG3Cf,KAAKiB,YACT,CAMEA,UAAAA,GAiBE,GAfAjB,KAAKC,UAAUiB,UAAY,GAG3BlB,KAAKY,gBAAkBO,SAASC,cAAc,OAE9CC,OAAOC,OAAOtB,KAAKY,gBAAgBzH,MAAO,CACxCoI,KAAM,IACNC,SAAU,OACVC,SAAU,WACVC,UAAW,IACXC,OAAQ,OACRC,UAAW,eAIT5B,KAAKO,mBAAoB,CAC3B,MAAMsB,EAAS7B,KAAKO,qBAChBsB,GACF7B,KAAKY,gBAAgBkB,YAAYD,EAEzC,CAGI7B,KAAKa,eAAiBM,SAASC,cAAc,OAE7CC,OAAOC,OAAOtB,KAAKa,eAAe1H,MAAO,CACvCsI,SAAU,WACVrD,MAAO,SAIT,MAAM2D,EAAe/B,KAAKe,YAAcf,KAAKgB,YAC7ChB,KAAKa,eAAe1H,MAAMwI,OAAS,GAAGI,MAGtC/B,KAAKc,iBAAmBK,SAASC,cAAc,OAE/CC,OAAOC,OAAOtB,KAAKc,iBAAiB3H,MAAO,CACzCsI,SAAU,WACVrD,MAAO,OACP4D,KAAM,MAIRhC,KAAKY,gBAAgBqB,iBAAiB,SAAUjC,KAAKkC,aAAaC,KAAKnC,OAGvEA,KAAKa,eAAeiB,YAAY9B,KAAKc,kBACrCd,KAAKY,gBAAgBkB,YAAY9B,KAAKa,gBACtCb,KAAKC,UAAU6B,YAAY9B,KAAKY,iBAGhCZ,KAAKoC,mBAAmB,EAAGC,KAAKC,IAAI,IAAKtC,KAAKE,MAAMd,QACxD,CAME8C,YAAAA,GACE,MAAMK,EAAYvC,KAAKY,gBAAgB2B,UACjCC,EAAkBxC,KAAKY,gBAAgB6B,aAGvCC,EAAgBH,EAAYvC,KAAKgB,YAGjC2B,EAAaN,KAAKO,IAAI,EAAGP,KAAKQ,MAAMH,EAAgB1C,KAAKG,YAAcH,KAAKI,YAC5E0C,EAAWT,KAAKC,IACpBtC,KAAKE,MAAMd,OACXiD,KAAKU,MAAML,EAAgBF,EAAkBxC,KAAKgB,aAAehB,KAAKG,YAAcH,KAAKI,YAIvFuC,IAAe3C,KAAKU,mBAClBoC,IAAa9C,KAAKW,iBACL,IAAbmC,IACJ9C,KAAKoC,mBAAmBO,EAAYG,GAEpC9C,KAAKU,kBAAoBiC,EACzB3C,KAAKW,gBAAkBmC,EAE7B,CAQEV,kBAAAA,CAAmBO,EAAYG,GAE7B9C,KAAKc,iBAAiBI,UAAY,GAGlClB,KAAKc,iBAAiB3H,MAAM6J,UAAY,cAAcL,EAAa3C,KAAKG,WAAaH,KAAKgB,iBAG1F,IAAK,IAAIiC,EAAIN,EAAYM,EAAIH,EAAUG,IAAK,CAC1C,MAAMC,EAAOlD,KAAKE,MAAM+C,GAExB,GAAIjD,KAAKK,iBAAkB,CAEzB,MAAM8C,EAAcnD,KAAKK,iBAAiB6C,EAAMD,GAC5CE,IAEFA,EAAYhK,MAAMwI,OAAY3B,KAAKG,WAAaH,KAAKgB,YAA1B,KAC3BmC,EAAYhK,MAAMyI,UAAY,aAC9BuB,EAAYhK,MAAMiF,MAAQ,OAE1B4B,KAAKc,iBAAiBgB,YAAYqB,GAE5C,KAAa,CAEL,MAAMC,EAAMjC,SAASC,cAAc,OACnCC,OAAOC,OAAO8B,EAAIjK,MAAO,CACvBwI,OAAW3B,KAAKG,WAAaH,KAAKgB,YAA1B,KACR5C,MAAO,OACPwD,UAAW,aACXyB,QAAS,MACTC,aAAc,mBAEhBF,EAAIG,YAAcC,KAAKC,UAAUP,GACjClD,KAAKc,iBAAiBgB,YAAYsB,EAC1C,CACA,CACA,CAOEM,WAAAA,CAAYxD,GACVF,KAAKE,MAAQA,GAAS,GACtBF,KAAKe,YAAcf,KAAKE,MAAMd,OAASY,KAAKG,WAG5CH,KAAKgB,YAAc,EACfhB,KAAKe,YAAcf,KAAKS,YAC1BT,KAAKgB,YAAchB,KAAKS,UAAYT,KAAKe,aAIvCf,KAAKa,iBACPb,KAAKa,eAAe1H,MAAMwI,OAAY3B,KAAKe,YAAcf,KAAKgB,YAA3B,MAGrChB,KAAKU,kBAAoB,EACzBV,KAAKW,gBAAkB,EAGvBX,KAAKkC,cACT,CAOEyB,aAAAA,CAAcC,GACRA,GAAS,GAAKA,EAAQ5D,KAAKE,MAAMd,SAEnCY,KAAKY,gBAAgB2B,UAAYqB,EAAQ5D,KAAKG,WAAaH,KAAKgB,YAEtE,CAME6C,OAAAA,GACM7D,KAAKY,iBACPZ,KAAKY,gBAAgBkD,oBAAoB,SAAU9D,KAAKkC,cAEtDlC,KAAKC,YACPD,KAAKC,UAAUiB,UAAY,IAE7BlB,KAAKE,MAAQ,KACbF,KAAKC,UAAY,KACjBD,KAAKY,gBAAkB,KACvBZ,KAAKa,eAAiB,KACtBb,KAAKc,iBAAmB,IAC5B,CAMEiD,OAAAA,GACE/D,KAAKkC,cACT,CAOE8B,kBAAAA,GACE,OAAOhE,KAAKY,eAChB;;;;;;GC9OsB,qBAAXqD,SACTA,OAAOC,UAAY,CACjBrE,iB,8KCZJ,MAAMlG,EAAa,CAAEE,MAAO,kCCwBtBsK,EAAc,GACdC,EAAc,GACdC,EAAgB,GDbtB,OAA4B1M,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,WACRC,MAAO,CACLyM,MAAO,CAAC,GAEV3I,MAAO,CAAC,eACR5D,KAAAA,CAAMC,GAAgB4D,KAAMC,ICP9B,MAAMhE,EAAQG,EAIR4D,EAAOC,EAKP0I,GAAiBxI,EAAAA,EAAAA,IAAwB,MACzCyI,GAAkBzI,EAAAA,EAAAA,IAAwB,MAChD,IAAI0I,EAAqB,KAMzB,MAAMC,EAAoBA,KACxB,IAAKF,EAAgBvL,MAAO,OAG5BuL,EAAgBvL,MAAMiI,UAAY,GAGlC,MAAMW,EAASV,SAASC,cAAc,OACtCS,EAAO8C,UAAY,aACnB9C,EAAO1I,MAAMwI,OAAS,GAAG0C,MAGzB,MAAMO,EAAWzD,SAASC,cAAc,OACxCwD,EAASrB,YAAc,KACvBqB,EAASD,UAAY,iBACrB9C,EAAOC,YAAY8C,GAGnB,MAAMC,EAAa1D,SAASC,cAAc,OAC1CyD,EAAWtB,YAAc,OACzBsB,EAAWF,UAAY,mBACvB9C,EAAOC,YAAY+C,GAGnB,MAAMC,EAAc3D,SAASC,cAAc,OAC3C0D,EAAYvB,YAAc,YAC1BuB,EAAYH,UAAY,oBACxB9C,EAAOC,YAAYgD,GAGnB,MAAMC,EAAe5D,SAASC,cAAc,OAC5C2D,EAAaxB,YAAc,SAC3BwB,EAAaJ,UAAY,qBACzB9C,EAAOC,YAAYiD,GAGnB,MAAMC,EAAe7D,SAASC,cAAc,OAC5C4D,EAAazB,YAAc,SAC3ByB,EAAaL,UAAY,qBACzB9C,EAAOC,YAAYkD,GAGnBR,EAAgBvL,MAAM6I,YAAYD,EAAO,EAIrCoD,EAAoBA,KACnBV,EAAetL,OAAUpB,EAAMyM,MAAMlF,SAG1CsF,IAGID,GACFA,EAAcZ,UAGhBY,EAAgB,IAAI5E,EAAAA,EAAc,CAChCI,UAAWsE,EAAetL,MAC1BiH,MAAOrI,EAAMyM,MACbnE,WAAYgE,EACZ/D,WAAYgE,EACZ9D,WAAYA,CAAC4C,EAAkBU,KAE7B,MAAMsB,EAAM/D,SAASC,cAAc,OACnC8D,EAAIP,UAAY,WAChBO,EAAIC,QAAU,IAAMvJ,EAAK,cAAesH,GAGxCgC,EAAI/L,MAAMwI,OAAS,GAAGwC,MACtBe,EAAI/L,MAAMiM,WAAa,GAAGjB,MAC1Be,EAAI/L,MAAMmK,aAAeM,IAAU/L,EAAMyM,MAAMlF,OAAS,EAAI,OAAS,oBACrE8F,EAAI/L,MAAMkM,gBAAkBnC,EAAKzD,GAAK,IAAM,EAAI,OAAS,UAGzDyF,EAAII,YAAc,KAChBJ,EAAI/L,MAAMkM,gBAAkB,SAAS,EAEvCH,EAAIK,WAAa,KACfL,EAAI/L,MAAMkM,gBAAkBnC,EAAKzD,GAAK,IAAM,EAAI,OAAS,UAEzDyF,EAAI/L,MAAMmK,aAAeM,IAAU/L,EAAMyM,MAAMlF,OAAS,EAAI,OAAS,mBAAmB,EAI1F,MAAMoG,EAASrE,SAASC,cAAc,OACtCoE,EAAOb,UAAY,mBAGnB,MAAMc,EAAQtE,SAASC,cAAc,OACrCqE,EAAMlC,YAAc,IAAIL,EAAKzD,KAC7BgG,EAAMd,UAAY,eAClBa,EAAO1D,YAAY2D,GAGnB,MAAMC,EAAUvE,SAASC,cAAc,OACvCsE,EAAQnC,YAAcL,EAAKhF,KAC3BwH,EAAQf,UAAY,iBACpBa,EAAO1D,YAAY4D,GAGnB,MAAMC,EAAWxE,SAASC,cAAc,OACxCuE,EAASpC,YAAcL,EAAKhE,UAC5ByG,EAAS1H,MAAQiF,EAAKhE,UACtByG,EAAShB,UAAY,kBACrBa,EAAO1D,YAAY6D,GAGnB,MAAMC,EAAYzE,SAASC,cAAc,OACzCwE,EAAUrC,YAAcL,EAAK/D,QAAU,IACvCyG,EAAU3H,MAAQiF,EAAK/D,QAAU,GACjCyG,EAAUjB,UAAY,mBACtBa,EAAO1D,YAAY8D,GAGnB,MAAMC,EAAY1E,SAASC,cAAc,OACzCyE,EAAUlB,UAAY,mBAGtB,MAAM1M,EAAU6N,EAAiB5C,EAAKpL,OAChCiO,EAAUC,EAAc9C,EAAKpL,OAE7BmO,EAAQ9E,SAASC,cAAc,QASrC,OARA6E,EAAMtB,UAAY,kBAAkB1M,kCACpCgO,EAAM1C,YAAcwC,EAEpBF,EAAU/D,YAAYmE,GACtBT,EAAO1D,YAAY+D,GAEnBX,EAAIpD,YAAY0D,GAETN,CAAG,IAEZ,EAIEgB,EAAsBA,KACtBzB,EACFA,EAAcf,YAAY7L,EAAMyM,QAEhC6B,EAAAA,EAAAA,KAAS,KACPlB,GAAmB,G,EAMnBa,EAAoBhO,IACxB,OAAQA,GACN,KAAKK,EAAAA,GAAeC,QAClB,MAAO,UACT,KAAKD,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeG,QAClB,MAAO,SACT,KAAKH,EAAAA,GAAeI,QACpB,QACE,MAAO,O,EAKPyN,EAAiBlO,IACrB,OAAQA,GACN,KAAKK,EAAAA,GAAeC,QAClB,MAAO,SACT,KAAKD,EAAAA,GAAeG,QAClB,MAAO,SACT,KAAKH,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeI,QAClB,MAAO,UACT,QACE,MAAO,U,ED0Bb,OCrBA4D,EAAAA,EAAAA,KAAM,IAAMtE,EAAMyM,QAAO,MACvB6B,EAAAA,EAAAA,KAAS,KACPD,GAAqB,GACrB,GACD,CAAEE,MAAM,KAGX5I,EAAAA,EAAAA,KAAU,MACR2I,EAAAA,EAAAA,KAAS,KACPlB,GAAmB,GACnB,KAIJoB,EAAAA,EAAAA,KAAY,KACN5B,IACFA,EAAcZ,UACdY,EAAgB,K,IDIb,CAAC/L,EAAUC,MACRG,EAAAA,EAAAA,OAAc2F,EAAAA,EAAAA,IAAoB,MAAO9E,EAAY,EAC3D2E,EAAAA,EAAAA,IAAoB,MAAO,CACzBzE,MAAO,eACPyM,QAAS,kBACTvK,IAAKyI,GACJ,KAAM,MACTlG,EAAAA,EAAAA,IAAoB,MAAO,CACzBgI,QAAS,iBACTvK,IAAKwI,EACL1K,MAAO,iBACN,KAAM,OAGb,I,UEhPA,MAAMJ,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,Q,oBCLA,MAAME,EAAa,CAAEE,MAAO,0BACtBC,EAAa,CAAED,MAAO,gBACtBE,EAAa,CACjBH,IAAK,EACLC,MAAO,qBAEHG,EAAa,CAAEH,MAAO,2BACtBI,EAAa,CACjBL,IAAK,EACLC,MAAO,qBAEHK,EAAa,CACjBN,IAAK,EACLC,MAAO,mBAEHM,EAAa,CACjBP,IAAK,EACLC,MAAO,iBAEHO,EAAa,CAAC,WACdC,EAAa,CAAER,MAAO,eACtBS,EAAc,CAAET,MAAO,6BACvBU,EAAc,CAAEV,MAAO,qBACvBW,EAAc,CAAEX,MAAO,mBACvBY,EAAc,CAAEZ,MAAO,iBACvBa,EAAc,CAAEb,MAAO,iBACvBc,EAAc,CAAEd,MAAO,iBACvBe,EAAc,CAAEf,MAAO,iBACvBgB,EAAc,CAAEhB,MAAO,gBACvBiB,EAAc,CAAEjB,MAAO,eACvBkB,EAAc,CAAElB,MAAO,kBACvBmB,EAAc,CAAEnB,MAAO,kBACvBoB,EAAc,CAAErB,IAAK,GACrBsB,EAAc,CAClBtB,IAAK,EACLC,MAAO,mBAEHsB,EAAc,CAAEtB,MAAO,eACvBuB,EAAc,CAClBvB,MAAO,sBACP,uBAAwB,yBAEpBwB,EAAc,CAAExB,MAAO,WACvByB,EAAc,CAAEzB,MAAO,eACvB0M,EAAc,CAAE1M,MAAO,kBACvB2M,EAAc,CAAE3M,MAAO,kBACvB4M,EAAc,CAAE5M,MAAO,kBAY7B,OAA4BlC,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,cACRG,KAAAA,CAAMC,GCuER,MAAM0O,GAAc3K,EAAAA,EAAAA,IAAkB,IAChC4K,GAAc5K,EAAAA,EAAAA,IAAkB,IAChC6K,GAAiB7K,EAAAA,EAAAA,IAAmB,MACpC8K,GAAiB9K,EAAAA,EAAAA,IAAmB,MACpC+K,GAAiB/K,EAAAA,EAAAA,KAAI,GAGrBgL,GAAqBhL,EAAAA,EAAAA,KAAI,GACzBiL,GAAejL,EAAAA,EAAAA,KAAI,GACnBkL,GAAoBlL,EAAAA,EAAAA,KAAI,GAGxBmL,GAAsBnL,EAAAA,EAAAA,KAAI,GAG1BoL,GAAYpL,EAAAA,EAAAA,KAAI,GAChBqL,GAAerL,EAAAA,EAAAA,KAAI,GACnBsL,GAAetL,EAAAA,EAAAA,KAAI,GAGnBuL,GAAmBpP,EAAAA,EAAAA,KAAS,KAChC,IAAK0O,EAAe3N,MAAO,MAAO,GAClC,MAAMsO,EAAeb,EAAYzN,MAAMuO,MAAKC,GAAQA,EAAKhI,KAAOmH,EAAe3N,QAC/E,OAAOsO,EAAeA,EAAaG,iBAAmB,EAAE,IAIpDC,GAAsBzP,EAAAA,EAAAA,KAAS,IAE9BkP,EAAanO,OAAUoO,EAAapO,MAIlC0N,EAAY1N,MAAM2O,QAAOC,IAC9B,MAAMC,EAAgC,YAArBD,EAAW/P,MACtBiQ,EAAgC,YAArBF,EAAW/P,MAE5B,OAAQgQ,GAAYV,EAAanO,OAAW8O,GAAYV,EAAapO,KAAM,IAPpE0N,EAAY1N,QAYjB+O,GAAmB1L,UACvByK,EAAmB9N,OAAQ,EAC3B,IACE,MAAMgP,QAAiBtL,EAAAA,GAAOuL,iBAC9BxB,EAAYzN,MAAQgP,EAASnL,I,CAC7B,MAAOC,GACPC,QAAQD,MAAM,cAAeA,GAC7BE,EAAAA,GAAUF,MAAM,+B,CAChB,QACAgK,EAAmB9N,OAAQ,C,GAKzBkP,GAAkB/E,IACtBgF,GAAiBhF,EAAI3D,GAAG,EAIpB2I,GAAmB9L,UACvBsK,EAAe3N,MAAQwC,EACvBqL,EAAe7N,OAAQ,QACjBoP,GAAW5M,EAAa,EAI1B4M,GAAa/L,UACjB0K,EAAa/N,OAAQ,EACrB0N,EAAY1N,MAAQ,GAEpB,IACE,MAAMgP,QAAiBtL,EAAAA,GAAO2L,SAAS7M,GACvCkL,EAAY1N,MAAQgP,EAASnL,I,CAC7B,MAAOC,GACPC,QAAQD,MAAM,cAAeA,GAC7BE,EAAAA,GAAUF,MAAM,+B,CAChB,QACAiK,EAAa/N,OAAQ,C,GAKnBsP,GAAiBC,IACrBC,EAAAA,EAAaC,QACX,gDAAgDF,EAAOd,qBACvD,UACA,CACEiB,kBAAmB,SACnBC,iBAAkB,SAClB5P,KAAM,YAGP6P,MAAK,KACJC,GAAiBN,EAAO/I,GAAG,IAE5BsJ,OAAM,QAEL,EAIAD,GAAmBxM,UACvB,UACQK,EAAAA,GAAOmM,iBAAiBrN,GAC9BwB,EAAAA,GAAU+L,QAAQ,0CAGZhB,KAGFpB,EAAe3N,QAAUwC,IAC3BmL,EAAe3N,MAAQ,KACvB0N,EAAY1N,MAAQ,GACpB6N,EAAe7N,OAAQ,E,CAEzB,MAAO8D,GACPC,QAAQD,MAAM,YAAaA,GAC3BE,EAAAA,GAAUF,MAAM,+B,GAKdkM,GAAqB3M,UACzB,GAAKb,EAAL,CAKAwL,EAAkBhO,OAAQ,EAC1B,UACQ0D,EAAAA,GAAOsM,mBAAmBxN,GAChCwB,EAAAA,GAAU+L,QAAQ,iC,CAClB,MAAOjM,GACPC,QAAQD,MAAM,0BAA2BA,GACzCE,EAAAA,GAAUF,MAAM,iC,CAChB,QACAkK,EAAkBhO,OAAQ,C,OAZ1BgE,EAAAA,GAAUC,QAAQ,gD,EAiBhBgM,GAAkBrB,IACtBhB,EAAe5N,MAAQ4O,EAAWpI,GAClCyH,EAAoBjO,OAAQ,CAAI,EAI5BkQ,GAAoBA,KACxBjC,EAAoBjO,OAAQ,EAC5B4N,EAAe5N,MAAQ,IAAI,EAIvBmQ,GAAkBC,IACtB,IAAKA,EAAY,OAAO,KACxB,IACE,MAAMC,EAAO,IAAIC,KAAKF,GACtB,OAAOC,EAAKE,gB,CACZ,MAAOC,GACP,OAAOJ,C,GAKLK,GAAqBL,IACzB,IAAKA,EAAY,OAAO,KACxB,IACE,MAAMC,EAAO,IAAIC,KAAKF,GAEhBM,EAAQ,IAAIJ,KACZK,EAAUN,EAAKO,YAAcF,EAAME,WACvCP,EAAKQ,aAAeH,EAAMG,YAC1BR,EAAKS,gBAAkBJ,EAAMI,cAE/B,OAAIH,EAEKN,EAAKU,mBAAmB,GAAI,CAAEC,KAAM,UAAWC,OAAQ,UAAWC,OAAQ,YAG1Eb,EAAKc,mBAAmB,GAAI,CAAEC,MAAO,UAAWC,IAAK,YAC1D,IACAhB,EAAKU,mBAAmB,GAAI,CAAEC,KAAM,UAAWC,OAAQ,W,CAE3D,MAAOT,GACP,OAAOJ,C,GAKLkB,GAAoBA,CAACC,EAA2BC,KACpD,IAAKD,IAAcC,EAAS,MAAO,IAEnC,IACE,MAAMC,EAAQ,IAAInB,KAAKiB,GACjBxL,EAAM,IAAIuK,KAAKkB,GACfE,EAAa3L,EAAI4L,UAAYF,EAAME,UAEzC,GAAID,EAAa,EAAG,MAAO,IAE3B,GAAIA,EAAa,IACf,MAAO,GAAGA,MACL,GAAIA,EAAa,IAAO,CAC7B,MAAMrN,EAAU+E,KAAKQ,MAAM8H,EAAa,KACxC,MAAO,GAAGrN,I,CACL,GAAIqN,EAAa,KAAS,CAC/B,MAAME,EAAUxI,KAAKQ,MAAM8H,EAAa,KAClCrN,EAAU+E,KAAKQ,MAAO8H,EAAa,IAAS,KAClD,MAAO,GAAGE,MAAYvN,I,CACjB,CACL,MAAMwN,EAAQzI,KAAKQ,MAAM8H,EAAa,MAChCE,EAAUxI,KAAKQ,MAAO8H,EAAa,KAAW,KACpD,MAAO,GAAGG,MAAUD,I,EAEtB,MAAOpB,GAEP,OADAzM,QAAQD,MAAM,8BAA+B0M,GACtC,G,GAKLsB,GAAwBA,KAC5B3D,EAAanO,MAAQkO,EAAUlO,MAC/BoO,EAAapO,MAAQkO,EAAUlO,KAAK,EAIhC+R,GAAqBA,KAEzB7D,EAAUlO,MAAQmO,EAAanO,OAASoO,EAAapO,KAAK,ED3D5D,OCiEAuE,EAAAA,EAAAA,KAAU,KACRwK,IAAkB,IDlEb,CAACtP,EAAUC,KAChB,MAAM8E,GAAyB5E,EAAAA,EAAAA,IAAkB,eAC3C6E,GAAsB7E,EAAAA,EAAAA,IAAkB,YACxCoS,GAAwBpS,EAAAA,EAAAA,IAAkB,cAC1CqS,GAAqBrS,EAAAA,EAAAA,IAAkB,WACvCgF,IAAuBhF,EAAAA,EAAAA,IAAkB,aACzCsS,IAAqBC,EAAAA,EAAAA,IAAkB,WAE7C,OAAQtS,EAAAA,EAAAA,OAAc2F,EAAAA,EAAAA,IAAoB,MAAO9E,EAAY,EAC3D2E,EAAAA,EAAAA,IAAoB,MAAOxE,EAAY,CACnCgN,EAAe7N,QA0HZH,EAAAA,EAAAA,OAAc2F,EAAAA,EAAAA,IAAoB,MAAOvD,EAAa,EACrDoD,EAAAA,EAAAA,IAAoB,MAAOnD,EAAa,EACtCoD,EAAAA,EAAAA,IAAaV,GAAsB,CACjC7E,KAAM,UACNE,KAAM,QACNsF,QAAS7F,EAAO,KAAOA,EAAO,GAAMqF,GAAiB8I,EAAe7N,OAAQ,GAC5EY,MAAO,eACN,CACDT,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBkF,EAAAA,EAAAA,IAAa2M,EAAoB,KAAM,CACrC9R,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBkF,EAAAA,EAAAA,KAAaM,EAAAA,EAAAA,IAAOwM,EAAAA,UAEtB7R,EAAG,IAELb,EAAO,KAAOA,EAAO,IAAKW,EAAAA,EAAAA,IAAiB,cAE7CE,EAAG,KAEL8E,EAAAA,EAAAA,IAAoB,KAAM,MAAM/E,EAAAA,EAAAA,IAAiB+N,EAAiBrO,OAAQ,MAE5EqS,EAAAA,EAAAA,MAAiBxS,EAAAA,EAAAA,OAAc2F,EAAAA,EAAAA,IAAoB,MAAOrD,EAAa,EACrEkD,EAAAA,EAAAA,IAAoB,MAAOjD,EAAa,EACtCiD,EAAAA,EAAAA,IAAoB,MAAOhD,EAAa,EACtCgD,EAAAA,EAAAA,IAAoB,QAASiI,EAAa,EACxC+E,EAAAA,EAAAA,KAAgBhN,EAAAA,EAAAA,IAAoB,QAAS,CAC3CtF,KAAM,WACNyG,GAAI,aACJ,sBAAuB9G,EAAO,KAAOA,EAAO,GAAMqF,GAAkBmJ,EAAWlO,MAAQ+E,GACvFuN,SAAUR,IACT,KAAM,KAAM,CACb,CAACS,EAAAA,GAAiBrE,EAAUlO,SAE9BN,EAAO,KAAOA,EAAO,IAAK2F,EAAAA,EAAAA,IAAoB,OAAQ,CAAEzE,MAAO,8BAAgC,OAAQ,OAEzGyE,EAAAA,EAAAA,IAAoB,QAASkI,EAAa,EACxC8E,EAAAA,EAAAA,KAAgBhN,EAAAA,EAAAA,IAAoB,QAAS,CAC3CtF,KAAM,WACNyG,GAAI,gBACJ,sBAAuB9G,EAAO,KAAOA,EAAO,GAAMqF,GAAkBoJ,EAAcnO,MAAQ+E,GAC1FuN,SAAUP,IACT,KAAM,KAAM,CACb,CAACQ,EAAAA,GAAiBpE,EAAanO,SAEjCN,EAAO,MAAQA,EAAO,KAAM2F,EAAAA,EAAAA,IAAoB,OAAQ,CAAEzE,MAAO,iCAAmC,UAAW,OAEjHyE,EAAAA,EAAAA,IAAoB,QAASmI,EAAa,EACxC6E,EAAAA,EAAAA,KAAgBhN,EAAAA,EAAAA,IAAoB,QAAS,CAC3CtF,KAAM,WACNyG,GAAI,gBACJ,sBAAuB9G,EAAO,KAAOA,EAAO,GAAMqF,GAAkBqJ,EAAcpO,MAAQ+E,GAC1FuN,SAAUP,IACT,KAAM,KAAM,CACb,CAACQ,EAAAA,GAAiBnE,EAAapO,SAEjCN,EAAO,MAAQA,EAAO,KAAM2F,EAAAA,EAAAA,IAAoB,OAAQ,CAAEzE,MAAO,iCAAmC,UAAW,WAIrH0E,EAAAA,EAAAA,IAAakN,EAAU,CACrBnH,MAAOqD,EAAoB1O,MAC3ByS,aAAcxC,IACb,KAAM,EAAG,CAAC,aACV,CACH,CAACiC,GAAoBnE,EAAa/N,cAzLrCH,EAAAA,EAAAA,OAAc2F,EAAAA,EAAAA,IAAoB,MAAO1E,EAAY,CACpDpB,EAAO,KAAOA,EAAO,IAAK2F,EAAAA,EAAAA,IAAoB,MAAO,CAAEzE,MAAO,eAAiB,EAC7EyE,EAAAA,EAAAA,IAAoB,KAAM,KAAM,kBAC9B,KACJA,EAAAA,EAAAA,IAAoB,MAAOtE,EAAY,CACpC+M,EAAmB9N,QACfH,EAAAA,EAAAA,OAAc2F,EAAAA,EAAAA,IAAoB,MAAOxE,EAAY,EACpDsE,EAAAA,EAAAA,IAAad,EAAwB,CACnCiB,KAAM,EACNC,SAAU,QAGgB,IAA7B+H,EAAYzN,MAAMmG,SAChBtG,EAAAA,EAAAA,OAAc2F,EAAAA,EAAAA,IAAoB,MAAOvE,EAAY,EACpDqE,EAAAA,EAAAA,IAAab,EAAqB,CAAE2B,YAAa,+BAElDvG,EAAAA,EAAAA,OAAc2F,EAAAA,EAAAA,IAAoB,QAAStE,EAAY,CACtDxB,EAAO,KAAOA,EAAO,IAAK2F,EAAAA,EAAAA,IAAoB,QAAS,KAAM,EAC3DA,EAAAA,EAAAA,IAAoB,KAAM,KAAM,EAC9BA,EAAAA,EAAAA,IAAoB,KAAM,CAAEzE,MAAO,eAAiB,SACpDyE,EAAAA,EAAAA,IAAoB,KAAM,CAAEzE,MAAO,qBAAuB,eAC1DyE,EAAAA,EAAAA,IAAoB,KAAM,CAAEzE,MAAO,mBAAqB,aACxDyE,EAAAA,EAAAA,IAAoB,KAAM,CAAEzE,MAAO,iBAAmB,WACtDyE,EAAAA,EAAAA,IAAoB,KAAM,CAAEzE,MAAO,iBAAmB,WACtDyE,EAAAA,EAAAA,IAAoB,KAAM,CAAEzE,MAAO,gBAAkB,UACrDyE,EAAAA,EAAAA,IAAoB,KAAM,CAAEzE,MAAO,kBAAoB,eAEvD,KACJyE,EAAAA,EAAAA,IAAoB,QAAS,KAAM,GAChCxF,EAAAA,EAAAA,KAAW,IAAO2F,EAAAA,EAAAA,IAAoBa,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAYmH,EAAYzN,OAAO,CAACmK,EAAKQ,MACnF9K,EAAAA,EAAAA,OAAc2F,EAAAA,EAAAA,IAAoB,KAAM,CAC9C7E,IAAKwJ,EAAI3D,GACT5F,OAAO8R,EAAAA,EAAAA,IAAgB,CAAE,aAAc/H,EAAQ,IAAM,IACrDpF,QAAUR,GAAiBmK,GAAe/E,IACzC,EACD9E,EAAAA,EAAAA,IAAoB,KAAMjE,EAAY,EACpCkE,EAAAA,EAAAA,IAAa0M,EAAuB,CAClCW,QAASxI,EAAIsE,iBACbmE,UAAW,MACX,aAAc,KACb,CACDzS,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBiF,EAAAA,EAAAA,IAAoB,OAAQhE,GAAaf,EAAAA,EAAAA,IAAiB6J,EAAIsE,kBAAmB,MAEnFlO,EAAG,GACF,KAAM,CAAC,eAEZ8E,EAAAA,EAAAA,IAAoB,KAAM/D,EAAa,EACrCgE,EAAAA,EAAAA,IAAa0M,EAAuB,CAClCW,QAASxC,GAAehG,EAAI0I,cAC5BD,UAAW,MACX,aAAc,KACb,CACDzS,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBiF,EAAAA,EAAAA,IAAoB,OAAQ,MAAM/E,EAAAA,EAAAA,IAAiBmQ,GAAkBtG,EAAI0I,eAAgB,MAE3FtS,EAAG,GACF,KAAM,CAAC,eAEZ8E,EAAAA,EAAAA,IAAoB,KAAM9D,EAAa,EACrC8D,EAAAA,EAAAA,IAAoB,OAAQ,MAAM/E,EAAAA,EAAAA,IAAiBgR,GAAkBnH,EAAI0I,aAAc1I,EAAIpE,MAAO,MAEpGV,EAAAA,EAAAA,IAAoB,KAAM7D,EAAa,EACrC6D,EAAAA,EAAAA,IAAoB,OAAQ5D,GAAanB,EAAAA,EAAAA,IAAiB6J,EAAI2I,cAAe,MAE/EzN,EAAAA,EAAAA,IAAoB,KAAM3D,EAAa,EACrC2D,EAAAA,EAAAA,IAAoB,OAAQ1D,GAAarB,EAAAA,EAAAA,IAAiB6J,EAAI4I,cAAe,MAE/E1N,EAAAA,EAAAA,IAAoB,KAAMzD,EAAa,EACrCyD,EAAAA,EAAAA,IAAoB,OAAQxD,GAAavB,EAAAA,EAAAA,IAAiB6J,EAAI6I,YAAa,MAE7E3N,EAAAA,EAAAA,IAAoB,KAAMvD,EAAa,EACrCuD,EAAAA,EAAAA,IAAoB,MAAOtD,EAAa,EACtCuD,EAAAA,EAAAA,IAAaV,GAAsB,CACjC7E,KAAM,UACNE,KAAM,QACNsF,SAAS0N,EAAAA,EAAAA,KAAgBlO,GAAiBiL,GAAmB7F,EAAI3D,KAAM,CAAC,SACxExB,MAAO,mBACN,CACD7E,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBkF,EAAAA,EAAAA,IAAa2M,EAAoB,KAAM,CACrC9R,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBkF,EAAAA,EAAAA,KAAaM,EAAAA,EAAAA,IAAOsN,EAAAA,cAEtB3S,EAAG,OAGPA,EAAG,GACF,KAAM,CAAC,aACV+E,EAAAA,EAAAA,IAAaV,GAAsB,CACjC7E,KAAM,SACNE,KAAM,QACNsF,SAAS0N,EAAAA,EAAAA,KAAgBlO,GAAiBuK,GAAcnF,IAAO,CAAC,SAChEnF,MAAO,iBACN,CACD7E,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBkF,EAAAA,EAAAA,IAAa2M,EAAoB,KAAM,CACrC9R,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBkF,EAAAA,EAAAA,KAAaM,EAAAA,EAAAA,IAAOuN,EAAAA,YAEtB5S,EAAG,OAGPA,EAAG,GACF,KAAM,CAAC,iBAGb,GAAIY,MACL,MAC0B,IAA7BsM,EAAYzN,MAAMmG,SACdtG,EAAAA,EAAAA,OAAc2F,EAAAA,EAAAA,IAAoB,KAAMxD,EAAatC,EAAO,KAAOA,EAAO,GAAK,EAC9E2F,EAAAA,EAAAA,IAAoB,KAAM,CACxB+N,QAAS,IACTxS,MAAO,aACN,WAAY,QAEjB+E,EAAAA,EAAAA,IAAoB,IAAI,gBAyEhDL,EAAAA,EAAAA,IAAa+N,EAAAA,EAAkB,CAC7B/Q,QAAS2L,EAAoBjO,MAC7B,mBAAoBN,EAAO,KAAOA,EAAO,GAAMqF,GAAkBkJ,EAAqBjO,MAAQ+E,GAC9FvC,aAAcmL,EAAe3N,MAC7ByC,aAAcmL,EAAe5N,MAC7BsT,QAASpD,IACR,KAAM,EAAG,CAAC,UAAW,eAAgB,kBACxC,CAEJ,IE5fA,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://fuzz-web/./src/components/common/CaseStateTag.vue?c0c7", "webpack://fuzz-web/./src/components/common/CaseStateTag.vue", "webpack://fuzz-web/./src/components/common/CaseStateTag.vue?4980", "webpack://fuzz-web/./src/utils/status.ts", "webpack://fuzz-web/./src/components/test/CaseDetailDialog.vue?5717", "webpack://fuzz-web/./src/components/test/CaseDetailDialog.vue", "webpack://fuzz-web/./src/components/test/CaseDetailDialog.vue?ca65", "webpack://fuzz-web/../src/virtual-scroll.js", "webpack://fuzz-web/../src/index.js", "webpack://fuzz-web/./src/components/test/CaseList.vue?a767", "webpack://fuzz-web/./src/components/test/CaseList.vue", "webpack://fuzz-web/./src/components/test/CaseList.vue?5681", "webpack://fuzz-web/./src/views/testplan/TestResults.vue?4a50", "webpack://fuzz-web/./src/views/testplan/TestResults.vue", "webpack://fuzz-web/./src/views/testplan/TestResults.vue?899d"], "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from \"vue\"\n\nimport { computed } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'CaseStateTag',\n  props: {\n    state: {}\n  },\n  setup(__props: any) {\n\r\nconst props = __props;\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getCaseStateName = (state: string): 'Not Run' | 'Running' | 'Passed' | 'Failed' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Passed';\r\n    case ExecutionState.Failure:\r\n      return 'Failed';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getCaseStateName(props.state);\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n\n  return (_openBlock(), _createBlock(_component_el_tag, {\n    type: tagType.value,\n    size: \"small\",\n    style: {\"min-width\":\"60px\"}\n  }, {\n    default: _withCtx(() => [\n      _createTextVNode(_toDisplayString(stateName.value), 1)\n    ]),\n    _: 1\n  }, 8, [\"type\"]))\n}\n}\n\n})", "<template>\r\n  <el-tag :type=\"tagType\" size=\"small\" style=\"min-width: 60px;\">\r\n    {{ stateName }}\r\n  </el-tag>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, defineProps } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\nconst props = defineProps<{\r\n  state: string;\r\n}>();\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getCaseStateName = (state: string): 'Not Run' | 'Running' | 'Passed' | 'Failed' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Passed';\r\n    case ExecutionState.Failure:\r\n      return 'Failed';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getCaseStateName(props.state);\r\n});\r\n</script>\r\n", "import script from \"./CaseStateTag.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./CaseStateTag.vue?vue&type=script&setup=true&lang=ts\"\n\nconst __exports__ = script;\n\nexport default __exports__", "import { ExecutionState } from '@/api/appApi';\r\n\r\n/**\r\n * 获取执行状态对应的时间线项类型\r\n * @param state 执行状态\r\n * @returns 时间线项类型\r\n */\r\nexport const getTimelineItemType = (state: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {\r\n  switch (state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'primary';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n};", "import { defineComponent as _defineComponent } from 'vue'\nimport { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, unref as _unref, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, createBlock as _createBlock, withCtx as _withCtx } from \"vue\"\n\nconst _hoisted_1 = {\n  key: 0,\n  class: \"loading\"\n}\nconst _hoisted_2 = {\n  key: 1,\n  class: \"case-detail-content\"\n}\nconst _hoisted_3 = { class: \"basic-info\" }\nconst _hoisted_4 = { class: \"info-grid\" }\nconst _hoisted_5 = {\n  key: 0,\n  class: \"info-item\"\n}\nconst _hoisted_6 = { class: \"value\" }\nconst _hoisted_7 = {\n  key: 1,\n  class: \"info-item\"\n}\nconst _hoisted_8 = { class: \"value\" }\nconst _hoisted_9 = { class: \"info-item\" }\nconst _hoisted_10 = { class: \"value\" }\nconst _hoisted_11 = { class: \"info-item\" }\nconst _hoisted_12 = { class: \"value status-combined\" }\nconst _hoisted_13 = {\n  key: 2,\n  class: \"info-item full-width\"\n}\nconst _hoisted_14 = [\"title\"]\nconst _hoisted_15 = {\n  key: 3,\n  class: \"info-item full-width\"\n}\nconst _hoisted_16 = [\"title\"]\nconst _hoisted_17 = { class: \"steps-section\" }\nconst _hoisted_18 = {\n  key: 0,\n  class: \"no-steps\"\n}\nconst _hoisted_19 = { class: \"step-content\" }\nconst _hoisted_20 = { class: \"step-row\" }\nconst _hoisted_21 = { class: \"step-left\" }\nconst _hoisted_22 = { class: \"step-timestamp\" }\nconst _hoisted_23 = [\"title\"]\nconst _hoisted_24 = [\"title\"]\nconst _hoisted_25 = { class: \"step-right\" }\nconst _hoisted_26 = { class: \"dialog-footer\" }\n\nimport { ref, watch, onMounted } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport { appApi, CaseStep, ExecutionState } from '@/api/appApi';\r\nimport { getTimelineItemType } from '@/utils/status';\r\nimport CaseStateTag from '@/components/common/CaseStateTag.vue';\r\nimport { formatDateTime } from '@/utils/timeUtils';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'CaseDetailDialog',\n  props: {\n    visible: { type: Boolean },\n    testResultId: {},\n    caseResultId: {}\n  },\n  emits: [\"update:visible\", \"close\"],\n  setup(__props: any, { emit: __emit }) {\n\r\nconst props = __props;\r\n\r\nconst emit = __emit;\r\n\r\nconst dialogVisible = ref(props.visible);\r\nconst caseData = ref<CaseResult | null>(null);\r\nconst steps = ref<CaseStep[]>([]);\r\nconst loading = ref(false);\r\n\r\nwatch(() => props.visible, (newValue) => {\r\n  dialogVisible.value = newValue;\r\n  if (newValue && props.testResultId && props.caseResultId) {\r\n    loadCaseData();\r\n  }\r\n});\r\n\r\nwatch(() => dialogVisible.value, (newValue) => {\r\n  emit('update:visible', newValue);\r\n  if (!newValue) emit('close');\r\n});\r\n\r\nconst loadCaseData = async () => {\r\n  if (!props.testResultId || !props.caseResultId) {\r\n    ElMessage.warning('Missing required parameters');\r\n    return;\r\n  }\r\n\r\n  loading.value = true;\r\n  try {\r\n    // 并行加载用例数据和步骤数据\r\n    const [caseResponse, stepsResponse] = await Promise.all([\r\n      appApi.getCaseResult(props.testResultId, props.caseResultId),\r\n      appApi.getCaseSteps(props.testResultId, props.caseResultId)\r\n    ]);\r\n\r\n    caseData.value = caseResponse.data;\r\n    steps.value = stepsResponse.data;\r\n  } catch (error) {\r\n    console.error('Failed to load case data:', error);\r\n    ElMessage.error('Failed to load case details');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst closeDialog = () => {\r\n  dialogVisible.value = false;\r\n  // 清空数据\r\n  caseData.value = null;\r\n  steps.value = [];\r\n};\r\n\r\n// 添加新的微秒格式化方法\r\nconst formatMicroseconds = (microseconds: number): string => {\r\n  if (!microseconds && microseconds !== 0) return 'N/A';\r\n\r\n  // 转换为秒并保留6位小数\r\n  const seconds = microseconds / 1000000;\r\n  // 使用toFixed(6)确保始终有6位小数\r\n  return `${seconds.toFixed(6)}`;\r\n};\r\n\r\n// 组件挂载时，如果对话框是可见的且有必要参数，则加载数据\r\nonMounted(() => {\r\n  if (dialogVisible.value && props.testResultId && props.caseResultId) {\r\n    loadCaseData();\r\n  }\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\")!\n  const _component_el_empty = _resolveComponent(\"el-empty\")!\n  const _component_el_timeline_item = _resolveComponent(\"el-timeline-item\")!\n  const _component_el_timeline = _resolveComponent(\"el-timeline\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_dialog = _resolveComponent(\"el-dialog\")!\n\n  return (_openBlock(), _createBlock(_component_el_dialog, {\n    modelValue: dialogVisible.value,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((dialogVisible).value = $event)),\n    title: `${caseData.value?.name || caseData.value?.sequenceName || ''}`,\n    width: \"60%\",\n    \"destroy-on-close\": \"\"\n  }, {\n    footer: _withCtx(() => [\n      _createElementVNode(\"span\", _hoisted_26, [\n        _createVNode(_component_el_button, { onClick: closeDialog }, {\n          default: _withCtx(() => _cache[9] || (_cache[9] = [\n            _createTextVNode(\"Close\")\n          ])),\n          _: 1\n        })\n      ])\n    ]),\n    default: _withCtx(() => [\n      (loading.value)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n            _createVNode(_component_el_skeleton, {\n              rows: 10,\n              animated: \"\"\n            })\n          ]))\n        : (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [\n            _createElementVNode(\"div\", _hoisted_3, [\n              _cache[7] || (_cache[7] = _createElementVNode(\"h4\", null, \"Information\", -1)),\n              _createElementVNode(\"div\", _hoisted_4, [\n                (caseData.value?.name)\n                  ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [\n                      _cache[1] || (_cache[1] = _createElementVNode(\"div\", { class: \"label\" }, \"Case Name:\", -1)),\n                      _createElementVNode(\"div\", _hoisted_6, _toDisplayString(caseData.value?.name), 1)\n                    ]))\n                  : _createCommentVNode(\"\", true),\n                (caseData.value?.name)\n                  ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [\n                      _cache[2] || (_cache[2] = _createElementVNode(\"div\", { class: \"label\" }, \"Sequence Name:\", -1)),\n                      _createElementVNode(\"div\", _hoisted_8, _toDisplayString(caseData.value?.sequenceName), 1)\n                    ]))\n                  : _createCommentVNode(\"\", true),\n                _createElementVNode(\"div\", _hoisted_9, [\n                  _cache[3] || (_cache[3] = _createElementVNode(\"div\", { class: \"label\" }, \"Start Time:\", -1)),\n                  _createElementVNode(\"div\", _hoisted_10, _toDisplayString(_unref(formatDateTime)(caseData.value?.begin)), 1)\n                ]),\n                _createElementVNode(\"div\", _hoisted_11, [\n                  _cache[4] || (_cache[4] = _createElementVNode(\"div\", { class: \"label\" }, \"End Time / Status:\", -1)),\n                  _createElementVNode(\"div\", _hoisted_12, [\n                    _createTextVNode(_toDisplayString(_unref(formatDateTime)(caseData.value?.end)) + \" \", 1),\n                    _createVNode(CaseStateTag, {\n                      state: caseData.value?.state || '',\n                      class: \"status-tag\"\n                    }, null, 8, [\"state\"])\n                  ])\n                ]),\n                (caseData.value?.parameter)\n                  ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [\n                      _cache[5] || (_cache[5] = _createElementVNode(\"div\", { class: \"label\" }, \"Parameter:\", -1)),\n                      _createElementVNode(\"div\", {\n                        class: \"value\",\n                        title: caseData.value?.parameter\n                      }, _toDisplayString(caseData.value?.parameter), 9, _hoisted_14)\n                    ]))\n                  : _createCommentVNode(\"\", true),\n                (caseData.value?.detail)\n                  ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [\n                      _cache[6] || (_cache[6] = _createElementVNode(\"div\", { class: \"label\" }, \"Detail:\", -1)),\n                      _createElementVNode(\"div\", {\n                        class: \"value\",\n                        title: caseData.value?.detail\n                      }, _toDisplayString(caseData.value.detail), 9, _hoisted_16)\n                    ]))\n                  : _createCommentVNode(\"\", true)\n              ])\n            ]),\n            _createElementVNode(\"div\", _hoisted_17, [\n              _cache[8] || (_cache[8] = _createElementVNode(\"h4\", null, \"Steps\", -1)),\n              (steps.value.length === 0)\n                ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [\n                    _createVNode(_component_el_empty, { description: \"No steps available\" })\n                  ]))\n                : (_openBlock(), _createBlock(_component_el_timeline, { key: 1 }, {\n                    default: _withCtx(() => [\n                      (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(steps.value, (step) => {\n                        return (_openBlock(), _createBlock(_component_el_timeline_item, {\n                          key: step.id,\n                          type: _unref(getTimelineItemType)(step.state),\n                          hollow: step.state !== 'Success'\n                        }, {\n                          default: _withCtx(() => [\n                            _createElementVNode(\"div\", _hoisted_19, [\n                              _createElementVNode(\"div\", _hoisted_20, [\n                                _createElementVNode(\"div\", _hoisted_21, [\n                                  _createElementVNode(\"span\", _hoisted_22, _toDisplayString(formatMicroseconds(step.timestamp)), 1),\n                                  _createElementVNode(\"span\", {\n                                    class: \"step-name\",\n                                    title: step.name\n                                  }, _toDisplayString(step.name), 9, _hoisted_23),\n                                  (step.detail)\n                                    ? (_openBlock(), _createElementBlock(\"span\", {\n                                        key: 0,\n                                        title: step.detail,\n                                        class: \"step-detail-inline\"\n                                      }, _toDisplayString(step.detail), 9, _hoisted_24))\n                                    : _createCommentVNode(\"\", true)\n                                ]),\n                                _createElementVNode(\"div\", _hoisted_25, [\n                                  (step.state != _unref(ExecutionState).Completed)\n                                    ? (_openBlock(), _createBlock(CaseStateTag, {\n                                        key: 0,\n                                        state: step.state\n                                      }, null, 8, [\"state\"]))\n                                    : _createCommentVNode(\"\", true)\n                                ])\n                              ])\n                            ])\n                          ]),\n                          _: 2\n                        }, 1032, [\"type\", \"hollow\"]))\n                      }), 128))\n                    ]),\n                    _: 1\n                  }))\n            ])\n          ]))\n    ]),\n    _: 1\n  }, 8, [\"modelValue\", \"title\"]))\n}\n}\n\n})", "<template>\r\n  <el-dialog v-model=\"dialogVisible\" :title=\"`${caseData?.name || caseData?.sequenceName || ''}`\" width=\"60%\"\r\n    destroy-on-close>\r\n    <div v-if=\"loading\" class=\"loading\">\r\n      <el-skeleton :rows=\"10\" animated />\r\n    </div>\r\n\r\n    <div v-else class=\"case-detail-content\">\r\n      <!-- 基本信息区域 -->\r\n      <div class=\"basic-info\">\r\n        <h4>Information</h4>\r\n        <div class=\"info-grid\">\r\n          <div class=\"info-item\" v-if=\"caseData?.name\">\r\n            <div class=\"label\">Case Name:</div>\r\n            <div class=\"value\">{{ caseData?.name }}</div>\r\n          </div>\r\n          <div class=\"info-item\" v-if=\"caseData?.name\">\r\n            <div class=\"label\">Sequence Name:</div>\r\n            <div class=\"value\">{{ caseData?.sequenceName }}</div>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <div class=\"label\">Start Time:</div>\r\n            <div class=\"value\">{{ formatDateTime(caseData?.begin) }}</div>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <div class=\"label\">End Time / Status:</div>\r\n            <div class=\"value status-combined\">\r\n              {{ formatDateTime(caseData?.end) }}\r\n              <CaseStateTag :state=\"caseData?.state || ''\" class=\"status-tag\" />\r\n            </div>\r\n          </div>\r\n          <div v-if=\"caseData?.parameter\" class=\"info-item full-width\">\r\n            <div class=\"label\">Parameter:</div>\r\n            <div class=\"value\" :title=\"caseData?.parameter\">{{ caseData?.parameter }}</div>\r\n          </div>\r\n          <div v-if=\"caseData?.detail\" class=\"info-item full-width\">\r\n            <div class=\"label\">Detail:</div>\r\n            <div class=\"value\" :title=\"caseData?.detail\">{{ caseData.detail }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 步骤列表区域 -->\r\n      <div class=\"steps-section\">\r\n        <h4>Steps</h4>\r\n\r\n        <div v-if=\"steps.length === 0\" class=\"no-steps\">\r\n          <el-empty description=\"No steps available\" />\r\n        </div>\r\n\r\n        <el-timeline v-else>\r\n          <el-timeline-item v-for=\"step in steps\" :key=\"step.id\" :type=\"getTimelineItemType(step.state)\"\r\n            :hollow=\"step.state !== 'Success'\">\r\n            <div class=\"step-content\">\r\n              <div class=\"step-row\">\r\n                <div class=\"step-left\">\r\n                  <span class=\"step-timestamp\">{{ formatMicroseconds(step.timestamp) }}</span>\r\n                  <span class=\"step-name\" :title=\"step.name\" >{{ step.name }}</span>\r\n                  <span v-if=\"step.detail\" :title=\"step.detail\" class=\"step-detail-inline\">{{ step.detail }}</span>\r\n                </div>\r\n                <div class=\"step-right\">\r\n                  <CaseStateTag v-if=\"step.state != ExecutionState.Completed\" :state=\"step.state\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n      </div>\r\n    </div>\r\n\r\n    <template #footer>\r\n      <span class=\"dialog-footer\">\r\n        <el-button @click=\"closeDialog\">Close</el-button>\r\n      </span>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, watch, defineProps, defineEmits, onMounted } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport { appApi, CaseStep, ExecutionState } from '@/api/appApi';\r\nimport { getTimelineItemType } from '@/utils/status';\r\nimport CaseStateTag from '@/components/common/CaseStateTag.vue';\r\nimport { formatDateTime } from '@/utils/timeUtils';\r\n\r\nconst props = defineProps<{\r\n  visible: boolean;\r\n  testResultId?: string | null;\r\n  caseResultId?: number | null;\r\n}>();\r\n\r\nconst emit = defineEmits<{\r\n  (e: 'update:visible', value: boolean): void;\r\n  (e: 'close'): void;\r\n}>();\r\n\r\nconst dialogVisible = ref(props.visible);\r\nconst caseData = ref<CaseResult | null>(null);\r\nconst steps = ref<CaseStep[]>([]);\r\nconst loading = ref(false);\r\n\r\nwatch(() => props.visible, (newValue) => {\r\n  dialogVisible.value = newValue;\r\n  if (newValue && props.testResultId && props.caseResultId) {\r\n    loadCaseData();\r\n  }\r\n});\r\n\r\nwatch(() => dialogVisible.value, (newValue) => {\r\n  emit('update:visible', newValue);\r\n  if (!newValue) emit('close');\r\n});\r\n\r\nconst loadCaseData = async () => {\r\n  if (!props.testResultId || !props.caseResultId) {\r\n    ElMessage.warning('Missing required parameters');\r\n    return;\r\n  }\r\n\r\n  loading.value = true;\r\n  try {\r\n    // 并行加载用例数据和步骤数据\r\n    const [caseResponse, stepsResponse] = await Promise.all([\r\n      appApi.getCaseResult(props.testResultId, props.caseResultId),\r\n      appApi.getCaseSteps(props.testResultId, props.caseResultId)\r\n    ]);\r\n\r\n    caseData.value = caseResponse.data;\r\n    steps.value = stepsResponse.data;\r\n  } catch (error) {\r\n    console.error('Failed to load case data:', error);\r\n    ElMessage.error('Failed to load case details');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst closeDialog = () => {\r\n  dialogVisible.value = false;\r\n  // 清空数据\r\n  caseData.value = null;\r\n  steps.value = [];\r\n};\r\n\r\n// 添加新的微秒格式化方法\r\nconst formatMicroseconds = (microseconds: number): string => {\r\n  if (!microseconds && microseconds !== 0) return 'N/A';\r\n\r\n  // 转换为秒并保留6位小数\r\n  const seconds = microseconds / 1000000;\r\n  // 使用toFixed(6)确保始终有6位小数\r\n  return `${seconds.toFixed(6)}`;\r\n};\r\n\r\n// 组件挂载时，如果对话框是可见的且有必要参数，则加载数据\r\nonMounted(() => {\r\n  if (dialogVisible.value && props.testResultId && props.caseResultId) {\r\n    loadCaseData();\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.loading {\r\n  min-height: 200px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.case-detail-content {\r\n  padding: 0 20px;\r\n}\r\n\r\n.basic-info {\r\n  margin-bottom: 20px;\r\n\r\n  h4 {\r\n    margin-top: 0;\r\n    margin-bottom: 16px;\r\n    font-size: 16px;\r\n    color: #303133;\r\n    border-bottom: 1px solid #ebeef5;\r\n    padding-bottom: 8px;\r\n  }\r\n\r\n  .info-grid {\r\n    display: grid;\r\n    grid-template-columns: 1fr 1fr;\r\n    gap: 12px 24px;\r\n\r\n    .info-item {\r\n      .label {\r\n        font-size: 13px;\r\n        color: #909399;\r\n        margin-bottom: 4px;\r\n      }\r\n\r\n      .value {\r\n        font-size: 14px;\r\n        color: #303133;\r\n        word-break: break-word;\r\n\r\n        &.status-combined {\r\n          display: flex;\r\n          align-items: center;\r\n          \r\n          .status-tag {\r\n            margin-left: 12px;\r\n          }\r\n        }\r\n      }\r\n\r\n      &.full-width {\r\n        grid-column: span 2;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.steps-section {\r\n  h4 {\r\n    margin-top: 20px;\r\n    margin-bottom: 16px;\r\n    font-size: 16px;\r\n    color: #303133;\r\n    border-bottom: 1px solid #ebeef5;\r\n    padding-bottom: 8px;\r\n  }\r\n\r\n  .no-steps {\r\n    padding: 20px 0;\r\n  }\r\n\r\n  .step-content {\r\n    font-size: 13px;\r\n\r\n    .step-row {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      width: 100%;\r\n      padding: 8px 4px;\r\n      transition: background-color 0.2s;\r\n    }\r\n\r\n    .step-left {\r\n      display: flex;\r\n      align-items: center;\r\n      flex: 1;\r\n      min-width: 0; // 防止flex子元素溢出\r\n    }\r\n\r\n    .step-right {\r\n      margin-left: 10px;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .step-timestamp {\r\n      min-width: 80px;\r\n      display: inline-block;\r\n      color: #606266;\r\n      margin-right: 12px;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .step-name {\r\n      min-width: 80px;\r\n      font-weight: 500;\r\n      display: inline-block;\r\n      color: #303133;\r\n      margin-right: 8px;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n      max-width: 200px; /* 限制最大宽度，可根据实际情况调整 */\r\n    }\r\n\r\n    .step-detail-inline {\r\n      color: #606266;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n    }\r\n  }\r\n\r\n  .el-timeline-item:nth-child(odd) .step-row {\r\n    background-color: #f5f5fa;\r\n  }\r\n\r\n  .el-timeline-item:nth-child(even) .step-row {\r\n    background-color: #f5f5fa;\r\n  }\r\n\r\n  :deep(.el-timeline-item__node) {\r\n    margin-top: 10px;\r\n  }\r\n\r\n  :deep(.el-timeline-item__tail) {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./CaseDetailDialog.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./CaseDetailDialog.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./CaseDetailDialog.vue?vue&type=style&index=0&id=0bc3c0cc&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-0bc3c0cc\"]])\n\nexport default __exports__", "/**\r\n * js-booster - High-performance frontend library\r\n * VirtualScroll - Virtual scrolling implementation\r\n * @version 1.1.3\r\n * <AUTHOR>\r\n * @license MIT\r\n */\r\n\r\nclass VirtualScroll {\r\n  /**\r\n   * Create a virtual scroll instance\r\n   * @param {Object} options Configuration options\r\n   * @param {HTMLElement} options.container Scroll container element\r\n   * @param {Array} options.items Data items to display\r\n   * @param {number} [options.itemHeight=20] Height of each list item (pixels)\r\n   * @param {number} [options.bufferSize=10] Number of buffer items outside the visible area\r\n   * @param {Function} [options.renderItem] Custom item rendering function\r\n   * @param {Function} [options.renderHeader] Custom header rendering function\r\n   * @param {number} [options.maxHeight=26840000] Maximum height in pixels for the content wrapper\r\n   */\r\n  constructor(options) {\r\n    this.container = options.container;\r\n    this.items = options.items || [];\r\n    this.itemHeight = options.itemHeight || 20;\r\n    this.bufferSize = options.bufferSize || 10;\r\n    this.customRenderItem = options.renderItem;\r\n    this.customRenderHeader = options.renderHeader;\r\n    this.maxHeight = options.maxHeight || 26840000; // Add maximum height limit to prevent DOM height overflow\r\n\r\n    this.visibleStartIndex = 0;\r\n    this.visibleEndIndex = 0;\r\n    this.scrollContainer = null;\r\n    this.contentWrapper = null;\r\n    this.contentContainer = null;\r\n    this.totalHeight = this.items.length * this.itemHeight;\r\n    this.heightScale = 1; // Height scaling factor\r\n\r\n    // If total height exceeds maximum height, calculate scaling factor\r\n    if (this.totalHeight > this.maxHeight) {\r\n      this.heightScale = this.maxHeight / this.totalHeight;\r\n    }\r\n\r\n    this.initialize();\r\n  }\r\n\r\n  /**\r\n   * Initialize virtual scroll component\r\n   * @private\r\n   */\r\n  initialize() {\r\n    // Clear container\r\n    this.container.innerHTML = '';\r\n\r\n    // Create scroll container\r\n    this.scrollContainer = document.createElement('div');\r\n    // Add inline styles\r\n    Object.assign(this.scrollContainer.style, {\r\n      flex: '1',\r\n      overflow: 'auto',\r\n      position: 'relative',\r\n      minHeight: '0',\r\n      height: '100%',\r\n      boxSizing: 'border-box'\r\n    });\r\n\r\n    // If there's a custom header render function, render the header\r\n    if (this.customRenderHeader) {\r\n      const header = this.customRenderHeader();\r\n      if (header) {\r\n        this.scrollContainer.appendChild(header);\r\n      }\r\n    }\r\n\r\n    // Create content wrapper\r\n    this.contentWrapper = document.createElement('div');\r\n    // Add inline styles\r\n    Object.assign(this.contentWrapper.style, {\r\n      position: 'relative',\r\n      width: '100%'\r\n    });\r\n\r\n    // Use scaled height to ensure it doesn't exceed browser limits\r\n    const scaledHeight = this.totalHeight * this.heightScale;\r\n    this.contentWrapper.style.height = `${scaledHeight}px`;\r\n\r\n    // Create content container\r\n    this.contentContainer = document.createElement('div');\r\n    // Add inline styles\r\n    Object.assign(this.contentContainer.style, {\r\n      position: 'absolute',\r\n      width: '100%',\r\n      left: '0'\r\n    });\r\n\r\n    // Add scroll event listener\r\n    this.scrollContainer.addEventListener('scroll', this.handleScroll.bind(this));\r\n\r\n    // Assemble DOM\r\n    this.contentWrapper.appendChild(this.contentContainer);\r\n    this.scrollContainer.appendChild(this.contentWrapper);\r\n    this.container.appendChild(this.scrollContainer);\r\n\r\n    // Render initial visible items\r\n    this.renderVisibleItems(0, Math.min(100, this.items.length));\r\n  }\r\n\r\n  /**\r\n   * Handle scroll event\r\n   * @private\r\n   */\r\n  handleScroll() {\r\n    const scrollTop = this.scrollContainer.scrollTop;\r\n    const containerHeight = this.scrollContainer.clientHeight;\r\n\r\n    // Consider scaling factor in calculations\r\n    const realScrollTop = scrollTop / this.heightScale;\r\n\r\n    // Calculate visible range\r\n    const startIndex = Math.max(0, Math.floor(realScrollTop / this.itemHeight) - this.bufferSize);\r\n    const endIndex = Math.min(\r\n      this.items.length,\r\n      Math.ceil((realScrollTop + containerHeight / this.heightScale) / this.itemHeight) + this.bufferSize\r\n    );\r\n\r\n    // Only update when visible range changes\r\n    if (startIndex !== this.visibleStartIndex\r\n       || endIndex !== this.visibleEndIndex\r\n       || endIndex === 0) {\r\n      this.renderVisibleItems(startIndex, endIndex);\r\n\r\n      this.visibleStartIndex = startIndex;\r\n      this.visibleEndIndex = endIndex;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Render visible items\r\n   * @param {number} startIndex Start index\r\n   * @param {number} endIndex End index\r\n   * @private\r\n   */\r\n  renderVisibleItems(startIndex, endIndex) {\r\n    // Clear content container\r\n    this.contentContainer.innerHTML = '';\r\n\r\n    // Set position considering scaling factor\r\n    this.contentContainer.style.transform = `translateY(${startIndex * this.itemHeight * this.heightScale}px)`;\r\n\r\n    // Render visible items\r\n    for (let i = startIndex; i < endIndex; i++) {\r\n      const item = this.items[i];\r\n\r\n      if (this.customRenderItem) {\r\n        // Use custom render function\r\n        const itemElement = this.customRenderItem(item, i);\r\n        if (itemElement) {\r\n          // Only set necessary height styles, other styles are determined by the caller\r\n          itemElement.style.height = `${this.itemHeight * this.heightScale}px`;\r\n          itemElement.style.boxSizing = 'border-box';\r\n          itemElement.style.width = '100%';\r\n\r\n          this.contentContainer.appendChild(itemElement);\r\n        }\r\n      } else {\r\n        // Use default rendering - very simple default implementation\r\n        const row = document.createElement('div');\r\n        Object.assign(row.style, {\r\n          height: `${this.itemHeight * this.heightScale}px`,\r\n          width: '100%',\r\n          boxSizing: 'border-box',\r\n          padding: '8px',\r\n          borderBottom: '1px solid #eee'\r\n        });\r\n        row.textContent = JSON.stringify(item);\r\n        this.contentContainer.appendChild(row);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update data items and re-render\r\n   * @param {Array} items New data items array\r\n   * @public\r\n   */\r\n  updateItems(items) {\r\n    this.items = items || [];\r\n    this.totalHeight = this.items.length * this.itemHeight;\r\n\r\n    // Recalculate scaling factor\r\n    this.heightScale = 1;\r\n    if (this.totalHeight > this.maxHeight) {\r\n      this.heightScale = this.maxHeight / this.totalHeight;\r\n    }\r\n\r\n    // Ensure height is set correctly\r\n    if (this.contentWrapper) {\r\n      this.contentWrapper.style.height = `${this.totalHeight * this.heightScale}px`;\r\n    }\r\n\r\n    this.visibleStartIndex = 0;\r\n    this.visibleEndIndex = 0;\r\n\r\n    // Force recalculation of visible items\r\n    this.handleScroll();\r\n  }\r\n\r\n  /**\r\n   * Scroll to specified index\r\n   * @param {number} index Index of the item to scroll to\r\n   * @public\r\n   */\r\n  scrollToIndex(index) {\r\n    if (index >= 0 && index < this.items.length) {\r\n      // Apply scaling factor when scrolling\r\n      this.scrollContainer.scrollTop = index * this.itemHeight * this.heightScale;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Destroy component, remove event listeners, etc.\r\n   * @public\r\n   */\r\n  destroy() {\r\n    if (this.scrollContainer) {\r\n      this.scrollContainer.removeEventListener('scroll', this.handleScroll);\r\n    }\r\n    if (this.container) {\r\n      this.container.innerHTML = '';\r\n    }\r\n    this.items = null;\r\n    this.container = null;\r\n    this.scrollContainer = null;\r\n    this.contentWrapper = null;\r\n    this.contentContainer = null;\r\n  }\r\n\r\n  /**\r\n   * Refresh virtual scroll, re-render current visible items\r\n   * @public\r\n   */\r\n  refresh() {\r\n    this.handleScroll();\r\n  }\r\n\r\n  /**\r\n   * Get scroll container element\r\n   * @returns {HTMLElement} Scroll container element\r\n   * @public\r\n   */\r\n  getScrollContainer() {\r\n    return this.scrollContainer;\r\n  }\r\n}\r\n\r\n// Export VirtualScroll class\r\nexport { VirtualScroll };\r\n", "/**\r\n * js-booster - High-performance frontend library\r\n * @version 1.1.3\r\n * <AUTHOR>\r\n * @license MIT\r\n */\r\n\r\nimport { VirtualScroll } from './virtual-scroll';\r\n\r\n// Export all components\r\nexport { VirtualScroll };\r\n\r\n// If in browser environment, add to global object\r\nif (typeof window !== 'undefined') {\r\n  window.JsBooster = {\r\n    VirtualScroll\r\n  };\r\n}\r\n", "import { defineComponent as _defineComponent } from 'vue'\nimport { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"cases-list-container case-list\" }\n\nimport { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';\nimport { VirtualScroll } from 'js-booster';\nimport { CaseResult } from '@/api/interoperationApi';\nimport { ExecutionState } from '@/api/appApi';\n\nconst ITEM_HEIGHT = 36; // 行高\nconst BUFFER_SIZE = 20; // 缓冲区大小\nconst HEADER_HEIGHT = 40; // 表头高度\n\n// 渲染固定表头\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'CaseList',\n  props: {\n    cases: {}\n  },\n  emits: [\"view-detail\"],\n  setup(__props: any, { emit: __emit }) {\n\nconst props = __props;\n\nconst emit = __emit;\n\n// 虚拟滚动相关\nconst casesContainer = ref<HTMLElement | null>(null);\nconst headerContainer = ref<HTMLElement | null>(null);\nlet virtualScroll: any = null;\nconst renderFixedHeader = () => {\n  if (!headerContainer.value) return;\n\n  // 清空表头容器\n  headerContainer.value.innerHTML = '';\n\n  // 创建表头\n  const header = document.createElement('div');\n  header.className = 'header-row';\n  header.style.height = `${HEADER_HEIGHT}px`; // 动态高度\n\n  // ID 列\n  const idHeader = document.createElement('div');\n  idHeader.textContent = 'ID';\n  idHeader.className = 'header-cell-id';\n  header.appendChild(idHeader);\n\n  // 名称列\n  const nameHeader = document.createElement('div');\n  nameHeader.textContent = 'Name';\n  nameHeader.className = 'header-cell-name';\n  header.appendChild(nameHeader);\n\n  // 参数列\n  const paramHeader = document.createElement('div');\n  paramHeader.textContent = 'Parameter';\n  paramHeader.className = 'header-cell-param';\n  header.appendChild(paramHeader);\n\n  // 详情列\n  const detailHeader = document.createElement('div');\n  detailHeader.textContent = 'Detail';\n  detailHeader.className = 'header-cell-detail';\n  header.appendChild(detailHeader);\n\n  // 状态列\n  const statusHeader = document.createElement('div');\n  statusHeader.textContent = 'Status';\n  statusHeader.className = 'header-cell-status';\n  header.appendChild(statusHeader);\n\n  // 添加到表头容器\n  headerContainer.value.appendChild(header);\n};\n\n// 初始化虚拟滚动\nconst initVirtualScroll = () => {\n  if (!casesContainer.value || !props.cases.length) return;\n\n  // 渲染固定表头\n  renderFixedHeader();\n\n  // 如果已经存在虚拟滚动实例，先销毁\n  if (virtualScroll) {\n    virtualScroll.destroy();\n  }\n\n  virtualScroll = new VirtualScroll({\n    container: casesContainer.value,\n    items: props.cases,\n    itemHeight: ITEM_HEIGHT,\n    bufferSize: BUFFER_SIZE,\n    renderItem: (item: CaseResult, index: number) => {\n      // 创建主容器\n      const div = document.createElement('div');\n      div.className = 'case-row';\n      div.onclick = () => emit('view-detail', item);\n\n      // 设置动态样式\n      div.style.height = `${ITEM_HEIGHT}px`;\n      div.style.lineHeight = `${ITEM_HEIGHT}px`;\n      div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';\n      div.style.backgroundColor = item.id % 2 === 0 ? '#fff' : '#fafafa';\n\n      // 添加悬停效果\n      div.onmouseover = () => {\n        div.style.backgroundColor = '#f5f7fa';\n      };\n      div.onmouseout = () => {\n        div.style.backgroundColor = item.id % 2 === 0 ? '#fff' : '#fafafa';\n        // 确保鼠标移出时保持最后一个项目没有底部边框\n        div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';\n      };\n\n      // 创建行内容\n      const rowDiv = document.createElement('div');\n      rowDiv.className = 'case-row-content';\n\n      // ID\n      const idDiv = document.createElement('div');\n      idDiv.textContent = `#${item.id}`;\n      idDiv.className = 'case-cell-id';\n      rowDiv.appendChild(idDiv);\n\n      // 名称\n      const nameDiv = document.createElement('div');\n      nameDiv.textContent = item.name;\n      nameDiv.className = 'case-cell-name';\n      rowDiv.appendChild(nameDiv);\n\n      // 参数\n      const paramDiv = document.createElement('div');\n      paramDiv.textContent = item.parameter;\n      paramDiv.title = item.parameter;\n      paramDiv.className = 'case-cell-param';\n      rowDiv.appendChild(paramDiv);\n\n      // 详情列\n      const detailDiv = document.createElement('div');\n      detailDiv.textContent = item.detail || '-';\n      detailDiv.title = item.detail || '';\n      detailDiv.className = 'case-cell-detail';\n      rowDiv.appendChild(detailDiv);\n\n      // 状态\n      const statusDiv = document.createElement('div');\n      statusDiv.className = 'case-cell-status';\n\n      // 创建状态标签\n      const tagType = getStatusTagType(item.state);\n      const tagText = getStatusText(item.state);\n\n      const tagEl = document.createElement('span');\n      tagEl.className = `el-tag el-tag--${tagType} el-tag--small case-status-tag`;\n      tagEl.textContent = tagText;\n\n      statusDiv.appendChild(tagEl);\n      rowDiv.appendChild(statusDiv);\n\n      div.appendChild(rowDiv);\n\n      return div;\n    }\n  });\n};\n\n// 更新虚拟滚动数据\nconst updateVirtualScroll = () => {\n  if (virtualScroll) {\n    virtualScroll.updateItems(props.cases);\n  } else {\n    nextTick(() => {\n      initVirtualScroll();\n    });\n  }\n};\n\n// 获取状态对应的标签类型\nconst getStatusTagType = (state: string): string => {\n  switch (state) {\n    case ExecutionState.Success:\n      return 'success';\n    case ExecutionState.Running:\n      return 'warning';\n    case ExecutionState.Failure:\n      return 'danger';\n    case ExecutionState.Pending:\n    default:\n      return 'info';\n  }\n};\n\n// 获取状态的文本描述\nconst getStatusText = (state: string): string => {\n  switch (state) {\n    case ExecutionState.Success:\n      return 'Passed';\n    case ExecutionState.Failure:\n      return 'Failed';\n    case ExecutionState.Running:\n      return 'Running';\n    case ExecutionState.Pending:\n      return 'Pending';\n    default:\n      return 'Unknown';\n  }\n};\n\n// 监听 cases 变化，更新虚拟滚动\nwatch(() => props.cases, () => {\n  nextTick(() => {\n    updateVirtualScroll();\n  });\n}, { deep: true });\n\n// 组件挂载时初始化虚拟滚动\nonMounted(() => {\n  nextTick(() => {\n    initVirtualScroll();\n  });\n});\n\n// 组件卸载时销毁虚拟滚动\nonUnmounted(() => {\n  if (virtualScroll) {\n    virtualScroll.destroy();\n    virtualScroll = null;\n  }\n});\n\nreturn (_ctx: any,_cache: any) => {\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", {\n      class: \"cases-header\",\n      ref_key: \"headerContainer\",\n      ref: headerContainer\n    }, null, 512),\n    _createElementVNode(\"div\", {\n      ref_key: \"casesContainer\",\n      ref: casesContainer,\n      class: \"cases-content\"\n    }, null, 512)\n  ]))\n}\n}\n\n})", "<template>\n  <div class=\"cases-list-container case-list\">\n    <!-- 固定表头 -->\n    <div class=\"cases-header\" ref=\"headerContainer\"></div>\n    <!-- 虚拟滚动内容容器 -->\n    <div ref=\"casesContainer\" class=\"cases-content\"></div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, onMounted, onUnmounted, watch, nextTick, defineProps, defineEmits } from 'vue';\nimport { VirtualScroll } from 'js-booster';\nimport { CaseResult } from '@/api/interoperationApi';\nimport { ExecutionState } from '@/api/appApi';\n\nconst props = defineProps<{\n  cases: CaseResult[];\n}>();\n\nconst emit = defineEmits<{\n  (e: 'view-detail', caseResult: CaseResult): void;\n}>();\n\n// 虚拟滚动相关\nconst casesContainer = ref<HTMLElement | null>(null);\nconst headerContainer = ref<HTMLElement | null>(null);\nlet virtualScroll: any = null;\nconst ITEM_HEIGHT = 36; // 行高\nconst BUFFER_SIZE = 20; // 缓冲区大小\nconst HEADER_HEIGHT = 40; // 表头高度\n\n// 渲染固定表头\nconst renderFixedHeader = () => {\n  if (!headerContainer.value) return;\n\n  // 清空表头容器\n  headerContainer.value.innerHTML = '';\n\n  // 创建表头\n  const header = document.createElement('div');\n  header.className = 'header-row';\n  header.style.height = `${HEADER_HEIGHT}px`; // 动态高度\n\n  // ID 列\n  const idHeader = document.createElement('div');\n  idHeader.textContent = 'ID';\n  idHeader.className = 'header-cell-id';\n  header.appendChild(idHeader);\n\n  // 名称列\n  const nameHeader = document.createElement('div');\n  nameHeader.textContent = 'Name';\n  nameHeader.className = 'header-cell-name';\n  header.appendChild(nameHeader);\n\n  // 参数列\n  const paramHeader = document.createElement('div');\n  paramHeader.textContent = 'Parameter';\n  paramHeader.className = 'header-cell-param';\n  header.appendChild(paramHeader);\n\n  // 详情列\n  const detailHeader = document.createElement('div');\n  detailHeader.textContent = 'Detail';\n  detailHeader.className = 'header-cell-detail';\n  header.appendChild(detailHeader);\n\n  // 状态列\n  const statusHeader = document.createElement('div');\n  statusHeader.textContent = 'Status';\n  statusHeader.className = 'header-cell-status';\n  header.appendChild(statusHeader);\n\n  // 添加到表头容器\n  headerContainer.value.appendChild(header);\n};\n\n// 初始化虚拟滚动\nconst initVirtualScroll = () => {\n  if (!casesContainer.value || !props.cases.length) return;\n\n  // 渲染固定表头\n  renderFixedHeader();\n\n  // 如果已经存在虚拟滚动实例，先销毁\n  if (virtualScroll) {\n    virtualScroll.destroy();\n  }\n\n  virtualScroll = new VirtualScroll({\n    container: casesContainer.value,\n    items: props.cases,\n    itemHeight: ITEM_HEIGHT,\n    bufferSize: BUFFER_SIZE,\n    renderItem: (item: CaseResult, index: number) => {\n      // 创建主容器\n      const div = document.createElement('div');\n      div.className = 'case-row';\n      div.onclick = () => emit('view-detail', item);\n\n      // 设置动态样式\n      div.style.height = `${ITEM_HEIGHT}px`;\n      div.style.lineHeight = `${ITEM_HEIGHT}px`;\n      div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';\n      div.style.backgroundColor = item.id % 2 === 0 ? '#fff' : '#fafafa';\n\n      // 添加悬停效果\n      div.onmouseover = () => {\n        div.style.backgroundColor = '#f5f7fa';\n      };\n      div.onmouseout = () => {\n        div.style.backgroundColor = item.id % 2 === 0 ? '#fff' : '#fafafa';\n        // 确保鼠标移出时保持最后一个项目没有底部边框\n        div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';\n      };\n\n      // 创建行内容\n      const rowDiv = document.createElement('div');\n      rowDiv.className = 'case-row-content';\n\n      // ID\n      const idDiv = document.createElement('div');\n      idDiv.textContent = `#${item.id}`;\n      idDiv.className = 'case-cell-id';\n      rowDiv.appendChild(idDiv);\n\n      // 名称\n      const nameDiv = document.createElement('div');\n      nameDiv.textContent = item.name;\n      nameDiv.className = 'case-cell-name';\n      rowDiv.appendChild(nameDiv);\n\n      // 参数\n      const paramDiv = document.createElement('div');\n      paramDiv.textContent = item.parameter;\n      paramDiv.title = item.parameter;\n      paramDiv.className = 'case-cell-param';\n      rowDiv.appendChild(paramDiv);\n\n      // 详情列\n      const detailDiv = document.createElement('div');\n      detailDiv.textContent = item.detail || '-';\n      detailDiv.title = item.detail || '';\n      detailDiv.className = 'case-cell-detail';\n      rowDiv.appendChild(detailDiv);\n\n      // 状态\n      const statusDiv = document.createElement('div');\n      statusDiv.className = 'case-cell-status';\n\n      // 创建状态标签\n      const tagType = getStatusTagType(item.state);\n      const tagText = getStatusText(item.state);\n\n      const tagEl = document.createElement('span');\n      tagEl.className = `el-tag el-tag--${tagType} el-tag--small case-status-tag`;\n      tagEl.textContent = tagText;\n\n      statusDiv.appendChild(tagEl);\n      rowDiv.appendChild(statusDiv);\n\n      div.appendChild(rowDiv);\n\n      return div;\n    }\n  });\n};\n\n// 更新虚拟滚动数据\nconst updateVirtualScroll = () => {\n  if (virtualScroll) {\n    virtualScroll.updateItems(props.cases);\n  } else {\n    nextTick(() => {\n      initVirtualScroll();\n    });\n  }\n};\n\n// 获取状态对应的标签类型\nconst getStatusTagType = (state: string): string => {\n  switch (state) {\n    case ExecutionState.Success:\n      return 'success';\n    case ExecutionState.Running:\n      return 'warning';\n    case ExecutionState.Failure:\n      return 'danger';\n    case ExecutionState.Pending:\n    default:\n      return 'info';\n  }\n};\n\n// 获取状态的文本描述\nconst getStatusText = (state: string): string => {\n  switch (state) {\n    case ExecutionState.Success:\n      return 'Passed';\n    case ExecutionState.Failure:\n      return 'Failed';\n    case ExecutionState.Running:\n      return 'Running';\n    case ExecutionState.Pending:\n      return 'Pending';\n    default:\n      return 'Unknown';\n  }\n};\n\n// 监听 cases 变化，更新虚拟滚动\nwatch(() => props.cases, () => {\n  nextTick(() => {\n    updateVirtualScroll();\n  });\n}, { deep: true });\n\n// 组件挂载时初始化虚拟滚动\nonMounted(() => {\n  nextTick(() => {\n    initVirtualScroll();\n  });\n});\n\n// 组件卸载时销毁虚拟滚动\nonUnmounted(() => {\n  if (virtualScroll) {\n    virtualScroll.destroy();\n    virtualScroll = null;\n  }\n});\n</script>\n\n<style scoped lang=\"scss\">\n.cases-list-container {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  font-size: 13px;\n  flex: 1;\n  overflow: auto;\n}\n\n.cases-header {\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  background-color: #f5f7fa;\n}\n\n.cases-content {\n  flex: 1;\n  overflow: auto;\n  position: relative;\n}\n</style>\n", "import script from \"./CaseList.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./CaseList.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./CaseList.vue?vue&type=style&index=0&id=89a1292a&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-89a1292a\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, toDisplayString as _toDisplayString, withCtx as _withCtx, unref as _unref, withModifiers as _withModifiers, normalizeClass as _normalizeClass, createTextVNode as _createTextVNode, vModelCheckbox as _vModelCheckbox, withDirectives as _withDirectives, resolveDirective as _resolveDirective } from \"vue\"\n\nconst _hoisted_1 = { class: \"test-results-container\" }\nconst _hoisted_2 = { class: \"content-area\" }\nconst _hoisted_3 = {\n  key: 0,\n  class: \"test-results-page\"\n}\nconst _hoisted_4 = { class: \"results-table-container\" }\nconst _hoisted_5 = {\n  key: 0,\n  class: \"loading-container\"\n}\nconst _hoisted_6 = {\n  key: 1,\n  class: \"empty-container\"\n}\nconst _hoisted_7 = {\n  key: 2,\n  class: \"results-table\"\n}\nconst _hoisted_8 = [\"onClick\"]\nconst _hoisted_9 = { class: \"column-name\" }\nconst _hoisted_10 = { class: \"result-name text-ellipsis\" }\nconst _hoisted_11 = { class: \"column-start-time\" }\nconst _hoisted_12 = { class: \"column-duration\" }\nconst _hoisted_13 = { class: \"column-passed\" }\nconst _hoisted_14 = { class: \"success-count\" }\nconst _hoisted_15 = { class: \"column-failed\" }\nconst _hoisted_16 = { class: \"failure-count\" }\nconst _hoisted_17 = { class: \"column-total\" }\nconst _hoisted_18 = { class: \"total-count\" }\nconst _hoisted_19 = { class: \"column-actions\" }\nconst _hoisted_20 = { class: \"action-buttons\" }\nconst _hoisted_21 = { key: 0 }\nconst _hoisted_22 = {\n  key: 1,\n  class: \"test-cases-page\"\n}\nconst _hoisted_23 = { class: \"page-header\" }\nconst _hoisted_24 = {\n  class: \"case-list-container\",\n  \"element-loading-text\": \"Loading test cases...\"\n}\nconst _hoisted_25 = { class: \"filters\" }\nconst _hoisted_26 = { class: \"filter-left\" }\nconst _hoisted_27 = { class: \"checkbox-label\" }\nconst _hoisted_28 = { class: \"checkbox-label\" }\nconst _hoisted_29 = { class: \"checkbox-label\" }\n\nimport { ref, onMounted, computed } from 'vue';\r\nimport { ElMessage, ElMessageBox } from 'element-plus';\r\nimport { appApi, TestResult } from '@/api/appApi';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport CaseList from '@/components/test/CaseList.vue';\r\nimport CaseDetailDialog from '@/components/test/CaseDetailDialog.vue';\r\nimport { Delete, Download, Back } from '@element-plus/icons-vue';\r\n\r\n// 状态变量\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TestResults',\n  setup(__props) {\n\r\nconst testResults = ref<TestResult[]>([]);\r\nconst caseResults = ref<CaseResult[]>([]);\r\nconst selectedTestId = ref<string | null>(null);\r\nconst selectedCaseId = ref<number | null>(null);\r\nconst showCaseDetail = ref(false);\r\n\r\n// 加载状态\r\nconst loadingTestResults = ref(true);\r\nconst loadingCases = ref(false);\r\nconst downloadingReport = ref(false);\r\n\r\n// 用例详情对话框相关\r\nconst detailDialogVisible = ref(false);\r\n\r\n// 筛选条件 - 修改默认值为全部不勾选\r\nconst filterAll = ref(false);\r\nconst filterPassed = ref(false);\r\nconst filterFailed = ref(false);\r\n\r\n// 计算属性：获取选中的测试名称\r\nconst selectedTestName = computed(() => {\r\n  if (!selectedTestId.value) return '';\r\n  const selectedTest = testResults.value.find(test => test.id === selectedTestId.value);\r\n  return selectedTest ? selectedTest.resultFolderName : '';\r\n});\r\n\r\n// 筛选后的测试用例结果 - 修改筛选逻辑\r\nconst filteredCaseResults = computed(() => {\r\n  // 如果全部没选中，则显示所有测试用例\r\n  if (!filterPassed.value && !filterFailed.value) {\r\n    return caseResults.value;\r\n  }\r\n\r\n  return caseResults.value.filter(caseResult => {\r\n    const isPassed = caseResult.state === \"Success\";\r\n    const isFailed = caseResult.state === \"Failure\";\r\n\r\n    return (isPassed && filterPassed.value) || (isFailed && filterFailed.value);\r\n  });\r\n});\r\n\r\n// 获取测试结果列表\r\nconst fetchTestResults = async () => {\r\n  loadingTestResults.value = true;\r\n  try {\r\n    const response = await appApi.getTestResults();\r\n    testResults.value = response.data;\r\n  } catch (error) {\r\n    console.error('获取测试结果列表失败:', error);\r\n    ElMessage.error('Failed to fetch test results');\r\n  } finally {\r\n    loadingTestResults.value = false;\r\n  }\r\n};\r\n\r\n// 处理表格行点击\r\nconst handleRowClick = (row: TestResult) => {\r\n  selectTestResult(row.id);\r\n};\r\n\r\n// 选择测试结果\r\nconst selectTestResult = async (testResultId: string) => {\r\n  selectedTestId.value = testResultId;\r\n  showCaseDetail.value = true;\r\n  await fetchCases(testResultId);\r\n};\r\n\r\n// 获取测试用例列表\r\nconst fetchCases = async (testResultId: string) => {\r\n  loadingCases.value = true;\r\n  caseResults.value = []; // 清空之前的用例\r\n\r\n  try {\r\n    const response = await appApi.getCases(testResultId);\r\n    caseResults.value = response.data;\r\n  } catch (error) {\r\n    console.error('获取测试用例列表失败:', error);\r\n    ElMessage.error('Failed to fetch case results');\r\n  } finally {\r\n    loadingCases.value = false;\r\n  }\r\n};\r\n\r\n// 确认删除\r\nconst confirmDelete = (result: TestResult) => {\r\n  ElMessageBox.confirm(\r\n    `Are you sure you want to delete test result \"${result.resultFolderName}\"?`,\r\n    'Warning',\r\n    {\r\n      confirmButtonText: 'Delete',\r\n      cancelButtonText: 'Cancel',\r\n      type: 'warning',\r\n    }\r\n  )\r\n    .then(() => {\r\n      deleteTestResult(result.id);\r\n    })\r\n    .catch(() => {\r\n      // 用户取消\r\n    });\r\n};\r\n\r\n// 删除测试结果\r\nconst deleteTestResult = async (testResultId: string) => {\r\n  try {\r\n    await appApi.deleteTestResult(testResultId);\r\n    ElMessage.success('Test result deleted successfully');\r\n\r\n    // 刷新测试结果列表\r\n    await fetchTestResults();\r\n\r\n    // 如果删除的是当前选中的测试结果，返回到结果列表\r\n    if (selectedTestId.value === testResultId) {\r\n      selectedTestId.value = null;\r\n      caseResults.value = [];\r\n      showCaseDetail.value = false;\r\n    }\r\n  } catch (error) {\r\n    console.error('删除测试结果失败:', error);\r\n    ElMessage.error('Failed to delete test result');\r\n  }\r\n};\r\n\r\n// 下载HTML报告\r\nconst downloadHtmlReport = async (testResultId: string) => {\r\n  if (!testResultId) {\r\n    ElMessage.warning('No test result selected for report generation');\r\n    return;\r\n  }\r\n\r\n  downloadingReport.value = true;\r\n  try {\r\n    await appApi.downloadHtmlReport(testResultId);\r\n    ElMessage.success('Report downloaded successfully');\r\n  } catch (error) {\r\n    console.error('Download report failed:', error);\r\n    ElMessage.error('Failed to download test report');\r\n  } finally {\r\n    downloadingReport.value = false;\r\n  }\r\n};\r\n\r\n// 查看用例详情\r\nconst viewCaseDetail = (caseResult: CaseResult) => {\r\n  selectedCaseId.value = caseResult.id;\r\n  detailDialogVisible.value = true;\r\n};\r\n\r\n// 关闭详情对话框\r\nconst closeDetailDialog = () => {\r\n  detailDialogVisible.value = false;\r\n  selectedCaseId.value = null;\r\n};\r\n\r\n// 格式化日期 - 完整格式（用于tooltip）\r\nconst formatDateFull = (dateString?: string | null) => {\r\n  if (!dateString) return null;\r\n  try {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleString();\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\n// 格式化日期 - 紧凑格式（用于表格显示）\r\nconst formatDateCompact = (dateString?: string | null) => {\r\n  if (!dateString) return null;\r\n  try {\r\n    const date = new Date(dateString);\r\n    // 只显示时间部分，或者只显示日期和时间的小时和分钟\r\n    const today = new Date();\r\n    const isToday = date.getDate() === today.getDate() &&\r\n      date.getMonth() === today.getMonth() &&\r\n      date.getFullYear() === today.getFullYear();\r\n\r\n    if (isToday) {\r\n      // 如果是今天，只显示时间\r\n      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });\r\n    } else {\r\n      // 否则显示日期和时间，但格式更紧凑\r\n      return date.toLocaleDateString([], { month: '2-digit', day: '2-digit' }) +\r\n        ' ' +\r\n        date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\r\n    }\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\n// 计算持续时间\r\nconst calculateDuration = (startTime?: string | null, endTime?: string | null) => {\r\n  if (!startTime || !endTime) return '-';\r\n\r\n  try {\r\n    const start = new Date(startTime);\r\n    const end = new Date(endTime);\r\n    const durationMs = end.getTime() - start.getTime();\r\n\r\n    if (durationMs < 0) return '-'; // 防止负值\r\n\r\n    if (durationMs < 1000) {\r\n      return `${durationMs}ms`;\r\n    } else if (durationMs < 60000) {\r\n      const seconds = Math.floor(durationMs / 1000);\r\n      return `${seconds}s`;\r\n    } else if (durationMs < 3600000) {\r\n      const minutes = Math.floor(durationMs / 60000);\r\n      const seconds = Math.floor((durationMs % 60000) / 1000);\r\n      return `${minutes}m ${seconds}s`;\r\n    } else {\r\n      const hours = Math.floor(durationMs / 3600000);\r\n      const minutes = Math.floor((durationMs % 3600000) / 60000);\r\n      return `${hours}h ${minutes}m`;\r\n    }\r\n  } catch (e) {\r\n    console.error('Error calculating duration:', e);\r\n    return '-';\r\n  }\r\n};\r\n\r\n// 处理\"全部\"复选框变更\r\nconst handleFilterAllChange = () => {\r\n  filterPassed.value = filterAll.value;\r\n  filterFailed.value = filterAll.value;\r\n};\r\n\r\n// 处理其他复选框变更 - 移除强制选中逻辑\r\nconst handleFilterChange = () => {\r\n  // 如果全部选中，则\"全部\"复选框也应该选中\r\n  filterAll.value = filterPassed.value && filterFailed.value;\r\n\r\n  // 移除强制选中一项的逻辑，允许全部取消选中\r\n};\r\n\r\n// 组件挂载时获取测试结果列表\r\nonMounted(() => {\r\n  fetchTestResults();\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\")!\n  const _component_el_empty = _resolveComponent(\"el-empty\")!\n  const _component_el_tooltip = _resolveComponent(\"el-tooltip\")!\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _directive_loading = _resolveDirective(\"loading\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      (!showCaseDetail.value)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [\n            _cache[7] || (_cache[7] = _createElementVNode(\"div\", { class: \"page-header\" }, [\n              _createElementVNode(\"h3\", null, \"Test Results\")\n            ], -1)),\n            _createElementVNode(\"div\", _hoisted_4, [\n              (loadingTestResults.value)\n                ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [\n                    _createVNode(_component_el_skeleton, {\n                      rows: 5,\n                      animated: \"\"\n                    })\n                  ]))\n                : (testResults.value.length === 0)\n                  ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [\n                      _createVNode(_component_el_empty, { description: \"No test results found\" })\n                    ]))\n                  : (_openBlock(), _createElementBlock(\"table\", _hoisted_7, [\n                      _cache[6] || (_cache[6] = _createElementVNode(\"thead\", null, [\n                        _createElementVNode(\"tr\", null, [\n                          _createElementVNode(\"th\", { class: \"column-name\" }, \"Name\"),\n                          _createElementVNode(\"th\", { class: \"column-start-time\" }, \"Start Time\"),\n                          _createElementVNode(\"th\", { class: \"column-duration\" }, \"Duration\"),\n                          _createElementVNode(\"th\", { class: \"column-passed\" }, \"Passed\"),\n                          _createElementVNode(\"th\", { class: \"column-failed\" }, \"Failed\"),\n                          _createElementVNode(\"th\", { class: \"column-total\" }, \"Total\"),\n                          _createElementVNode(\"th\", { class: \"column-actions\" }, \"Actions\")\n                        ])\n                      ], -1)),\n                      _createElementVNode(\"tbody\", null, [\n                        (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(testResults.value, (row, index) => {\n                          return (_openBlock(), _createElementBlock(\"tr\", {\n                            key: row.id,\n                            class: _normalizeClass({ 'row-stripe': index % 2 === 1 }),\n                            onClick: ($event: any) => (handleRowClick(row))\n                          }, [\n                            _createElementVNode(\"td\", _hoisted_9, [\n                              _createVNode(_component_el_tooltip, {\n                                content: row.resultFolderName,\n                                placement: \"top\",\n                                \"show-after\": 500\n                              }, {\n                                default: _withCtx(() => [\n                                  _createElementVNode(\"span\", _hoisted_10, _toDisplayString(row.resultFolderName), 1)\n                                ]),\n                                _: 2\n                              }, 1032, [\"content\"])\n                            ]),\n                            _createElementVNode(\"td\", _hoisted_11, [\n                              _createVNode(_component_el_tooltip, {\n                                content: formatDateFull(row.creationTime),\n                                placement: \"top\",\n                                \"show-after\": 500\n                              }, {\n                                default: _withCtx(() => [\n                                  _createElementVNode(\"span\", null, _toDisplayString(formatDateCompact(row.creationTime)), 1)\n                                ]),\n                                _: 2\n                              }, 1032, [\"content\"])\n                            ]),\n                            _createElementVNode(\"td\", _hoisted_12, [\n                              _createElementVNode(\"span\", null, _toDisplayString(calculateDuration(row.creationTime, row.end)), 1)\n                            ]),\n                            _createElementVNode(\"td\", _hoisted_13, [\n                              _createElementVNode(\"span\", _hoisted_14, _toDisplayString(row.successCount), 1)\n                            ]),\n                            _createElementVNode(\"td\", _hoisted_15, [\n                              _createElementVNode(\"span\", _hoisted_16, _toDisplayString(row.failureCount), 1)\n                            ]),\n                            _createElementVNode(\"td\", _hoisted_17, [\n                              _createElementVNode(\"span\", _hoisted_18, _toDisplayString(row.totalCount), 1)\n                            ]),\n                            _createElementVNode(\"td\", _hoisted_19, [\n                              _createElementVNode(\"div\", _hoisted_20, [\n                                _createVNode(_component_el_button, {\n                                  type: \"primary\",\n                                  size: \"small\",\n                                  onClick: _withModifiers(($event: any) => (downloadHtmlReport(row.id)), [\"stop\"]),\n                                  title: 'Download Report'\n                                }, {\n                                  default: _withCtx(() => [\n                                    _createVNode(_component_el_icon, null, {\n                                      default: _withCtx(() => [\n                                        _createVNode(_unref(Download))\n                                      ]),\n                                      _: 1\n                                    })\n                                  ]),\n                                  _: 2\n                                }, 1032, [\"onClick\"]),\n                                _createVNode(_component_el_button, {\n                                  type: \"danger\",\n                                  size: \"small\",\n                                  onClick: _withModifiers(($event: any) => (confirmDelete(row)), [\"stop\"]),\n                                  title: 'Delete Result'\n                                }, {\n                                  default: _withCtx(() => [\n                                    _createVNode(_component_el_icon, null, {\n                                      default: _withCtx(() => [\n                                        _createVNode(_unref(Delete))\n                                      ]),\n                                      _: 1\n                                    })\n                                  ]),\n                                  _: 2\n                                }, 1032, [\"onClick\"])\n                              ])\n                            ])\n                          ], 10, _hoisted_8))\n                        }), 128)),\n                        (testResults.value.length === 0)\n                          ? (_openBlock(), _createElementBlock(\"tr\", _hoisted_21, _cache[5] || (_cache[5] = [\n                              _createElementVNode(\"td\", {\n                                colspan: \"7\",\n                                class: \"empty-row\"\n                              }, \"No data\", -1)\n                            ])))\n                          : _createCommentVNode(\"\", true)\n                      ])\n                    ]))\n            ])\n          ]))\n        : (_openBlock(), _createElementBlock(\"div\", _hoisted_22, [\n            _createElementVNode(\"div\", _hoisted_23, [\n              _createVNode(_component_el_button, {\n                type: \"primary\",\n                size: \"small\",\n                onClick: _cache[0] || (_cache[0] = ($event: any) => (showCaseDetail.value = false)),\n                class: \"back-button\"\n              }, {\n                default: _withCtx(() => [\n                  _createVNode(_component_el_icon, null, {\n                    default: _withCtx(() => [\n                      _createVNode(_unref(Back))\n                    ]),\n                    _: 1\n                  }),\n                  _cache[8] || (_cache[8] = _createTextVNode(\" Back \"))\n                ]),\n                _: 1\n              }),\n              _createElementVNode(\"h3\", null, _toDisplayString(selectedTestName.value), 1)\n            ]),\n            _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_24, [\n              _createElementVNode(\"div\", _hoisted_25, [\n                _createElementVNode(\"div\", _hoisted_26, [\n                  _createElementVNode(\"label\", _hoisted_27, [\n                    _withDirectives(_createElementVNode(\"input\", {\n                      type: \"checkbox\",\n                      id: \"filter-all\",\n                      \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((filterAll).value = $event)),\n                      onChange: handleFilterAllChange\n                    }, null, 544), [\n                      [_vModelCheckbox, filterAll.value]\n                    ]),\n                    _cache[9] || (_cache[9] = _createElementVNode(\"span\", { class: \"filter-checkbox filter-all\" }, \"All\", -1))\n                  ]),\n                  _createElementVNode(\"label\", _hoisted_28, [\n                    _withDirectives(_createElementVNode(\"input\", {\n                      type: \"checkbox\",\n                      id: \"filter-passed\",\n                      \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((filterPassed).value = $event)),\n                      onChange: handleFilterChange\n                    }, null, 544), [\n                      [_vModelCheckbox, filterPassed.value]\n                    ]),\n                    _cache[10] || (_cache[10] = _createElementVNode(\"span\", { class: \"filter-checkbox filter-passed\" }, \"Passed\", -1))\n                  ]),\n                  _createElementVNode(\"label\", _hoisted_29, [\n                    _withDirectives(_createElementVNode(\"input\", {\n                      type: \"checkbox\",\n                      id: \"filter-failed\",\n                      \"onUpdate:modelValue\": _cache[3] || (_cache[3] = ($event: any) => ((filterFailed).value = $event)),\n                      onChange: handleFilterChange\n                    }, null, 544), [\n                      [_vModelCheckbox, filterFailed.value]\n                    ]),\n                    _cache[11] || (_cache[11] = _createElementVNode(\"span\", { class: \"filter-checkbox filter-failed\" }, \"Failed\", -1))\n                  ])\n                ])\n              ]),\n              _createVNode(CaseList, {\n                cases: filteredCaseResults.value,\n                onViewDetail: viewCaseDetail\n              }, null, 8, [\"cases\"])\n            ])), [\n              [_directive_loading, loadingCases.value]\n            ])\n          ]))\n    ]),\n    _createVNode(CaseDetailDialog, {\n      visible: detailDialogVisible.value,\n      \"onUpdate:visible\": _cache[4] || (_cache[4] = ($event: any) => ((detailDialogVisible).value = $event)),\n      testResultId: selectedTestId.value,\n      caseResultId: selectedCaseId.value,\n      onClose: closeDetailDialog\n    }, null, 8, [\"visible\", \"testResultId\", \"caseResultId\"])\n  ]))\n}\n}\n\n})", "<template>\r\n  <div class=\"test-results-container\">\r\n    <!-- 主内容区域 - 单页布局 -->\r\n    <div class=\"content-area\">\r\n      <!-- 测试结果列表页面 -->\r\n      <div v-if=\"!showCaseDetail\" class=\"test-results-page\">\r\n        <div class=\"page-header\">\r\n          <h3>Test Results</h3>\r\n        </div>\r\n\r\n        <!-- 测试结果表格 -->\r\n        <div class=\"results-table-container\">\r\n          <div v-if=\"loadingTestResults\" class=\"loading-container\">\r\n            <el-skeleton :rows=\"5\" animated />\r\n          </div>\r\n\r\n          <div v-else-if=\"testResults.length === 0\" class=\"empty-container\">\r\n            <el-empty description=\"No test results found\" />\r\n          </div>\r\n\r\n          <table v-else class=\"results-table\">\r\n            <thead>\r\n              <tr>\r\n                <th class=\"column-name\">Name</th>\r\n                <th class=\"column-start-time\">Start Time</th>\r\n                <th class=\"column-duration\">Duration</th>\r\n                <th class=\"column-passed\">Passed</th>\r\n                <th class=\"column-failed\">Failed</th>\r\n                <th class=\"column-total\">Total</th>\r\n                <th class=\"column-actions\">Actions</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr v-for=\"(row, index) in testResults\" :key=\"row.id\" :class=\"{ 'row-stripe': index % 2 === 1 }\"\r\n                @click=\"handleRowClick(row)\">\r\n                <td class=\"column-name\">\r\n                  <el-tooltip :content=\"row.resultFolderName\" placement=\"top\" :show-after=\"500\">\r\n                    <span class=\"result-name text-ellipsis\">{{ row.resultFolderName }}</span>\r\n                  </el-tooltip>\r\n                </td>\r\n                <td class=\"column-start-time\">\r\n                  <el-tooltip :content=\"formatDateFull(row.creationTime)\" placement=\"top\" :show-after=\"500\">\r\n                    <span>{{ formatDateCompact(row.creationTime) }}</span>\r\n                  </el-tooltip>\r\n                </td>\r\n                <td class=\"column-duration\">\r\n                  <span>{{ calculateDuration(row.creationTime, row.end) }}</span>\r\n                </td>\r\n                <td class=\"column-passed\">\r\n                  <span class=\"success-count\">{{ row.successCount }}</span>\r\n                </td>\r\n                <td class=\"column-failed\">\r\n                  <span class=\"failure-count\">{{ row.failureCount }}</span>\r\n                </td>\r\n                <td class=\"column-total\">\r\n                  <span class=\"total-count\">{{ row.totalCount }}</span>\r\n                </td>\r\n                <td class=\"column-actions\">\r\n                  <div class=\"action-buttons\">\r\n                    <el-button type=\"primary\" size=\"small\" @click.stop=\"downloadHtmlReport(row.id)\"\r\n                      :title=\"'Download Report'\">\r\n                      <el-icon>\r\n                        <Download />\r\n                      </el-icon>\r\n                    </el-button>\r\n\r\n                    <el-button type=\"danger\" size=\"small\" @click.stop=\"confirmDelete(row)\" :title=\"'Delete Result'\">\r\n                      <el-icon>\r\n                        <Delete />\r\n                      </el-icon>\r\n                    </el-button>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n              <tr v-if=\"testResults.length === 0\">\r\n                <td colspan=\"7\" class=\"empty-row\">No data</td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 测试用例详情页面 -->\r\n      <div v-else class=\"test-cases-page\">\r\n        <div class=\"page-header\">\r\n          <el-button type=\"primary\" size=\"small\" @click=\"showCaseDetail = false\" class=\"back-button\">\r\n            <el-icon>\r\n              <Back />\r\n            </el-icon>\r\n            Back\r\n          </el-button>\r\n          <h3>{{ selectedTestName }}</h3>\r\n        </div>\r\n\r\n        <!-- 筛选工具栏 -->\r\n        <div class=\"case-list-container\" v-loading=\"loadingCases\" element-loading-text=\"Loading test cases...\">\r\n          <div class=\"filters\">\r\n            <div class=\"filter-left\">\r\n              <label class=\"checkbox-label\">\r\n                <input type=\"checkbox\" id=\"filter-all\" v-model=\"filterAll\" @change=\"handleFilterAllChange\">\r\n                <span class=\"filter-checkbox filter-all\">All</span>\r\n              </label>\r\n              <label class=\"checkbox-label\">\r\n                <input type=\"checkbox\" id=\"filter-passed\" v-model=\"filterPassed\" @change=\"handleFilterChange\">\r\n                <span class=\"filter-checkbox filter-passed\">Passed</span>\r\n              </label>\r\n              <label class=\"checkbox-label\">\r\n                <input type=\"checkbox\" id=\"filter-failed\" v-model=\"filterFailed\" @change=\"handleFilterChange\">\r\n                <span class=\"filter-checkbox filter-failed\">Failed</span>\r\n              </label>\r\n            </div>\r\n          </div>\r\n          <!-- 用例列表 -->\r\n          <CaseList :cases=\"filteredCaseResults\" @view-detail=\"viewCaseDetail\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 添加用例详情对话框组件 -->\r\n    <CaseDetailDialog v-model:visible=\"detailDialogVisible\" :testResultId=\"selectedTestId\"\r\n      :caseResultId=\"selectedCaseId\" @close=\"closeDetailDialog\" />\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, onMounted, computed } from 'vue';\r\nimport { ElMessage, ElMessageBox } from 'element-plus';\r\nimport { appApi, TestResult } from '@/api/appApi';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport CaseList from '@/components/test/CaseList.vue';\r\nimport CaseDetailDialog from '@/components/test/CaseDetailDialog.vue';\r\nimport { Delete, Download, Back } from '@element-plus/icons-vue';\r\n\r\n// 状态变量\r\nconst testResults = ref<TestResult[]>([]);\r\nconst caseResults = ref<CaseResult[]>([]);\r\nconst selectedTestId = ref<string | null>(null);\r\nconst selectedCaseId = ref<number | null>(null);\r\nconst showCaseDetail = ref(false);\r\n\r\n// 加载状态\r\nconst loadingTestResults = ref(true);\r\nconst loadingCases = ref(false);\r\nconst downloadingReport = ref(false);\r\n\r\n// 用例详情对话框相关\r\nconst detailDialogVisible = ref(false);\r\n\r\n// 筛选条件 - 修改默认值为全部不勾选\r\nconst filterAll = ref(false);\r\nconst filterPassed = ref(false);\r\nconst filterFailed = ref(false);\r\n\r\n// 计算属性：获取选中的测试名称\r\nconst selectedTestName = computed(() => {\r\n  if (!selectedTestId.value) return '';\r\n  const selectedTest = testResults.value.find(test => test.id === selectedTestId.value);\r\n  return selectedTest ? selectedTest.resultFolderName : '';\r\n});\r\n\r\n// 筛选后的测试用例结果 - 修改筛选逻辑\r\nconst filteredCaseResults = computed(() => {\r\n  // 如果全部没选中，则显示所有测试用例\r\n  if (!filterPassed.value && !filterFailed.value) {\r\n    return caseResults.value;\r\n  }\r\n\r\n  return caseResults.value.filter(caseResult => {\r\n    const isPassed = caseResult.state === \"Success\";\r\n    const isFailed = caseResult.state === \"Failure\";\r\n\r\n    return (isPassed && filterPassed.value) || (isFailed && filterFailed.value);\r\n  });\r\n});\r\n\r\n// 获取测试结果列表\r\nconst fetchTestResults = async () => {\r\n  loadingTestResults.value = true;\r\n  try {\r\n    const response = await appApi.getTestResults();\r\n    testResults.value = response.data;\r\n  } catch (error) {\r\n    console.error('获取测试结果列表失败:', error);\r\n    ElMessage.error('Failed to fetch test results');\r\n  } finally {\r\n    loadingTestResults.value = false;\r\n  }\r\n};\r\n\r\n// 处理表格行点击\r\nconst handleRowClick = (row: TestResult) => {\r\n  selectTestResult(row.id);\r\n};\r\n\r\n// 选择测试结果\r\nconst selectTestResult = async (testResultId: string) => {\r\n  selectedTestId.value = testResultId;\r\n  showCaseDetail.value = true;\r\n  await fetchCases(testResultId);\r\n};\r\n\r\n// 获取测试用例列表\r\nconst fetchCases = async (testResultId: string) => {\r\n  loadingCases.value = true;\r\n  caseResults.value = []; // 清空之前的用例\r\n\r\n  try {\r\n    const response = await appApi.getCases(testResultId);\r\n    caseResults.value = response.data;\r\n  } catch (error) {\r\n    console.error('获取测试用例列表失败:', error);\r\n    ElMessage.error('Failed to fetch case results');\r\n  } finally {\r\n    loadingCases.value = false;\r\n  }\r\n};\r\n\r\n// 确认删除\r\nconst confirmDelete = (result: TestResult) => {\r\n  ElMessageBox.confirm(\r\n    `Are you sure you want to delete test result \"${result.resultFolderName}\"?`,\r\n    'Warning',\r\n    {\r\n      confirmButtonText: 'Delete',\r\n      cancelButtonText: 'Cancel',\r\n      type: 'warning',\r\n    }\r\n  )\r\n    .then(() => {\r\n      deleteTestResult(result.id);\r\n    })\r\n    .catch(() => {\r\n      // 用户取消\r\n    });\r\n};\r\n\r\n// 删除测试结果\r\nconst deleteTestResult = async (testResultId: string) => {\r\n  try {\r\n    await appApi.deleteTestResult(testResultId);\r\n    ElMessage.success('Test result deleted successfully');\r\n\r\n    // 刷新测试结果列表\r\n    await fetchTestResults();\r\n\r\n    // 如果删除的是当前选中的测试结果，返回到结果列表\r\n    if (selectedTestId.value === testResultId) {\r\n      selectedTestId.value = null;\r\n      caseResults.value = [];\r\n      showCaseDetail.value = false;\r\n    }\r\n  } catch (error) {\r\n    console.error('删除测试结果失败:', error);\r\n    ElMessage.error('Failed to delete test result');\r\n  }\r\n};\r\n\r\n// 下载HTML报告\r\nconst downloadHtmlReport = async (testResultId: string) => {\r\n  if (!testResultId) {\r\n    ElMessage.warning('No test result selected for report generation');\r\n    return;\r\n  }\r\n\r\n  downloadingReport.value = true;\r\n  try {\r\n    await appApi.downloadHtmlReport(testResultId);\r\n    ElMessage.success('Report downloaded successfully');\r\n  } catch (error) {\r\n    console.error('Download report failed:', error);\r\n    ElMessage.error('Failed to download test report');\r\n  } finally {\r\n    downloadingReport.value = false;\r\n  }\r\n};\r\n\r\n// 查看用例详情\r\nconst viewCaseDetail = (caseResult: CaseResult) => {\r\n  selectedCaseId.value = caseResult.id;\r\n  detailDialogVisible.value = true;\r\n};\r\n\r\n// 关闭详情对话框\r\nconst closeDetailDialog = () => {\r\n  detailDialogVisible.value = false;\r\n  selectedCaseId.value = null;\r\n};\r\n\r\n// 格式化日期 - 完整格式（用于tooltip）\r\nconst formatDateFull = (dateString?: string | null) => {\r\n  if (!dateString) return null;\r\n  try {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleString();\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\n// 格式化日期 - 紧凑格式（用于表格显示）\r\nconst formatDateCompact = (dateString?: string | null) => {\r\n  if (!dateString) return null;\r\n  try {\r\n    const date = new Date(dateString);\r\n    // 只显示时间部分，或者只显示日期和时间的小时和分钟\r\n    const today = new Date();\r\n    const isToday = date.getDate() === today.getDate() &&\r\n      date.getMonth() === today.getMonth() &&\r\n      date.getFullYear() === today.getFullYear();\r\n\r\n    if (isToday) {\r\n      // 如果是今天，只显示时间\r\n      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });\r\n    } else {\r\n      // 否则显示日期和时间，但格式更紧凑\r\n      return date.toLocaleDateString([], { month: '2-digit', day: '2-digit' }) +\r\n        ' ' +\r\n        date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\r\n    }\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\n// 计算持续时间\r\nconst calculateDuration = (startTime?: string | null, endTime?: string | null) => {\r\n  if (!startTime || !endTime) return '-';\r\n\r\n  try {\r\n    const start = new Date(startTime);\r\n    const end = new Date(endTime);\r\n    const durationMs = end.getTime() - start.getTime();\r\n\r\n    if (durationMs < 0) return '-'; // 防止负值\r\n\r\n    if (durationMs < 1000) {\r\n      return `${durationMs}ms`;\r\n    } else if (durationMs < 60000) {\r\n      const seconds = Math.floor(durationMs / 1000);\r\n      return `${seconds}s`;\r\n    } else if (durationMs < 3600000) {\r\n      const minutes = Math.floor(durationMs / 60000);\r\n      const seconds = Math.floor((durationMs % 60000) / 1000);\r\n      return `${minutes}m ${seconds}s`;\r\n    } else {\r\n      const hours = Math.floor(durationMs / 3600000);\r\n      const minutes = Math.floor((durationMs % 3600000) / 60000);\r\n      return `${hours}h ${minutes}m`;\r\n    }\r\n  } catch (e) {\r\n    console.error('Error calculating duration:', e);\r\n    return '-';\r\n  }\r\n};\r\n\r\n// 处理\"全部\"复选框变更\r\nconst handleFilterAllChange = () => {\r\n  filterPassed.value = filterAll.value;\r\n  filterFailed.value = filterAll.value;\r\n};\r\n\r\n// 处理其他复选框变更 - 移除强制选中逻辑\r\nconst handleFilterChange = () => {\r\n  // 如果全部选中，则\"全部\"复选框也应该选中\r\n  filterAll.value = filterPassed.value && filterFailed.value;\r\n\r\n  // 移除强制选中一项的逻辑，允许全部取消选中\r\n};\r\n\r\n// 组件挂载时获取测试结果列表\r\nonMounted(() => {\r\n  fetchTestResults();\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.test-results-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex: 1;\r\n  padding: 15px 20px;\r\n}\r\n\r\n/* 内容区域样式 */\r\n.content-area {\r\n  flex: 1;\r\n  min-height: 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  min-width: 0;\r\n  /* 允许容器缩小到小于内容宽度 */\r\n\r\n  .test-results-page,\r\n  .test-cases-page {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .page-header {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 10px;\r\n    height: 30px;\r\n\r\n    h2,\r\n    h3 {\r\n      margin: 0;\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n      color: #303133;\r\n    }\r\n\r\n    .back-button {\r\n      margin-right: 16px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .el-icon {\r\n        margin-right: 4px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .results-table-container {\r\n    flex: 1;\r\n    overflow: auto;\r\n    border: 1px solid #ebeef5;\r\n    border-radius: 4px;\r\n    box-shadow: none;\r\n    width: 100%;\r\n    min-width: 0;\r\n    /* 允许容器缩小到小于内容宽度 */\r\n\r\n    .loading-container,\r\n    .empty-container {\r\n      padding: 40px;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n    }\r\n\r\n    .result-name {\r\n      font-weight: 500;\r\n      color: var(--el-color-primary);\r\n      font-size: 12px;\r\n    }\r\n\r\n    .text-ellipsis {\r\n      max-width: 100%;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n      display: inline-block;\r\n      width: 100%;\r\n      /* 确保占满整个单元格 */\r\n    }\r\n\r\n    .success-count {\r\n      color: var(--el-color-success);\r\n      font-weight: 500;\r\n      font-size: 12px;\r\n    }\r\n\r\n    .failure-count {\r\n      color: var(--el-color-danger);\r\n      font-weight: 500;\r\n      font-size: 12px;\r\n    }\r\n\r\n    .total-count {\r\n      color: var(--el-color-primary);\r\n      font-weight: 500;\r\n      font-size: 12px;\r\n    }\r\n\r\n    .action-buttons {\r\n      display: flex;\r\n      justify-content: center;\r\n      gap: 4px;\r\n\r\n      .el-button {\r\n        padding: 5px 8px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .case-list-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    flex: 1;\r\n    border: 1px solid #ebeef5;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n  }\r\n}\r\n\r\n/* 普通表格样式 */\r\n.results-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  font-size: 12px;\r\n  table-layout: auto;\r\n\r\n  /* 表头样式 */\r\n  thead {\r\n    tr {\r\n      background-color: #f5f7fa;\r\n\r\n      th {\r\n        color: #606266;\r\n        font-size: 12px;\r\n        height: 40px;\r\n        padding: 4px 6px;\r\n        font-weight: bold;\r\n        text-align: left;\r\n        border-bottom: 1px solid #ebeef5;\r\n      }\r\n    }\r\n  }\r\n\r\n  /* 表格内容样式 */\r\n  tbody {\r\n    tr {\r\n      cursor: pointer;\r\n      height: 36px;\r\n      border-bottom: 1px solid #ebeef5;\r\n\r\n      &:hover {\r\n        background-color: #f5f7fa;\r\n      }\r\n\r\n      &.row-stripe {\r\n        background-color: #fafafa;\r\n\r\n        &:hover {\r\n          background-color: #f5f7fa;\r\n        }\r\n      }\r\n\r\n      td {\r\n        padding: 0 6px;\r\n        text-align: left;\r\n      }\r\n    }\r\n  }\r\n\r\n  /* 列对齐方式 */\r\n  .column-name {\r\n    text-align: left;\r\n    min-width: 80px;\r\n  }\r\n\r\n  .column-start-time {\r\n    text-align: left;\r\n    min-width: 80px;\r\n  }\r\n\r\n  .column-duration {\r\n    text-align: center;\r\n    min-width: 70px;\r\n  }\r\n\r\n  .column-passed,\r\n  .column-failed,\r\n  .column-total {\r\n    text-align: center;\r\n    min-width: 50px;\r\n  }\r\n\r\n  .column-actions {\r\n    text-align: center;\r\n    min-width: 60px;\r\n  }\r\n\r\n  /* 空数据行样式 */\r\n  .empty-row {\r\n    text-align: center;\r\n    padding: 20px;\r\n    color: #909399;\r\n  }\r\n\r\n  /* 优化表格在窄屏幕上的显示 */\r\n  @media (max-width: 768px) {\r\n    font-size: 11px;\r\n\r\n    thead th,\r\n    tbody td {\r\n      padding: 0 4px;\r\n    }\r\n  }\r\n}\r\n\r\n/* 筛选工具栏样式 */\r\n.filters {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #fff;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  padding: 10px 15px;\r\n}\r\n\r\n.filter-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.checkbox-label {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  user-select: none;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.checkbox-label input[type=\"checkbox\"] {\r\n  margin-right: 5px;\r\n}\r\n\r\n.filter-checkbox {\r\n  min-width: 30px;\r\n}\r\n\r\n.filter-all {\r\n  color: var(--el-color-primary);\r\n}\r\n\r\n.filter-passed {\r\n  color: var(--el-color-success);\r\n}\r\n\r\n.filter-failed {\r\n  color: var(--el-color-danger);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .content-area {\r\n    padding: 8px;\r\n\r\n    .page-header {\r\n      flex-direction: column;\r\n      align-items: flex-start;\r\n      gap: 8px;\r\n\r\n      .back-button {\r\n        margin-right: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./TestResults.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TestResults.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./TestResults.vue?vue&type=style&index=0&id=5bb9d170&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-5bb9d170\"]])\n\nexport default __exports__"], "names": ["_defineComponent", "__name", "props", "state", "setup", "__props", "tagType", "computed", "ExecutionState", "Success", "Running", "Failure", "Pending", "getCaseStateName", "stateName", "_ctx", "_cache", "_component_el_tag", "_resolveComponent", "_openBlock", "_createBlock", "type", "value", "size", "style", "default", "_withCtx", "_createTextVNode", "_toDisplayString", "_", "__exports__", "getTimelineItemType", "_hoisted_1", "key", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "visible", "Boolean", "testResultId", "caseResultId", "emits", "emit", "__emit", "dialogVisible", "ref", "caseData", "steps", "loading", "watch", "newValue", "loadCaseData", "async", "caseResponse", "stepsResponse", "Promise", "all", "appApi", "getCaseResult", "getCaseSteps", "data", "error", "console", "ElMessage", "warning", "closeDialog", "formatMicroseconds", "microseconds", "seconds", "toFixed", "onMounted", "_component_el_skeleton", "_component_el_empty", "_component_el_timeline_item", "_component_el_timeline", "_component_el_button", "_component_el_dialog", "modelValue", "$event", "title", "name", "sequenceName", "width", "footer", "_createElementVNode", "_createVNode", "onClick", "_createElementBlock", "rows", "animated", "_createCommentVNode", "_unref", "formatDateTime", "begin", "end", "CaseStateTag", "parameter", "detail", "length", "description", "_Fragment", "_renderList", "step", "id", "hollow", "timestamp", "Completed", "VirtualScroll", "constructor", "options", "this", "container", "items", "itemHeight", "bufferSize", "customRenderItem", "renderItem", "customRenderHeader", "renderHeader", "maxHeight", "visibleStartIndex", "visibleEndIndex", "scrollContainer", "contentWrapper", "contentContainer", "totalHeight", "heightScale", "initialize", "innerHTML", "document", "createElement", "Object", "assign", "flex", "overflow", "position", "minHeight", "height", "boxSizing", "header", "append<PERSON><PERSON><PERSON>", "scaledHeight", "left", "addEventListener", "handleScroll", "bind", "renderVisibleItems", "Math", "min", "scrollTop", "containerHeight", "clientHeight", "realScrollTop", "startIndex", "max", "floor", "endIndex", "ceil", "transform", "i", "item", "itemElement", "row", "padding", "borderBottom", "textContent", "JSON", "stringify", "updateItems", "scrollToIndex", "index", "destroy", "removeEventListener", "refresh", "getScrollContainer", "window", "JsBooster", "ITEM_HEIGHT", "BUFFER_SIZE", "HEADER_HEIGHT", "cases", "casesContainer", "headerContainer", "virtualScroll", "renderFixedHeader", "className", "idHeader", "nameHeader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "statusH<PERSON>er", "initVirtualScroll", "div", "onclick", "lineHeight", "backgroundColor", "on<PERSON><PERSON>ver", "onmouseout", "rowDiv", "idDiv", "nameDiv", "paramDiv", "detailDiv", "statusDiv", "getStatusTagType", "tagText", "getStatusText", "tagEl", "updateVirtualScroll", "nextTick", "deep", "onUnmounted", "ref_key", "_hoisted_27", "_hoisted_28", "_hoisted_29", "testResults", "caseResults", "selectedTestId", "selectedCaseId", "showCaseDetail", "loadingTestResults", "loadingCases", "downloadingReport", "detailDialogVisible", "filterAll", "filterPassed", "filterFailed", "selectedTestName", "selectedTest", "find", "test", "resultFolderName", "filteredCaseResults", "filter", "caseResult", "isPassed", "isFailed", "fetchTestResults", "response", "getTestResults", "handleRowClick", "selectTestResult", "fetchCases", "getCases", "confirmDelete", "result", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "then", "deleteTestResult", "catch", "success", "downloadHtmlReport", "viewCaseDetail", "closeDetailDialog", "formatDateFull", "dateString", "date", "Date", "toLocaleString", "e", "formatDateCompact", "today", "isToday", "getDate", "getMonth", "getFullYear", "toLocaleTimeString", "hour", "minute", "second", "toLocaleDateString", "month", "day", "calculateDuration", "startTime", "endTime", "start", "durationMs", "getTime", "minutes", "hours", "handleFilterAllChange", "handleFilterChange", "_component_el_tooltip", "_component_el_icon", "_directive_loading", "_resolveDirective", "Back", "_withDirectives", "onChange", "_vModelCheckbox", "CaseList", "onViewDetail", "_normalizeClass", "content", "placement", "creationTime", "successCount", "failureCount", "totalCount", "_withModifiers", "Download", "Delete", "colspan", "CaseDetailDialog", "onClose"], "sourceRoot": ""}