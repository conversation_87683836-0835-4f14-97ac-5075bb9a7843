using Alsi.Common.Utils;
using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service;
using ICSharpCode.SharpZipLib.Zip;
using System;
using System.IO;
using System.IO.Compression;
using System.Text;

namespace Alsi.Fuzz.Core.Storage
{
    public class TestPlanStorage
    {
        private const string ManifestFile = "manifest.json";
        private const string ConfigFile = "config.json";
        private const string CaseCollectionFile = "case_collection.json";
        public const string SecurityAccessFile = "SecurityAccess.dll";

        public void Save(string filePath, TestPlan testPlan)
        {
            using (var _ = TestPlanFileLocker.BeginWrite(filePath))
            {
                testPlan.Manifest.Modified = DateTime.Now;

                if (!File.Exists(filePath))
                {
                    // 文件不存在，创建新文件
                    CreateNewTestPlanFile(filePath, testPlan);
                }
                else
                {
                    // 文件存在，使用 Update 模式只更新需要更新的条目
                    UpdateExistingTestPlanFile(filePath, testPlan);
                }
            }
        }

        /// <summary>
        /// 创建新的测试计划文件
        /// </summary>
        private void CreateNewTestPlanFile(string filePath, TestPlan testPlan)
        {
            using (var zipStream = new ZipOutputStream(File.Create(filePath)))
            {
                zipStream.SetLevel(9);

                // 保存manifest.json
                WriteZipEntry(zipStream, ManifestFile, JsonUtils.Serialize(testPlan.Manifest, isIndented: true));

                // 保存config.json
                WriteZipEntry(zipStream, ConfigFile, JsonUtils.Serialize(testPlan.Config, isIndented: true));

                // 保存SecurityAccess.dll（如果存在）
                WriteSecurityAccessDll(zipStream, testPlan.Config.SecurityConfig);
            }
        }

        /// <summary>
        /// 更新现有的测试计划文件，只更新需要更新的条目，保留 CaseCollection
        /// </summary>
        private void UpdateExistingTestPlanFile(string filePath, TestPlan testPlan)
        {
            using (var fileStream = new FileStream(filePath, FileMode.Open))
            {
                using (var zipArchive = new ZipArchive(fileStream, ZipArchiveMode.Update))
                {
                    // 更新 manifest.json
                    var manifestBytes = JsonUtils.SerializeToBytes(testPlan.Manifest, isIndented: true);
                    UpdateZipEntry(zipArchive, ManifestFile, manifestBytes);

                    // 更新 config.json
                    var configBytes = JsonUtils.SerializeToBytes(testPlan.Config, isIndented: true);
                    UpdateZipEntry(zipArchive, ConfigFile, configBytes);

                    // 更新 SecurityAccess.dll
                    UpdateZipEntry(zipArchive, SecurityAccessFile, testPlan.Config.SecurityConfig?.DllBytes);

                    // CaseCollection 不需要更新，保持原样
                }
            }
        }

        public TestPlan Load(string filePath)
        {
            using (var _ = TestPlanFileLocker.BeginWrite(filePath))
            {
                var testPlan = new TestPlan();

                using (var zipStream = new ZipFile(filePath))
                {
                    // 读取manifest.json
                    var manifestEntry = zipStream.GetEntry(ManifestFile);
                    if (manifestEntry == null)
                    {
                        throw new FileNotFoundException("manifest.json not found in test plan file.");
                    }

                    using (var stream = zipStream.GetInputStream(manifestEntry))
                    using (var reader = new StreamReader(stream))
                    {
                        var json = reader.ReadToEnd();
                        testPlan.Manifest = JsonUtils.Deserialize<TestPlanManifest>(json);
                    }

                    // 读取config.json
                    var configEntry = zipStream.GetEntry(ConfigFile);
                    if (configEntry == null)
                    {
                        throw new FileNotFoundException("config.json not found in test plan file.");
                    }

                    using (var stream = zipStream.GetInputStream(configEntry))
                    using (var reader = new StreamReader(stream))
                    {
                        var json = reader.ReadToEnd();
                        testPlan.Config = JsonUtils.Deserialize<TestPlanConfig>(json);
                    }

                    // 读取 Security Access 的 dll
                    var securityAccessEntry = zipStream.GetEntry(SecurityAccessFile);
                    if (securityAccessEntry != null)
                    {
                        using (var stream = zipStream.GetInputStream(securityAccessEntry))
                        using (var memoryStream = new MemoryStream())
                        {
                            byte[] buffer = new byte[4096];
                            int bytesRead;
                            while ((bytesRead = stream.Read(buffer, 0, buffer.Length)) > 0)
                            {
                                memoryStream.Write(buffer, 0, bytesRead);
                            }
                            testPlan.Config.SecurityConfig.DllBytes = memoryStream.ToArray();
                        }
                    }
                }

                // 向前兼容，如果旧的数据，没有设置 CustomName，系统提供一个默认的名称
                var customNameNumber = 0;
                foreach (var sequenceConfig in testPlan.Config.SequenceConfigList)
                {
                    if (!sequenceConfig.IsBuiltIn && string.IsNullOrWhiteSpace(sequenceConfig.CustomName))
                    {
                        ++customNameNumber;
                        sequenceConfig.CustomName = $"Custom Package {customNameNumber}";
                    }
                }

                if (customNameNumber != 0)
                {
                    Save(filePath, testPlan);
                }

                return testPlan;
            }
        }

        public CaseCollection LoadCaseCollection(string filePath)
        {
            using (var _ = TestPlanFileLocker.BeginWrite(filePath))
            {
                using (var zipStream = new ZipFile(filePath))
                {
                    var zipEntry = zipStream.GetEntry(CaseCollectionFile);
                    if (zipEntry == null)
                    {
                        return null;
                    }

                    using (var stream = zipStream.GetInputStream(zipEntry))
                    using (var reader = new StreamReader(stream))
                    {
                        var json = reader.ReadToEnd();
                        return JsonUtils.Deserialize<CaseCollection>(json);
                    }
                }
            }
        }

        public void SaveCaseCollection(string filePath, CaseCollection caseCollection)
        {
            using (var _ = TestPlanFileLocker.BeginWrite(filePath))
            {
                // 序列化为字节数据
                var json = JsonUtils.Serialize(caseCollection);
                var bytes = Encoding.UTF8.GetBytes(json);

                // 使用 ZipArchive 的 Update 模式来更新特定条目
                using (var fileStream = new FileStream(filePath, FileMode.Open))
                {
                    using (var zipArchive = new ZipArchive(fileStream, ZipArchiveMode.Update))
                    {
                        UpdateZipEntry(zipArchive, CaseCollectionFile, bytes);
                    }
                }
            }
        }

        /// <summary>
        /// 写入 ZIP 条目到 ZipOutputStream
        /// </summary>
        private void WriteZipEntry(ZipOutputStream zipStream, string entryName, string content)
        {
            var bytes = Encoding.UTF8.GetBytes(content);
            var entry = new ZipEntry(entryName);
            zipStream.PutNextEntry(entry);
            zipStream.Write(bytes, 0, bytes.Length);
        }

        /// <summary>
        /// 写入 SecurityAccess.dll 到 ZipOutputStream
        /// </summary>
        private void WriteSecurityAccessDll(ZipOutputStream zipStream, SecurityConfig securityConfig)
        {
            if (securityConfig?.DllBytes != null && securityConfig.DllBytes.Length > 0)
            {
                var entry = new ZipEntry(SecurityAccessFile);
                zipStream.PutNextEntry(entry);
                zipStream.Write(securityConfig.DllBytes, 0, securityConfig.DllBytes.Length);
            }
        }

        /// <summary>
        /// 更新 ZipArchive 中的条目
        /// </summary>
        /// <param name="zipArchive">ZIP 归档</param>
        /// <param name="entryName">条目名称</param>
        /// <param name="bytes">条目数据，如果为 null 则删除条目</param>
        private void UpdateZipEntry(ZipArchive zipArchive, string entryName, byte[] bytes)
        {
            // 删除旧条目
            var oldEntry = zipArchive.GetEntry(entryName);
            if (oldEntry != null)
            {
                oldEntry.Delete();
            }

            // 如果有新数据，创建新条目
            if (bytes != null && bytes.Length > 0)
            {
                var newEntry = zipArchive.CreateEntry(entryName);
                using (var writer = new BinaryWriter(newEntry.Open()))
                {
                    writer.Write(bytes);
                }
            }
        }
    }
}
