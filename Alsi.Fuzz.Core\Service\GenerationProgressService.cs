namespace Alsi.Fuzz.Core.Service
{
    public class GenerationProgressService
    {
        private static int _caseCount = 0;
        private static int _groupLeafNodeCaseCount = 0;

        public static void Reset()
        {
            _caseCount = 0;
            _groupLeafNodeCaseCount = 0;
        }

        public static void SetGeneratedCaseCount(int caseCount)
        {
            _caseCount = caseCount;
        }

        public static void SetGroupLeafNodeCaseCount(int groupLeafNodeCaseCount)
        {
            _groupLeafNodeCaseCount = groupLeafNodeCaseCount;
        }

        public static string GetProgressText()
        {
            if (_groupLeafNodeCaseCount != 0 && _caseCount != 0)
            {
                return $"Indexing case group({_groupLeafNodeCaseCount}/{_caseCount} cases, {_groupLeafNodeCaseCount * 100.0 / _caseCount:f2}%)...";
            }

            if (_caseCount != 0)
            {
                return $"Generating test cases ({_caseCount} cases)...";
            }

            return "Generating test cases...";
        }

        public static bool IsCompleted()
        {
            return _groupLeafNodeCaseCount >= _caseCount && _caseCount != 0;
        }
    }
}
