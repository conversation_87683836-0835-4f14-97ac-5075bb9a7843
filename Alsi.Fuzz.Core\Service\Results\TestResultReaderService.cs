using Alsi.App;
using Alsi.App.Database;
using Alsi.App.Database.Midwares;
using Alsi.App.Devices.Core;
using Alsi.Fuzz.Core.Storage;
using System;
using System.IO;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.Results
{
    public partial class TestResultReaderService : ITestResultReaderService
    {
        private IFreeSql _appSql;

        public TestResultReaderService(DbContext dbContext = null)
        {
            _appSql = (dbContext ?? DbEnv.AppDbContext).FreeSql;
        }

        public TestResult[] GetTestResults()
        {
            return _appSql.Select<TestResult>()
                .OrderByDescending(x => x.Begin)
                .ToList()
                .ToArray();
        }

        public CaseResult[] GetCaseResults(TestResult testResult)
        {
            var resultContext = CreateResultContext(testResult);
            if (!File.Exists(resultContext.ResultDbPath))
            {
                return Array.Empty<CaseResult>();
            }

            using (var dbContext = new DbContext(resultContext.ResultDbPath))
            {
                dbContext.Initialize();
                return dbContext.FreeSql.Select<CaseResult>()
                    .Where(x => x.TestResultId == testResult.Id)
                    .OrderBy(x => x.Id)
                    .ToList()
                    .ToArray();
            }
        }

        public CaseResult GetCaseResultById(TestResult testResult, int caseResultId)
        {
            var resultContext = CreateResultContext(testResult);
            if (!File.Exists(resultContext.ResultDbPath))
            {
                return null;
            }

            using (var dbContext = new DbContext(resultContext.ResultDbPath))
            {
                dbContext.Initialize();
                return dbContext.FreeSql.Select<CaseResult>()
                    .Where(x => x.TestResultId == testResult.Id && x.Id == caseResultId)
                    .ToList()
                    .FirstOrDefault();
            }
        }

        public CaseStep[] GetCaseSteps(CaseResult caseResult)
        {
            // 查找关联的测试结果
            var testResult = _appSql.Select<TestResult>()
                .Where(x => x.Id == caseResult.TestResultId)
                .First();

            if (testResult == null)
            {
                return Array.Empty<CaseStep>();
            }

            // 使用测试结果的本地数据库
            return GetCaseStepsFromLocalDb(testResult, caseResult);
        }

        public CanFrame[] GetFrames(TestResult testResult)
        {
            var resultContext = CreateResultContext(testResult);
            if (!File.Exists(resultContext.DataLogPath))
            {
                return Array.Empty<CanFrame>();
            }

            return DataLogStorage.ReadFrames(resultContext.DataLogPath);
        }

        // 提取通用的本地数据库访问逻辑
        private CaseStep[] GetCaseStepsFromLocalDb(TestResult testResult, CaseResult caseResult)
        {
            var resultContext = CreateResultContext(testResult);
            if (!File.Exists(resultContext.ResultDbPath))
            {
                return Array.Empty<CaseStep>();
            }

            using (var dbContext = new DbContext(resultContext.ResultDbPath))
            {
                dbContext.Initialize();
                return dbContext.FreeSql.Select<CaseStep>()
                    .Where(x => x.CaseResultId == caseResult.Id)
                    .OrderBy(x => x.Begin)
                    .ToList()
                    .ToArray();
            }
        }

        // 创建结果上下文的辅助方法;
        private ResultContext CreateResultContext(TestResult testResult)
        {
            if (testResult == null)
            {
                throw new ArgumentNullException(nameof(testResult));
            }

            return new ResultContext(testResult.TestType, testResult.ResultFolderName);
        }

        public int UpdateTestResult(TestResult testResult)
        {
            // 更新记录
            var rows = _appSql.Update<TestResult>()
                .SetSource(testResult)
                .ExecuteAffrows();

            return rows;
        }

        public void DeleteTestResult(Guid testResultId)
        {
            // 首先获取测试结果记录
            var testResult = _appSql.Select<TestResult>()
                .Where(x => x.Id == testResultId)
                .First();

            if (testResult == null)
            {
                return;
            }

            // 创建结果上下文，以便获取文件路径
            var resultContext = CreateResultContext(testResult);

            // 删除结果目录及其内容
            if (Directory.Exists(resultContext.ResultFolder))
            {
                // 调用递归删除前，严格文件夹目录正确
                if (!string.IsNullOrWhiteSpace(testResult.ResultFolderName)
                    && resultContext.ResultFolder.Contains(testResult.ResultFolderName)
                    && !string.IsNullOrWhiteSpace(AppEnv.WebHostApp.AppName)
                    && resultContext.ResultFolder.Contains(AppEnv.WebHostApp.AppName)
                    && resultContext.ResultFolder.Contains(AppEnv.WebHostApp.DataFolder))
                {
                    Directory.Delete(resultContext.ResultFolder, true);
                }
            }

            // 删除数据库记录
            _appSql.Delete<TestResult>().Where(x => x.Id == testResultId).ExecuteAffrows();
        }

        public TestResult CreateTestResult(string planName, TestType testType)
        {
            var typeName = string.Empty;
            if (testType == TestType.Interoperation)
            {
                typeName = "interop";
            }
            else if (testType == TestType.Case)
            {
                typeName = "case";
            }
            else
            {
                throw new NotImplementedException($"Unknown test type: {testType}");
            }

            var resultFolderName = $"{planName}_{typeName}_{DateTime.Now:yyyyMMdd_HHmmss}";

            // 创建新的测试记录
            var testResult = new TestResult
            {
                Id = Guid.NewGuid(),
                TestType = testType,
                TestPlanName = planName,
                ResultFolderName = resultFolderName,
                TotalCount = 0,
                SuccessCount = 0,
                FailureCount = 0,
                CreationTime = DateTime.Now
            };

            // 将测试记录插入主数据库
            _appSql.Insert(testResult).ExecuteAffrows();

            return testResult;
        }
    }
}
