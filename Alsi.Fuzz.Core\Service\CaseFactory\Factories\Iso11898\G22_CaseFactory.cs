using Alsi.App.Devices.Core;
using Alsi.Common.Utils.Autosar;
using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Iso11898;
using System;
using System.Collections.Generic;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso11898
{
    public class G22_CaseFactory
    {
        public CaseMutation[] Generate(MutationOptions options)
        {
            var list = new List<CaseMutation>();
            if (options.CommunicationType != CommunicationType.CanFd)
            {
                return list.ToArray();
            }

            var random = options.Random;

            // G221
            for (var id = 0; id <= 0x7FF; id++)
            {
                var bytes = new byte[0];
                var mutation = CaseMutation.Create($"G221-ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(0)
                    .MutateRtr(0)
                    .MutateExt(false)
                    .MutateData(bytes);
                list.Add(mutation);
            }

            // G222
            for (var id = 0; id <= 0x7FF; id++)
            {
                var mutationG222 = CaseMutation.Create($"G222-ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(0)
                    .MutateRtr(1)
                    .MutateExt(false)
                    .MutateData(Array.Empty<byte>());
                list.Add(mutationG222);
            }

            // G223
            for (var id = 0; id <= 0x7FF; id++)
            {
                for (var dlc = 1; dlc <= 0xF; dlc++)
                {
                    var bytes = new byte[DlcUtils.GetDataLength(dlc)];
                    random.NextBytes(bytes);
                    var mutation = CaseMutation.Create($"G223-ID_{id:X}")
                        .MutateId(id)
                        .MutateDlc(dlc)
                        .MutateRtr(0)
                        .MutateExt(false)
                        .MutateData(bytes);
                    list.Add(mutation);
                }
            }

            // G224 11bit

            for (var dlc = 1; dlc <= 0xF; dlc++)
            {
                var idG224 = random.Next(0, 0x800);
                var mutationG224 = CaseMutation.Create($"G224-ID_{idG224:X}")
                    .MutateId(idG224)
                    .MutateDlc(dlc)
                    .MutateRtr(1)
                    .MutateExt(false)
                    .MutateData(Array.Empty<byte>());
                list.Add(mutationG224);
            }

            int[] idArray;
            if (options.WhiteListFrames.Length == 0)
            {
                // 29bit均匀采样4096
                idArray = new int[4096];
                var maxId = 0x1FFFFFFF;
                int step = maxId / 4095; // 4096个点，间隔4095段
                for (var i = 0; i < 4095; i++)
                {
                    idArray[i] = i * step;
                }
                idArray[4095] = maxId;
            }
            else
            {
                idArray = new int[] {
                    0x0, 0x1, 0xFF, 0x100, 0x1FF, 0x200,
                    0x1000, 0x1FFF, 0x2000,
                    0x10000, 0x1FFFF, 0x20000,
                    0x100000, 0x1FFFFF, 0x200000,
                    0x1000000, 0x1FFFFFF, 0x2000000,
                    0xFFFFFFE, 0xFFFFFFF, 0x10000000,
                    0x10000001, 0x1FFFFFF0, 0x1FFFFFF1, 0x1FFFFFF2, 0x1FFFFFF3,
                    0x1FFFFFF4, 0x1FFFFFF5, 0x1FFFFFF6, 0x1FFFFFF7, 0x1FFFFFF8,
                    0x1FFFFFF9, 0x1FFFFFFA, 0x1FFFFFFB, 0x1FFFFFFC, 0x1FFFFFFD,
                    0x1FFFFFFE, 0x1FFFFFFF
                };
            }

            // G225
            foreach (var id in idArray)
            {
                var bytes = new byte[0];
                var mutation = CaseMutation.Create($"G225-ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(0)
                    .MutateRtr(0)
                    .MutateExt(true)
                    .MutateData(bytes);
                list.Add(mutation);
            }

            // G226
            foreach (var id in idArray)
            {
                var mutationG226 = CaseMutation.Create($"G226-ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(random.Next(1, 16))
                    .MutateRtr(1)
                    .MutateExt(true)
                    .MutateData(Array.Empty<byte>());
                list.Add(mutationG226);
            }

            // G227
            foreach (var id in idArray)
            {
                var dlc = random.Next(1, 16);
                var bytes = new byte[DlcUtils.GetDataLength(dlc)];
                random.NextBytes(bytes);
                var mutation = CaseMutation.Create($"G227-ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(dlc)
                    .MutateRtr(0)
                    .MutateExt(true)
                    .MutateData(bytes);
                list.Add(mutation);
            }

            // G228
            var idG228 = random.Next(0, 0x20000000);
            var mutationG228 = CaseMutation.Create($"G228-ID_{idG228:X}")
                .MutateId(idG228)
                .MutateDlc(random.Next(1, 16))
                .MutateRtr(1)
                .MutateExt(true)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG228);

            return list.ToArray();
        }
        public (CaseMutation[], List<string> groupPaths) GenerateWithGroup(MutationOptions options)
        {
            var list = new List<CaseMutation>();
            var groupPaths = new List<string>();
            if (options.CommunicationType != CommunicationType.CanFd)
            {
                return (list.ToArray(), groupPaths);
            }
            var random = options.Random;


            var groupPath = "";
            var name = "";

            // G221
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
                        .GetTxMessages()
                        .Get11BitId()
                        .GetRtr(0)
                        .IsCorrectDLC(true)
                        .DlcPath;
            for (var id = 0; id <= 0x7FF; id++)
            {
                name = $"id:{id:X}";
                var bytes = new byte[0];
                var mutation = CaseMutation.Create(name, groupPath)
                    .MutateId(id)
                    .MutateDlc(0)
                    .MutateRtr(0)
                    .MutateExt(false)
                    .MutateData(bytes);
                list.Add(mutation);
            }
            groupPaths.Add(groupPath);

            // G222
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
            .GetTxMessages()
            .Get11BitId()
            .GetRtr(1)
            .RtrPath;
            for (var id = 0; id <= 0x7FF; id++)
            {
                name = $"id:{id:X}";
                var mutationG222 = CaseMutation.Create(name, groupPath)
                    .MutateId(id)
                    .MutateDlc(0)
                    .MutateRtr(1)
                    .MutateExt(false)
                    .MutateData(Array.Empty<byte>());
                list.Add(mutationG222);
            }
            groupPaths.Add(groupPath);

            // G223
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
            .GetTxMessages()
            .Get11BitId()
            .GetRtr(0)
            .IsCorrectDLC(true)
            .DlcPath;
            for (var id = 0; id <= 0x7FF; id++)
            {
                for (var dlc = 1; dlc <= 0xF; dlc++)
                {
                    var bytes = new byte[DlcUtils.GetDataLength(dlc)];
                    random.NextBytes(bytes);
                    name = $"id:{id:X} dlc:{dlc} byte random";
                    var mutation = CaseMutation.Create(name, groupPath)
                        .MutateId(id)
                        .MutateDlc(dlc)
                        .MutateRtr(0)
                        .MutateExt(false)
                        .MutateData(bytes);
                    list.Add(mutation);
                }
            }
            groupPaths.Add(groupPath);

            // G224 11bit
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
            .GetTxMessages()
            .Get11BitId()
            .GetRtr(1)
            .RtrPath;
            for (var dlc = 1; dlc <= 0xF; dlc++)
            {
                var idG224 = random.Next(0, 0x800);
                name = $" id random id:{idG224:X} dlc:{dlc}";
                var mutationG224 = CaseMutation.Create(name, groupPath)
                    .MutateId(idG224)
                    .MutateDlc(dlc)
                    .MutateRtr(1)
                    .MutateExt(false)
                    .MutateData(Array.Empty<byte>());
                list.Add(mutationG224);
            }
            groupPaths.Add(groupPath);

            // G225 226 227 共通
            int[] idArray;
            if (options.WhiteListFrames.Length == 0)
            {
                // 29bit均匀采样4096
                idArray = new int[4096];
                var maxId = 0x1FFFFFFF;
                int step = maxId / 4095; // 4096个点，间隔4095段
                for (var i = 0; i < 4095; i++)
                {
                    idArray[i] = i * step;
                }
                idArray[4095] = maxId;
            }
            else
            {
                idArray = new int[] {
                    0x0, 0x1, 0xFF, 0x100, 0x1FF, 0x200,
                    0x1000, 0x1FFF, 0x2000,
                    0x10000, 0x1FFFF, 0x20000,
                    0x100000, 0x1FFFFF, 0x200000,
                    0x1000000, 0x1FFFFFF, 0x2000000,
                    0xFFFFFFE, 0xFFFFFFF, 0x10000000,
                    0x10000001, 0x1FFFFFF0, 0x1FFFFFF1, 0x1FFFFFF2, 0x1FFFFFF3,
                    0x1FFFFFF4, 0x1FFFFFF5, 0x1FFFFFF6, 0x1FFFFFF7, 0x1FFFFFF8,
                    0x1FFFFFF9, 0x1FFFFFFA, 0x1FFFFFFB, 0x1FFFFFFC, 0x1FFFFFFD,
                    0x1FFFFFFE, 0x1FFFFFFF
                };
            }

            // G225
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
            .GetTxMessages()
            .Get29BitId()
            .GetRtr(0)
            .IsCorrectDLC(true)
            .DlcPath;
            foreach (var id in idArray)
            {
                var bytes = new byte[0];
                name = $"id:{id:X}";
                var mutation = CaseMutation.Create(name, groupPath)
                    .MutateId(id)
                    .MutateDlc(0)
                    .MutateRtr(0)
                    .MutateExt(true)
                    .MutateData(bytes);
                list.Add(mutation);
            }
            groupPaths.Add(groupPath);

            // G226
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
            .GetTxMessages()
            .Get29BitId()
            .GetRtr(1)
            .RtrPath;
            foreach (var id in idArray)
            {
                var dlc = random.Next(1, 16);
                name = $"id:{id:X} dlc:{dlc}";
                var mutationG226 = CaseMutation.Create(name, groupPath)
                    .MutateId(id)
                    .MutateDlc(dlc)
                    .MutateRtr(1)
                    .MutateExt(true)
                    .MutateData(Array.Empty<byte>());
                list.Add(mutationG226);
            }
            groupPaths.Add(groupPath);

            // G227
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
            .GetTxMessages()
            .Get29BitId()
            .GetRtr(0)
            .IsCorrectDLC(true)
            .DlcPath;
            foreach (var id in idArray)
            {
                var dlc = random.Next(1, 16);
                var bytes = new byte[DlcUtils.GetDataLength(dlc)];
                name = $"id:{id:X} dlc:{dlc}}} byte random";
                random.NextBytes(bytes);
                var mutation = CaseMutation.Create(name, groupPath)
                    .MutateId(id)
                    .MutateDlc(dlc)
                    .MutateRtr(0)
                    .MutateExt(true)
                    .MutateData(bytes);
                list.Add(mutation);
            }
            groupPaths.Add(groupPath);

            // G228
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
            .GetTxMessages()
            .Get29BitId()
            .GetRtr(1)
            .RtrPath;
            var dlc1 = random.Next(1, 16);
            var idG228 = random.Next(0, 0x20000000);
            name = $"idG228:{idG228:X} dlc:{dlc1}";
            var mutationG228 = CaseMutation.Create(name, groupPath)
                .MutateId(idG228)
                .MutateDlc(dlc1)
                .MutateRtr(1)
                .MutateExt(true)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG228);
            groupPaths.Add(groupPath);

            return (list.ToArray(), groupPaths);
        }
    }
}
