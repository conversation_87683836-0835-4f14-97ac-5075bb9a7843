using Alsi.App.Devices.Core;
using Alsi.Common.Utils;
using Alsi.Common.Utils.Timers;
using Alsi.Fuzz.Core.Models.Tester;
using Alsi.Fuzz.Core.Models.TestSuites.Steps;
using Alsi.Fuzz.Core.Models.TestSuites.Steps.Diagnostic;
using Alsi.Fuzz.Core.Models.TestSuites.Steps.Isotp;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.Results;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace Alsi.Fuzz.Tester.Testers
{
    public class StepTester : IDisposable
    {
        public StepTester()
        {
            DataBus.OnSent += RecordDataFrame;
            DataBus.OnReceived += RecordDataFrame;
        }

        public void Dispose()
        {
            DataBus.OnSent -= RecordDataFrame;
            DataBus.OnReceived -= RecordDataFrame;
        }

        private DataCache Cache = new DataCache();

        private void RecordDataFrame(object _, CanFrame frame)
        {
            Cache.Add(frame);
        }

        private IsotpStepTester isotpStepTester = new IsotpStepTester();
        private DiagStepTester diagStepTester = new DiagStepTester();

        private ErrorFrameChecker errorFrameChecker;

        public async Task ExecuteStepAsync(StepBase step, CaseContext caseContext)
        {
            var stepName = step.Name;
            if (string.IsNullOrWhiteSpace(stepName))
            {
                stepName = step.GetType().Name;
            }

            // 如果名称以"Step"结尾，删除"Step"后缀
            if (stepName.EndsWith("Step", StringComparison.OrdinalIgnoreCase))
            {
                stepName = stepName.Substring(0, stepName.Length - 4);
            }

            var caseStep = new CaseStep
            {
                Id = Guid.NewGuid(),
                Name = stepName,
                CaseResultId = caseContext.CaseResult.Id,
                Timestamp = DataBus.Timestamp,
                FrameTimestamp = null,
                State = CaseStepState.Running,
                Begin = DateTime.Now,
                End = null,
                Detail = string.Empty
            };

            try
            {
                switch (step)
                {
                    case PrintStep printStep:
                        var detail = printStep.Text;
                        caseContext.EnvVars.Eval(ref detail);
                        caseStep.Detail = detail;
                        break;

                    case WaitStep waitStep:
                        await ExecuteWaitStepAsync(waitStep, caseStep, caseContext);
                        break;

                    case CalcKeyStep calcKeyStep:
                        await ExecuteCalcKeyStepAsync(calcKeyStep, caseStep, caseContext);
                        break;

                    #region ISO-11898 的发送接收步骤
                    case SendStep sendStep:
                        Cache.Clear();
                        errorFrameChecker = new ErrorFrameChecker();
                        await ExecuteSendStepAsync(sendStep, caseStep, caseContext);
                        break;

                    case ReceiveStep receiveStep:
                        await ExecuteReceiveStepAsync(receiveStep, caseStep, caseContext);
                        break;
                    #endregion

                    #region ISO-14229 的发送接收步骤
                    case SendDiagStep sendDiagStep:
                        Cache.Clear();
                        errorFrameChecker = new ErrorFrameChecker();
                        await diagStepTester.SendDiagAsync(sendDiagStep, caseStep, caseContext, errorFrameChecker);
                        break;

                    case RecvDiagStep recvDiagStep:
                        await diagStepTester.RecvDiagAsync(recvDiagStep, caseStep, caseContext);
                        break;
                    #endregion

                    #region ISO-15765 的发送接收步骤
                    case SendIsotpStep sendIsotpStep:
                        Cache.Clear();
                        errorFrameChecker = new ErrorFrameChecker();
                        await isotpStepTester.SendIsotpAsync(sendIsotpStep, caseStep, caseContext);
                        break;

                    case RecvIsotpStep recvIsotpStep:
                        await isotpStepTester.RecvIsotpAsync(recvIsotpStep, caseStep, caseContext);
                        break;
                    #endregion

                    default:
                        throw new Exception($"Unknown step type: {step.GetType().Name}");
                }

                if (caseStep.State != CaseStepState.Success)
                {
                    caseStep.State = CaseStepState.Completed;
                }
            }
            catch (Exception e)
            {
                caseStep.Detail = e.Message;
                caseStep.State = CaseStepState.Failure;
                throw e;
            }
            finally
            {
                caseStep.End = DateTime.Now;

                caseContext.CaseSteps.Add(caseStep);

                // 用户可配置是否忽略 error frame 的检查
                if (errorFrameChecker != null && step is ICommRecvStep commomRecvStep)
                {
                    try
                    {
                        if (!commomRecvStep.IgnoreErrorFrame)
                        {
                            // 检查接收过程中，是否收到错误帧
                            errorFrameChecker.Check(caseContext.CaseResult.Id, out var errorFrameCaseStep);
                            if (errorFrameCaseStep != null)
                            {
                                caseContext.CaseSteps.Add(errorFrameCaseStep);

                                throw new Exception(errorFrameCaseStep.Detail);
                            }
                        }
                    }
                    finally
                    {
                        errorFrameChecker.Release();
                        errorFrameChecker = null;
                    }
                }
            }
        }

        private Task ExecuteWaitStepAsync(WaitStep step, CaseStep caseStep, CaseContext caseContext)
        {
            if (step.TimeoutMs.HasValue && step.TimeoutMs.Value > 0)
            {
                HighResolutionTimer.Sleep(step.TimeoutMs.Value);
                caseStep.Detail = $"Wait for {step.TimeoutMs.Value} ms";
            }
            else
            {
                caseStep.Detail = $"Wait for {0} ms";
            }

            return Task.CompletedTask;
        }

        private Task ExecuteSendStepAsync(SendStep step, CaseStep caseStep, CaseContext caseContext)
        {
            if (step.Frame != null)
            {
                var frame = step.Frame.ToCanFrame(caseContext.EnvVars);
                var caseMutation = CaseMutation.Deserialize(caseContext.CaseResult.Parameter);
                caseMutation.ApplyToFrame(frame);

                if (frame.Rtr)
                {
                    // 2025/04/29 讨论结果：遇到远程帧，使用 CAN 发送，而不是 CANFD
                    // 注：CANFD 不支持 RTR，参考 https://elearning.vector.com/mod/page/view.php?id=363
                    frame.IsCanFd = false;
                }

                LatestSentFrame = frame;
                LatestSentTimestamp = DataBus.Timestamp;
                DataBus.Send(frame);
                LatestSendCaseStep = caseStep;
            }

            return Task.CompletedTask;
        }

        private Task ExecuteCalcKeyStepAsync(CalcKeyStep calcKeyStep, CaseStep caseStep, CaseContext caseContext)
        {
            var seed = calcKeyStep.Seed;
            caseContext.EnvVars.Eval(ref seed);

            var seedBytes = SequenceUtils.ParseBytes(seed);

            var levelText = calcKeyStep.Level;
            caseContext.EnvVars.Eval(ref levelText);
            if (!uint.TryParse(levelText, NumberStyles.HexNumber, null, out var level))
            {
                throw new Exception($"Failed to parse level : {calcKeyStep.Level}");
            }

            var key = Array.Empty<byte>();
            if (seedBytes.Length > 0)
            {
                key = SecurityAccessUtils.CalculateKey(seedBytes, level);
            }
            caseStep.Detail = $"Seed={seedBytes.ToHex()}; Level={level:X}; Key={key.ToHex()}";

            caseContext.EnvVars.SetByCommand(calcKeyStep.SetVars, key);
            return Task.CompletedTask;
        }

        private static ulong? LatestSentTimestamp { get; set; }
        private static CanFrame LatestSentFrame { get; set; }
        private static CaseStep LatestSendCaseStep { get; set; }

        private Task ExecuteReceiveStepAsync(ReceiveStep receiveStep, CaseStep caseStep, CaseContext caseContext)
        {
            var timeout = caseContext.CaseConfig.TimeoutMs;
            var begin = DataBus.Timestamp;

            CanFrame txFrame = null;
            while (timeout != 0)
            {
                // 当前轮次的开始时间，如果 timeout 不为 0，至少检测一轮
                var beginOfCurrentRound = DataBus.Timestamp;
                var frames = Cache.GetAll();

                if (txFrame == null)
                {
                    var latestSentTimestamp = LatestSentTimestamp;
                    var latestSentFrame = LatestSentFrame;
                    if (latestSentFrame != null && latestSentTimestamp.HasValue)
                    {
                        // 查找 TX 数据时，仅比较 ID、DLC
                        txFrame = frames.LastOrDefault(x => x.IsTx
                            && !x.IsErrorFrame
                            && x.Id == latestSentFrame.Id
                            && x.Dlc == latestSentFrame.Dlc);

                        if (LatestSendCaseStep != null && txFrame != null)
                        {
                            LatestSendCaseStep.FrameTimestamp = txFrame.TimeUS;
                            LatestSendCaseStep.Detail = BuildDetail(txFrame);
                            LatestSendCaseStep = null;
                        }
                    }
                }
                else
                {
                    var rxFrames = frames
                        .Where(x => !x.IsTx && !x.IsErrorFrame && x.TimeUS > txFrame.TimeUS)
                        .ToArray();

                    var matchFrames = receiveStep.MatchFrames;
                    if (!matchFrames.Any())
                    {
                        // 未定义匹配帧，可以匹配任意帧
                        matchFrames = new List<MatchFrame> { MatchFrame.Empty };
                    }

                    CanFrame rxFrame = null;
                    foreach (var frame in rxFrames)
                    {
                        if (frame.Id != caseContext.CaseConfig.ResponseId)
                        {
                            continue;
                        }

                        foreach (var matchFrame in matchFrames)
                        {
                            if (matchFrame.IsMatch(frame, caseContext.EnvVars))
                            {
                                rxFrame = frame;
                                break;
                            }
                        }

                        if (rxFrame != null)
                        {
                            break;
                        }
                    }

                    if (rxFrame != null)
                    {
                        // 收到任意 RX 数据
                        if (rxFrame.TimeUS - txFrame.TimeUS <= (ulong)timeout * 1000)
                        {
                            caseStep.State = CaseStepState.Success;
                            caseStep.FrameTimestamp = rxFrame.TimeUS;
                            caseStep.Detail = BuildDetail(rxFrame);
                            return Task.CompletedTask;
                        }
                        else
                        {
                            var message = $"Timeout({(rxFrame.TimeUS - txFrame.TimeUS) / 1000}ms > {timeout}ms), " +
                                $"Tx frame timestamp: {TimestampUtils.GetString(txFrame.TimeUS)}, " +
                                $"Rx frame timestamp: {TimestampUtils.GetString(rxFrame.TimeUS)}";
                            caseStep.State = CaseStepState.Failure;
                            caseStep.Detail = message;
                            throw new Exception(message);
                        }
                    }
                }

                // 如果超时，则不再检测
                if (beginOfCurrentRound - begin > (ulong)timeout * 1000)
                {
                    break;
                }

                HighResolutionTimer.Sleep(5);
            }

            if (txFrame == null)
            {
                var message = $"Can't find the TX frame on data bus.";
                caseStep.State = CaseStepState.Failure;
                caseStep.Detail = message;
                throw new Exception(message);
            }
            else
            {
                var message = $"Receive timeout after {timeout} ms.";
                caseStep.State = CaseStepState.Failure;
                caseStep.Detail = message;
                throw new Exception(message);
            }
        }

        private static string BuildDetail(CanFrame frame)
        {
            var byteCount = frame.Data.Length;
            var unit = byteCount < 2 ? "byte" : "bytes";
            var data = string.Empty;
            var maxDisplayCount = 10;
            if (byteCount > maxDisplayCount)
            {
                data = $"{frame.Data.Take(maxDisplayCount).ToHex()} ...";
            }
            else
            {
                data = $"{frame.Data.ToHex()}";
            }
            var dir = frame.IsTx ? "Tx" : "Rx";
            return $"Time={TimestampUtils.GetString(frame.TimeUS)}s, Id=0x{frame.Id.ToHex()}, Dir={dir}, DLC={frame.Dlc:X}, Length={byteCount} Data={data}";
        }
    }
}
