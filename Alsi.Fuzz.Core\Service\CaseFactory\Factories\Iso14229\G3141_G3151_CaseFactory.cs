using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts;
using Alsi.Fuzz.Core.Service.CaseFactory.Iso14229;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G3141_G3151_CaseFactory : CaseFactoryBase
    {
        public override void Generate(MutationOptions options, Action<CreateCaseInfo> createCase)
        {
            var xmlServices = options.XmlServices;
            var supportedXmlServicesWithSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == true)
                .ToArray();

            var supportedXmlServicesWithoutSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => !x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历没有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == false)
                .ToArray();

            var udsSids = IsoUdsConsts.Services.Select(x => x.Id).Distinct().ToArray();
            var sidsOutOfUds = Enumerable.Range(0, 255).Select(x => (byte)x).Except(udsSids).ToArray();

            foreach (var sid in sidsOutOfUds)
            {
                foreach (var xmlServiceWithSubfunction in supportedXmlServicesWithSubfunction)
                {
                    var payload = new List<byte> { sid, xmlServiceWithSubfunction.SubfunctionId.Value};
                    payload.AddRange(xmlServiceWithSubfunction.Parameter2k);

                    var serviceWithSubName = xmlServiceWithSubfunction.IsoUdsServiceDisplayName;
                    //TO Check
                    var groupPath = Iso14229CaseGroupConsts.UnknownServiceId.Path;
                    //var name = $"SidsOutOfUds & ServiceWithSub:{serviceWithSubName} -{groupPath}";
                    var name = $"Sid{sid:X2}-OtherSid{xmlServiceWithSubfunction.Id:X2}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G3141-Sid{sid:X2}-OtherSid{xmlServiceWithSubfunction.Id:X2}", caseMutation, sid, null));
                }
            }

            foreach (var sid in sidsOutOfUds)
            {
                foreach (var xmlServiceWithoutSubfunction in supportedXmlServicesWithoutSubfunction)
                {
                    var payload = new List<byte> { sid, };
                    payload.AddRange(xmlServiceWithoutSubfunction.Parameter2k);
                    var serviceWithoutSubName = xmlServiceWithoutSubfunction.IsoUdsServiceDisplayName;
                    var groupPath = Iso14229CaseGroupConsts.UnknownServiceId.Path;
                    //var name = $"SidsOutOfUds & serviceWithoutSub:{serviceWithoutSubName} -{groupPath}";
                    var name = $"Sid{sid:X2}-OtherSid{xmlServiceWithoutSubfunction.Id:X2}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G3151-Sid{sid:X2}-OtherSid{xmlServiceWithoutSubfunction.Id:X2}", caseMutation, sid, null));
                }
            }
        }
    }
}
