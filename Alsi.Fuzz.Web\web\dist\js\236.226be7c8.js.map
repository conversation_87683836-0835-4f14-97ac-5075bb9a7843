{"version": 3, "file": "js/236.226be7c8.js", "mappings": "+cAgFA,MAAMA,GAAUC,EAAAA,EAAAA,IAAI,MAGdC,EAAeC,UACnB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,aAC9BN,EAAQO,MAAQH,EAASI,IAC3B,CAAE,MAAOC,GACPC,QAAQD,MAAM,4BAA6BA,EAC7C,GAIIE,EAAaR,UACjB,UACQS,EAAAA,EAAYC,aAAaC,EACjC,CAAE,MAAOL,GACPC,QAAQD,MAAM,yBAA0BA,EAC1C,G,OAIFM,EAAAA,EAAAA,IAAUb,G,47EChGV,MAAMc,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://fuzz-web/./src/views/AboutView.vue", "webpack://fuzz-web/./src/views/AboutView.vue?d56f"], "sourcesContent": ["<template>\n  <div class=\"about-container\">\n    <el-card class=\"about-card\">\n      <div class=\"about-content\">\n        <div>\n          <h2>Fuzz</h2>\n          <p class=\"description\">Fuzz is a professional fuzzing tool mainly used for automotive communication\n            protocol testing.</p>\n        </div>\n\n        <div>\n          <h2>Features</h2>\n          <ul class=\"feature-list\">\n            <li><el-icon class=\"feature-icon\">\n                <Check />\n              </el-icon>Support for CAN/CANFD protocol testing</li>\n            <li><el-icon class=\"feature-icon\">\n                <Check />\n              </el-icon>Support for ISO 11898, ISO 14229 and ISO 15765 related testing</li>\n            <li><el-icon class=\"feature-icon\">\n                <Check />\n              </el-icon>Interoperation test to detect services supported by the device</li>\n            <li><el-icon class=\"feature-icon\">\n                <Check />\n              </el-icon>Automatic generation and execution of targeted test cases</li>\n            <li><el-icon class=\"feature-icon\">\n                <Check />\n              </el-icon>Local storage of test results and logs</li>\n          </ul>\n        </div>\n\n        <!-- 应用信息部分 -->\n        <div v-if=\"appInfo\">\n          <h2>Application Info</h2>\n          <div class=\"info-list\">\n            <div class=\"info-item\">\n              <span class=\"label\">Data Folder:</span>\n              <span class=\"folder-path\">{{ appInfo.dataFolder }}</span>\n              <el-button \n                type=\"text\" \n                size=\"small\" \n                @click=\"openFolder(appInfo.dataFolder)\"\n                title=\"Open in File Explorer\"\n                class=\"folder-btn\"\n              >\n                <font-awesome-icon icon=\"folder-open\" />\n              </el-button>\n            </div>\n            <div class=\"info-item\">\n              <span class=\"label\">Log Folder:</span>\n              <span class=\"folder-path\">{{ appInfo.logFolder }}</span>\n              <el-button \n                type=\"text\" \n                size=\"small\" \n                @click=\"openFolder(appInfo.logFolder)\"\n                title=\"Open in File Explorer\"\n                class=\"folder-btn\"\n              >\n                <font-awesome-icon icon=\"folder-open\" />\n              </el-button>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"copyright-info\">\n          <p>Copyright © 2025, ALPS System Integration(Dalian) Co., Ltd.</p>\n          <p>All rights reserved.</p>\n        </div>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue';\nimport { Check } from '@element-plus/icons-vue';\nimport { appApi } from '@/api/appApi';\nimport { explorerApi } from '@/api/explorerApi';\n\n// 用于存储应用信息\nconst appInfo = ref(null);\n\n// 获取应用信息\nconst fetchAppInfo = async () => {\n  try {\n    const response = await appApi.getAppInfo();\n    appInfo.value = response.data;\n  } catch (error) {\n    console.error('Failed to fetch app info:', error);\n  }\n};\n\n// 在文件浏览器中打开文件夹\nconst openFolder = async (path) => {\n  try {\n    await explorerApi.openExplorer(path);\n  } catch (error) {\n    console.error('Failed to open folder:', error);\n  }\n};\n\n// 组件挂载时获取应用信息\nonMounted(fetchAppInfo);\n</script>\n\n<style scoped>\n.about-container {\n  height: 100%;\n  padding: 20px;\n  box-sizing: border-box;\n  background-color: #f5f7fa;\n}\n\n.about-card {\n  height: 100%;\n  border-radius: 8px;\n  display: flex;\n  flex-direction: column;\n}\n\n.panel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  min-height: 32px;\n}\n\n.title-section h2 {\n  margin: 0;\n  font-weight: 600;\n  color: var(--el-text-color-primary);\n  font-size: 20px;\n  border-bottom: none;\n}\n\n.about-content {\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n  line-height: 1.6;\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.about-content::-webkit-scrollbar {\n  width: 6px;\n}\n\n.about-content::-webkit-scrollbar-thumb {\n  background-color: var(--el-border-color-light);\n  border-radius: 3px;\n}\n\nh2 {\n  font-size: 18px;\n  color: var(--el-color-primary);\n  border-bottom: 1px solid var(--el-border-color-light);\n  padding-bottom: 8px;\n  margin-bottom: 16px;\n  margin-top: 0;\n}\n\n.description {\n  font-size: 14px;\n  color: var(--el-text-color-primary);\n  margin-left: 8px;\n}\n\n.feature-list {\n  padding-left: 0;\n  list-style-type: none;\n}\n\n.feature-list li {\n  padding: 6px 0;\n  font-size: 14px;\n  display: flex;\n  align-items: center;\n  color: var(--el-text-color-primary);\n}\n\n.feature-icon {\n  color: var(--el-color-success);\n  margin-right: 10px;\n}\n\n.info-list {\n  padding-left: 8px;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n  font-size: 14px;\n}\n\n.label {\n  min-width: 100px;\n  color: var(--el-text-color-primary);\n}\n\n.folder-path {\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  color: var(--el-text-color-secondary);\n  margin-right: 10px;\n}\n\n.folder-btn {\n  padding: 2px;\n}\n\n.copyright-info {\n  margin-top: auto;\n  padding-top: 20px;\n  margin-bottom: 0;\n  color: var(--el-text-color-secondary);\n  font-size: 12px;\n  text-align: center;\n  border-top: 1px solid var(--el-border-color-lighter);\n}\n\ncopyright-info p {\n  margin: 5px 0;\n}\n</style>\n", "import script from \"./AboutView.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./AboutView.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./AboutView.vue?vue&type=style&index=0&id=6cb96e2c&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-6cb96e2c\"]])\n\nexport default __exports__"], "names": ["appInfo", "ref", "fetchAppInfo", "async", "response", "appApi", "getAppInfo", "value", "data", "error", "console", "openFolder", "<PERSON><PERSON><PERSON>", "openExplorer", "path", "onMounted", "__exports__"], "sourceRoot": ""}