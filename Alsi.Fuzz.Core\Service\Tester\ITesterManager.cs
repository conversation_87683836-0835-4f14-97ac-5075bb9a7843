using System;
using System.Threading.Tasks;

namespace Alsi.Fuzz.Core.Service.Tester
{
    /// <summary>
    /// Tester进程管理器接口
    /// </summary>
    public interface ITesterManager : IDisposable
    {
        /// <summary>
        /// 启动Tester进程
        /// </summary>
        /// <param name="arguments">启动参数</param>
        /// <returns>启动是否成功</returns>
        Task<bool> StartAsync();

        /// <summary>
        /// 停止Tester进程
        /// </summary>
        /// <returns>停止是否成功</returns>
        Task<bool> StopAsync();

        /// <summary>
        /// 获取Tester进程是否正在运行
        /// </summary>
        bool IsRunning { get; }

        /// <summary>
        /// Tester进程退出事件
        /// </summary>
        event EventHandler<int> ProcessExited;

        ITesterApiClient ApiClient { get; }

        Task WaitTesterAsync(bool expectIsRunning);
    }
}
