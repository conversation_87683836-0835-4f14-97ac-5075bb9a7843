"use strict";(self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[]).push([[596],{3596:function(e,a,t){t.r(a),t.d(a,{default:function(){return w}});var l=t(6768),i=t(144),n=t(4232),s=t(7477),d=t(2616),u=t(4441);const o={class:"basic-setting-container"},c={class:"description-section"},r={class:"section-header"},v={key:0,class:"description-content"},p={key:0},k={key:1,class:"description-edit"},f={class:"edit-actions"},m={class:"additional-info-section"},y={class:"path-display"},b={class:"path-display"},g={class:"path-display"};var _=(0,l.pM)({__name:"BasicSetting",setup(e){const a=d.f.getState(),t=(0,l.EW)((()=>a.currentPlan)),_=(0,l.EW)((()=>a.isLoading)),h=(0,i.KR)(!1),C=(0,i.KR)(""),w=(0,i.KR)(!1),E=async()=>{await d.f.getCurrentPlan()},F=()=>{C.value=t.value?.manifest.description||"",h.value=!0},L=()=>{h.value=!1},W=async()=>{if(t.value){w.value=!0;try{await d.f.updateBasicInfo(C.value),h.value=!1}finally{w.value=!1}}};return(0,l.sV)((()=>{E()})),(e,a)=>{const d=(0,l.g2)("el-button"),E=(0,l.g2)("el-skeleton"),X=(0,l.g2)("el-empty"),z=(0,l.g2)("el-input"),R=(0,l.g2)("el-tooltip"),K=(0,l.g2)("el-descriptions-item"),V=(0,l.g2)("el-descriptions"),x=(0,l.gN)("loading");return(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",o,[(0,l.Lk)("div",c,[(0,l.Lk)("div",r,[a[2]||(a[2]=(0,l.Lk)("h4",null,"Description",-1)),h.value?(0,l.Q3)("",!0):((0,l.uX)(),(0,l.Wv)(d,{key:0,type:"primary",text:"",size:"small",onClick:F,icon:(0,i.R1)(s.Edit)},{default:(0,l.k6)((()=>a[1]||(a[1]=[(0,l.eW)(" Edit ")]))),_:1},8,["icon"]))]),_.value?((0,l.uX)(),(0,l.Wv)(E,{key:0,rows:3,animated:""})):((0,l.uX)(),(0,l.CE)(l.FK,{key:1},[h.value?((0,l.uX)(),(0,l.CE)("div",k,[(0,l.bF)(z,{modelValue:C.value,"onUpdate:modelValue":a[0]||(a[0]=e=>C.value=e),type:"textarea",rows:3,placeholder:"Enter test plan description",maxlength:"500","show-word-limit":""},null,8,["modelValue"]),(0,l.Lk)("div",f,[(0,l.bF)(d,{type:"primary",onClick:W,loading:w.value},{default:(0,l.k6)((()=>a[3]||(a[3]=[(0,l.eW)(" Save ")]))),_:1},8,["loading"]),(0,l.bF)(d,{onClick:L},{default:(0,l.k6)((()=>a[4]||(a[4]=[(0,l.eW)("Cancel")]))),_:1})])])):((0,l.uX)(),(0,l.CE)("div",v,[t.value?.manifest.description?((0,l.uX)(),(0,l.CE)("p",p,(0,n.v_)(t.value?.manifest.description),1)):((0,l.uX)(),(0,l.Wv)(X,{key:1,description:"No description","image-size":100}))]))],64))]),(0,l.Lk)("div",m,[a[5]||(a[5]=(0,l.Lk)("h4",null,"Additional Information",-1)),(0,l.bF)(V,{border:"",column:1},{default:(0,l.k6)((()=>[(0,l.bF)(K,{label:"File Path"},{default:(0,l.k6)((()=>[(0,l.bF)(R,{content:t.value?.path,placement:"top"},{default:(0,l.k6)((()=>[(0,l.Lk)("div",y,(0,n.v_)(t.value?.path),1)])),_:1},8,["content"])])),_:1}),(0,l.bF)(K,{label:"Created"},{default:(0,l.k6)((()=>[(0,l.Lk)("div",b,(0,n.v_)((0,i.R1)(u.r)(t.value?.manifest.created)),1)])),_:1}),(0,l.bF)(K,{label:"Modified"},{default:(0,l.k6)((()=>[(0,l.Lk)("div",g,(0,n.v_)((0,i.R1)(u.r)(t.value?.manifest.modified)),1)])),_:1})])),_:1})])])),[[x,_.value]])}}}),h=t(1241);const C=(0,h.A)(_,[["__scopeId","data-v-cf104d54"]]);var w=C}}]);
//# sourceMappingURL=596.55d4f8cf.js.map