"use strict";(self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[]).push([[557],{3557:function(e,a,n){n.r(a),n.d(a,{default:function(){return K}});var t=n(6768),l=n(2616),i=n(4232);const o={class:"hardware-config-container"},c={class:"middle-panel"},d={class:"form-item-with-tag"},s={class:"status-text"},u={class:"form-item-with-tag"},r={class:"status-text"},m={class:"bottom-panel"};function f(e,a,n,l,f,C){const v=(0,t.g2)("el-radio"),g=(0,t.g2)("el-radio-group"),p=(0,t.g2)("el-form-item"),h=(0,t.g2)("device-channel-select"),y=(0,t.g2)("el-icon"),b=(0,t.g2)("el-tag"),F=(0,t.g2)("el-option"),k=(0,t.g2)("el-select"),S=(0,t.g2)("el-form"),w=(0,t.g2)("el-button"),N=(0,t.gN)("loading");return(0,t.bo)(((0,t.uX)(),(0,t.CE)("div",o,[(0,t.Lk)("div",c,[(0,t.bF)(S,{model:e.form,"label-position":"top","label-width":"160px"},{default:(0,t.k6)((()=>[(0,t.bF)(p,{label:"Bus Type"},{default:(0,t.k6)((()=>[(0,t.bF)(g,{modelValue:e.form.communicationType,"onUpdate:modelValue":a[0]||(a[0]=a=>e.form.communicationType=a),onChange:e.handleCommunicationTypeChange},{default:(0,t.k6)((()=>[(0,t.bF)(v,{label:"Can"},{default:(0,t.k6)((()=>a[6]||(a[6]=[(0,t.eW)("CAN")]))),_:1}),(0,t.bF)(v,{label:"CanFd"},{default:(0,t.k6)((()=>a[7]||(a[7]=[(0,t.eW)("CANFD")]))),_:1})])),_:1},8,["modelValue","onChange"])])),_:1}),"Can"===e.form.communicationType?((0,t.uX)(),(0,t.CE)(t.FK,{key:0},[(0,t.Lk)("div",d,[(0,t.bF)(p,{label:"Device Channel",class:"form-item"},{default:(0,t.k6)((()=>[(0,t.bF)(h,{modelValue:e.form.canConfig.deviceChannelName,"onUpdate:modelValue":a[1]||(a[1]=a=>e.form.canConfig.deviceChannelName=a),devices:e.canDevices},null,8,["modelValue","devices"])])),_:1}),((0,t.uX)(),(0,t.Wv)(b,{key:e.updateTimestamp,class:"device-status-tag",size:"small",type:e.getDeviceConnectStatus(e.form.canConfig.deviceChannelName).isConnected?"success":"danger"},{default:(0,t.k6)((()=>[(0,t.bF)(y,{class:"status-icon"},{default:(0,t.k6)((()=>[((0,t.uX)(),(0,t.Wv)((0,t.$y)(e.getDeviceConnectStatus(e.form.canConfig.deviceChannelName).isConnected?"Connection":"CircleClose")))])),_:1}),(0,t.Lk)("span",s,(0,i.v_)(e.getDeviceConnectStatus(e.form.canConfig.deviceChannelName).isConnected?"Connected":"Disconnected"),1)])),_:1},8,["type"]))]),(0,t.bF)(p,{label:"Baud Rate",class:"form-item"},{default:(0,t.k6)((()=>[(0,t.bF)(k,{modelValue:e.form.canConfig.dataBitrate,"onUpdate:modelValue":a[2]||(a[2]=a=>e.form.canConfig.dataBitrate=a),placeholder:"Select Baud Rate",style:{width:"100%"}},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(e.baudRates,(e=>((0,t.uX)(),(0,t.Wv)(F,{key:e,label:e/1e3+" kbit/s",value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})],64)):((0,t.uX)(),(0,t.CE)(t.FK,{key:1},[(0,t.Lk)("div",u,[(0,t.bF)(p,{label:"Device Channel",class:"form-item"},{default:(0,t.k6)((()=>[(0,t.bF)(h,{modelValue:e.form.canFdConfig.deviceChannelName,"onUpdate:modelValue":a[3]||(a[3]=a=>e.form.canFdConfig.deviceChannelName=a),devices:e.canFdDevices},null,8,["modelValue","devices"])])),_:1}),(0,t.bF)(b,{class:"device-status-tag",size:"small",type:e.getDeviceConnectStatus(e.form.canFdConfig.deviceChannelName).isConnected?"success":"danger"},{default:(0,t.k6)((()=>[(0,t.bF)(y,{class:"status-icon"},{default:(0,t.k6)((()=>[((0,t.uX)(),(0,t.Wv)((0,t.$y)(e.getDeviceConnectStatus(e.form.canFdConfig.deviceChannelName).isConnected?"Connection":"CircleClose")))])),_:1}),(0,t.Lk)("span",r,(0,i.v_)(e.getDeviceConnectStatus(e.form.canFdConfig.deviceChannelName).isConnected?"Connected":"Disconnected"),1)])),_:1},8,["type"])]),(0,t.bF)(p,{label:"Arbitration Phase Baud Rate",class:"form-item"},{default:(0,t.k6)((()=>[(0,t.bF)(k,{modelValue:e.form.canFdConfig.arbitrationBitrate,"onUpdate:modelValue":a[4]||(a[4]=a=>e.form.canFdConfig.arbitrationBitrate=a),placeholder:"Select Arbitration Phase Baud Rate",style:{width:"100%"}},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(e.baudRates,(e=>((0,t.uX)(),(0,t.Wv)(F,{key:e,label:e/1e3+" kbit/s",value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),(0,t.bF)(p,{label:"Data Phase Baud Rate",class:"form-item"},{default:(0,t.k6)((()=>[(0,t.bF)(k,{modelValue:e.form.canFdConfig.dataBitrate,"onUpdate:modelValue":a[5]||(a[5]=a=>e.form.canFdConfig.dataBitrate=a),placeholder:"Select Data Phase Baud Rate",style:{width:"100%"}},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(e.fdDataRates,(e=>((0,t.uX)(),(0,t.Wv)(F,{key:e,label:e/1e3+" kbit/s",value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})],64))])),_:1},8,["model"])]),(0,t.Lk)("div",m,[(0,t.bF)(w,{type:"primary",onClick:e.handleSave,loading:e.isSaving},{default:(0,t.k6)((()=>a[8]||(a[8]=[(0,t.eW)("Save")]))),_:1},8,["onClick","loading"])])])),[[N,e.isLoading]])}n(8111),n(2489),n(116),n(1701),n(3579);var C=n(144),v=n(7477),g=n(1219),p=n(4373),h=n(3144);const y="/api/hardwareConfig",b={getHardwareConfig:()=>h.Xo?h.Z0.hardware.getHardwareConfig():p.A.get(y),updateHardwareConfig:e=>h.Xo?h.Z0.hardware.updateHardwareConfig(e):p.A.post(`${y}/update`,e)};const F={class:"device-option"},k={class:"device-name"};function S(e,a,n,l,o,c){const d=(0,t.g2)("el-tag"),s=(0,t.g2)("el-option"),u=(0,t.g2)("el-select");return(0,t.uX)(),(0,t.Wv)(u,{modelValue:e.selectedValue,"onUpdate:modelValue":a[0]||(a[0]=a=>e.selectedValue=a),placeholder:e.placeholder,class:"device-channel-select","popper-class":"device-channel-select-dropdown",onChange:e.handleChange},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(e.devices,(e=>((0,t.uX)(),(0,t.Wv)(s,{key:e.name,label:e.name,value:e.name,disabled:!e.isConnected},{default:(0,t.k6)((()=>[(0,t.Lk)("div",F,[(0,t.Lk)("span",k,(0,i.v_)(e.name),1),(0,t.bF)(d,{size:"small",type:e.isConnected?"success":"danger"},{default:(0,t.k6)((()=>[(0,t.eW)((0,i.v_)(e.isConnected?"Connected":"Disconnected"),1)])),_:2},1032,["type"])])])),_:2},1032,["label","value","disabled"])))),128))])),_:1},8,["modelValue","placeholder","onChange"])}var w=(0,t.pM)({name:"DeviceChannelSelect",props:{modelValue:{type:String,default:""},devices:{type:Array,required:!0,default:()=>[]},placeholder:{type:String,default:"Select Device Channel"}},emits:["update:modelValue","change"],setup(e,{emit:a}){const n=(0,t.EW)({get:()=>e.modelValue,set:e=>a("update:modelValue",e)}),l=e=>{a("change",e)};return{selectedValue:n,handleChange:l}}}),N=n(1241);const _=(0,N.A)(w,[["render",S],["__scopeId","data-v-f26f9cc6"]]);var V=_,D=(0,t.pM)({components:{DeviceChannelSelect:V,Connection:v.Connection,CircleClose:v.CircleClose},props:{testPlanId:{type:String,required:!1}},setup(e){const a=(0,C.KR)(Date.now()),n=(0,C.KR)({communicationType:"Can",canConfig:{deviceChannelName:"",dataBitrate:5e5},canFdConfig:{deviceChannelName:"",arbitrationBitrate:5e5,dataBitrate:2e6}}),l=(0,C.KR)({}),i=(0,C.KR)(!1),o=(0,C.KR)(!1),c=(0,C.KR)(!1),d=(0,C.KR)(null),s=(0,C.KR)([]),u=(0,C.KR)([]),r=(0,C.KR)([125e3,25e4,5e5,1e6]),m=(0,C.KR)([1e6,2e6,4e6,8e6]),f=(0,t.EW)((()=>s.value.filter((e=>"Can"===e.communicationType||"CanFd"===e.communicationType)))),v=(0,t.EW)((()=>s.value.filter((e=>"CanFd"===e.communicationType))));let p=null,h=!0;const y=()=>{_()},F=async()=>{try{i.value=!0;const e={communicationType:n.value.communicationType,canConfig:"Can"===n.value.communicationType?n.value.canConfig:void 0,canFdConfig:"CanFd"===n.value.communicationType?n.value.canFdConfig:void 0};await b.updateHardwareConfig(e),l.value=JSON.parse(JSON.stringify(n.value)),g.nk.success("Hardware settings saved.")}catch(e){console.error("Failed to save hardware config:",e),g.nk.error("Failed to save hardware configuration")}finally{i.value=!1}},k=async()=>{if(h)try{o.value=!0;const e=await b.getHardwareConfig(),t=e.data.deviceChannels||[],i=S(u.value,t);if(i&&(s.value=t,u.value=JSON.parse(JSON.stringify(t)),a.value=Date.now()),e.data.testPlanConfig){const a=e.data.testPlanConfig,t=w(d.value,a);t&&(d.value=a,n.value={communicationType:a.communicationType,canConfig:{...n.value.canConfig,...a.canConfig},canFdConfig:{...n.value.canFdConfig,...a.canFdConfig}},l.value=JSON.parse(JSON.stringify(n.value)))}}catch(e){console.error("Failed to load hardware config:",e),g.nk.error("Failed to load hardware configuration")}finally{o.value=!1}},S=(e,a)=>{if(!e||!a)return!0;if(e.length!==a.length)return!0;const n=new Map(e.map((e=>[e.name,e.isConnected])));return a.some((e=>n.get(e.name)!==e.isConnected))},w=(e,a)=>{if(!e&&a)return!0;if(e&&!a)return!0;if(!e&&!a)return!1;try{const n=JSON.stringify(e),t=JSON.stringify(a);return n!==t}catch(n){return e.communicationType!==a.communicationType||JSON.stringify(e.canConfig)!==JSON.stringify(a.canConfig)||JSON.stringify(e.canFdConfig)!==JSON.stringify(a.canFdConfig)}},N=()=>{r.value=[125e3,25e4,5e5,1e6],m.value=[1e6,2e6,4e6,8e6]},_=()=>{if("Can"===n.value.communicationType){const e=s.value.find((e=>e.name===n.value.canConfig.deviceChannelName));e&&"Can"!==e.communicationType&&"CanFd"!==e.communicationType&&(n.value.canConfig.deviceChannelName="")}else{const e=s.value.find((e=>e.name===n.value.canFdConfig.deviceChannelName));e&&"CanFd"!==e.communicationType&&(n.value.canFdConfig.deviceChannelName="")}},V=(0,t.EW)((()=>e=>{const a=s.value.find((a=>a.name===e));return{isConnected:a?.isConnected||!1}})),D=e=>`${e.name} ${e.isConnected?"(Connected)":"(Disconnected)"}`,T=async()=>{try{c.value=!0,h=!0,await k(),g.nk.success("Device list refreshed")}finally{c.value=!1}},R=()=>{p=setInterval((()=>{k()}),2e3)},W=()=>{h=!1,p&&(clearInterval(p),p=null)};return(0,t.sV)((async()=>{N(),await k(),(0,t.dY)((()=>{l.value=JSON.parse(JSON.stringify(n.value))})),R()})),(0,t.hi)((()=>{W()})),(0,t.wB)((()=>e.testPlanId),(async e=>{e&&(await k(),(0,t.dY)((()=>{l.value=JSON.parse(JSON.stringify(n.value))})))})),{form:n,isLoading:o,isRefreshing:c,isSaving:i,canDevices:f,canFdDevices:v,baudRates:r,fdDataRates:m,handleCommunicationTypeChange:_,handleValueChange:y,handleSave:F,getDeviceConnectStatus:V,getDeviceLabel:D,refreshDevices:T,updateTimestamp:a}}});const T=(0,N.A)(D,[["render",f],["__scopeId","data-v-602982e2"]]);var R=T;const W={class:"hardware-setting-container"};var X=(0,t.pM)({__name:"HardwareSetting",setup(e){const a=l.f.getState(),n=(0,t.EW)((()=>a.currentPlan)),i=(0,t.EW)((()=>a.isLoading)),o=async()=>{await l.f.getCurrentPlan()};return(0,t.sV)((()=>{o()})),(e,a)=>{const l=(0,t.gN)("loading");return(0,t.bo)(((0,t.uX)(),(0,t.CE)("div",W,[(0,t.bF)(R,{"test-plan-id":n.value?.path},null,8,["test-plan-id"])])),[[l,i.value]])}}});const B=(0,N.A)(X,[["__scopeId","data-v-01948689"]]);var K=B}}]);
//# sourceMappingURL=557.e6805341.js.map