using FreeSql.DataAnnotations;
using System;

namespace Alsi.Fuzz.Core.Service.Results
{
    [Index("index_CaseResultId", "CaseResultId")]
    public class CaseStep
    {
        [Column(IsIdentity = true, IsPrimary = true)]
        public Guid Id { get; set; }
        public string Name { get; set; }
        public int CaseResultId { get; set; }
        public ulong Timestamp { get; set; }
        public ulong? FrameTimestamp { get; set; }
        public CaseStepState State { get; set; }
        public DateTime? Begin { get; set; }
        public DateTime? End { get; set; }
        public string Detail { get; set; }
    }

    public enum CaseStepState
    {
        // 用例状态，没有待执行
        //Pending = 1,
        Running = 2,
        Success = 3,
        Failure = 4,
        // 用例状态，没有暂停
        //Paused = 5,
        Completed = 6
    }
}
