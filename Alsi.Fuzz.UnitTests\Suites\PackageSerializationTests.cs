using Alsi.Fuzz.Core;
using Alsi.Fuzz.Core.Models.TestSuites.Steps;
using Alsi.Fuzz.Core.Service;
using Alsi.Fuzz.Core.Utils;
using Shouldly;

namespace Alsi.Fuzz.UnitTests.Suites
{
    public class PackageSerializationTests : UnitTestBase
    {
        private readonly string _testDir;
        private const string TempFolderName = "AlsiFuzzTests";
        private readonly BuiltInTestSuiteService _builtInTestSuiteService;

        public PackageSerializationTests() : base()
        {
            _testDir = Path.Combine(Path.GetTempPath(), TempFolderName, Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDir);
            _builtInTestSuiteService = new BuiltInTestSuiteService();
        }

        private static readonly string testSuiteNameCan = "CAN-Bus Test Suite";
        private static readonly string testSuiteNameCanFd = "CAN-FD Test Suite";

        private static readonly string sequenceName14229 = "DiagnosticSessionControl (0x10) service - Default Session";

        private static string sequenceName11898 => FuzzConsts.SequenceName11898Basic;
        private static string packageName11898 => FuzzConsts.PackageName11898;
        private static string packageName14229 => FuzzConsts.PackageName14229;

        private static readonly string TestXml = $@"
<package name=""{packageName11898}"">
    <sequence name=""{sequenceName11898}"">
        <send name=""diagnostic-control-session"">
            <store field=""*.DiagnosticService.*.DiagnosticSessionControl"" />
        </send>
        <recv name=""uds-response"">
            <match field=""*.DiagnosticService.*.DiagnosticSessionControlResponse""/>
        </recv>
    </sequence>
</package>";

        [Fact]
        public void LoadFromString_ValidXml_ShouldDeserializeCorrectly()
        {
            // Act
            var package = SequencePackageUtils.LoadFromString(TestXml);

            // Assert
            package.ShouldNotBeNull();
            package.Name.ShouldBe(packageName11898);

            package.Sequences.Count.ShouldBe(1);
            var sequence = package.Sequences[0];
            sequence.Name.ShouldBe(sequenceName11898);

            sequence.Steps.Count.ShouldBe(2);
            var sendStep = sequence.Steps[0].ShouldBeOfType<SendStep>();
            sendStep.Name.ShouldBe("diagnostic-control-session");

            var recvStep = sequence.Steps[1].ShouldBeOfType<ReceiveStep>();
            recvStep.Name.ShouldBe("uds-response");
        }

        [Fact]
        public void LoadFromString_InvalidXml_ShouldThrow()
        {
            // Act & Assert
            Should.Throw<InvalidOperationException>(() =>
                SequencePackageUtils.LoadFromString("<invalid>xml"));
        }

        [Fact]
        public void LoadFromString_CanSdkSeq_ShouldDeserialize()
        {
            // Arrange & Act
            var canBusSuite = _builtInTestSuiteService.GetByName(testSuiteNameCan);
            var package = canBusSuite.Packages.First();

            // Assert
            package.ShouldNotBeNull();
            package.Sequences.ShouldNotBeEmpty();

            var sequence = package.Sequences[0];
            sequence.Name.ShouldBe(sequenceName11898);
        }

        [Fact]
        public void LoadFromString_CanUdsSeq_ShouldDeserialize()
        {
            // Arrange & Act
            var canFdSuite = _builtInTestSuiteService.GetByName(testSuiteNameCanFd);
            var package = canFdSuite.Packages.First(x => x.Name == packageName14229);

            // Assert
            package.ShouldNotBeNull();
            package.Sequences.ShouldNotBeEmpty();

            var sequence = package.Sequences.First();
            sequence.ShouldNotBeNull();
            sequence.Name.ShouldBe(sequenceName14229);
        }

        [Fact]
        public void BuiltInTestSuiteService_ShouldLoadTestSuites()
        {
            // Act
            var testSuites = _builtInTestSuiteService.Get();

            // Assert
            testSuites.Count.ShouldBe(2);
            testSuites.ShouldContain(x => x.Name == testSuiteNameCan);
            testSuites.ShouldContain(x => x.Name == testSuiteNameCanFd);

            foreach (var suite in testSuites)
            {
                suite.Packages.ShouldNotBeEmpty();
                foreach (var package in suite.Packages)
                {
                    package.Sequences.ShouldNotBeEmpty();
                }
            }
        }

        [Fact]
        public void BuiltInTestSuiteService_GetByName_ShouldReturnCorrectSuite()
        {
            // Arrange
            var service = new BuiltInTestSuiteService();

            // Act
            var canBusSuite = service.GetByName(testSuiteNameCan);
            var canFdSuite = service.GetByName(testSuiteNameCanFd);

            // Assert
            canBusSuite.ShouldNotBeNull();
            canFdSuite.ShouldNotBeNull();

            canBusSuite.Version.ShouldBe("1.0");
            canFdSuite.Version.ShouldBe("1.0");

            canBusSuite.Packages.ShouldNotBeEmpty();
            canFdSuite.Packages.ShouldNotBeEmpty();
        }

        [Fact]
        public void GetXml_ValidTestSuite_ShouldReturnXmlContent()
        {
            var testSuite = _builtInTestSuiteService.Get().First(x => x.Name == testSuiteNameCan);
            var suiteName = testSuite.Name;
            var package = testSuite.Packages.First(x => x.Name == packageName11898);

            // Act
            var xml = _builtInTestSuiteService.GetBuiltInXml(suiteName, package.Name);

            // Assert
            xml.ShouldNotBeNullOrWhiteSpace();
            xml.ShouldContain("<package");
            xml.ShouldContain(packageName11898);
        }

        [Fact]
        public void GetXml_InvalidTestSuiteName_ShouldThrow()
        {
            var packageName = _builtInTestSuiteService.Get().First().Packages.First().Name;

            // Act & Assert
            Should.Throw<InvalidOperationException>(() =>
                _builtInTestSuiteService.GetBuiltInXml("Non-Existent Suite", packageName))
                .Message.ShouldBe("Test suite 'Non-Existent Suite' not found");
        }

        [Fact]
        public void GetXml_XmlWithEnvVars_ShouldDeserializeCorrectly()
        {
            // Act
            var testSuite = _builtInTestSuiteService.Get().First(x => x.Name == testSuiteNameCan);
            var package = testSuite.Packages.First(x => x.Name == packageName11898);

            // Assert
            package.ShouldNotBeNull();
            package.Name.ShouldBe(packageName11898);

            // 验证环境变量
            package.SetVars.ShouldNotBeNull();
            package.SetVars.Count.ShouldBeGreaterThan(2);

            var dict = new Dictionary<string, string> {
                {  "$SendFrameId", "0x200" },
                {  "$SendFrameData", "11 22 33 44 55 66 77 88" },
            };

            foreach (var key in dict.Keys)
            {
                var envVar = package.SetVars.FirstOrDefault(v => v.Name == key);
                envVar.ShouldNotBeNull();
                envVar.Value.ShouldBe(envVar.Value);
            }
        }

        public override void Dispose()
        {
            base.Dispose();

            if (Directory.Exists(_testDir))
            {
                if (_testDir.Contains(TempFolderName))
                {
                    try
                    {
                        Directory.Delete(_testDir, true);
                    }
                    catch
                    {
                        // 忽略删除临时目录的错误
                    }
                }
            }
        }
    }
}
