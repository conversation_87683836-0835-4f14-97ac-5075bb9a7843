using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Iso14229;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G3211_CaseFactory : CaseFactoryBase
    {
        public override void Generate(MutationOptions options, Action<CreateCaseInfo> createCase)
        {
            var xmlServices = options.XmlServices;

            // 仅遍历支持的服务
            var supportedXmlServices = xmlServices
                .Where(x => x.IsSupported)
                .ToArray();

            foreach (var supportedXmlService in supportedXmlServices)
            {
                var sid = supportedXmlService.Id;
                var serviceName = supportedXmlService.IsoUdsServiceDisplayName;

                for (var parameter2 = 0; parameter2 <= 0xFF; parameter2++)
                {
                    var payload = new List<byte> { 0x7F, sid, (byte)parameter2 };
                    var groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat.NegativeResponse().GetService(serviceName).Path;
                    var name = $"SupportedXmlService:{serviceName} -{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G3211-Sid{sid:X2}-Parameter2{parameter2:X2}", caseMutation, sid, supportedXmlService.SubfunctionId));
                }
            }
        }
    }
}
