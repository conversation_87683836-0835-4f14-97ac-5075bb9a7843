using Alsi.Common.Utils;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Iso14229;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G431_G432_CaseFactory : CaseFactoryBase
    {
        public override void Generate(MutationOptions options, Action<CreateCaseInfo> createCase)
        {
            var xmlServices = options.XmlServices;
            var supportedXmlServicesSF = xmlServices
                .Where(x => x.IsSupported && !x.HasMultipleFrameRequest)
                .ToArray();
            var groupPath = Iso14229CaseGroupConsts.IsoTpBuilder.FirstFrame().Path;

            foreach (var byteN_PCI in new byte[] { 0x10, 0x21, 0x22, 0x23 })
            {
                foreach (var xmlService in supportedXmlServicesSF)
                {
                    var sid = xmlService.Id;
                    var parameter2k = xmlService.Parameter2k;

                    var payload = new List<byte> { sid };
                    if (xmlService.SubfunctionId.HasValue)
                    {
                        payload.Add(xmlService.SubfunctionId.Value);
                    }
                    payload.AddRange(parameter2k);
                    var serviceName = xmlService.IsoUdsServiceDisplayName;
                    var name = $"serviceName {serviceName} byteN_PCI 0x{byteN_PCI:X}-{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .Mutate(MutationFieldType.UDS_Insert_Byte_Before_N_PCI, $"0x{byteN_PCI:X}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G431-Sid{sid:X2}", caseMutation, sid, xmlService.SubfunctionId));
                }
            }

            var supportedXmlServicesMF = xmlServices
                .Where(x => x.IsSupported && x.HasMultipleFrameRequest)
                .ToArray();
            groupPath = Iso14229CaseGroupConsts.IsoTpBuilder.SingleFrame().Path;
            // #4-3-2 Send Multi Frame service Content As SingleFrame
            foreach (var xmlService in supportedXmlServicesMF)
            {
                var sid = xmlService.Id;
                var parameter2k = xmlService.Parameter2k;

                var payload = new List<byte> { sid };
                if (xmlService.SubfunctionId.HasValue)
                {
                    payload.Add(xmlService.SubfunctionId.Value);
                }
                payload.AddRange(parameter2k);

                foreach (var length in new[] { 1, 2, 3, 4, 5, 6 })
                {
                    var serviceName = xmlService.IsoUdsServiceDisplayName;
                    var name = $"serviceName {serviceName}-{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .MutatePayload(payload.Take(length).ToArray())
                        .MutatePayloadLength(length);
                    createCase(new CreateCaseInfo($"G432-Sid{sid:X2}", caseMutation, sid, xmlService.SubfunctionId));
                }
            }

            // #4-3-3 Multiframe Transmit N_PCI Error
            // 通过增加/减少数据，调整到 23 bytes
            // 然后按 MF 发送，异变其中 FF、CF 的 HighNibble 和 SN
            groupPath = Iso14229CaseGroupConsts.IsoTpBuilder.N_PCIError().Path;
            foreach (var xmlService in supportedXmlServicesMF)
            {
                var sid = xmlService.Id;
                var parameter2k = xmlService.Parameter2k;
                var serviceName = xmlService.IsoUdsServiceDisplayName;

                var payload = new List<byte> { sid };
                if (xmlService.SubfunctionId.HasValue)
                {
                    payload.Add(xmlService.SubfunctionId.Value);
                }
                payload.AddRange(parameter2k);

                payload = payload.Take(23).ToList();
                while (payload.Count < 23)
                {
                    payload.Add(0x55);
                }

                for (var invalidHighNibble = 0; invalidHighNibble <= 0xF; invalidHighNibble++)
                {
                    if (invalidHighNibble != 1)
                    {
                        
                        var name = $"serviceName {serviceName} invalidHighNibble 0x{invalidHighNibble:X}-{groupPath}";
                        var caseMutation = CaseMutation.Create(name, groupPath)
                            .Mutate(MutationFieldType.UDS_FF_HighNibble, $"0x{invalidHighNibble:X}")
                            .MutatePayload(payload.ToArray())
                            .MutatePayloadLength(payload.Count);
                        createCase(new CreateCaseInfo($"G4331-Sid{sid:X2}", caseMutation, sid, xmlService.SubfunctionId));
                    }
                }


                // FFDL_12bit = 00
                {
                    var name = $"serviceName {serviceName} 0x0 -{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .Mutate(MutationFieldType.UDS_FF_DL_12b, $"0x0")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G4332-Sid{sid:X2}", caseMutation, sid, xmlService.SubfunctionId));
                }
                // FFDL_12bit = 均匀采样15（01h, FFFh）
                var sampler = new UniformSampler(options.Random);
                foreach (var ffdl_12b in sampler.UniformSample(1, 0xFFF, 15))
                {
                    var name = $"serviceName {serviceName} ffdl_12b 0x{ffdl_12b:X} -{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .Mutate(MutationFieldType.UDS_FF_DL_12b, $"0x{ffdl_12b:X}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G4332-Sid{sid:X2}", caseMutation, sid, xmlService.SubfunctionId));
                }

                foreach (var invalidHighNibble in Enumerable.Range(0, 0xF + 1).Except(new[] { 2 }))
                {
                    var data = new byte[] { (byte)(invalidHighNibble * 0x10 + 1), 0x22, 0x23 };
                    var name = $"serviceName {serviceName} UDS_CF_BYTE1_LIST hex: {data.ToHex()} -{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .Mutate(MutationFieldType.UDS_CF_BYTE1_LIST, $"hex: {data.ToHex()}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G4333-Sid{sid:X2}", caseMutation, sid, xmlService.SubfunctionId));
                }

                foreach (var invalidSN in Enumerable.Range(0, 0xF + 1).Except(new[] { 1 }))
                {
                    var data = new byte[] { (byte)(0x20 + invalidSN), 0x22, 0x23 };
                    var name = $"serviceName {serviceName} UDS_CF_BYTE1_LIST hex: {data.ToHex()} -{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .Mutate(MutationFieldType.UDS_CF_BYTE1_LIST, $"hex: {data.ToHex()}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G4334-Sid{sid:X2}", caseMutation, sid, xmlService.SubfunctionId));
                }

                foreach (var invalidHighNibble in Enumerable.Range(0, 0xF + 1).Except(new[] { 2 }))
                {
                    var data = new byte[] { 0x21, (byte)(invalidHighNibble * 0x10 + 2), 0x23 };
                    var name = $"serviceName {serviceName} invalidHighNibble hex: {data.ToHex()} -{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .Mutate(MutationFieldType.UDS_CF_BYTE1_LIST, $"hex: {data.ToHex()}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G4335-Sid{sid:X2}", caseMutation, sid, xmlService.SubfunctionId));
                }

                foreach (var invalidSN in Enumerable.Range(0, 0xF + 1).Except(new[] { 2 }))
                {
                    var data = new byte[] { 0x21, (byte)(0x20 + invalidSN), 0x23 };
                    var name = $"serviceName {serviceName} invalidSN hex: {data.ToHex()} -{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .Mutate(MutationFieldType.UDS_CF_BYTE1_LIST, $"hex: {data.ToHex()}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G4336-Sid{sid:X2}", caseMutation, sid, xmlService.SubfunctionId));
                }

                foreach (var invalidHighNibble in Enumerable.Range(0, 0xF + 1).Except(new[] { 2 }))
                {
                    var data = new byte[] { 0x21, 0x22, (byte)(invalidHighNibble * 0x10 + 3) };
                    var name = $"serviceName {serviceName} invalidHighNibble hex: {data.ToHex()} -{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .Mutate(MutationFieldType.UDS_CF_BYTE1_LIST, $"hex: {data.ToHex()}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G4337-Sid{sid:X2}", caseMutation, sid, xmlService.SubfunctionId));
                }

                foreach (var invalidSN in Enumerable.Range(0, 0xF + 1).Except(new[] { 3 }))
                {
                    var data = new byte[] { 0x21, 0x22, (byte)(0x20 + invalidSN) };
                    var name = $"serviceName {serviceName} invalidSN hex: {data.ToHex()} -{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .Mutate(MutationFieldType.UDS_CF_BYTE1_LIST, $"hex: {data.ToHex()}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G4338-Sid{sid:X2}", caseMutation, sid, xmlService.SubfunctionId));
                }
            }

            // #4-3-4 Multiframe Transmit Sequence Error
            groupPath = Iso14229CaseGroupConsts.IsoTpBuilder.SequenceError().Path;
            foreach (var xmlService in supportedXmlServicesMF)
            {
                var sid = xmlService.Id;
                var parameter2k = xmlService.Parameter2k;
                var serviceName = xmlService.IsoUdsServiceDisplayName;

                var payload = new List<byte> { sid };
                if (xmlService.SubfunctionId.HasValue)
                {
                    payload.Add(xmlService.SubfunctionId.Value);
                }
                payload.AddRange(parameter2k);

                payload = payload.Take(23).ToList();
                while (payload.Count < 23)
                {
                    payload.Add(0x55);
                }

                var name = "";
 
                // 1/2 : Replace Initial CF1 with manipulated FF
                for (var invalidHighNibble = 0; invalidHighNibble <= 0xF; invalidHighNibble++)
                {
                    if (invalidHighNibble != 1)
                    {
                        name = $"serviceName {serviceName}-CF1 with manipulated FF-{groupPath}";
                        var caseMutation = CaseMutation.Create(name, groupPath)
                            .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_NUM, "1")
                            .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_N_PCI, $"0x{invalidHighNibble:X}")
                            .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_DL_12b, "0x017")
                            .MutatePayload(payload.ToArray())
                            .MutatePayloadLength(payload.Count);
                        createCase(new CreateCaseInfo($"G4341-Sid{sid:X2}-CF1", caseMutation, sid, xmlService.SubfunctionId));
                    }
                }

                var sampler = new UniformSampler(options.Random);
                foreach (var ffdl_12b in new[] { 0 }.Concat(sampler.UniformSample(1, 0xFFF, 15)))
                {
                    name = $"serviceName {serviceName}-CF1 with manipulated FF-{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                       .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_NUM, "1")
                        .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_N_PCI, "0x1")
                        .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_DL_12b, $"0x{ffdl_12b:X}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G4341-Sid{sid:X2}-CF1", caseMutation, sid, xmlService.SubfunctionId));
                }

                // 3/4 : Replace Initial CF2 with manipulated FF
                for (var invalidHighNibble = 0; invalidHighNibble <= 0xF; invalidHighNibble++)
                {
                    if (invalidHighNibble != 1)
                    {
                        name = $"serviceName {serviceName}-CF2 with manipulated FF-{groupPath}";
                        var caseMutation = CaseMutation.Create(name, groupPath)
                            .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_NUM, "2")
                            .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_N_PCI, $"0x{invalidHighNibble:X}")
                            .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_DL_12b, "0x017")
                            .MutatePayload(payload.ToArray())
                            .MutatePayloadLength(payload.Count);
                        createCase(new CreateCaseInfo($"G4341-Sid{sid:X2}-CF2", caseMutation, sid, xmlService.SubfunctionId));
                    }
                }

                foreach (var ffdl_12b in new[] { 0 }.Concat(sampler.UniformSample(1, 0xFFF, 15)))
                {
                    name = $"serviceName {serviceName}-CF2 with manipulated FF-{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_NUM, "2")
                        .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_N_PCI, "0x1")
                        .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_DL_12b, $"0x{ffdl_12b:X}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G4341-Sid{sid:X2}-CF2", caseMutation, sid, xmlService.SubfunctionId));
                }

                // 5/6 : Replace Initial CF3 with manipulated FF
                for (var invalidHighNibble = 0; invalidHighNibble <= 0xF; invalidHighNibble++)
                {
                    if (invalidHighNibble != 1)
                    {
                        name = $"serviceName {serviceName}-CF3 with manipulated FF-{groupPath}";
                        var caseMutation = CaseMutation.Create(name, groupPath)
                            .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_NUM, "3")
                            .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_N_PCI, $"0x{invalidHighNibble:X}")
                            .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_DL_12b, "0x017")
                            .MutatePayload(payload.ToArray())
                            .MutatePayloadLength(payload.Count);
                        createCase(new CreateCaseInfo($"G4341-Sid{sid:X2}-CF3", caseMutation, sid, xmlService.SubfunctionId));
                    }
                }

                foreach (var ffdl_12b in new[] { 0 }.Concat(sampler.UniformSample(1, 0xFFF, 15)))
                {
                    name = $"serviceName {serviceName}-CF3 with manipulated FF-{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_NUM, "3")
                        .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_N_PCI, "0x1")
                        .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_DL_12b, $"0x{ffdl_12b:X}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G4341-Sid{sid:X2}-CF3", caseMutation, sid, xmlService.SubfunctionId));
                }

                // 7-10: Repeat Initial Frames
                foreach (int frameIndex in new[] { 0, 1, 2, 3 }) // 0=FF, 1=CF1, 2=CF2, 3=CF3
                {
                    foreach (int times in new[] { 5, 50, 100 })
                    {
                        name = $"serviceName {serviceName}-repeat Initial Frame-{groupPath}";
                        var caseMutation = CaseMutation.Create(name, groupPath)
                            .Mutate(MutationFieldType.UDS_Repeat_Index, $"{frameIndex}")
                            .Mutate(MutationFieldType.UDS_Repeat_Times, $"{times}")
                            .MutatePayload(payload.ToArray())
                            .MutatePayloadLength(payload.Count);
                        createCase(new CreateCaseInfo($"G4342-Sid{sid:X2}", caseMutation, sid, xmlService.SubfunctionId));
                    }
                }

                // 11-14: Skip Initial Frames
                foreach (int frameIndex in new[] { 0, 1, 2, 3 }) // 0=FF, 1=CF1, 2=CF2, 3=CF3
                {
                    name = $"serviceName {serviceName}-Skip Initial Frame-{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .Mutate(MutationFieldType.UDS_Skip_Index, $"{frameIndex}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G4343-Sid{sid:X2}", caseMutation, sid, xmlService.SubfunctionId));
                }

                // 15-20: Swap Frames
                // Swap FF with CFx
                foreach (int cfIndex in new[] { 1, 2, 3 })
                {
                    name = $"serviceName {serviceName}-Swap Frames-{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .Mutate(MutationFieldType.UDS_Swap_A, "0")  // FF
                        .Mutate(MutationFieldType.UDS_Swap_B, $"{cfIndex}")  // CFx
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G4344-Sid{sid:X2}", caseMutation, sid, xmlService.SubfunctionId));
                }

                // Swap CF1 with CF2/CF3
                foreach (int cfIndex in new[] { 2, 3 })
                {
                    name = $"serviceName {serviceName}-Swap CF1 with CF{cfIndex}-{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .Mutate(MutationFieldType.UDS_Swap_A, "1")  // CF1
                        .Mutate(MutationFieldType.UDS_Swap_B, $"{cfIndex}")  // CF2 or CF3
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G4344-Sid{sid:X2}", caseMutation, sid, xmlService.SubfunctionId));
                }

                // Swap CF2 with CF3
                name = $"serviceName {serviceName}-Swap CF2 with CF3-{groupPath}";
                var caseSwapCF2CF3 = CaseMutation.Create(name, groupPath)
                    .Mutate(MutationFieldType.UDS_Swap_A, "2")  // CF2
                    .Mutate(MutationFieldType.UDS_Swap_B, "3")  // CF3
                    .MutatePayload(payload.ToArray())
                    .MutatePayloadLength(payload.Count);
                createCase(new CreateCaseInfo($"G4344-Sid{sid:X2}", caseSwapCF2CF3, sid, xmlService.SubfunctionId));

                // 21-22: Reverse Frame Order
                // Reverse: CF3->CF2->CF1->FF
                name = $"serviceName {serviceName}-Reverse Frame Order:CF3->CF2->CF1->FF-{groupPath}";
                var caseReverse1 = CaseMutation.Create(name, groupPath)
                    .Mutate(MutationFieldType.UDS_Reverse_From, "0")  // FF
                    .Mutate(MutationFieldType.UDS_Reverse_To, "3")    // CF3
                    .MutatePayload(payload.ToArray())
                    .MutatePayloadLength(payload.Count);
                createCase(new CreateCaseInfo($"G4345-Sid{sid:X2}", caseReverse1, sid, xmlService.SubfunctionId));

                // Reverse: FF->CF3->CF2->CF1
                name = $"serviceName {serviceName}-Reverse Frame Order:FF->CF3->CF2->CF1-{groupPath}";
                var caseReverse2 = CaseMutation.Create(name, groupPath)
                    .Mutate(MutationFieldType.UDS_Reverse_From, "1")  // FF
                    .Mutate(MutationFieldType.UDS_Reverse_To, "3")    // CF1
                    .MutatePayload(payload.ToArray())
                    .MutatePayloadLength(payload.Count);
                createCase(new CreateCaseInfo($"G4345-Sid{sid:X2}", caseReverse2, sid, xmlService.SubfunctionId));
            }
        }
    }
}
