{"version": 3, "file": "js/946.04d16d95.js", "mappings": "iLAOA,GAA4BA,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,eACRC,MAAO,CACLC,MAAO,CAAC,GAEVC,KAAAA,CAAMC,GCFR,MAAMH,EAAQG,EAIRC,GAAUC,EAAAA,EAAAA,KAAoD,KAClE,OAAQL,EAAMC,OACZ,KAAKK,EAAAA,GAAeC,QAClB,MAAO,UACT,KAAKD,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeG,QAClB,MAAO,SACT,KAAKH,EAAAA,GAAeI,QACpB,QACE,MAAO,O,IAIPC,EAAoBV,IACxB,OAAQA,GACN,KAAKK,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeI,QAClB,MAAO,UACT,KAAKJ,EAAAA,GAAeC,QAClB,MAAO,YACT,KAAKD,EAAAA,GAAeG,QAClB,MAAO,UACT,KAAKH,EAAAA,GAAeM,OAClB,MAAO,SACT,QACE,MAAO,U,EAIPC,GAAYR,EAAAA,EAAAA,KAAS,IAClBM,EAAiBX,EAAMC,SDKhC,MAAO,CAACa,EAAUC,KAChB,MAAMC,GAAoBC,EAAAA,EAAAA,IAAkB,UAE5C,OAAQC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAaH,EAAmB,CACpDI,KAAMhB,EAAQiB,MACdC,KAAM,SACL,CACDC,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBC,EAAAA,EAAAA,KAAiBC,EAAAA,EAAAA,IAAiBb,EAAUQ,OAAQ,MAEtDM,EAAG,GACF,EAAG,CAAC,QAAQ,CAEjB,IE7DA,MAAMC,EAAc,EAEpB,O,uJCFA,MAAMC,EAAa,CAAEC,MAAO,sBACtBC,EAAa,CAAED,MAAO,WACtBE,EAAa,CAAEF,MAAO,kBACtBG,EAAa,CAAEH,MAAO,gBACtBI,EAAa,CAAEJ,MAAO,mBACtBK,EAAa,CAAEL,MAAO,0BACtBM,EAAa,CAAEN,MAAO,6BACtBO,EAAa,CAAEP,MAAO,cACtBQ,EAAa,CAAER,MAAO,iBACtBS,EAAc,CAAET,MAAO,cACvBU,EAAc,CAAEV,MAAO,yBACvBW,EAAc,CAAEX,MAAO,aACvBY,EAAc,CAAEZ,MAAO,aACvBa,EAAc,CAAEb,MAAO,cACvBc,EAAc,CAAEd,MAAO,8BACvBe,EAAc,CAAEf,MAAO,aACvBgB,EAAc,CAAEhB,MAAO,aACvBiB,EAAc,CAAEjB,MAAO,cACvBkB,EAAc,CAAElB,MAAO,2BACvBmB,EAAc,CAAEnB,MAAO,aACvBoB,EAAc,CAAEpB,MAAO,aACvBqB,EAAc,CAAErB,MAAO,cACvBsB,EAAc,CAAEtB,MAAO,2BACvBuB,EAAc,CAAEvB,MAAO,aACvBwB,EAAc,CAAExB,MAAO,aACvByB,EAAc,CAAEzB,MAAO,cACvB0B,EAAc,CAClBC,IAAK,EACL3B,MAAO,yBAEH4B,EAAc,CAAE5B,MAAO,qBACvB6B,EAAc,CAAE7B,MAAO,mBACvB8B,EAAc,CAAE9B,MAAO,sBACvB+B,EAAc,CAAE/B,MAAO,eACvBgC,EAAc,CAClBL,IAAK,EACL3B,MAAO,aAEHiC,EAAc,CAAEjC,MAAO,cACvBkC,EAAc,CAClBP,IAAK,EACL3B,MAAO,aAEHmC,EAAc,CAAEnC,MAAO,cACvBoC,EAAc,CAClBT,IAAK,EACL3B,MAAO,aAEHqC,EAAc,CAAErC,MAAO,cACvBsC,EAAc,CAClBX,IAAK,EACL3B,MAAO,gBAqBT,OAA4BhC,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,UACRG,KAAAA,CAAMC,GCiGR,MAAMkE,GAAsBC,EAAAA,EAAAA,IAAI,GAC1BC,GAAUD,EAAAA,EAAAA,KAAI,GACdE,GAAWF,EAAAA,EAAAA,KAAI,GACfG,GAAWH,EAAAA,EAAAA,KAAI,GACfI,GAAUJ,EAAAA,EAAAA,KAAI,GACdK,GAAWL,EAAAA,EAAAA,KAAI,GACfM,GAAYN,EAAAA,EAAAA,IAAI,IAEhBO,IAAYP,EAAAA,EAAAA,IAAoB,CACpCQ,aAAcxE,EAAAA,GAAeI,QAC7BqE,iBAAkB,GAClBC,WAAY,CACVC,GAAI,GACJC,iBAAkB,GAClBC,SAAU,GACVC,aAAc,GACdC,WAAY,EACZC,aAAc,EACdC,aAAc,GAEhBC,YAAa,KAETC,IAAcnB,EAAAA,EAAAA,IAAI,IAGxB,IAAIoB,GAAoC,KAGxC,MAAMC,IAAYtF,EAAAA,EAAAA,KAAS,IAClBwE,GAAUxD,MAAMyD,eAAiBxE,EAAAA,GAAeE,UAGnDoF,IAAWvF,EAAAA,EAAAA,KAAS,IACjBwE,GAAUxD,MAAMyD,eAAiBxE,EAAAA,GAAeM,SAGnDyE,IAAahF,EAAAA,EAAAA,KAAS,IAErBwE,GAAUxD,MAAM2D,YAAwD,IAA1CH,GAAUxD,MAAM2D,WAAWK,WAGvDR,GAAUxD,MAAM2D,WAAWK,WAFzBhB,EAAoBhD,QAKzBwE,IAAiBxF,EAAAA,EAAAA,KAAS,IAEzBwE,GAAUxD,MAAM2D,YAGbH,GAAUxD,MAAM2D,WAAWM,cAAgB,IAAMT,GAAUxD,MAAM2D,WAAWO,cAAgB,GAF3F,IAKLO,IAAqBzF,EAAAA,EAAAA,KAAS,IACT,IAArBgF,GAAWhE,MAAoB,EAC5B0E,KAAKC,MAAOH,GAAexE,MAAQgE,GAAWhE,MAAS,OAG1D4E,IAAiB5F,EAAAA,EAAAA,KAAS,IACL,IAArBgF,GAAWhE,MAAoB,GAE3BwE,GAAexE,MAAQgE,GAAWhE,MAAQ,KAAK6E,QAAQ,KAI3DC,GAAUC,IACd,MAAMC,EAAO,GAAGR,GAAexE,WAAWgE,GAAWhE,QAoBrD,OAjBAiF,EAAAA,EAAAA,KAAS,KACP,MAAMC,EAAeC,SAASC,cAAc,sBACxCF,IAEFA,EAAaG,UAAUC,OAAO,aAAc,cAAe,cAGvDN,EAAKO,QAAU,GACjBL,EAAaG,UAAUG,IAAI,cAClBR,EAAKO,QAAU,GACxBL,EAAaG,UAAUG,IAAI,eAE3BN,EAAaG,UAAUG,IAAI,c,IAK1BR,CAAI,EAIPS,IAAkBzG,EAAAA,EAAAA,KAAS,KAC/B,IAAKuE,EAAUvD,OAA8B,IAArBgE,GAAWhE,OAAwC,IAAzBwE,GAAexE,QAAgBsE,GAAUtE,MACzF,OAAO,EAGT,MAAM0F,EAAe,IAAIC,KAAKpC,EAAUvD,OAClC4F,EAAYD,KAAKE,MAAQH,EAAaI,UACtCC,EAAYH,EAAYpB,GAAexE,MACvCgG,EAAiBhC,GAAWhE,MAAQwE,GAAexE,MACzD,OAAO+F,EAAYC,CAAc,IAI7BC,IAAmBjH,EAAAA,EAAAA,KAAS,KAChC,IAAKuE,EAAUvD,OAA8B,IAArBgE,GAAWhE,OAAwC,IAAzBwE,GAAexE,QAAgBsE,GAAUtE,MACzF,MAAO,GAGT,MAAM0F,EAAe,IAAIC,KAAKpC,EAAUvD,OAClC4F,EAAYD,KAAKE,MAAQH,EAAaI,UACtCC,EAAYH,EAAYpB,GAAexE,MACvCgG,EAAiBhC,GAAWhE,MAAQwE,GAAexE,MACnDkG,EAAcH,EAAYC,EAEhC,GAAIE,GAAe,EACjB,MAAO,gBAGT,MAAMC,EAAa,IAAIR,KAAKA,KAAKE,MAAQK,GACzC,OAAOE,EAAAA,EAAAA,GAAeD,EAAW,IAI7BE,GAA2BC,UAC/BpD,EAAQlD,OAAQ,EAChB,IACE,MAAMuG,QAAiBC,EAAAA,GAAOC,oBAC9BzD,EAAoBhD,MAAQuG,EAASG,I,CACrC,MAAOC,GACPC,QAAQD,MAAM,iBAAkBA,GAChCE,EAAAA,GAAUF,MAAM,mC,CAChB,QACAzD,EAAQlD,OAAQ,C,GAKd8G,GAAqBR,UACzB,GAAkC,IAA9BtD,EAAoBhD,MAAxB,CAKAmD,EAASnD,OAAQ,EACjB,UACQwG,EAAAA,GAAOO,YACbxD,EAAUvD,OAAQoG,EAAAA,EAAAA,GAAe,IAAIT,MACrCkB,EAAAA,GAAUG,QAAQ,gCAGZC,KACNC,I,CACA,MAAOP,GACPC,QAAQD,MAAM,UAAWA,GACzBE,EAAAA,GAAUF,MAAM,iC,CAChB,QACAxD,EAASnD,OAAQ,C,OAjBjB6G,EAAAA,GAAUM,QAAQ,qC,EAsBhBC,GAAqBd,UACzBjD,EAAQrD,OAAQ,EAChB,UACQwG,EAAAA,GAAOa,YACbR,EAAAA,GAAUG,QAAQ,+BAGZC,I,CACN,MAAON,GACPC,QAAQD,MAAM,UAAWA,GACzBE,EAAAA,GAAUF,MAAM,iC,CAChB,QACAtD,EAAQrD,OAAQ,C,GAKdsH,GAAsBhB,UAC1BhD,EAAStD,OAAQ,EACjB,UACQwG,EAAAA,GAAOe,aACbV,EAAAA,GAAUG,QAAQ,gCAGZC,I,CACN,MAAON,GACPC,QAAQD,MAAM,UAAWA,GACzBE,EAAAA,GAAUF,MAAM,kC,CAChB,QACArD,EAAStD,OAAQ,C,GAKfwH,GAAoBlB,UACxBlD,EAASpD,OAAQ,EACjB,UACQwG,EAAAA,GAAOiB,WACbZ,EAAAA,GAAUG,QAAQ,gCAGZC,I,CACN,MAAON,GACPC,QAAQD,MAAM,UAAWA,GACzBE,EAAAA,GAAUF,MAAM,gC,CAChB,QACAvD,EAASpD,OAAQ,C,GAKfiH,GAAkBX,UACtB,IACE,MAAMC,QAAiBC,EAAAA,GAAOkB,gBAC9BlE,GAAUxD,MAAQuG,EAASG,KAGtBlD,GAAUxD,MAAM2D,aACnBH,GAAUxD,MAAMyD,aAAexE,EAAAA,GAAeI,SAGhD+E,GAAYpE,OAAQoG,EAAAA,EAAAA,GAAe,IAAIT,OAGnCgC,EAAAA,EAAAA,IAAkBnE,GAAUxD,QAAUqE,KACxCgC,KACAuB,K,CAEF,MAAOjB,GACPC,QAAQD,MAAM,YAAaA,E,GAKzBO,GAAqBA,KAEzBU,KACAvD,GAAqBwD,OAAOC,YAAYb,GAAiB,IAAI,EAIzDW,GAAoBA,KACpBvD,KACF0D,cAAc1D,IACdA,GAAqB,K,EDrEzB,OC0EA2D,EAAAA,EAAAA,KAAU,KACR3B,KACAY,KAAkBgB,MAAK,KAEjB3D,GAAUtE,QACRwD,GAAUxD,MAAM2D,YAAYI,aAC9BR,EAAUvD,OAAQoG,EAAAA,EAAAA,GAAe,IAAIT,KAAKnC,GAAUxD,MAAM2D,WAAWI,eAErER,EAAUvD,OAAQoG,EAAAA,EAAAA,GAAe,IAAIT,MAEvCuB,K,GAEF,KAIJgB,EAAAA,EAAAA,KAAY,KACVN,IAAmB,ID3Fd,CAACnI,EAAUC,KAChB,MAAMyI,GAAuBvI,EAAAA,EAAAA,IAAkB,aACzCwI,GAAyBxI,EAAAA,EAAAA,IAAkB,eAC3CyI,IAAqBzI,EAAAA,EAAAA,IAAkB,WAE7C,OAAQC,EAAAA,EAAAA,OAAcyI,EAAAA,EAAAA,IAAoB,MAAO9H,EAAY,EAC3D+H,EAAAA,EAAAA,IAAoB,MAAO7H,EAAY,CACrChB,EAAO,KAAOA,EAAO,IAAK6I,EAAAA,EAAAA,IAAoB,KAAM,KAAM,kBAAmB,KAC7EA,EAAAA,EAAAA,IAAoB,MAAO5H,EAAY,CACnC2D,GAAUtE,OAAUuE,GAASvE,OAe3BwI,EAAAA,EAAAA,IAAoB,IAAI,KAdvB3I,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAaqI,EAAsB,CAChD/F,IAAK,EACLrC,KAAM,UACNE,KAAM,QACNwI,MAAMC,EAAAA,EAAAA,IAAOC,EAAAA,YACbzF,QAASC,EAASnD,MAClB4I,QAAS9B,GACT+B,SAAwC,IAA9B7F,EAAoBhD,OAC7B,CACDE,SAASC,EAAAA,EAAAA,KAAS,IAAMT,EAAO,KAAOA,EAAO,GAAK,EAChDU,EAAAA,EAAAA,IAAiB,eAEnBE,EAAG,GACF,EAAG,CAAC,OAAQ,UAAW,cAE7BgE,GAAUtE,QAAUuE,GAASvE,QACzBH,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAaqI,EAAsB,CAChD/F,IAAK,EACLrC,KAAM,UACNE,KAAM,QACNwI,MAAMC,EAAAA,EAAAA,IAAOI,EAAAA,YACb5F,QAASG,EAAQrD,MACjB4I,QAASxB,IACR,CACDlH,SAASC,EAAAA,EAAAA,KAAS,IAAMT,EAAO,KAAOA,EAAO,GAAK,EAChDU,EAAAA,EAAAA,IAAiB,eAEnBE,EAAG,GACF,EAAG,CAAC,OAAQ,cACfkI,EAAAA,EAAAA,IAAoB,IAAI,GAC3BjE,GAASvE,QACLH,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAaqI,EAAsB,CAChD/F,IAAK,EACLrC,KAAM,OACNE,KAAM,QACNwI,MAAMC,EAAAA,EAAAA,IAAOC,EAAAA,YACbzF,QAASI,EAAStD,MAClB4I,QAAStB,IACR,CACDpH,SAASC,EAAAA,EAAAA,KAAS,IAAMT,EAAO,KAAOA,EAAO,GAAK,EAChDU,EAAAA,EAAAA,IAAiB,gBAEnBE,EAAG,GACF,EAAG,CAAC,OAAQ,cACfkI,EAAAA,EAAAA,IAAoB,IAAI,GAC3BlE,GAAUtE,OAASuE,GAASvE,QACxBH,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAaqI,EAAsB,CAChD/F,IAAK,EACLrC,KAAM,SACNE,KAAM,QACNwI,MAAMC,EAAAA,EAAAA,IAAOK,EAAAA,WACb7F,QAASE,EAASpD,MAClB4I,QAASpB,IACR,CACDtH,SAASC,EAAAA,EAAAA,KAAS,IAAMT,EAAO,KAAOA,EAAO,GAAK,EAChDU,EAAAA,EAAAA,IAAiB,cAEnBE,EAAG,GACF,EAAG,CAAC,OAAQ,cACfkI,EAAAA,EAAAA,IAAoB,IAAI,QAGhCD,EAAAA,EAAAA,IAAoB,MAAO3H,EAAY,EACrC2H,EAAAA,EAAAA,IAAoB,MAAO1H,EAAY,EACrC0H,EAAAA,EAAAA,IAAoB,MAAOzH,EAAY,EACrCyH,EAAAA,EAAAA,IAAoB,MAAOxH,EAAY,EACrCiI,EAAAA,EAAAA,IAAaZ,EAAwB,CACnCrI,KAAM,YACNgF,WAAYN,GAAmBzE,MAC/BiJ,MAAO,IACP,eAAgB,GAChBnE,OAAQA,IACP,KAAM,EAAG,CAAC,gBACbyD,EAAAA,EAAAA,IAAoB,MAAOvH,EAAY,EACrCgI,EAAAA,EAAAA,IAAaE,EAAAA,EAAc,CACzBtK,MAAO4E,GAAUxD,MAAMyD,cACtB,KAAM,EAAG,CAAC,iBAInB8E,EAAAA,EAAAA,IAAoB,MAAOtH,EAAY,EACrCsH,EAAAA,EAAAA,IAAoB,MAAOrH,EAAa,EACtCqH,EAAAA,EAAAA,IAAoB,MAAOpH,EAAa,EACtCoH,EAAAA,EAAAA,IAAoB,MAAOnH,EAAa,EACtC4H,EAAAA,EAAAA,IAAaX,GAAoB,KAAM,CACrCnI,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtB6I,EAAAA,EAAAA,KAAaN,EAAAA,EAAAA,IAAOS,EAAAA,gBAEtB7I,EAAG,OAGPiI,EAAAA,EAAAA,IAAoB,MAAOlH,EAAa,EACtCkH,EAAAA,EAAAA,IAAoB,MAAOjH,GAAajB,EAAAA,EAAAA,IAAiB2D,GAAWhE,OAAQ,GAC5EN,EAAO,KAAOA,EAAO,IAAK6I,EAAAA,EAAAA,IAAoB,MAAO,CAAE9H,MAAO,cAAgB,oBAAqB,SAGvG8H,EAAAA,EAAAA,IAAoB,MAAOhH,EAAa,EACtCgH,EAAAA,EAAAA,IAAoB,MAAO/G,EAAa,EACtCwH,EAAAA,EAAAA,IAAaX,GAAoB,KAAM,CACrCnI,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtB6I,EAAAA,EAAAA,KAAaN,EAAAA,EAAAA,IAAOU,EAAAA,eAEtB9I,EAAG,OAGPiI,EAAAA,EAAAA,IAAoB,MAAO9G,EAAa,EACtC8G,EAAAA,EAAAA,IAAoB,MAAO7G,GAAarB,EAAAA,EAAAA,IAAiBuE,GAAe5E,OAAS,IAAK,GACtFN,EAAO,KAAOA,EAAO,IAAK6I,EAAAA,EAAAA,IAAoB,MAAO,CAAE9H,MAAO,cAAgB,mBAAoB,SAGtG8H,EAAAA,EAAAA,IAAoB,MAAO5G,EAAa,EACtC4G,EAAAA,EAAAA,IAAoB,MAAO3G,EAAa,EACtCoH,EAAAA,EAAAA,IAAaX,GAAoB,KAAM,CACrCnI,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtB6I,EAAAA,EAAAA,KAAaN,EAAAA,EAAAA,IAAOW,EAAAA,uBAEtB/I,EAAG,OAGPiI,EAAAA,EAAAA,IAAoB,MAAO1G,EAAa,EACtC0G,EAAAA,EAAAA,IAAoB,MAAOzG,GAAazB,EAAAA,EAAAA,IAAiBmD,GAAUxD,MAAM2D,YAAYM,cAAgB,GAAI,GACzGvE,EAAO,KAAOA,EAAO,IAAK6I,EAAAA,EAAAA,IAAoB,MAAO,CAAE9H,MAAO,cAAgB,qBAAsB,SAGxG8H,EAAAA,EAAAA,IAAoB,MAAOxG,EAAa,EACtCwG,EAAAA,EAAAA,IAAoB,MAAOvG,EAAa,EACtCgH,EAAAA,EAAAA,IAAaX,GAAoB,KAAM,CACrCnI,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtB6I,EAAAA,EAAAA,KAAaN,EAAAA,EAAAA,IAAOY,EAAAA,uBAEtBhJ,EAAG,OAGPiI,EAAAA,EAAAA,IAAoB,MAAOtG,EAAa,EACtCsG,EAAAA,EAAAA,IAAoB,MAAOrG,GAAa7B,EAAAA,EAAAA,IAAiBmD,GAAUxD,MAAM2D,YAAYO,cAAgB,GAAI,GACzGxE,EAAO,KAAOA,EAAO,IAAK6I,EAAAA,EAAAA,IAAoB,MAAO,CAAE9H,MAAO,cAAgB,qBAAsB,cAM7G+C,GAAUxD,MAAM0D,kBAAoBuC,GAAiBjG,QACjDH,EAAAA,EAAAA,OAAcyI,EAAAA,EAAAA,IAAoB,MAAOnG,EAAa,EACrDoG,EAAAA,EAAAA,IAAoB,MAAOlG,EAAa,CACtC3C,EAAO,KAAOA,EAAO,IAAK6I,EAAAA,EAAAA,IAAoB,OAAQ,CAAE9H,MAAO,mBAAqB,WAAY,KAChG8H,EAAAA,EAAAA,IAAoB,OAAQjG,GAAajC,EAAAA,EAAAA,IAAiBmD,GAAUxD,MAAM0D,kBAAmB,SAGjG8E,EAAAA,EAAAA,IAAoB,IAAI,IAC5BD,EAAAA,EAAAA,IAAoB,MAAOhG,EAAa,EACtCgG,EAAAA,EAAAA,IAAoB,MAAO/F,EAAa,CACrCe,EAAUvD,QACNH,EAAAA,EAAAA,OAAcyI,EAAAA,EAAAA,IAAoB,MAAO7F,EAAa,CACrD/C,EAAO,MAAQA,EAAO,KAAM6I,EAAAA,EAAAA,IAAoB,OAAQ,CAAE9H,MAAO,cAAgB,UAAW,KAC5F8H,EAAAA,EAAAA,IAAoB,OAAQ7F,GAAarC,EAAAA,EAAAA,IAAiBkD,EAAUvD,OAAQ,OAE9EwI,EAAAA,EAAAA,IAAoB,IAAI,GAC3BvC,GAAiBjG,QACbH,EAAAA,EAAAA,OAAcyI,EAAAA,EAAAA,IAAoB,MAAO3F,EAAa,CACrDjD,EAAO,MAAQA,EAAO,KAAM6I,EAAAA,EAAAA,IAAoB,OAAQ,CAAE9H,MAAO,cAAgB,kBAAmB,KACpG8H,EAAAA,EAAAA,IAAoB,OAAQ3F,GAAavC,EAAAA,EAAAA,IAAiB4F,GAAiBjG,OAAQ,OAErFwI,EAAAA,EAAAA,IAAoB,IAAI,GAC3B/C,GAAgBzF,MAAQ,IACpBH,EAAAA,EAAAA,OAAcyI,EAAAA,EAAAA,IAAoB,MAAOzF,EAAa,CACrDnD,EAAO,MAAQA,EAAO,KAAM6I,EAAAA,EAAAA,IAAoB,OAAQ,CAAE9H,MAAO,cAAgB,cAAe,KAChG8H,EAAAA,EAAAA,IAAoB,OAAQzF,GAAazC,EAAAA,EAAAA,KAAiBqI,EAAAA,EAAAA,IAAOa,EAAAA,EAAPb,CAAuBjD,GAAgBzF,QAAS,OAE5GwI,EAAAA,EAAAA,IAAoB,IAAI,KAE7BpE,GAAYpE,QACRH,EAAAA,EAAAA,OAAcyI,EAAAA,EAAAA,IAAoB,MAAOvF,EAAa,mBAAoB1C,EAAAA,EAAAA,IAAiB+D,GAAYpE,OAAQ,KAChHwI,EAAAA,EAAAA,IAAoB,IAAI,QAGhC,CAEJ,I,UElhBA,MAAMjI,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://fuzz-web/./src/components/common/TestStateTag.vue?fb59", "webpack://fuzz-web/./src/components/common/TestStateTag.vue", "webpack://fuzz-web/./src/components/common/TestStateTag.vue?d49a", "webpack://fuzz-web/./src/views/testplan/TestRun.vue?0daa", "webpack://fuzz-web/./src/views/testplan/TestRun.vue", "webpack://fuzz-web/./src/views/testplan/TestRun.vue?f1b4"], "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from \"vue\"\n\nimport { computed } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TestStateTag',\n  props: {\n    state: {}\n  },\n  setup(__props: any) {\n\r\nconst props = __props;\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getTestStateName = (state: string): 'Not Run' | 'Running' | 'Completed' | 'Faulted' | 'Paused' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Completed';\r\n    case ExecutionState.Failure:\r\n      return 'Faulted';\r\n    case ExecutionState.Paused:\r\n      return 'Paused';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getTestStateName(props.state);\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n\n  return (_openBlock(), _createBlock(_component_el_tag, {\n    type: tagType.value,\n    size: \"small\"\n  }, {\n    default: _withCtx(() => [\n      _createTextVNode(_toDisplayString(stateName.value), 1)\n    ]),\n    _: 1\n  }, 8, [\"type\"]))\n}\n}\n\n})", "<template>\r\n  <el-tag :type=\"tagType\" size=\"small\">\r\n    {{ stateName }}\r\n  </el-tag>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, defineProps } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\nconst props = defineProps<{\r\n  state: string;\r\n}>();\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getTestStateName = (state: string): 'Not Run' | 'Running' | 'Completed' | 'Faulted' | 'Paused' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Completed';\r\n    case ExecutionState.Failure:\r\n      return 'Faulted';\r\n    case ExecutionState.Paused:\r\n      return 'Paused';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getTestStateName(props.state);\r\n});\r\n</script>\r\n", "import script from \"./TestStateTag.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TestStateTag.vue?vue&type=script&setup=true&lang=ts\"\n\nconst __exports__ = script;\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { createElementVNode as _createElementVNode, unref as _unref, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, create<PERSON><PERSON> as _createBlock, createCommentVNode as _createCommentVNode, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"test-run-container\" }\nconst _hoisted_2 = { class: \"toolbar\" }\nconst _hoisted_3 = { class: \"action-buttons\" }\nconst _hoisted_4 = { class: \"content-area\" }\nconst _hoisted_5 = { class: \"overview-layout\" }\nconst _hoisted_6 = { class: \"progress-section panel\" }\nconst _hoisted_7 = { class: \"progress-circle-container\" }\nconst _hoisted_8 = { class: \"test-state\" }\nconst _hoisted_9 = { class: \"stats-section\" }\nconst _hoisted_10 = { class: \"stats-grid\" }\nconst _hoisted_11 = { class: \"stat-card total panel\" }\nconst _hoisted_12 = { class: \"stat-icon\" }\nconst _hoisted_13 = { class: \"stat-data\" }\nconst _hoisted_14 = { class: \"stat-value\" }\nconst _hoisted_15 = { class: \"stat-card completion panel\" }\nconst _hoisted_16 = { class: \"stat-icon\" }\nconst _hoisted_17 = { class: \"stat-data\" }\nconst _hoisted_18 = { class: \"stat-value\" }\nconst _hoisted_19 = { class: \"stat-card success panel\" }\nconst _hoisted_20 = { class: \"stat-icon\" }\nconst _hoisted_21 = { class: \"stat-data\" }\nconst _hoisted_22 = { class: \"stat-value\" }\nconst _hoisted_23 = { class: \"stat-card failure panel\" }\nconst _hoisted_24 = { class: \"stat-icon\" }\nconst _hoisted_25 = { class: \"stat-data\" }\nconst _hoisted_26 = { class: \"stat-value\" }\nconst _hoisted_27 = {\n  key: 0,\n  class: \"operation-panel panel\"\n}\nconst _hoisted_28 = { class: \"current-operation\" }\nconst _hoisted_29 = { class: \"operation-value\" }\nconst _hoisted_30 = { class: \"footer-panel panel\" }\nconst _hoisted_31 = { class: \"timing-info\" }\nconst _hoisted_32 = {\n  key: 0,\n  class: \"time-item\"\n}\nconst _hoisted_33 = { class: \"time-value\" }\nconst _hoisted_34 = {\n  key: 1,\n  class: \"time-item\"\n}\nconst _hoisted_35 = { class: \"time-value\" }\nconst _hoisted_36 = {\n  key: 2,\n  class: \"time-item\"\n}\nconst _hoisted_37 = { class: \"time-value\" }\nconst _hoisted_38 = {\n  key: 0,\n  class: \"last-updated\"\n}\n\nimport { ref, onMounted, onUnmounted, computed, nextTick } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { appApi, TesterSnapshot, ExecutionState, isTesterCompleted } from '@/api/appApi';\r\nimport { \r\n  CircleCheckFilled, \r\n  CircleCloseFilled, \r\n  InfoFilled, \r\n  VideoPlay,\r\n  VideoPause,\r\n  CaretRight,\r\n  Histogram,\r\n  Monitor\r\n} from '@element-plus/icons-vue';\r\nimport TestStateTag from '@/components/common/TestStateTag.vue';\r\nimport { formatDuration, formatDateTime } from '@/utils/timeUtils';\r\n\r\n// 状态变量\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TestRun',\n  setup(__props) {\n\r\n/* eslint-disable */\r\nconst savedTestCasesCount = ref(0);\r\nconst loading = ref(true);\r\nconst starting = ref(false);\r\nconst stopping = ref(false);\r\nconst pausing = ref(false);\r\nconst resuming = ref(false);\r\nconst startTime = ref('');\r\n// 将 estimatedEndTime 从 ref 变为 computed\r\nconst runStatus = ref<TesterSnapshot>({\r\n  processState: ExecutionState.Pending,\r\n  currentOperation: '',\r\n  testResult: {\r\n    id: '',\r\n    resultFolderName: '',\r\n    testType: '',\r\n    creationTime: '',\r\n    totalCount: 0,\r\n    successCount: 0,\r\n    failureCount: 0\r\n  },\r\n  caseResults: []\r\n});\r\nconst lastUpdated = ref('');\r\n\r\n// 状态轮询定时器\r\nlet statusPollingTimer: number | null = null;\r\n\r\n// 计算属性\r\nconst isRunning = computed(() => {\r\n  return runStatus.value.processState === ExecutionState.Running;\r\n});\r\n\r\nconst isPaused = computed(() => {\r\n  return runStatus.value.processState === ExecutionState.Paused;\r\n});\r\n\r\nconst totalCount = computed(() => {\r\n  // 如果testResult为null，使用savedTestCasesCount\r\n  if (!runStatus.value.testResult || runStatus.value.testResult.totalCount === 0) {\r\n    return savedTestCasesCount.value;\r\n  }\r\n  return runStatus.value.testResult.totalCount;\r\n});\r\n\r\nconst completedCount = computed(() => {\r\n  // 如果testResult为null，返回0\r\n  if (!runStatus.value.testResult) {\r\n    return 0;\r\n  }\r\n  return (runStatus.value.testResult.successCount || 0) + (runStatus.value.testResult.failureCount || 0);\r\n});\r\n\r\nconst progressPercentage = computed(() => {\r\n  if (totalCount.value === 0) return 0;\r\n  return Math.round((completedCount.value / totalCount.value) * 100);\r\n});\r\n\r\nconst completionRate = computed(() => {\r\n  if (totalCount.value === 0) return 0;\r\n  // 计算完成率，保留两位小数\r\n  return (completedCount.value / totalCount.value * 100).toFixed(2);\r\n});\r\n\r\n// 格式化进度显示\r\nconst format = (percentage: number) => {\r\n  const text = `${completedCount.value} / ${totalCount.value}`;\r\n  \r\n  // 使用 nextTick 确保 DOM 已更新后再设置class\r\n  nextTick(() => {\r\n    const progressText = document.querySelector('.el-progress__text');\r\n    if (progressText) {\r\n      // 清除之前的字体大小class\r\n      progressText.classList.remove('font-small', 'font-medium', 'font-large');\r\n      \r\n      // 根据文字长度添加对应的class\r\n      if (text.length <= 11) {\r\n        progressText.classList.add('font-large');\r\n      } else if (text.length <= 14) {\r\n        progressText.classList.add('font-medium');\r\n      } else {\r\n        progressText.classList.add('font-small');\r\n      }\r\n    }\r\n  });\r\n  \r\n  return text;\r\n};\r\n\r\n// 计算剩余时间(毫秒)\r\nconst remainingTimeMs = computed(() => {\r\n  if (!startTime.value || totalCount.value === 0 || completedCount.value === 0 || !isRunning.value) {\r\n    return 0;\r\n  }\r\n\r\n  const startTimeObj = new Date(startTime.value);\r\n  const elapsedMs = Date.now() - startTimeObj.getTime();\r\n  const msPerCase = elapsedMs / completedCount.value;\r\n  const remainingCases = totalCount.value - completedCount.value;\r\n  return msPerCase * remainingCases;\r\n});\r\n\r\n// 计算估计完成时间 - 改为计算属性\r\nconst estimatedEndTime = computed(() => {\r\n  if (!startTime.value || totalCount.value === 0 || completedCount.value === 0 || !isRunning.value) {\r\n    return '';\r\n  }\r\n\r\n  const startTimeObj = new Date(startTime.value);\r\n  const elapsedMs = Date.now() - startTimeObj.getTime();\r\n  const msPerCase = elapsedMs / completedCount.value;\r\n  const remainingCases = totalCount.value - completedCount.value;\r\n  const remainingMs = msPerCase * remainingCases;\r\n  \r\n  if (remainingMs <= 0) {\r\n    return 'Completing...';\r\n  }\r\n  \r\n  const endTimeObj = new Date(Date.now() + remainingMs);\r\n  return formatDateTime(endTimeObj);\r\n});\r\n\r\n// 获取保存的测试用例数量\r\nconst fetchSavedTestCasesCount = async () => {\r\n  loading.value = true;\r\n  try {\r\n    const response = await appApi.getSavedCaseCount();\r\n    savedTestCasesCount.value = response.data;\r\n  } catch (error) {\r\n    console.error('获取保存的测试用例数量失败:', error);\r\n    ElMessage.error('Failed to fetch test cases count');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 开始测试执行\r\nconst startTestExecution = async () => {\r\n  if (savedTestCasesCount.value === 0) {\r\n    ElMessage.warning('No test cases available to execute');\r\n    return;\r\n  }\r\n\r\n  starting.value = true;\r\n  try {\r\n    await appApi.startTest();\r\n    startTime.value = formatDateTime(new Date());\r\n    ElMessage.success('Test execution started');\r\n\r\n    // 立即获取状态并开始轮询\r\n    await fetchTestStatus();\r\n    startStatusPolling();\r\n  } catch (error) {\r\n    console.error('启动测试失败:', error);\r\n    ElMessage.error('Failed to start test execution');\r\n  } finally {\r\n    starting.value = false;\r\n  }\r\n};\r\n\r\n// 暂停测试执行\r\nconst pauseTestExecution = async () => {\r\n  pausing.value = true;\r\n  try {\r\n    await appApi.pauseTest();\r\n    ElMessage.success('Test execution paused');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('暂停测试失败:', error);\r\n    ElMessage.error('Failed to pause test execution');\r\n  } finally {\r\n    pausing.value = false;\r\n  }\r\n};\r\n\r\n// 恢复测试执行\r\nconst resumeTestExecution = async () => {\r\n  resuming.value = true;\r\n  try {\r\n    await appApi.resumeTest();\r\n    ElMessage.success('Test execution resumed');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('恢复测试失败:', error);\r\n    ElMessage.error('Failed to resume test execution');\r\n  } finally {\r\n    resuming.value = false;\r\n  }\r\n};\r\n\r\n// 停止测试执行\r\nconst stopTestExecution = async () => {\r\n  stopping.value = true;\r\n  try {\r\n    await appApi.stopTest();\r\n    ElMessage.success('Test execution stopped');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('停止测试失败:', error);\r\n    ElMessage.error('Failed to stop test execution');\r\n  } finally {\r\n    stopping.value = false;\r\n  }\r\n};\r\n\r\n// 获取测试状态\r\nconst fetchTestStatus = async () => {\r\n  try {\r\n    const response = await appApi.getTestStatus();\r\n    runStatus.value = response.data;\r\n    \r\n    // 如果testResult为null，设置为Pending状态\r\n    if (!runStatus.value.testResult) {\r\n      runStatus.value.processState = ExecutionState.Pending;\r\n    }\r\n    \r\n    lastUpdated.value = formatDateTime(new Date());\r\n\r\n    // 使用工具函数判断测试是否完成\r\n    if (isTesterCompleted(runStatus.value) && statusPollingTimer) {\r\n      fetchSavedTestCasesCount();\r\n      stopStatusPolling();\r\n    }\r\n  } catch (error) {\r\n    console.error('获取测试状态失败:', error);\r\n  }\r\n};\r\n\r\n// 开始状态轮询\r\nconst startStatusPolling = () => {\r\n  // 清除可能存在的轮询定时器\r\n  stopStatusPolling();\r\n  statusPollingTimer = window.setInterval(fetchTestStatus, 500);\r\n};\r\n\r\n// 停止状态轮询\r\nconst stopStatusPolling = () => {\r\n  if (statusPollingTimer) {\r\n    clearInterval(statusPollingTimer);\r\n    statusPollingTimer = null;\r\n  }\r\n};\r\n\r\n// 组件挂载时获取保存的测试用例数量和测试状态\r\nonMounted(() => {\r\n  fetchSavedTestCasesCount();\r\n  fetchTestStatus().then(() => {\r\n    // 如果测试正在运行，开始轮询\r\n    if (isRunning.value) {\r\n      if (runStatus.value.testResult?.creationTime) {\r\n        startTime.value = formatDateTime(new Date(runStatus.value.testResult.creationTime));\r\n      } else {\r\n        startTime.value = formatDateTime(new Date());\r\n      }\r\n      startStatusPolling();\r\n    }\r\n  });\r\n});\r\n\r\n// 组件卸载时停止轮询\r\nonUnmounted(() => {\r\n  stopStatusPolling();\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_progress = _resolveComponent(\"el-progress\")!\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _cache[4] || (_cache[4] = _createElementVNode(\"h3\", null, \"Test Execution\", -1)),\n      _createElementVNode(\"div\", _hoisted_3, [\n        (!isRunning.value && !isPaused.value)\n          ? (_openBlock(), _createBlock(_component_el_button, {\n              key: 0,\n              type: \"success\",\n              size: \"small\",\n              icon: _unref(CaretRight),\n              loading: starting.value,\n              onClick: startTestExecution,\n              disabled: savedTestCasesCount.value === 0\n            }, {\n              default: _withCtx(() => _cache[0] || (_cache[0] = [\n                _createTextVNode(\" Start \")\n              ])),\n              _: 1\n            }, 8, [\"icon\", \"loading\", \"disabled\"]))\n          : _createCommentVNode(\"\", true),\n        (isRunning.value && !isPaused.value)\n          ? (_openBlock(), _createBlock(_component_el_button, {\n              key: 1,\n              type: \"warning\",\n              size: \"small\",\n              icon: _unref(VideoPause),\n              loading: pausing.value,\n              onClick: pauseTestExecution\n            }, {\n              default: _withCtx(() => _cache[1] || (_cache[1] = [\n                _createTextVNode(\" Pause \")\n              ])),\n              _: 1\n            }, 8, [\"icon\", \"loading\"]))\n          : _createCommentVNode(\"\", true),\n        (isPaused.value)\n          ? (_openBlock(), _createBlock(_component_el_button, {\n              key: 2,\n              type: \"info\",\n              size: \"small\",\n              icon: _unref(CaretRight),\n              loading: resuming.value,\n              onClick: resumeTestExecution\n            }, {\n              default: _withCtx(() => _cache[2] || (_cache[2] = [\n                _createTextVNode(\" Resume \")\n              ])),\n              _: 1\n            }, 8, [\"icon\", \"loading\"]))\n          : _createCommentVNode(\"\", true),\n        (isRunning.value || isPaused.value)\n          ? (_openBlock(), _createBlock(_component_el_button, {\n              key: 3,\n              type: \"danger\",\n              size: \"small\",\n              icon: _unref(VideoPlay),\n              loading: stopping.value,\n              onClick: stopTestExecution\n            }, {\n              default: _withCtx(() => _cache[3] || (_cache[3] = [\n                _createTextVNode(\" Stop \")\n              ])),\n              _: 1\n            }, 8, [\"icon\", \"loading\"]))\n          : _createCommentVNode(\"\", true)\n      ])\n    ]),\n    _createElementVNode(\"div\", _hoisted_4, [\n      _createElementVNode(\"div\", _hoisted_5, [\n        _createElementVNode(\"div\", _hoisted_6, [\n          _createElementVNode(\"div\", _hoisted_7, [\n            _createVNode(_component_el_progress, {\n              type: \"dashboard\",\n              percentage: progressPercentage.value,\n              width: 180,\n              \"stroke-width\": 12,\n              format: format\n            }, null, 8, [\"percentage\"]),\n            _createElementVNode(\"div\", _hoisted_8, [\n              _createVNode(TestStateTag, {\n                state: runStatus.value.processState\n              }, null, 8, [\"state\"])\n            ])\n          ])\n        ]),\n        _createElementVNode(\"div\", _hoisted_9, [\n          _createElementVNode(\"div\", _hoisted_10, [\n            _createElementVNode(\"div\", _hoisted_11, [\n              _createElementVNode(\"div\", _hoisted_12, [\n                _createVNode(_component_el_icon, null, {\n                  default: _withCtx(() => [\n                    _createVNode(_unref(InfoFilled))\n                  ]),\n                  _: 1\n                })\n              ]),\n              _createElementVNode(\"div\", _hoisted_13, [\n                _createElementVNode(\"div\", _hoisted_14, _toDisplayString(totalCount.value), 1),\n                _cache[5] || (_cache[5] = _createElementVNode(\"div\", { class: \"stat-label\" }, \"Total Test Cases\", -1))\n              ])\n            ]),\n            _createElementVNode(\"div\", _hoisted_15, [\n              _createElementVNode(\"div\", _hoisted_16, [\n                _createVNode(_component_el_icon, null, {\n                  default: _withCtx(() => [\n                    _createVNode(_unref(Histogram))\n                  ]),\n                  _: 1\n                })\n              ]),\n              _createElementVNode(\"div\", _hoisted_17, [\n                _createElementVNode(\"div\", _hoisted_18, _toDisplayString(completionRate.value) + \"%\", 1),\n                _cache[6] || (_cache[6] = _createElementVNode(\"div\", { class: \"stat-label\" }, \"Completion Rate\", -1))\n              ])\n            ]),\n            _createElementVNode(\"div\", _hoisted_19, [\n              _createElementVNode(\"div\", _hoisted_20, [\n                _createVNode(_component_el_icon, null, {\n                  default: _withCtx(() => [\n                    _createVNode(_unref(CircleCheckFilled))\n                  ]),\n                  _: 1\n                })\n              ]),\n              _createElementVNode(\"div\", _hoisted_21, [\n                _createElementVNode(\"div\", _hoisted_22, _toDisplayString(runStatus.value.testResult?.successCount || 0), 1),\n                _cache[7] || (_cache[7] = _createElementVNode(\"div\", { class: \"stat-label\" }, \"Passed Test Cases\", -1))\n              ])\n            ]),\n            _createElementVNode(\"div\", _hoisted_23, [\n              _createElementVNode(\"div\", _hoisted_24, [\n                _createVNode(_component_el_icon, null, {\n                  default: _withCtx(() => [\n                    _createVNode(_unref(CircleCloseFilled))\n                  ]),\n                  _: 1\n                })\n              ]),\n              _createElementVNode(\"div\", _hoisted_25, [\n                _createElementVNode(\"div\", _hoisted_26, _toDisplayString(runStatus.value.testResult?.failureCount || 0), 1),\n                _cache[8] || (_cache[8] = _createElementVNode(\"div\", { class: \"stat-label\" }, \"Failed Test Cases\", -1))\n              ])\n            ])\n          ])\n        ])\n      ]),\n      (runStatus.value.currentOperation && estimatedEndTime.value)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_27, [\n            _createElementVNode(\"div\", _hoisted_28, [\n              _cache[9] || (_cache[9] = _createElementVNode(\"span\", { class: \"operation-label\" }, \"Status:\", -1)),\n              _createElementVNode(\"span\", _hoisted_29, _toDisplayString(runStatus.value.currentOperation), 1)\n            ])\n          ]))\n        : _createCommentVNode(\"\", true),\n      _createElementVNode(\"div\", _hoisted_30, [\n        _createElementVNode(\"div\", _hoisted_31, [\n          (startTime.value)\n            ? (_openBlock(), _createElementBlock(\"div\", _hoisted_32, [\n                _cache[10] || (_cache[10] = _createElementVNode(\"span\", { class: \"time-label\" }, \"Start:\", -1)),\n                _createElementVNode(\"span\", _hoisted_33, _toDisplayString(startTime.value), 1)\n              ]))\n            : _createCommentVNode(\"\", true),\n          (estimatedEndTime.value)\n            ? (_openBlock(), _createElementBlock(\"div\", _hoisted_34, [\n                _cache[11] || (_cache[11] = _createElementVNode(\"span\", { class: \"time-label\" }, \"Estimated End:\", -1)),\n                _createElementVNode(\"span\", _hoisted_35, _toDisplayString(estimatedEndTime.value), 1)\n              ]))\n            : _createCommentVNode(\"\", true),\n          (remainingTimeMs.value > 0)\n            ? (_openBlock(), _createElementBlock(\"div\", _hoisted_36, [\n                _cache[12] || (_cache[12] = _createElementVNode(\"span\", { class: \"time-label\" }, \"Remaining:\", -1)),\n                _createElementVNode(\"span\", _hoisted_37, _toDisplayString(_unref(formatDuration)(remainingTimeMs.value)), 1)\n              ]))\n            : _createCommentVNode(\"\", true)\n        ]),\n        (lastUpdated.value)\n          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_38, \" Last updated: \" + _toDisplayString(lastUpdated.value), 1))\n          : _createCommentVNode(\"\", true)\n      ])\n    ])\n  ]))\n}\n}\n\n})", "<template>\r\n  <div class=\"test-run-container\">\r\n    <!-- 顶部控制区 -->\r\n    <div class=\"toolbar\">\r\n      <h3>Test Execution</h3>\r\n      <div class=\"action-buttons\">\r\n        <!-- 开始按钮：在测试未运行且未暂停时显示 -->\r\n        <el-button\r\n          v-if=\"!isRunning && !isPaused\"\r\n          type=\"success\"\r\n          size=\"small\"\r\n          :icon=\"CaretRight\"\r\n          :loading=\"starting\"\r\n          @click=\"startTestExecution\"\r\n          :disabled=\"savedTestCasesCount === 0\">\r\n          Start\r\n        </el-button>\r\n\r\n        <!-- 暂停按钮：只在测试运行且未暂停时显示 -->\r\n        <el-button\r\n          v-if=\"isRunning && !isPaused\"\r\n          type=\"warning\"\r\n          size=\"small\"\r\n          :icon=\"VideoPause\"\r\n          :loading=\"pausing\"\r\n          @click=\"pauseTestExecution\">\r\n          Pause\r\n        </el-button>\r\n\r\n        <!-- 恢复按钮：只在测试已暂停时显示 -->\r\n        <el-button\r\n          v-if=\"isPaused\"\r\n          type=\"info\"\r\n          size=\"small\"\r\n          :icon=\"CaretRight\"\r\n          :loading=\"resuming\"\r\n          @click=\"resumeTestExecution\">\r\n          Resume\r\n        </el-button>\r\n\r\n        <!-- 停止按钮：在测试运行或暂停时都显示 -->\r\n        <el-button\r\n          v-if=\"isRunning || isPaused\"\r\n          type=\"danger\"\r\n          size=\"small\"\r\n          :icon=\"VideoPlay\"\r\n          :loading=\"stopping\"\r\n          @click=\"stopTestExecution\">\r\n          Stop\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 测试进度显示区域 - 始终显示 -->\r\n    <div class=\"content-area\">\r\n      <!-- 状态概览和统计区域 - 左右布局 -->\r\n      <div class=\"overview-layout\">\r\n        <!-- 左侧：进度圆环 -->\r\n        <div class=\"progress-section panel\">\r\n          <div class=\"progress-circle-container\">\r\n            <el-progress\r\n              type=\"dashboard\"\r\n              :percentage=\"progressPercentage\"\r\n              :width=\"180\"\r\n              :stroke-width=\"12\"\r\n              :format=\"format\">\r\n            </el-progress>\r\n            <div class=\"test-state\">\r\n              <TestStateTag :state=\"runStatus.processState\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- 右侧：统计卡片 -->\r\n        <div class=\"stats-section\">\r\n          <div class=\"stats-grid\">\r\n            <!-- 总用例卡片 - 移到第一个位置 -->\r\n            <div class=\"stat-card total panel\">\r\n              <div class=\"stat-icon\">\r\n                <el-icon><InfoFilled /></el-icon>\r\n              </div>\r\n              <div class=\"stat-data\">\r\n                <div class=\"stat-value\">{{ totalCount }}</div>\r\n                <div class=\"stat-label\">Total Test Cases</div>\r\n              </div>\r\n            </div>\r\n            \r\n            <!-- 完成率卡片 - 移到第二个位置 -->\r\n            <div class=\"stat-card completion panel\">\r\n              <div class=\"stat-icon\">\r\n                <el-icon><Histogram /></el-icon>\r\n              </div>\r\n              <div class=\"stat-data\">\r\n                <div class=\"stat-value\">{{ completionRate }}%</div>\r\n                <div class=\"stat-label\">Completion Rate</div>\r\n              </div>\r\n            </div>\r\n            \r\n            <!-- 成功用例卡片 - 移到第三个位置 -->\r\n            <div class=\"stat-card success panel\">\r\n              <div class=\"stat-icon\">\r\n                <el-icon><CircleCheckFilled /></el-icon>\r\n              </div>\r\n              <div class=\"stat-data\">\r\n                <div class=\"stat-value\">{{ runStatus.testResult?.successCount || 0 }}</div>\r\n                <div class=\"stat-label\">Passed Test Cases</div>\r\n              </div>\r\n            </div>\r\n            \r\n            <!-- 失败用例卡片 - 移到第四个位置 -->\r\n            <div class=\"stat-card failure panel\">\r\n              <div class=\"stat-icon\">\r\n                <el-icon><CircleCloseFilled /></el-icon>\r\n              </div>\r\n              <div class=\"stat-data\">\r\n                <div class=\"stat-value\">{{ runStatus.testResult?.failureCount || 0 }}</div>\r\n                <div class=\"stat-label\">Failed Test Cases</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <!-- 当前操作信息 - 如果有内容则显示 -->\r\n      <div v-if=\"runStatus.currentOperation && estimatedEndTime\" class=\"operation-panel panel\">\r\n        <div class=\"current-operation\">\r\n          <span class=\"operation-label\">Status:</span>\r\n          <span class=\"operation-value\">{{ runStatus.currentOperation }}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 底部信息区域 -->\r\n      <div class=\"footer-panel panel\">\r\n        <div class=\"timing-info\">\r\n          <div v-if=\"startTime\" class=\"time-item\">\r\n            <span class=\"time-label\">Start:</span>\r\n            <span class=\"time-value\">{{ startTime }}</span>\r\n          </div>\r\n          <div v-if=\"estimatedEndTime\" class=\"time-item\">\r\n            <span class=\"time-label\">Estimated End:</span>\r\n            <span class=\"time-value\">{{ estimatedEndTime }}</span>\r\n          </div>\r\n          <div v-if=\"remainingTimeMs > 0\" class=\"time-item\">\r\n            <span class=\"time-label\">Remaining:</span>\r\n            <span class=\"time-value\">{{ formatDuration(remainingTimeMs) }}</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"last-updated\" v-if=\"lastUpdated\">\r\n          Last updated: {{ lastUpdated }}\r\n        </div>\r\n      </div>      \r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\n/* eslint-disable */\r\nimport { ref, onMounted, onUnmounted, computed, nextTick } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { appApi, TesterSnapshot, ExecutionState, isTesterCompleted } from '@/api/appApi';\r\nimport { \r\n  CircleCheckFilled, \r\n  CircleCloseFilled, \r\n  InfoFilled, \r\n  VideoPlay,\r\n  VideoPause,\r\n  CaretRight,\r\n  Histogram,\r\n  Monitor\r\n} from '@element-plus/icons-vue';\r\nimport TestStateTag from '@/components/common/TestStateTag.vue';\r\nimport { formatDuration, formatDateTime } from '@/utils/timeUtils';\r\n\r\n// 状态变量\r\nconst savedTestCasesCount = ref(0);\r\nconst loading = ref(true);\r\nconst starting = ref(false);\r\nconst stopping = ref(false);\r\nconst pausing = ref(false);\r\nconst resuming = ref(false);\r\nconst startTime = ref('');\r\n// 将 estimatedEndTime 从 ref 变为 computed\r\nconst runStatus = ref<TesterSnapshot>({\r\n  processState: ExecutionState.Pending,\r\n  currentOperation: '',\r\n  testResult: {\r\n    id: '',\r\n    resultFolderName: '',\r\n    testType: '',\r\n    creationTime: '',\r\n    totalCount: 0,\r\n    successCount: 0,\r\n    failureCount: 0\r\n  },\r\n  caseResults: []\r\n});\r\nconst lastUpdated = ref('');\r\n\r\n// 状态轮询定时器\r\nlet statusPollingTimer: number | null = null;\r\n\r\n// 计算属性\r\nconst isRunning = computed(() => {\r\n  return runStatus.value.processState === ExecutionState.Running;\r\n});\r\n\r\nconst isPaused = computed(() => {\r\n  return runStatus.value.processState === ExecutionState.Paused;\r\n});\r\n\r\nconst totalCount = computed(() => {\r\n  // 如果testResult为null，使用savedTestCasesCount\r\n  if (!runStatus.value.testResult || runStatus.value.testResult.totalCount === 0) {\r\n    return savedTestCasesCount.value;\r\n  }\r\n  return runStatus.value.testResult.totalCount;\r\n});\r\n\r\nconst completedCount = computed(() => {\r\n  // 如果testResult为null，返回0\r\n  if (!runStatus.value.testResult) {\r\n    return 0;\r\n  }\r\n  return (runStatus.value.testResult.successCount || 0) + (runStatus.value.testResult.failureCount || 0);\r\n});\r\n\r\nconst progressPercentage = computed(() => {\r\n  if (totalCount.value === 0) return 0;\r\n  return Math.round((completedCount.value / totalCount.value) * 100);\r\n});\r\n\r\nconst completionRate = computed(() => {\r\n  if (totalCount.value === 0) return 0;\r\n  // 计算完成率，保留两位小数\r\n  return (completedCount.value / totalCount.value * 100).toFixed(2);\r\n});\r\n\r\n// 格式化进度显示\r\nconst format = (percentage: number) => {\r\n  const text = `${completedCount.value} / ${totalCount.value}`;\r\n  \r\n  // 使用 nextTick 确保 DOM 已更新后再设置class\r\n  nextTick(() => {\r\n    const progressText = document.querySelector('.el-progress__text');\r\n    if (progressText) {\r\n      // 清除之前的字体大小class\r\n      progressText.classList.remove('font-small', 'font-medium', 'font-large');\r\n      \r\n      // 根据文字长度添加对应的class\r\n      if (text.length <= 11) {\r\n        progressText.classList.add('font-large');\r\n      } else if (text.length <= 14) {\r\n        progressText.classList.add('font-medium');\r\n      } else {\r\n        progressText.classList.add('font-small');\r\n      }\r\n    }\r\n  });\r\n  \r\n  return text;\r\n};\r\n\r\n// 计算剩余时间(毫秒)\r\nconst remainingTimeMs = computed(() => {\r\n  if (!startTime.value || totalCount.value === 0 || completedCount.value === 0 || !isRunning.value) {\r\n    return 0;\r\n  }\r\n\r\n  const startTimeObj = new Date(startTime.value);\r\n  const elapsedMs = Date.now() - startTimeObj.getTime();\r\n  const msPerCase = elapsedMs / completedCount.value;\r\n  const remainingCases = totalCount.value - completedCount.value;\r\n  return msPerCase * remainingCases;\r\n});\r\n\r\n// 计算估计完成时间 - 改为计算属性\r\nconst estimatedEndTime = computed(() => {\r\n  if (!startTime.value || totalCount.value === 0 || completedCount.value === 0 || !isRunning.value) {\r\n    return '';\r\n  }\r\n\r\n  const startTimeObj = new Date(startTime.value);\r\n  const elapsedMs = Date.now() - startTimeObj.getTime();\r\n  const msPerCase = elapsedMs / completedCount.value;\r\n  const remainingCases = totalCount.value - completedCount.value;\r\n  const remainingMs = msPerCase * remainingCases;\r\n  \r\n  if (remainingMs <= 0) {\r\n    return 'Completing...';\r\n  }\r\n  \r\n  const endTimeObj = new Date(Date.now() + remainingMs);\r\n  return formatDateTime(endTimeObj);\r\n});\r\n\r\n// 获取保存的测试用例数量\r\nconst fetchSavedTestCasesCount = async () => {\r\n  loading.value = true;\r\n  try {\r\n    const response = await appApi.getSavedCaseCount();\r\n    savedTestCasesCount.value = response.data;\r\n  } catch (error) {\r\n    console.error('获取保存的测试用例数量失败:', error);\r\n    ElMessage.error('Failed to fetch test cases count');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 开始测试执行\r\nconst startTestExecution = async () => {\r\n  if (savedTestCasesCount.value === 0) {\r\n    ElMessage.warning('No test cases available to execute');\r\n    return;\r\n  }\r\n\r\n  starting.value = true;\r\n  try {\r\n    await appApi.startTest();\r\n    startTime.value = formatDateTime(new Date());\r\n    ElMessage.success('Test execution started');\r\n\r\n    // 立即获取状态并开始轮询\r\n    await fetchTestStatus();\r\n    startStatusPolling();\r\n  } catch (error) {\r\n    console.error('启动测试失败:', error);\r\n    ElMessage.error('Failed to start test execution');\r\n  } finally {\r\n    starting.value = false;\r\n  }\r\n};\r\n\r\n// 暂停测试执行\r\nconst pauseTestExecution = async () => {\r\n  pausing.value = true;\r\n  try {\r\n    await appApi.pauseTest();\r\n    ElMessage.success('Test execution paused');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('暂停测试失败:', error);\r\n    ElMessage.error('Failed to pause test execution');\r\n  } finally {\r\n    pausing.value = false;\r\n  }\r\n};\r\n\r\n// 恢复测试执行\r\nconst resumeTestExecution = async () => {\r\n  resuming.value = true;\r\n  try {\r\n    await appApi.resumeTest();\r\n    ElMessage.success('Test execution resumed');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('恢复测试失败:', error);\r\n    ElMessage.error('Failed to resume test execution');\r\n  } finally {\r\n    resuming.value = false;\r\n  }\r\n};\r\n\r\n// 停止测试执行\r\nconst stopTestExecution = async () => {\r\n  stopping.value = true;\r\n  try {\r\n    await appApi.stopTest();\r\n    ElMessage.success('Test execution stopped');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('停止测试失败:', error);\r\n    ElMessage.error('Failed to stop test execution');\r\n  } finally {\r\n    stopping.value = false;\r\n  }\r\n};\r\n\r\n// 获取测试状态\r\nconst fetchTestStatus = async () => {\r\n  try {\r\n    const response = await appApi.getTestStatus();\r\n    runStatus.value = response.data;\r\n    \r\n    // 如果testResult为null，设置为Pending状态\r\n    if (!runStatus.value.testResult) {\r\n      runStatus.value.processState = ExecutionState.Pending;\r\n    }\r\n    \r\n    lastUpdated.value = formatDateTime(new Date());\r\n\r\n    // 使用工具函数判断测试是否完成\r\n    if (isTesterCompleted(runStatus.value) && statusPollingTimer) {\r\n      fetchSavedTestCasesCount();\r\n      stopStatusPolling();\r\n    }\r\n  } catch (error) {\r\n    console.error('获取测试状态失败:', error);\r\n  }\r\n};\r\n\r\n// 开始状态轮询\r\nconst startStatusPolling = () => {\r\n  // 清除可能存在的轮询定时器\r\n  stopStatusPolling();\r\n  statusPollingTimer = window.setInterval(fetchTestStatus, 500);\r\n};\r\n\r\n// 停止状态轮询\r\nconst stopStatusPolling = () => {\r\n  if (statusPollingTimer) {\r\n    clearInterval(statusPollingTimer);\r\n    statusPollingTimer = null;\r\n  }\r\n};\r\n\r\n// 组件挂载时获取保存的测试用例数量和测试状态\r\nonMounted(() => {\r\n  fetchSavedTestCasesCount();\r\n  fetchTestStatus().then(() => {\r\n    // 如果测试正在运行，开始轮询\r\n    if (isRunning.value) {\r\n      if (runStatus.value.testResult?.creationTime) {\r\n        startTime.value = formatDateTime(new Date(runStatus.value.testResult.creationTime));\r\n      } else {\r\n        startTime.value = formatDateTime(new Date());\r\n      }\r\n      startStatusPolling();\r\n    }\r\n  });\r\n});\r\n\r\n// 组件卸载时停止轮询\r\nonUnmounted(() => {\r\n  stopStatusPolling();\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.test-run-container {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 15px 20px;\r\n}\r\n\r\n/* 顶部工具栏样式 */\r\n.toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  height: 30px;\r\n\r\n  h3 {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    color: #303133;\r\n  }\r\n\r\n  .action-buttons {\r\n    display: flex;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n/* 共用面板样式 */\r\n.panel {\r\n  background-color: #fff;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 内容区域样式 */\r\n.content-area {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 0;\r\n  gap: 12px;\r\n  min-height: 0; /* 确保内容能正确滚动 */\r\n}\r\n\r\n/* 左右布局：进度和统计信息 */\r\n.overview-layout {\r\n  display: flex;\r\n  gap: 12px;\r\n  min-height: 0; /* 防止Flex项目过度增长 */\r\n}\r\n\r\n/* 左侧：进度圆环部分样式 */\r\n.progress-section {\r\n  flex: 0 0 300px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 20px;\r\n  \r\n  .progress-circle-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 16px;\r\n    \r\n    .test-state {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n    }\r\n\r\n    /* 使用 :deep 选择器覆盖 el-progress 组件的样式，保持主题色不变 */\r\n    :deep(.el-progress__text) {\r\n      color: #606266 !important;\r\n      \r\n      &.font-large {\r\n        font-size: 22px !important;\r\n      }\r\n      \r\n      &.font-medium {\r\n        font-size: 18px !important;\r\n      }\r\n      \r\n      &.font-small {\r\n        font-size: 15px !important;\r\n      }\r\n    }\r\n    \r\n    :deep(.el-progress-circle path:nth-child(2)) {\r\n      stroke: var(--el-color-primary) !important;\r\n    }\r\n  }\r\n}\r\n\r\n/* 右侧：统计部分样式 */\r\n.stats-section {\r\n  flex: 1;\r\n  min-width: 0; /* 防止内容溢出 */\r\n}\r\n\r\n/* 统计卡片网格样式 */\r\n.stats-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  grid-template-rows: repeat(2, 1fr);\r\n  gap: 12px;\r\n  height: 100%;\r\n}\r\n\r\n.stat-card {\r\n  padding: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  transition: transform 0.2s ease;\r\n\r\n  .stat-icon {\r\n    font-size: 32px;\r\n    margin-right: 16px;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n  }\r\n  \r\n  .stat-data {\r\n    display: flex;\r\n    flex-direction: column;\r\n    \r\n    .stat-value {\r\n      font-size: 24px;\r\n      font-weight: 600;\r\n      line-height: 1.2;\r\n    }\r\n    \r\n    .stat-label {\r\n      font-size: 13px;\r\n      color: #909399;\r\n    }\r\n  }\r\n  \r\n  &.success {\r\n    .stat-icon, .stat-value {\r\n      color: var(--el-color-success);\r\n    }\r\n    border-left: 3px solid var(--el-color-success);\r\n  }\r\n  \r\n  &.failure {\r\n    .stat-icon, .stat-value {\r\n      color: var(--el-color-danger);\r\n    }\r\n    border-left: 3px solid var(--el-color-danger);\r\n  }\r\n  \r\n  &.total {\r\n    .stat-icon, .stat-value {\r\n      color: var(--el-color-primary);\r\n    }\r\n    border-left: 3px solid var(--el-color-primary);\r\n  }\r\n  \r\n  &.completion {\r\n    .stat-icon, .stat-value {\r\n      color: var(--el-color-warning);\r\n    }\r\n    border-left: 3px solid var(--el-color-warning);\r\n  }\r\n}\r\n\r\n/* 底部信息区域样式 */\r\n.footer-panel {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 12px 16px;\r\n  \r\n  .timing-info {\r\n    display: flex;\r\n    gap: 16px;\r\n    \r\n    .time-item {\r\n      display: flex;\r\n      gap: 6px;\r\n      font-size: 13px;\r\n      \r\n      .time-label {\r\n        font-weight: 600;\r\n        color: #606266;\r\n      }\r\n      \r\n      .time-value {\r\n        color: #303133;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .last-updated {\r\n    color: #909399;\r\n    font-size: 12px;\r\n    font-style: italic;\r\n  }\r\n}\r\n\r\n/* 当前操作信息面板 */\r\n.operation-panel {\r\n  padding: 12px 16px;\r\n  margin-top: -6px;  /* 减少与上方面板的间距 */\r\n  \r\n  .current-operation {\r\n    display: flex;\r\n    gap: 8px;\r\n    font-size: 13px;\r\n    \r\n    .operation-label {\r\n      font-weight: 600;\r\n      color: #606266;\r\n    }\r\n    \r\n    .operation-value {\r\n      color: #303133;\r\n      word-break: break-word;  /* 确保长文本能够换行 */\r\n    }\r\n  }\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .overview-layout {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .progress-section {\r\n    flex: 0 0 auto;\r\n  }\r\n  \r\n  .stats-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./TestRun.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TestRun.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./TestRun.vue?vue&type=style&index=0&id=b43f6810&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-b43f6810\"]])\n\nexport default __exports__"], "names": ["_defineComponent", "__name", "props", "state", "setup", "__props", "tagType", "computed", "ExecutionState", "Success", "Running", "Failure", "Pending", "getTestStateName", "Paused", "stateName", "_ctx", "_cache", "_component_el_tag", "_resolveComponent", "_openBlock", "_createBlock", "type", "value", "size", "default", "_withCtx", "_createTextVNode", "_toDisplayString", "_", "__exports__", "_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "key", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "_hoisted_37", "_hoisted_38", "savedTestCasesCount", "ref", "loading", "starting", "stopping", "pausing", "resuming", "startTime", "runStatus", "processState", "currentOperation", "testResult", "id", "resultFolderName", "testType", "creationTime", "totalCount", "successCount", "failureCount", "caseResults", "lastUpdated", "statusPollingTimer", "isRunning", "isPaused", "completedCount", "progressPercentage", "Math", "round", "completionRate", "toFixed", "format", "percentage", "text", "nextTick", "progressText", "document", "querySelector", "classList", "remove", "length", "add", "remainingTimeMs", "startTimeObj", "Date", "elapsedMs", "now", "getTime", "msPerCase", "remainingCases", "estimatedEndTime", "remainingMs", "endTimeObj", "formatDateTime", "fetchSavedTestCasesCount", "async", "response", "appApi", "getSavedCaseCount", "data", "error", "console", "ElMessage", "startTestExecution", "startTest", "success", "fetchTestStatus", "startStatusPolling", "warning", "pauseTestExecution", "pauseTest", "resumeTestExecution", "resumeTest", "stopTestExecution", "stopTest", "getTestStatus", "isTesterCompleted", "stopStatusPolling", "window", "setInterval", "clearInterval", "onMounted", "then", "onUnmounted", "_component_el_button", "_component_el_progress", "_component_el_icon", "_createElementBlock", "_createElementVNode", "_createCommentVNode", "icon", "_unref", "CaretRight", "onClick", "disabled", "VideoPause", "VideoPlay", "_createVNode", "width", "TestStateTag", "InfoFilled", "Histogram", "CircleCheckFilled", "CircleCloseFilled", "formatDuration"], "sourceRoot": ""}