namespace Alsi.Fuzz.Core.Service.CaseFactory.Core
{
    public enum MutationFieldType
    {
        Id = 1,
        Dir = 2,
        Dlc = 3,
        Data = 4,
        Rtr = 7,
        Ext = 8,

        PayloadLength = 997,
        Payload = 998,

        UDS_Repeat_Frame = 200,
        UDS_Insert_Byte_Before_N_PCI = 201,

        UDS_FF_HighNibble = 203,
        UDS_FF_DL_12b = 204,
        UDS_CF_BYTE1_LIST = 205,

        UDS_Replace_CF_With_FF_NUM = 210,
        UDS_Replace_CF_With_FF_N_PCI = 211,
        UDS_Replace_CF_With_FF_DL_12b = 212,

        // Repeat 场景
        UDS_Repeat_Index = 220,
        UDS_Repeat_Times = 221,

        // Skip 场景
        UDS_Skip_Index = 222,

        // Swap 场景
        UDS_Swap_A = 223,
        UDS_Swap_B = 224,

        // Reverse 场景
        UDS_Reverse_From = 225,
        UDS_Reverse_To = 226,

        // TP - SF
        TP_SF_Byte1 = 10,
        TP_SF_Byte2 = 11,

        TP_FF_HighNibble = 12,
        TP_FF_DL_12b = 13,

        TP_FF_DL_32b = 14,
        TP_FF_Byte1 = 15,
        TP_FF_Byte2 = 16,

        TP_CF_BYTE1_LIST = 20,

        TP_FC = 40,

        // Repeat 场景
        TP_Repeat_Index = 50,
        TP_Repeat_Times = 51,

        // Skip 场景
        TP_Skip_Index = 52,

        // Swap 场景
        TP_Swap_A = 53,
        TP_Swap_B = 54,

        // Reverse 场景
        TP_Reverse_From = 55,
        TP_Reverse_To = 56,

        // BigData - FF
        TP_BigData_FF_HighNibble = 60,
        TP_BigData_FF_DL_12b = 61,

        // BigData - CF
        TP_BigData_CF_Byte1 = 95,
        TP_BigData_CF_TargetSN = 97,

        // BigData - Repeat
        TP_BigData_Repeat_Index = 100,
        TP_BigData_Repeat_Times = 101,

        // BigData - Skip
        TP_BigData_Skip_Index = 102,

        // BigData - Swap
        TP_BigData_Swap_A = 103,
        TP_BigData_Swap_B = 104,

        // BigData - Reverse
        TP_BigData_Reverse_From = 105,
        TP_BigData_Reverse_To = 106,

        TP_Parameters = 999
    }
}
