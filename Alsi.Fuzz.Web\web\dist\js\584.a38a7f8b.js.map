{"version": 3, "file": "js/584.a38a7f8b.js", "mappings": "qUAgEA,MAAMA,EAAW,kBAEJC,EAAU,CACrBC,aAAAA,GACE,OAAIC,EAAAA,GACKC,EAAAA,GAAQC,KAAKH,gBAEfI,EAAAA,EAAMC,IAAI,GAAGP,IACtB,EAEAQ,gBAAAA,CAAiBC,GACf,OAAIN,EAAAA,GACKC,EAAAA,GAAQC,KAAKG,iBAAiBC,GAEhCH,EAAAA,EAAMI,KAAK,GAAGV,WAAmBS,EAC1C,EAEAE,SAAAA,GACE,OAAIR,EAAAA,GACKC,EAAAA,GAAQC,KAAKM,YAEfL,EAAAA,EAAMC,IAAI,GAAGP,eACtB,EAGAY,iBAAAA,GACE,OAAIT,EAAAA,GACKC,EAAAA,GAAQC,KAAKO,oBAEfN,EAAAA,EAAMI,KAAK,GAAGV,wBACvB,GC3FF,MAAMa,EAAa,CAAEC,MAAO,0BACtBC,EAAa,CAAED,MAAO,uBACtBE,EAAa,CAAEF,MAAO,gBACtBG,EAAa,CAAEH,MAAO,gBACtBI,EAAa,CAAEJ,MAAO,kBACtBK,EAAa,CAAEL,MAAO,YACtBM,EAAa,CAAEN,MAAO,YACtBO,EAAa,CAAEP,MAAO,eACtBQ,EAAa,CAAER,MAAO,kBACtBS,EAAc,CAClBC,IAAK,EACLV,MAAO,eAEHW,EAAc,CAClBD,IAAK,EACLV,MAAO,2BAEHY,EAAc,CAAEZ,MAAO,kBACvBa,EAAc,CAAEb,MAAO,iBACvBc,EAAc,CAAEd,MAAO,kBACvBe,EAAc,CAAEf,MAAO,YACvBgB,EAAc,CAAEhB,MAAO,eACvBiB,EAAc,CAAEjB,MAAO,wBACvBkB,EAAc,CAAElB,MAAO,oBACvBmB,EAAc,CAClBT,IAAK,EACLV,MAAO,qBAEHoB,EAAc,CAClBV,IAAK,EACLV,MAAO,qBAEHqB,EAAc,CAAErB,MAAO,kBACvBsB,EAAc,CAAEtB,MAAO,YACvBuB,EAAc,CAAEvB,MAAO,eACvBwB,EAAc,CAAExB,MAAO,0BAQ7B,OAA4ByB,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,cACRC,KAAAA,CAAMC,GCgNR,MAAMC,GAAUC,EAAAA,EAAAA,KAAI,GACdnC,GAASmC,EAAAA,EAAAA,IAAmB,CAChCC,gBAAiB,GACjBC,gBAAgB,EAChBC,WAAY,KACZC,eAAe,EACfC,YAAa,EACbC,aAAc,CAAC,GAAM,GAAM,IAAM,IAAM,IAAM,IAAM,IAAM,KACzDC,0BAA2B,MAC3BC,gBAAiB,IACjBC,gBAAiB,IACjBC,UAAW,KACXC,cAAc,EACdC,WAAY,KACZC,UAAW,IACXC,sBAAsB,EACtBC,wBAAwB,EACxBC,wBAAyB,CAAC,GAAM,GAChCC,aAAc,CACZC,QAAQ,EACRC,iBAAaC,EACbC,QAAS,KAKPC,GAAYtB,EAAAA,EAAAA,IAAc,IAC1BuB,GAAevB,EAAAA,EAAAA,IAAY,IAC3BwB,GAAYxB,EAAAA,EAAAA,IAAsB,IAGlCyB,GAAiBC,EAAAA,EAAAA,KAAS,KAC9B,IAAKH,EAAaI,MAAO,MAAO,GAEhC,MAAMC,EAAWJ,EAAUG,MAAME,QAAOC,GACtCA,EAAMC,cAAgBR,EAAaI,QAG/BK,EAAWR,EAAUG,MAAME,QAAOC,GACtCA,EAAMC,cAAgBR,EAAaI,OACnCG,EAAMG,UAAUC,SAASX,EAAaI,SAGxC,MAAO,IAAIC,KAAaI,EAAS,IAG7BG,GAAcnC,EAAAA,EAAAA,IAAI,CAAC,OAAQ,WAAY,WAAY,MAAO,QAE1DoC,GAAkBpC,EAAAA,EAAAA,IAAI,IACtBqC,GAAmBrC,EAAAA,EAAAA,IAAI,IACvBsC,GAAoBtC,EAAAA,EAAAA,IAAI,IACxBuC,GAAiBvC,EAAAA,EAAAA,IAAI,IACrBwC,GAAkBxC,EAAAA,EAAAA,IAAI,IACtByC,GAA+BzC,EAAAA,EAAAA,IAAI,IAGnC0C,GAAoBhB,EAAAA,EAAAA,IAAS,CACjC/D,IAAKA,IAC+C,QAA3CE,EAAO8D,MAAMpB,0BAAsC,YAAc,cAE1EoC,IAAMhB,IACJ9D,EAAO8D,MAAMpB,0BAAsC,cAAVoB,EAAwB,MAAQ,OAAO,IAI9EiB,GAAkB5C,EAAAA,EAAAA,IAAmB,MACrC6C,GAAkB7C,EAAAA,EAAAA,IAAmB,MACrC8C,GAAkB9C,EAAAA,EAAAA,KAAI,GAEtB+C,EAA+B,CACnC9C,gBAAiB,GACjBC,gBAAgB,EAChBC,WAAY,KACZC,eAAe,EACfC,YAAa,EACbC,aAAc,CAAC,GAAM,GAAM,IAAM,IAAM,IAAM,IAAM,IAAM,KACzDC,0BAA2B,MAC3BC,gBAAiB,IACjBC,gBAAiB,IACjBC,UAAW,KACXC,cAAc,EACdC,WAAY,KACZC,UAAW,IACXC,sBAAsB,EACtBC,wBAAwB,EACxBC,wBAAyB,CAAC,GAAM,GAChCC,aAAc,CACZC,QAAQ,EACRC,iBAAaC,EACbC,QAAS,IAIP2B,EAAoBC,GACnBA,EAGEA,EAAIC,KAAIC,GAAK,KAAOA,EAAEC,SAAS,IAAIC,gBAAeC,KAAK,KAFrD,GAKLC,EAA0B5B,IAC9B,IAEE,MAAM6B,EAAa7B,EAAM8B,QAAQ,OAAQ,IACnCC,EAAKC,SAASH,EAAY,IAChC,GAAII,MAAMF,GAER,YADAG,EAAAA,GAAUC,MAAM,qBAGlBjG,EAAO8D,MAAMxB,WAAauD,EAE1BtB,EAAgBT,MAAQ,KAAO+B,EAAGN,SAAS,IAAIC,a,CAC/C,MAAOS,GACPD,EAAAA,GAAUC,MAAM,sB,GAIdC,EAA2BpC,IAC/B,IAEE,MAAM6B,EAAa7B,EAAM8B,QAAQ,OAAQ,IACnCO,EAAML,SAASH,EAAY,IACjC,GAAII,MAAMI,GAER,YADAH,EAAAA,GAAUC,MAAM,sBAGlB,GAAIE,EAAM,GAAKA,EAAM,GAEnB,YADAH,EAAAA,GAAUI,QAAQ,kCAGpBpG,EAAO8D,MAAMtB,YAAc2D,EAE3B3B,EAAiBV,MAAQ,KAAOqC,EAAIZ,SAAS,IAAIC,a,CACjD,MAAOS,GACPD,EAAAA,GAAUC,MAAM,sB,GAIdI,EAA4BvC,IAChC,IACE,MAAMwC,EAAQxC,EAAMyC,MAAM,KACvBlB,KAAImB,GAAKA,EAAEC,SACXzC,QAAOwC,GAAKA,IACZnB,KAAImB,GAAKV,SAASU,EAAG,MAExB,GAAIF,EAAMI,OAAS,EAEjB,YADAV,EAAAA,GAAUI,QAAQ,2BAGpB,GAAIE,EAAMK,MAAKC,GAAKb,MAAMa,IAAMA,EAAI,GAAKA,EAAI,MAE3C,YADAZ,EAAAA,GAAUI,QAAQ,2CAGpBpG,EAAO8D,MAAMrB,aAAe6D,C,CAC5B,MAAOL,GACPD,EAAAA,GAAUC,MAAM,uB,GAIdY,EAAyB/C,IAC7B,IAEE,MAAM6B,EAAa7B,EAAM8B,QAAQ,OAAQ,IACnCC,EAAKC,SAASH,EAAY,IAChC,GAAII,MAAMF,GAER,YADAG,EAAAA,GAAUC,MAAM,qBAGlBjG,EAAO8D,MAAMjB,UAAYgD,EAEzBnB,EAAeZ,MAAQ,KAAO+B,EAAGN,SAAS,IAAIC,a,CAC9C,MAAOS,GACPD,EAAAA,GAAUC,MAAM,sB,GAIda,GAA0BhD,IAC9B,IAEE,MAAM6B,EAAa7B,EAAM8B,QAAQ,OAAQ,IACnCC,EAAKC,SAASH,EAAY,IAChC,GAAII,MAAMF,GAER,YADAG,EAAAA,GAAUC,MAAM,qBAGlBjG,EAAO8D,MAAMf,WAAa8C,EAE1BlB,EAAgBb,MAAQ,KAAO+B,EAAGN,SAAS,IAAIC,a,CAC/C,MAAOS,GACPD,EAAAA,GAAUC,MAAM,sB,GAIdc,GAAuCjD,IAC3C,IACE,MAAMwC,EAAQxC,EAAMyC,MAAM,KACvBlB,KAAImB,GAAKA,EAAEC,SACXzC,QAAOwC,GAAKA,IACZnB,KAAImB,GAAKV,SAASU,EAAG,MAExB,GAAIF,EAAMK,MAAKC,GAAKb,MAAMa,IAAMA,EAAI,GAAKA,EAAI,MAE3C,YADAZ,EAAAA,GAAUI,QAAQ,2CAGpBpG,EAAO8D,MAAMX,wBAA0BmD,C,CACvC,MAAOL,GACPD,EAAAA,GAAUC,MAAM,uB,GAIde,GAAiBA,KACrBhH,EAAO8D,MAAQmD,KAAKC,MAAMD,KAAKE,UAAUjC,IACzCX,EAAgBT,MAAQ,KAAOoB,EAAc5C,WAAWiD,SAAS,IAAIC,cACrEhB,EAAiBV,MAAQ,KAAOoB,EAAc1C,YAAY+C,SAAS,IAAIC,cACvEf,EAAkBX,MAAQqB,EAAiBD,EAAczC,cACzDiC,EAAeZ,MAAQ,KAAOoB,EAAcrC,UAAU0C,SAAS,IAAIC,cACnEb,EAAgBb,MAAQ,KAAOoB,EAAcnC,WAAWwC,SAAS,IAAIC,cACrEZ,EAA6Bd,MAAQqB,EAAiBD,EAAc/B,yBACpE4B,EAAgBjB,MAAQ,KACxBkB,EAAgBlB,MAAQ,KACxBmB,EAAgBnB,OAAQ,EAGxBL,EAAUK,MAAQ,GAClBJ,EAAaI,MAAQ,GACrBH,EAAUG,MAAQ,EAAE,EAGhBsD,GAAaC,UACjBnF,EAAQ4B,OAAQ,EAChB,IACE,MAAMwD,QAAiB9H,EAAQC,gBAmC/B,GAlCAO,EAAO8D,MAAQwD,EAASC,KACxBhD,EAAgBT,MAAQ,KAAOwD,EAASC,KAAKjF,WAAWiD,SAAS,IAAIC,cACrEf,EAAkBX,MAAQqB,EAAiBmC,EAASC,KAAK9E,cACzDiC,EAAeZ,MAAQ,KAAOwD,EAASC,KAAK1E,UAAU0C,SAAS,IAAIC,cACnEb,EAAgBb,MAAQ,KAAOwD,EAASC,KAAKxE,WAAWwC,SAAS,IAAIC,cAGjE8B,EAASC,KAAKpE,wBAChByB,EAA6Bd,MAAQqB,EAAiBmC,EAASC,KAAKpE,yBAEpEyB,EAA6Bd,MAAQ,iBAIHP,IAAhC+D,EAASC,KAAKhF,gBAChBvC,EAAO8D,MAAMvB,eAAgB,QAEGgB,IAA9B+D,EAASC,KAAK/E,cAChBxC,EAAO8D,MAAMtB,YAAc,QAEMe,IAA/B+D,EAASC,KAAKzE,eAChB9C,EAAO8D,MAAMhB,cAAe,QAEeS,IAAzC+D,EAASC,KAAKrE,yBAChBlD,EAAO8D,MAAMZ,wBAAyB,QAEMK,IAA1C+D,EAASC,KAAKpE,0BAChBnD,EAAO8D,MAAMX,wBAA0B,CAAC,GAAM,IAIhDqB,EAAiBV,MAAQ,KAAO9D,EAAO8D,MAAMtB,YAAY+C,SAAS,IAAIC,cAGlE8B,EAASC,KAAKnF,gBAAgBsE,OAAS,EAAG,CAC5C,MAAMc,EAASF,EAASC,KAAKnF,gBAC7BuB,EAAUG,MAAQ0D,EAGlB,MAAMC,EAAQ,IAAIC,IAClBF,EAAOG,SAAQ1D,IACTA,EAAMC,aAAauD,EAAMG,IAAI3D,EAAMC,aACvCD,EAAMG,WAAWuD,SAAQE,GAAKJ,EAAMG,IAAIC,IAAG,IAG7CpE,EAAUK,MAAQgE,MAAMC,KAAKN,GAGzBH,EAASC,KAAKS,kBAAoBvE,EAAUK,MAAMO,SAASiD,EAASC,KAAKS,kBAC3EtE,EAAaI,MAAQwD,EAASC,KAAKS,iBAGD,IAA3BvE,EAAUK,MAAM4C,SACvBhD,EAAaI,MAAQL,EAAUK,MAAM,G,EAGzC,MAAOmC,GACPD,EAAAA,GAAUC,MAAM,+B,CAChB,QACA/D,EAAQ4B,OAAQ,C,GAKdmE,GAAoBnE,IAEnBA,GACHkC,EAAAA,GAAUI,QAAQ,6B,EAIhB8B,GAAab,UACjBnF,EAAQ4B,OAAQ,EAChB,IAEE,MAAMqE,EAAa,IAAKnI,EAAO8D,OAG/BqE,EAAW/F,gBAAkBuB,EAAUG,MAGvCqE,EAAWH,iBAAmBtE,EAAaI,MAEvCiB,EAAgBjB,QAClBqE,EAAWC,gBAAkBrD,EAAgBjB,OAG3CmB,EAAgBnB,QAClBqE,EAAWE,mBAAoB,GAGjC,MAAMC,QAAe9I,EAAQO,iBAAiBoI,GAC9CnI,EAAO8D,MAAQwE,EAAOf,KAEtBxC,EAAgBjB,MAAQ,KACxBkB,EAAgBlB,MAAQ,KACxBmB,EAAgBnB,OAAQ,EAExBkC,EAAAA,GAAUuC,QAAQ,uB,CAClB,MAAOtC,GACPD,EAAAA,GAAUC,MAAM,c,CAChB,QACA/D,EAAQ4B,OAAQ,C,GAId0E,IAAgBrG,EAAAA,EAAAA,KAAI,GAEpBsG,GAAkBpB,UACtBmB,GAAc1E,OAAQ,EACtB,IACE,MAAMwD,QAAiB9H,EAAQU,YAG/ByD,EAAUG,MAAQwD,EAASC,KAAKnF,gBAChCqB,EAAUK,MAAQwD,EAASC,KAAK9D,UAGhCC,EAAaI,MAAQ,GAGrB9D,EAAO8D,MAAM1B,gBAAkB,GAE/B4D,EAAAA,GAAUuC,QAAQ,8D,CAClB,MAAOtC,GACP,GAA6B,iBAAzBA,EAAMqB,UAAUC,KAClB,OAEFvB,EAAAA,GAAUC,MAA+B,sBAAzBA,EAAMqB,UAAUC,KAC5B,0BACA,4B,CACJ,QACAiB,GAAc1E,OAAQ,C,GAIpB4E,GAAkBpC,IACtB,IAAKA,EAAO,MAAO,MAEnB,MAAMqC,EAAQ,CAAC,IAAK,KAAM,KAAM,MAChC,IAAIC,EAAOtC,EACPuC,EAAY,EAEhB,MAAOD,GAAQ,MAAQC,EAAYF,EAAMjC,OAAS,EAChDkC,GAAQ,KACRC,IAGF,MAAO,GAAGD,EAAKE,QAAQ,MAAMH,EAAME,IAAY,EAG3CE,GAAY1B,UAChB,IACE,MAAMC,QAAiB9H,EAAQW,oBACzB6I,EAAU1B,EAASC,KAAK0B,KAC9BlE,EAAgBjB,MAAQkF,EACxBhE,EAAgBlB,MAAQkF,EAAQzC,MAAM,MAAM2C,OAASF,C,CACrD,MAAO/C,GACP,GAA6B,iBAAzBA,EAAMqB,UAAUC,KAClB,OAEFvB,EAAAA,GAAUC,MAA+B,sBAAzBA,EAAMqB,UAAUC,KAC5B,0BACA,4B,GAIF4B,GAAYA,KAChBlE,EAAgBnB,OAAQ,CAAI,EAGxBsF,IAAqBvF,EAAAA,EAAAA,KAAS,IAC9BmB,EAAgBlB,MACXkB,EAAgBlB,MAGrBmB,EAAgBnB,MACX,iCAGF9D,EAAO8D,MAAMV,cAAcC,OAC9B,GAAGrD,EAAO8D,MAAMV,aAAaE,gBAAgBoF,GAAe1I,EAAO8D,MAAMV,aAAaI,YACtF,KAMA6F,GAAYA,KAChB/E,EAAYR,MAAQ,CAAC,OAAQ,WAAY,WAAY,MAAO,MAAM,EAI9DwF,GAAcA,KAClBhF,EAAYR,MAAQ,EAAE,EDvMxB,OC0MAyF,EAAAA,EAAAA,KAAU,KACRnC,IAAY,ID3MP,CAACoC,EAAUC,KAChB,MAAMC,GAA+BC,EAAAA,EAAAA,IAAkB,qBACjDC,GAAuBD,EAAAA,EAAAA,IAAkB,aACzCE,GAAsBF,EAAAA,EAAAA,IAAkB,YACxCG,GAA0BH,EAAAA,EAAAA,IAAkB,gBAC5CI,IAAuBJ,EAAAA,EAAAA,IAAkB,aACzCK,IAA6BL,EAAAA,EAAAA,IAAkB,mBAC/CM,IAAqBN,EAAAA,EAAAA,IAAkB,WACvCO,IAAqBP,EAAAA,EAAAA,IAAkB,WACvCQ,IAA8BR,EAAAA,EAAAA,IAAkB,oBAChDS,IAAuBT,EAAAA,EAAAA,IAAkB,aACzCU,IAAuBV,EAAAA,EAAAA,IAAkB,aACzCW,IAA6BX,EAAAA,EAAAA,IAAkB,mBAC/CY,IAAsBZ,EAAAA,EAAAA,IAAkB,YACxCa,IAAyBb,EAAAA,EAAAA,IAAkB,eAC3Cc,IAAqBC,EAAAA,EAAAA,IAAkB,WAE7C,OAAOC,EAAAA,EAAAA,MAAiBC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOzK,EAAY,EAC3E0K,EAAAA,EAAAA,IAAoB,MAAOxK,EAAY,EACrCwK,EAAAA,EAAAA,IAAoB,MAAOvK,EAAY,EACrCwK,EAAAA,EAAAA,IAAanB,EAAsB,CACjCoB,QAAS3B,GACT4B,KAAM,UACNrC,KAAM,QACNvI,MAAO,iBACN,CACD6K,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAarB,EAA8B,CAAE0B,KAAM,uCACnD3B,EAAO,MAAQA,EAAO,KAAMqB,EAAAA,EAAAA,IAAoB,OAAQ,CAAEzK,MAAO,eAAiB,cAAe,OAEnGgL,EAAG,KAELN,EAAAA,EAAAA,IAAanB,EAAsB,CACjCoB,QAAS1B,GACT2B,KAAM,UACNrC,KAAM,QACNvI,MAAO,mBACN,CACD6K,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAarB,EAA8B,CAAE0B,KAAM,qCACnD3B,EAAO,MAAQA,EAAO,KAAMqB,EAAAA,EAAAA,IAAoB,OAAQ,CAAEzK,MAAO,eAAiB,gBAAiB,OAErGgL,EAAG,SAITP,EAAAA,EAAAA,IAAoB,MAAOtK,EAAY,EACrCuK,EAAAA,EAAAA,IAAaP,GAAwB,CACnCc,WAAYhH,EAAYR,MACxB,sBAAuB2F,EAAO,MAAQA,EAAO,IAAO8B,GAAkBjH,EAAaR,MAAQyH,IAC1F,CACDL,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBL,EAAAA,EAAAA,IAAoB,MAAOrK,EAAY,EACrCsK,EAAAA,EAAAA,IAAaZ,GAA6B,CACxCqB,MAAO,eACPC,KAAM,OACNpL,MAAO,eACN,CACD6K,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAab,GAAoB,CAC/BwB,MAAO1L,EAAO8D,MACd,cAAe,QACf,iBAAkB,OACjB,CACDoH,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBL,EAAAA,EAAAA,IAAoB,MAAOpK,EAAY,EACrCqK,EAAAA,EAAAA,IAAajB,EAAyB,CACpC6B,MAAO,aACPtL,MAAO,aACN,CACD6K,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAalB,EAAqB,CAChCyB,WAAY5G,EAAeZ,MAC3B,sBAAuB2F,EAAO,KAAOA,EAAO,GAAM8B,GAAkB7G,EAAgBZ,MAAQyH,GAC5FK,YAAa,8BACbC,SAAUhF,GACT,KAAM,EAAG,CAAC,kBAEfwE,EAAG,KAELN,EAAAA,EAAAA,IAAajB,EAAyB,CACpC6B,MAAO,WACPtL,MAAO,YACPyL,MAAO,CAAC,MAAQ,SACf,CACDZ,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAahB,GAAsB,CACjCuB,WAAYtL,EAAO8D,MAAMhB,aACzB,sBAAuB2G,EAAO,KAAOA,EAAO,GAAM8B,GAAkBvL,EAAO8D,MAAMhB,aAAgByI,IAChG,KAAM,EAAG,CAAC,kBAEfF,EAAG,OAGPP,EAAAA,EAAAA,IAAoB,MAAOnK,EAAY,EACrCoK,EAAAA,EAAAA,IAAajB,EAAyB,CACpC6B,MAAO,cACPtL,MAAO,aACN,CACD6K,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAalB,EAAqB,CAChCyB,WAAY3G,EAAgBb,MAC5B,sBAAuB2F,EAAO,KAAOA,EAAO,GAAM8B,GAAkB5G,EAAiBb,MAAQyH,GAC7FK,YAAa,8BACbC,SAAU/E,IACT,KAAM,EAAG,CAAC,kBAEfuE,EAAG,KAELN,EAAAA,EAAAA,IAAajB,EAAyB,CACpC6B,MAAO,eACPtL,MAAO,aACN,CACD6K,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaf,GAA4B,CACvCsB,WAAYtL,EAAO8D,MAAMd,UACzB,sBAAuByG,EAAO,KAAOA,EAAO,GAAM8B,GAAkBvL,EAAO8D,MAAMd,UAAauI,GAC9FQ,IAAK,EACLC,IAAK,IACLF,MAAO,CAAC,MAAQ,UACf,KAAM,EAAG,CAAC,kBAEfT,EAAG,OAGPP,EAAAA,EAAAA,IAAoB,MAAOlK,EAAY,EACrCmK,EAAAA,EAAAA,IAAad,GAAoB,KAAM,CACrCiB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,KAAakB,EAAAA,EAAAA,IAAOC,EAAAA,gBAEtBb,EAAG,IAEL5B,EAAO,MAAQA,EAAO,KAAM0C,EAAAA,EAAAA,OAC5B1C,EAAO,MAAQA,EAAO,KAAMqB,EAAAA,EAAAA,IAAoB,OAAQ,KAAM,sFAAuF,SAGzJO,EAAG,GACF,EAAG,CAAC,aAETA,EAAG,OAGPP,EAAAA,EAAAA,IAAoB,MAAOjK,EAAY,EACrCkK,EAAAA,EAAAA,IAAaZ,GAA6B,CACxCqB,MAAO,mBACPC,KAAM,WACNpL,MAAO,eACN,CACD6K,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAab,GAAoB,CAC/BwB,MAAO1L,EAAO8D,MACd,cAAe,QACf,iBAAkB,OACjB,CACDoH,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAajB,EAAyB,KAAM,CAC1CoB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAanB,EAAsB,CACjCoB,QAASvC,GACTvG,QAASsG,GAAc1E,MACvBmH,KAAM,UACNrC,KAAM,QACNkD,MAAO,CAAC,gBAAgB,SACvB,CACDZ,SAASC,EAAAA,EAAAA,KAAS,IAAM1B,EAAO,MAAQA,EAAO,IAAM,EAClD0C,EAAAA,EAAAA,IAAiB,oBAEnBd,EAAG,GACF,EAAG,CAAC,eAETA,EAAG,IAEJ5H,EAAUK,MAAM4C,OAAS,IACrBkE,EAAAA,EAAAA,OAAcwB,EAAAA,EAAAA,IAAatC,EAAyB,CACnD/I,IAAK,EACL4K,MAAO,oBACN,CACDT,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaV,GAAsB,CACjCiB,WAAY5H,EAAaI,MACzB,sBAAuB2F,EAAO,KAAOA,EAAO,GAAM8B,GAAkB7H,EAAcI,MAAQyH,GAC1FK,YAAa,oBACbE,MAAO,CAAC,MAAQ,QAChBD,SAAU5D,IACT,CACDiD,SAASC,EAAAA,EAAAA,KAAS,IAAM,GACrBP,EAAAA,EAAAA,KAAW,IAAOC,EAAAA,EAAAA,IAAoBwB,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAY7I,EAAUK,OAAQyI,KAC5E3B,EAAAA,EAAAA,OAAcwB,EAAAA,EAAAA,IAAahC,GAAsB,CACvDrJ,IAAKwL,EACLZ,MAAOY,EACPzI,MAAOyI,GACN,KAAM,EAAG,CAAC,QAAS,aACpB,SAENlB,EAAG,GACF,EAAG,CAAC,kBAETA,EAAG,MAELmB,EAAAA,EAAAA,IAAoB,IAAI,IAC5BzB,EAAAA,EAAAA,IAAajB,EAAyB,CACpC6B,MAAO,sBAAsB/H,EAAeE,MAAM4C,WACjD,CACDwE,SAASC,EAAAA,EAAAA,KAAS,IAAM,CACM,IAA3BxH,EAAUG,MAAM4C,SACZkE,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAO/J,EAAa,8EACrD4C,EAAaI,QAEZ8G,EAAAA,EAAAA,OAAcwB,EAAAA,EAAAA,IAAa7B,GAAqB,CAC/CxJ,IAAK,EACLwG,KAAM3D,EAAeE,MACrBgI,MAAO,CAAC,MAAQ,QAChB,aAAc,IACdW,OAAQ,IACP,CACDvB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaT,GAA4B,CACvCqB,MAAO,KACP,YAAa,OACZ,CACDT,SAASC,EAAAA,EAAAA,KAAS,EAAGuB,SAAU,EAC7BP,EAAAA,EAAAA,IAAiB,OAAQQ,EAAAA,EAAAA,IAAiBD,EAAI7G,GAAGN,SAAS,IAAIC,cAAcoH,SAAS,EAAG,MAAO,MAEjGvB,EAAG,KAELN,EAAAA,EAAAA,IAAaT,GAA4B,CACvCuC,KAAM,OACNlB,MAAO,OACP,YAAa,SAEfZ,EAAAA,EAAAA,IAAaT,GAA4B,CACvCqB,MAAO,MACP,YAAa,MACZ,CACDT,SAASC,EAAAA,EAAAA,KAAS,EAAGuB,SAAU,EAC7BP,EAAAA,EAAAA,IAAiB,OAAQQ,EAAAA,EAAAA,IAAiBD,EAAIvG,IAAIZ,SAAS,IAAIC,eAAgB,MAEjF6F,EAAG,KAELN,EAAAA,EAAAA,IAAaT,GAA4B,CACvCqB,MAAO,WACP,YAAa,OACZ,CACDT,SAASC,EAAAA,EAAAA,KAAS,EAAGuB,SAAU,EAC7BP,EAAAA,EAAAA,KAAiBQ,EAAAA,EAAAA,IAAiBD,EAAII,MAAQ,eAAiB,gBAAiB,MAElFzB,EAAG,KAELN,EAAAA,EAAAA,IAAaT,GAA4B,CACvCuC,KAAM,cACNlB,MAAO,cACP,YAAa,SAEfZ,EAAAA,EAAAA,IAAaT,GAA4B,CACvCqB,MAAO,YACP,YAAa,OACZ,CACDT,SAASC,EAAAA,EAAAA,KAAS,EAAGuB,SAAU,EAC7BP,EAAAA,EAAAA,KAAiBQ,EAAAA,EAAAA,IAAiBD,EAAItI,UAAUqB,KAAK,OAAQ,MAE/D4F,EAAG,OAGPA,EAAG,GACF,EAAG,CAAC,YAzDNT,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAO7J,EAAa,2DA2D/DqK,EAAG,GACF,EAAG,CAAC,aAETA,EAAG,GACF,EAAG,CAAC,aAETA,EAAG,OAGPP,EAAAA,EAAAA,IAAoB,MAAO7J,EAAa,EACtC8J,EAAAA,EAAAA,IAAaZ,GAA6B,CACxCqB,MAAO,qBACPC,KAAM,WACNpL,MAAO,eACN,CACD6K,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAab,GAAoB,CAC/BwB,MAAO1L,EAAO8D,MACd,cAAe,QACf,iBAAkB,OACjB,CACDoH,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAajB,EAAyB,CAAE6B,MAAO,qBAAuB,CACpET,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAahB,GAAsB,CACjCuB,WAAYtL,EAAO8D,MAAMzB,eACzB,sBAAuBoH,EAAO,KAAOA,EAAO,GAAM8B,GAAkBvL,EAAO8D,MAAMzB,eAAkBkJ,IAClG,KAAM,EAAG,CAAC,kBAEfF,EAAG,KAELP,EAAAA,EAAAA,IAAoB,MAAO,CACzBzK,OAAO0M,EAAAA,EAAAA,IAAgB,CAAE,yBAA0B/M,EAAO8D,MAAMzB,kBAC/D,EACD0I,EAAAA,EAAAA,IAAaR,GAAqB,CAChChD,KAAM,CAAC,CAAC,GACRkF,OAAQ,GACRX,MAAO,CAAC,MAAQ,OAAO,gBAAgB,QACvC,eAAe,EACfzL,MAAO,YACN,CACD6K,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaT,GAA4B,CACvCqB,MAAO,KACPqB,MAAO,OACN,CACD9B,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAalB,EAAqB,CAChCyB,WAAY/G,EAAgBT,MAC5B,sBAAuB2F,EAAO,KAAOA,EAAO,GAAM8B,GAAkBhH,EAAiBT,MAAQyH,GAC7FK,YAAa,kBACbC,SAAUnG,EACVuH,UAAWjN,EAAO8D,MAAMzB,gBACvB,KAAM,EAAG,CAAC,aAAc,gBAE7BgJ,EAAG,KAELN,EAAAA,EAAAA,IAAaT,GAA4B,CACvCqB,MAAO,WACPqB,MAAO,OACN,CACD9B,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAahB,GAAsB,CACjCuB,WAAYtL,EAAO8D,MAAMvB,cACzB,sBAAuBkH,EAAO,KAAOA,EAAO,GAAM8B,GAAkBvL,EAAO8D,MAAMvB,cAAiBgJ,GAClG0B,UAAWjN,EAAO8D,MAAMzB,gBACvB,KAAM,EAAG,CAAC,aAAc,gBAE7BgJ,EAAG,KAELN,EAAAA,EAAAA,IAAaT,GAA4B,CACvCqB,MAAO,aACPqB,MAAO,OACN,CACD9B,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaV,GAAsB,CACjCiB,WAAYzG,EAAkBf,MAC9B,sBAAuB2F,EAAO,KAAOA,EAAO,GAAM8B,GAAkB1G,EAAmBf,MAAQyH,GAC/F0B,UAAWjN,EAAO8D,MAAMzB,gBACvB,CACD6I,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaX,GAAsB,CACjCtG,MAAO,YACP6H,MAAO,eAETZ,EAAAA,EAAAA,IAAaX,GAAsB,CACjCtG,MAAO,cACP6H,MAAO,mBAGXN,EAAG,GACF,EAAG,CAAC,aAAc,gBAEvBA,EAAG,KAELN,EAAAA,EAAAA,IAAaT,GAA4B,CACvCqB,MAAO,MACPqB,MAAO,OACN,CACD9B,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAalB,EAAqB,CAChCyB,WAAY9G,EAAiBV,MAC7B,sBAAuB2F,EAAO,KAAOA,EAAO,GAAM8B,GAAkB/G,EAAkBV,MAAQyH,GAC9FK,YAAa,YACbC,SAAU3F,EACV+G,UAAWjN,EAAO8D,MAAMzB,gBACvB,KAAM,EAAG,CAAC,aAAc,gBAE7BgJ,EAAG,KAELN,EAAAA,EAAAA,IAAaT,GAA4B,CAAEqB,MAAO,QAAU,CAC1DT,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAalB,EAAqB,CAChCyB,WAAY7G,EAAkBX,MAC9B,sBAAuB2F,EAAO,MAAQA,EAAO,IAAO8B,GAAkB9G,EAAmBX,MAAQyH,GACjGK,YAAa,iEACbC,SAAUxF,EACV4G,UAAWjN,EAAO8D,MAAMzB,gBACvB,KAAM,EAAG,CAAC,aAAc,gBAE7BgJ,EAAG,OAGPA,EAAG,KAELP,EAAAA,EAAAA,IAAoB,MAAO5J,EAAa,EACtC6J,EAAAA,EAAAA,IAAajB,EAAyB,CACpC6B,MAAO,aACPtL,MAAO,kBACN,CACD6K,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaf,GAA4B,CACvCsB,WAAYtL,EAAO8D,MAAMnB,gBACzB,sBAAuB8G,EAAO,MAAQA,EAAO,IAAO8B,GAAkBvL,EAAO8D,MAAMnB,gBAAmB4I,GACtGQ,IAAK,EACLC,IAAK,IACLF,MAAO,CAAC,MAAQ,SAChBmB,UAAWjN,EAAO8D,MAAMzB,gBACvB,KAAM,EAAG,CAAC,aAAc,gBAE7BgJ,EAAG,KAELN,EAAAA,EAAAA,IAAajB,EAAyB,CACpC6B,MAAO,qBACPtL,MAAO,kBACN,CACD6K,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaf,GAA4B,CACvCsB,WAAYtL,EAAO8D,MAAMlB,gBACzB,sBAAuB6G,EAAO,MAAQA,EAAO,IAAO8B,GAAkBvL,EAAO8D,MAAMlB,gBAAmB2I,GACtGQ,IAAK,EACLC,IAAK,IACLF,MAAO,CAAC,MAAQ,SAChBmB,UAAWjN,EAAO8D,MAAMzB,gBACvB,KAAM,EAAG,CAAC,aAAc,gBAE7BgJ,EAAG,OAGN,MAELA,EAAG,GACF,EAAG,CAAC,aAETA,EAAG,OAGPP,EAAAA,EAAAA,IAAoB,MAAO3J,EAAa,EACtC4J,EAAAA,EAAAA,IAAaZ,GAA6B,CACxCqB,MAAO,cACPC,KAAM,MACNpL,MAAO,eACN,CACD6K,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAab,GAAoB,CAC/BwB,MAAO1L,EAAO8D,MACd,cAAe,QACf,iBAAkB,OACjB,CACDoH,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBL,EAAAA,EAAAA,IAAoB,MAAO1J,EAAa,EACtC2J,EAAAA,EAAAA,IAAajB,EAAyB,CACpC6B,MAAO,uBACPtL,MAAO,aACN,CACD6K,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAahB,GAAsB,CACjCuB,WAAYtL,EAAO8D,MAAMb,qBACzB,sBAAuBwG,EAAO,MAAQA,EAAO,IAAO8B,GAAkBvL,EAAO8D,MAAMb,qBAAwBsI,IAC1G,KAAM,EAAG,CAAC,kBAEfF,EAAG,KAELN,EAAAA,EAAAA,IAAajB,EAAyB,CACpC6B,MAAO,uBACPtL,MAAO,aACN,CACD6K,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAahB,GAAsB,CACjCuB,WAAYtL,EAAO8D,MAAMZ,uBACzB,sBAAuBuG,EAAO,MAAQA,EAAO,IAAO8B,GAAkBvL,EAAO8D,MAAMZ,uBAA0BqI,IAC5G,KAAM,EAAG,CAAC,kBAEfF,EAAG,KAELN,EAAAA,EAAAA,IAAajB,EAAyB,CACpC6B,MAAO,wBACPtL,MAAO,YACPyL,MAAO,CAAC,MAAQ,UACf,CACDZ,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAalB,EAAqB,CAChCyB,WAAY1G,EAA6Bd,MACzC,sBAAuB2F,EAAO,MAAQA,EAAO,IAAO8B,GAAkB3G,EAA8Bd,MAAQyH,GAC5GK,YAAa,mCACbC,SAAU9E,GACVkG,UAAWjN,EAAO8D,MAAMZ,wBACvB,KAAM,EAAG,CAAC,aAAc,gBAE7BmI,EAAG,OAGPP,EAAAA,EAAAA,IAAoB,MAAOzJ,EAAa,EACtC0J,EAAAA,EAAAA,IAAad,GAAoB,KAAM,CACrCiB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,KAAakB,EAAAA,EAAAA,IAAOC,EAAAA,gBAEtBb,EAAG,IAEL5B,EAAO,MAAQA,EAAO,KAAM0C,EAAAA,EAAAA,OAC5B1C,EAAO,MAAQA,EAAO,KAAMqB,EAAAA,EAAAA,IAAoB,OAAQ,KAAM,sIAAuI,OAEvMA,EAAAA,EAAAA,IAAoB,MAAO,KAAM,EAC/BC,EAAAA,EAAAA,IAAajB,EAAyB,CAAE6B,MAAO,uBAAyB,CACtET,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBL,EAAAA,EAAAA,IAAoB,MAAOxJ,EAAa,EACtCyJ,EAAAA,EAAAA,IAAalB,EAAqB,CAChCyB,WAAYlC,GAAmBtF,MAC/B,sBAAuB2F,EAAO,MAAQA,EAAO,IAAO8B,GAAkBnC,GAAoBtF,MAAQyH,GAClGK,YAAa,kBACbsB,SAAU,GACV7M,MAAO,sBACN,KAAM,EAAG,CAAC,gBACbyK,EAAAA,EAAAA,IAAoB,MAAOvJ,EAAa,CACpCvB,EAAO8D,MAAMV,cAAcC,QAQzBmJ,EAAAA,EAAAA,IAAoB,IAAI,KAPvB5B,EAAAA,EAAAA,OAAcwB,EAAAA,EAAAA,IAAaxC,EAAsB,CAChD7I,IAAK,EACLiK,QAASjC,GACTkC,KAAM,UACNG,MAAMa,EAAAA,EAAAA,IAAOkB,EAAAA,MACb3B,MAAO,cACN,KAAM,EAAG,CAAC,UAEhBxL,EAAO8D,MAAMV,cAAcC,SACvBuH,EAAAA,EAAAA,OAAcwB,EAAAA,EAAAA,IAAaxC,EAAsB,CAChD7I,IAAK,EACLiK,QAAS7B,GACT8B,KAAM,SACNG,MAAMa,EAAAA,EAAAA,IAAOmB,EAAAA,QACb5B,MAAO,cACN,KAAM,EAAG,CAAC,WACbgB,EAAAA,EAAAA,IAAoB,IAAI,OAG/BxH,EAAgBlB,QACZ8G,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOrJ,EAAa,0CACvDgL,EAAAA,EAAAA,IAAoB,IAAI,GAC3BvH,EAAgBnB,QACZ8G,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOpJ,EAAa,oDACvD+K,EAAAA,EAAAA,IAAoB,IAAI,MAE9BnB,EAAG,SAITA,EAAG,GACF,EAAG,CAAC,aAETA,EAAG,OAGPP,EAAAA,EAAAA,IAAoB,MAAOpJ,EAAa,EACtCqJ,EAAAA,EAAAA,IAAaZ,GAA6B,CACxCqB,MAAO,cACPC,KAAM,MACNpL,MAAO,eACN,CACD6K,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAab,GAAoB,CAC/BwB,MAAO1L,EAAO8D,MACd,cAAe,QACf,iBAAkB,OACjB,CACDoH,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBL,EAAAA,EAAAA,IAAoB,MAAOnJ,EAAa,EACtCoJ,EAAAA,EAAAA,IAAajB,EAAyB,CACpC6B,MAAO,oBACPtL,MAAO,aACN,CACD6K,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAahB,GAAsB,CACjCuB,WAAYtL,EAAO8D,MAAMuJ,gBACzB,sBAAuB5D,EAAO,MAAQA,EAAO,IAAO8B,GAAkBvL,EAAO8D,MAAMuJ,gBAAmB9B,IACrG,KAAM,EAAG,CAAC,kBAEfF,EAAG,OAGPP,EAAAA,EAAAA,IAAoB,MAAOlJ,EAAa,EACtCmJ,EAAAA,EAAAA,IAAad,GAAoB,KAAM,CACrCiB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,KAAakB,EAAAA,EAAAA,IAAOC,EAAAA,gBAEtBb,EAAG,IAEL5B,EAAO,MAAQA,EAAO,KAAM0C,EAAAA,EAAAA,OAC5B1C,EAAO,MAAQA,EAAO,KAAMqB,EAAAA,EAAAA,IAAoB,OAAQ,KAAM,2JAA4J,SAG9NO,EAAG,GACF,EAAG,CAAC,aAETA,EAAG,SAITA,EAAG,GACF,EAAG,CAAC,kBAETP,EAAAA,EAAAA,IAAoB,MAAOjJ,EAAa,EACtCkJ,EAAAA,EAAAA,IAAanB,EAAsB,CACjCqB,KAAM,UACND,QAAS9C,IACR,CACDgD,SAASC,EAAAA,EAAAA,KAAS,IAAM1B,EAAO,MAAQA,EAAO,IAAM,EAClD0C,EAAAA,EAAAA,IAAiB,YAEnBd,EAAG,KAELN,EAAAA,EAAAA,IAAanB,EAAsB,CACjCoB,QAAShE,GACT8E,MAAO,CAAC,cAAc,SACrB,CACDZ,SAASC,EAAAA,EAAAA,KAAS,IAAM1B,EAAO,MAAQA,EAAO,IAAM,EAClD0C,EAAAA,EAAAA,IAAiB,aAEnBd,EAAG,SAGJ,CACH,CAACZ,GAAoBvI,EAAQ4B,QAC7B,CAEJ,I,UExkCA,MAAMwJ,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://fuzz-web/./src/api/caseApi.ts", "webpack://fuzz-web/./src/views/testplan/CaseSetting.vue?b1e6", "webpack://fuzz-web/./src/views/testplan/CaseSetting.vue", "webpack://fuzz-web/./src/views/testplan/CaseSetting.vue?4fa3"], "sourcesContent": ["import axios, { AxiosResponse } from 'axios';\r\nimport { USE_MOCK, mockApi } from '@/mock/mockApi';\r\n\r\n// 修改 WhiteListFrame 接口，添加 transmitter、receivers 和 IsExt\r\nexport interface WhiteListFrame {\r\n  id: number;\r\n  name: string;\r\n  dlc: number;\r\n  isExt: boolean;\r\n  transmitter: string;\r\n  receivers: string[];\r\n}\r\n\r\n// 安全配置信息接口\r\nexport interface SecurityConfigInfo {\r\n  hasDll: boolean;\r\n  dllFileName?: string;\r\n  dllSize?: number;\r\n}\r\n\r\nexport interface CaseConfigDto {\r\n  // 基本属性 - 移除 whiteListIds, dlc, limitValues\r\n  whiteListFrames: WhiteListFrame[]; // 新增：用于替代 whiteListIds 和 dlc\r\n  selectedNodeName?: string;        // 新增：保存选中的目标节点名称\r\n\r\n  // NM唤醒相关配置\r\n  enableNmWakeup: boolean;\r\n  nmWakeupId: number;\r\n  nmWakeupIsExt: boolean;\r\n  nmWakeupDlc: number;\r\n  nmWakeupData: number[];\r\n  nmWakeupCommunicationType: string;\r\n  nmWakeupCycleMs: number;\r\n  nmWakeupDelayMs: number;\r\n\r\n  requestId: number;\r\n  requestIsExt: boolean;\r\n  responseId: number;\r\n  timeoutMs: number;\r\n\r\n  isDutMtuLessThan4096?: boolean;   // DUT MTU小于4096的标识\r\n  enableDiagRetryRequest?: boolean; // 当诊断请求无响应时，是否发送其它诊断请求\r\n  diagRetryRequestPayload?: number[]; // 备用诊断请求数据\r\n\r\n  enableLogFilter?: boolean;   // 记录日志时，是否使用 Log 过滤器\r\n\r\n  // 安全配置相关\r\n  securityInfo?: SecurityConfigInfo;  // 显示用\r\n  securityDllPath?: string;           // 新选择的DLL路径\r\n  removeSecurityDll?: boolean;        // 是否移除现有DLL\r\n}\r\n\r\n// 向后兼容的类型别名，保持接口一致性\r\nexport type CaseConfig = CaseConfigDto;\r\n\r\nexport interface CaseConfigFromDbc {\r\n  whiteListFrames: WhiteListFrame[]; // 帧列表\r\n  nodeNames: string[];               // 新增：节点名称列表\r\n}\r\n\r\nexport interface SecurityDllPathResponse {\r\n  path: string;\r\n}\r\n\r\nconst BASE_URL = '/api/caseconfig';\r\n\r\nexport const caseApi = {\r\n  getCaseConfig(): Promise<AxiosResponse<CaseConfigDto>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.case.getCaseConfig();\r\n    }\r\n    return axios.get(`${BASE_URL}`);\r\n  },\r\n\r\n  updateCaseConfig(config: CaseConfigDto): Promise<AxiosResponse<CaseConfigDto>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.case.updateCaseConfig(config);\r\n    }\r\n    return axios.post(`${BASE_URL}/update`, config);\r\n  },\r\n\r\n  importDbc(): Promise<AxiosResponse<CaseConfigFromDbc>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.case.importDbc();\r\n    }\r\n    return axios.get(`${BASE_URL}/import-dbc`);\r\n  },\r\n\r\n  // 新增：选择安全DLL文件\r\n  selectSecurityDll(): Promise<AxiosResponse<SecurityDllPathResponse>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.case.selectSecurityDll();\r\n    }\r\n    return axios.post(`${BASE_URL}/select-security-dll`);\r\n  }\r\n};\r\n\r\nexport default caseApi;\r\n", "import { defineComponent as _defineComponent } from 'vue'\nimport { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, withCtx as _withCtx, unref as _unref, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElement<PERSON><PERSON> as _createElementBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\"\n\nconst _hoisted_1 = { class: \"case-setting-container\" }\nconst _hoisted_2 = { class: \"toolbar top-toolbar\" }\nconst _hoisted_3 = { class: \"toolbar-left\" }\nconst _hoisted_4 = { class: \"content-area\" }\nconst _hoisted_5 = { class: \"card-container\" }\nconst _hoisted_6 = { class: \"form-row\" }\nconst _hoisted_7 = { class: \"form-row\" }\nconst _hoisted_8 = { class: \"comment-row\" }\nconst _hoisted_9 = { class: \"card-container\" }\nconst _hoisted_10 = {\n  key: 0,\n  class: \"import-note\"\n}\nconst _hoisted_11 = {\n  key: 1,\n  class: \"node-selection-required\"\n}\nconst _hoisted_12 = { class: \"card-container\" }\nconst _hoisted_13 = { class: \"nm-wakeup-row\" }\nconst _hoisted_14 = { class: \"card-container\" }\nconst _hoisted_15 = { class: \"form-row\" }\nconst _hoisted_16 = { class: \"comment-row\" }\nconst _hoisted_17 = { class: \"security-input-group\" }\nconst _hoisted_18 = { class: \"security-buttons\" }\nconst _hoisted_19 = {\n  key: 0,\n  class: \"selected-dll-info\"\n}\nconst _hoisted_20 = {\n  key: 1,\n  class: \"selected-dll-info\"\n}\nconst _hoisted_21 = { class: \"card-container\" }\nconst _hoisted_22 = { class: \"form-row\" }\nconst _hoisted_23 = { class: \"comment-row\" }\nconst _hoisted_24 = { class: \"toolbar bottom-toolbar\" }\n\nimport { ref, onMounted, computed } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { Delete, InfoFilled, Plus, } from '@element-plus/icons-vue';\r\nimport { caseApi, CaseConfigDto, WhiteListFrame } from '@/api/caseApi';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'CaseSetting',\n  setup(__props) {\n\r\nconst loading = ref(false);\r\nconst config = ref<CaseConfigDto>({\r\n  whiteListFrames: [],\r\n  enableNmWakeup: true,\r\n  nmWakeupId: 0x53F,\r\n  nmWakeupIsExt: false,\r\n  nmWakeupDlc: 8,\r\n  nmWakeupData: [0x3F, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF],\r\n  nmWakeupCommunicationType: 'Can',\r\n  nmWakeupCycleMs: 100,\r\n  nmWakeupDelayMs: 2000,\r\n  requestId: 0x731,\r\n  requestIsExt: false,\r\n  responseId: 0x631,\r\n  timeoutMs: 500,\r\n  isDutMtuLessThan4096: false,\r\n  enableDiagRetryRequest: false,\r\n  diagRetryRequestPayload: [0x10, 0x01],\r\n  securityInfo: {\r\n    hasDll: false,\r\n    dllFileName: undefined,\r\n    dllSize: 0\r\n  }\r\n});\r\n\r\n// 新增变量\r\nconst nodeNames = ref<string[]>([]);\r\nconst selectedNode = ref<string>('');\r\nconst allFrames = ref<WhiteListFrame[]>([]);\r\n\r\n// 根据选定节点过滤帧\r\nconst filteredFrames = computed(() => {\r\n  if (!selectedNode.value) return [];\r\n\r\n  const txFrames = allFrames.value.filter(frame =>\r\n    frame.transmitter === selectedNode.value\r\n  );\r\n\r\n  const rxFrames = allFrames.value.filter(frame =>\r\n    frame.transmitter !== selectedNode.value &&\r\n    frame.receivers.includes(selectedNode.value)\r\n  );\r\n\r\n  return [...txFrames, ...rxFrames];\r\n});\r\n\r\nconst activeNames = ref(['case', 'database', 'nmWakeup', 'uds', 'log']); // 默认展开所有分组\r\n\r\nconst nmWakeupIdInput = ref('');\r\nconst nmWakeupDlcInput = ref('');\r\nconst nmWakeupDataInput = ref('');\r\nconst requestIdInput = ref('');\r\nconst responseIdInput = ref('');\r\nconst diagRetryRequestPayloadInput = ref('');\r\n\r\n// 用于显示和选择 Frame Type\r\nconst nmWakeupFrameType = computed({\r\n  get: () => {\r\n    return config.value.nmWakeupCommunicationType === 'Can' ? 'CAN Frame' : 'CANFD Frame';\r\n  },\r\n  set: (value: string) => {\r\n    config.value.nmWakeupCommunicationType = value === 'CAN Frame' ? 'Can' : 'Canfd';\r\n  }\r\n});\r\n\r\nconst selectedDllPath = ref<string | null>(null);\r\nconst selectedDllName = ref<string | null>(null);\r\nconst shouldRemoveDll = ref(false);\r\n\r\nconst defaultConfig: CaseConfigDto = {\r\n  whiteListFrames: [], // 修改为空数组\r\n  enableNmWakeup: true,\r\n  nmWakeupId: 0x53F,\r\n  nmWakeupIsExt: false,\r\n  nmWakeupDlc: 8,\r\n  nmWakeupData: [0x3F, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF],\r\n  nmWakeupCommunicationType: 'Can',\r\n  nmWakeupCycleMs: 100,\r\n  nmWakeupDelayMs: 2000,\r\n  requestId: 0x731,\r\n  requestIsExt: false,\r\n  responseId: 0x631,\r\n  timeoutMs: 500,\r\n  isDutMtuLessThan4096: false,\r\n  enableDiagRetryRequest: false,\r\n  diagRetryRequestPayload: [0x10, 0x01],\r\n  securityInfo: {\r\n    hasDll: false,\r\n    dllFileName: undefined,\r\n    dllSize: 0\r\n  }\r\n};\r\n\r\nconst arrayToHexString = (arr?: number[]) => {\r\n  if (!arr) {\r\n    return '';\r\n  }\r\n  return arr.map(n => '0x' + n.toString(16).toUpperCase()).join(',');\r\n};\r\n\r\nconst handleNmWakeupIdChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const id = parseInt(cleanValue, 16);\r\n    if (isNaN(id)) {\r\n      ElMessage.error('Invalid ID format');\r\n      return;\r\n    }\r\n    config.value.nmWakeupId = id;\r\n    // 确保显示带有 0x 前缀\r\n    nmWakeupIdInput.value = '0x' + id.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleNmWakeupDlcChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const dlc = parseInt(cleanValue, 16);\r\n    if (isNaN(dlc)) {\r\n      ElMessage.error('Invalid DLC format');\r\n      return;\r\n    }\r\n    if (dlc < 0 || dlc > 64) {\r\n      ElMessage.warning('DLC should be between 0 and 64');\r\n      return;\r\n    }\r\n    config.value.nmWakeupDlc = dlc;\r\n    // 确保显示带有 0x 前缀\r\n    nmWakeupDlcInput.value = '0x' + dlc.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleNmWakeupDataChange = (value: string) => {\r\n  try {\r\n    const bytes = value.split(',')\r\n      .map(s => s.trim())\r\n      .filter(s => s)\r\n      .map(s => parseInt(s, 16));\r\n\r\n    if (bytes.length > 8) {\r\n      ElMessage.warning('Maximum 8 bytes allowed');\r\n      return;\r\n    }\r\n    if (bytes.some(b => isNaN(b) || b < 0 || b > 0xFF)) {\r\n      ElMessage.warning('Each byte must be between 0x00 and 0xFF');\r\n      return;\r\n    }\r\n    config.value.nmWakeupData = bytes;\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input format');\r\n  }\r\n};\r\n\r\nconst handleRequestIdChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const id = parseInt(cleanValue, 16);\r\n    if (isNaN(id)) {\r\n      ElMessage.error('Invalid ID format');\r\n      return;\r\n    }\r\n    config.value.requestId = id;\r\n    // 确保显示带有 0x 前缀\r\n    requestIdInput.value = '0x' + id.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleResponseIdChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const id = parseInt(cleanValue, 16);\r\n    if (isNaN(id)) {\r\n      ElMessage.error('Invalid ID format');\r\n      return;\r\n    }\r\n    config.value.responseId = id;\r\n    // 确保显示带有 0x 前缀\r\n    responseIdInput.value = '0x' + id.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleDiagRetryRequestPayloadChange = (value: string) => {\r\n  try {\r\n    const bytes = value.split(',')\r\n      .map(s => s.trim())\r\n      .filter(s => s)\r\n      .map(s => parseInt(s, 16));\r\n\r\n    if (bytes.some(b => isNaN(b) || b < 0 || b > 0xFF)) {\r\n      ElMessage.warning('Each byte must be between 0x00 and 0xFF');\r\n      return;\r\n    }\r\n    config.value.diagRetryRequestPayload = bytes;\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input format');\r\n  }\r\n};\r\n\r\nconst resetToDefault = () => {\r\n  config.value = JSON.parse(JSON.stringify(defaultConfig));\r\n  nmWakeupIdInput.value = '0x' + defaultConfig.nmWakeupId.toString(16).toUpperCase();\r\n  nmWakeupDlcInput.value = '0x' + defaultConfig.nmWakeupDlc.toString(16).toUpperCase();\r\n  nmWakeupDataInput.value = arrayToHexString(defaultConfig.nmWakeupData);\r\n  requestIdInput.value = '0x' + defaultConfig.requestId.toString(16).toUpperCase();\r\n  responseIdInput.value = '0x' + defaultConfig.responseId.toString(16).toUpperCase();\r\n  diagRetryRequestPayloadInput.value = arrayToHexString(defaultConfig.diagRetryRequestPayload);\r\n  selectedDllPath.value = null;\r\n  selectedDllName.value = null;\r\n  shouldRemoveDll.value = false;\r\n\r\n  // 重置节点和帧相关状态\r\n  nodeNames.value = [];\r\n  selectedNode.value = '';\r\n  allFrames.value = [];\r\n};\r\n\r\nconst loadConfig = async () => {\r\n  loading.value = true;\r\n  try {\r\n    const response = await caseApi.getCaseConfig();\r\n    config.value = response.data;\r\n    nmWakeupIdInput.value = '0x' + response.data.nmWakeupId.toString(16).toUpperCase();\r\n    nmWakeupDataInput.value = arrayToHexString(response.data.nmWakeupData);\r\n    requestIdInput.value = '0x' + response.data.requestId.toString(16).toUpperCase();\r\n    responseIdInput.value = '0x' + response.data.responseId.toString(16).toUpperCase();\r\n\r\n    // 设置备用诊断请求数据\r\n    if (response.data.diagRetryRequestPayload) {\r\n      diagRetryRequestPayloadInput.value = arrayToHexString(response.data.diagRetryRequestPayload);\r\n    } else {\r\n      diagRetryRequestPayloadInput.value = '0x10,0x01';\r\n    }\r\n\r\n    // 确保新增字段有默认值\r\n    if (response.data.nmWakeupIsExt === undefined) {\r\n      config.value.nmWakeupIsExt = false;\r\n    }\r\n    if (response.data.nmWakeupDlc === undefined) {\r\n      config.value.nmWakeupDlc = 8;\r\n    }\r\n    if (response.data.requestIsExt === undefined) {\r\n      config.value.requestIsExt = false;\r\n    }\r\n    if (response.data.enableDiagRetryRequest === undefined) {\r\n      config.value.enableDiagRetryRequest = false;\r\n    }\r\n    if (response.data.diagRetryRequestPayload === undefined) {\r\n      config.value.diagRetryRequestPayload = [0x10, 0x01];\r\n    }\r\n\r\n    // 设置 DLC 输入框的值\r\n    nmWakeupDlcInput.value = '0x' + config.value.nmWakeupDlc.toString(16).toUpperCase();\r\n\r\n    // 如果已存在帧，尝试提取节点列表\r\n    if (response.data.whiteListFrames.length > 0) {\r\n      const frames = response.data.whiteListFrames;\r\n      allFrames.value = frames;\r\n\r\n      // 提取所有唯一的节点名\r\n      const nodes = new Set<string>();\r\n      frames.forEach(frame => {\r\n        if (frame.transmitter) nodes.add(frame.transmitter);\r\n        frame.receivers?.forEach(r => nodes.add(r));\r\n      });\r\n\r\n      nodeNames.value = Array.from(nodes);\r\n\r\n      // 如果保存了选中的节点，恢复选中状态\r\n      if (response.data.selectedNodeName && nodeNames.value.includes(response.data.selectedNodeName)) {\r\n        selectedNode.value = response.data.selectedNodeName;\r\n      }\r\n      // 否则，如果只有一个节点，自动选择\r\n      else if (nodeNames.value.length === 1) {\r\n        selectedNode.value = nodeNames.value[0];\r\n      }\r\n    }\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load configuration');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 处理节点改变\r\nconst handleNodeChange = (value: string) => {\r\n  // 更新过滤后的帧列表 (通过 computed 自动实现)\r\n  if (!value) {\r\n    ElMessage.warning('Please select a target ECU');\r\n  }\r\n};\r\n\r\nconst handleSave = async () => {\r\n  loading.value = true;\r\n  try {\r\n    // 准备提交数据\r\n    const submitData = { ...config.value };\r\n\r\n    // 修改: 保存所有帧数据，而不只是筛选后的数据\r\n    submitData.whiteListFrames = allFrames.value;\r\n\r\n    // 保存选中的节点名称\r\n    submitData.selectedNodeName = selectedNode.value;\r\n\r\n    if (selectedDllPath.value) {\r\n      submitData.securityDllPath = selectedDllPath.value;\r\n    }\r\n\r\n    if (shouldRemoveDll.value) {\r\n      submitData.removeSecurityDll = true;\r\n    }\r\n\r\n    const result = await caseApi.updateCaseConfig(submitData);\r\n    config.value = result.data;\r\n\r\n    selectedDllPath.value = null;\r\n    selectedDllName.value = null;\r\n    shouldRemoveDll.value = false;\r\n\r\n    ElMessage.success('Case settings saved.');\r\n  } catch (error) {\r\n    ElMessage.error('Save failed');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst importLoading = ref(false);\r\n\r\nconst handleImportDbc = async () => {\r\n  importLoading.value = true;\r\n  try {\r\n    const response = await caseApi.importDbc();\r\n\r\n    // 保存所有帧和节点信息\r\n    allFrames.value = response.data.whiteListFrames;\r\n    nodeNames.value = response.data.nodeNames;\r\n\r\n    // 重置选择的节点\r\n    selectedNode.value = '';\r\n\r\n    // 清空当前已保存的帧列表，等待用户选择节点\r\n    config.value.whiteListFrames = [];\r\n\r\n    ElMessage.success('DBC file imported successfully. Please select a target ECU.');\r\n  } catch (error: any) {\r\n    if (error.response?.data === 'UserCanceled') {\r\n      return;\r\n    }\r\n    ElMessage.error(error.response?.data === 'InvalidFileFormat'\r\n      ? 'Invalid DBC file format'\r\n      : 'Failed to import DBC file');\r\n  } finally {\r\n    importLoading.value = false;\r\n  }\r\n};\r\n\r\nconst formatFileSize = (bytes?: number): string => {\r\n  if (!bytes) return '0 B';\r\n\r\n  const units = ['B', 'KB', 'MB', 'GB'];\r\n  let size = bytes;\r\n  let unitIndex = 0;\r\n\r\n  while (size >= 1024 && unitIndex < units.length - 1) {\r\n    size /= 1024;\r\n    unitIndex++;\r\n  }\r\n\r\n  return `${size.toFixed(2)} ${units[unitIndex]}`;\r\n};\r\n\r\nconst selectDll = async () => {\r\n  try {\r\n    const response = await caseApi.selectSecurityDll();\r\n    const dllPath = response.data.path;\r\n    selectedDllPath.value = dllPath;\r\n    selectedDllName.value = dllPath.split('\\\\').pop() || dllPath;\r\n  } catch (error: any) {\r\n    if (error.response?.data === 'UserCanceled') {\r\n      return;\r\n    }\r\n    ElMessage.error(error.response?.data === 'InvalidFileFormat'\r\n      ? 'Invalid DLL file format'\r\n      : 'Failed to select DLL file');\r\n  }\r\n};\r\n\r\nconst removeDll = () => {\r\n  shouldRemoveDll.value = true;\r\n};\r\n\r\nconst securityDllDisplay = computed(() => {\r\n  if (selectedDllName.value) {\r\n    return selectedDllName.value;\r\n  }\r\n\r\n  if (shouldRemoveDll.value) {\r\n    return 'DLL will be removed after save';\r\n  }\r\n\r\n  return config.value.securityInfo?.hasDll\r\n    ? `${config.value.securityInfo.dllFileName} (${formatFileSize(config.value.securityInfo.dllSize)})`\r\n    : '';\r\n});\r\n\r\n\r\n\r\n// 展开全部面板\r\nconst expandAll = () => {\r\n  activeNames.value = ['case', 'database', 'nmWakeup', 'uds', 'log'];\r\n};\r\n\r\n// 收起全部面板\r\nconst collapseAll = () => {\r\n  activeNames.value = [];\r\n};\r\n\r\nonMounted(() => {\r\n  loadConfig();\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_form_item = _resolveComponent(\"el-form-item\")!\n  const _component_el_switch = _resolveComponent(\"el-switch\")!\n  const _component_el_input_number = _resolveComponent(\"el-input-number\")!\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_form = _resolveComponent(\"el-form\")!\n  const _component_el_collapse_item = _resolveComponent(\"el-collapse-item\")!\n  const _component_el_option = _resolveComponent(\"el-option\")!\n  const _component_el_select = _resolveComponent(\"el-select\")!\n  const _component_el_table_column = _resolveComponent(\"el-table-column\")!\n  const _component_el_table = _resolveComponent(\"el-table\")!\n  const _component_el_collapse = _resolveComponent(\"el-collapse\")!\n  const _directive_loading = _resolveDirective(\"loading\")!\n\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createElementVNode(\"div\", _hoisted_3, [\n        _createVNode(_component_el_button, {\n          onClick: expandAll,\n          type: \"primary\",\n          size: \"small\",\n          class: \"expand-button\"\n        }, {\n          default: _withCtx(() => [\n            _createVNode(_component_font_awesome_icon, { icon: \"up-right-and-down-left-from-center\" }),\n            _cache[19] || (_cache[19] = _createElementVNode(\"span\", { class: \"button-text\" }, \"Expand All\", -1))\n          ]),\n          _: 1\n        }),\n        _createVNode(_component_el_button, {\n          onClick: collapseAll,\n          type: \"primary\",\n          size: \"small\",\n          class: \"collapse-button\"\n        }, {\n          default: _withCtx(() => [\n            _createVNode(_component_font_awesome_icon, { icon: \"down-left-and-up-right-to-center\" }),\n            _cache[20] || (_cache[20] = _createElementVNode(\"span\", { class: \"button-text\" }, \"Collapse All\", -1))\n          ]),\n          _: 1\n        })\n      ])\n    ]),\n    _createElementVNode(\"div\", _hoisted_4, [\n      _createVNode(_component_el_collapse, {\n        modelValue: activeNames.value,\n        \"onUpdate:modelValue\": _cache[18] || (_cache[18] = ($event: any) => ((activeNames).value = $event))\n      }, {\n        default: _withCtx(() => [\n          _createElementVNode(\"div\", _hoisted_5, [\n            _createVNode(_component_el_collapse_item, {\n              title: \"Case Setting\",\n              name: \"case\",\n              class: \"custom-card\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_form, {\n                  model: config.value,\n                  \"label-width\": \"160px\",\n                  \"label-position\": \"top\"\n                }, {\n                  default: _withCtx(() => [\n                    _createElementVNode(\"div\", _hoisted_6, [\n                      _createVNode(_component_el_form_item, {\n                        label: \"Request ID\",\n                        class: \"form-item\"\n                      }, {\n                        default: _withCtx(() => [\n                          _createVNode(_component_el_input, {\n                            modelValue: requestIdInput.value,\n                            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((requestIdInput).value = $event)),\n                            placeholder: \"Enter ID in hex, e.g. 0x731\",\n                            onChange: handleRequestIdChange\n                          }, null, 8, [\"modelValue\"])\n                        ]),\n                        _: 1\n                      }),\n                      _createVNode(_component_el_form_item, {\n                        label: \"Ext Flag\",\n                        class: \"form-item\",\n                        style: {\"width\":\"80px\"}\n                      }, {\n                        default: _withCtx(() => [\n                          _createVNode(_component_el_switch, {\n                            modelValue: config.value.requestIsExt,\n                            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((config.value.requestIsExt) = $event))\n                          }, null, 8, [\"modelValue\"])\n                        ]),\n                        _: 1\n                      })\n                    ]),\n                    _createElementVNode(\"div\", _hoisted_7, [\n                      _createVNode(_component_el_form_item, {\n                        label: \"Response ID\",\n                        class: \"form-item\"\n                      }, {\n                        default: _withCtx(() => [\n                          _createVNode(_component_el_input, {\n                            modelValue: responseIdInput.value,\n                            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((responseIdInput).value = $event)),\n                            placeholder: \"Enter ID in hex, e.g. 0x631\",\n                            onChange: handleResponseIdChange\n                          }, null, 8, [\"modelValue\"])\n                        ]),\n                        _: 1\n                      }),\n                      _createVNode(_component_el_form_item, {\n                        label: \"Timeout (ms)\",\n                        class: \"form-item\"\n                      }, {\n                        default: _withCtx(() => [\n                          _createVNode(_component_el_input_number, {\n                            modelValue: config.value.timeoutMs,\n                            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = ($event: any) => ((config.value.timeoutMs) = $event)),\n                            min: 1,\n                            max: 60000,\n                            style: {\"width\":\"200px\"}\n                          }, null, 8, [\"modelValue\"])\n                        ]),\n                        _: 1\n                      })\n                    ]),\n                    _createElementVNode(\"div\", _hoisted_8, [\n                      _createVNode(_component_el_icon, null, {\n                        default: _withCtx(() => [\n                          _createVNode(_unref(InfoFilled))\n                        ]),\n                        _: 1\n                      }),\n                      _cache[21] || (_cache[21] = _createTextVNode()),\n                      _cache[22] || (_cache[22] = _createElementVNode(\"span\", null, \"ISO-11898 and ISO-14229 protocols share the same Response ID, and Timeout setting.\", -1))\n                    ])\n                  ]),\n                  _: 1\n                }, 8, [\"model\"])\n              ]),\n              _: 1\n            })\n          ]),\n          _createElementVNode(\"div\", _hoisted_9, [\n            _createVNode(_component_el_collapse_item, {\n              title: \"Database Setting\",\n              name: \"database\",\n              class: \"custom-card\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_form, {\n                  model: config.value,\n                  \"label-width\": \"160px\",\n                  \"label-position\": \"top\"\n                }, {\n                  default: _withCtx(() => [\n                    _createVNode(_component_el_form_item, null, {\n                      default: _withCtx(() => [\n                        _createVNode(_component_el_button, {\n                          onClick: handleImportDbc,\n                          loading: importLoading.value,\n                          type: \"primary\",\n                          size: \"small\",\n                          style: {\"margin-bottom\":\"15px\"}\n                        }, {\n                          default: _withCtx(() => _cache[23] || (_cache[23] = [\n                            _createTextVNode(\" Import DBC \")\n                          ])),\n                          _: 1\n                        }, 8, [\"loading\"])\n                      ]),\n                      _: 1\n                    }),\n                    (nodeNames.value.length > 0)\n                      ? (_openBlock(), _createBlock(_component_el_form_item, {\n                          key: 0,\n                          label: \"Target ECU (DUT)\"\n                        }, {\n                          default: _withCtx(() => [\n                            _createVNode(_component_el_select, {\n                              modelValue: selectedNode.value,\n                              \"onUpdate:modelValue\": _cache[4] || (_cache[4] = ($event: any) => ((selectedNode).value = $event)),\n                              placeholder: \"Select target ECU\",\n                              style: {\"width\":\"100%\"},\n                              onChange: handleNodeChange\n                            }, {\n                              default: _withCtx(() => [\n                                (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(nodeNames.value, (node) => {\n                                  return (_openBlock(), _createBlock(_component_el_option, {\n                                    key: node,\n                                    label: node,\n                                    value: node\n                                  }, null, 8, [\"label\", \"value\"]))\n                                }), 128))\n                              ]),\n                              _: 1\n                            }, 8, [\"modelValue\"])\n                          ]),\n                          _: 1\n                        }))\n                      : _createCommentVNode(\"\", true),\n                    _createVNode(_component_el_form_item, {\n                      label: `White List Frames (${filteredFrames.value.length})`\n                    }, {\n                      default: _withCtx(() => [\n                        (allFrames.value.length === 0)\n                          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, \" No frames imported. Please use \\\"Import DBC\\\" button above to add frames. \"))\n                          : (!selectedNode.value)\n                            ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, \" Please select a target ECU to view related frames \"))\n                            : (_openBlock(), _createBlock(_component_el_table, {\n                                key: 2,\n                                data: filteredFrames.value,\n                                style: {\"width\":\"100%\"},\n                                \"max-height\": 400,\n                                border: \"\"\n                              }, {\n                                default: _withCtx(() => [\n                                  _createVNode(_component_el_table_column, {\n                                    label: \"ID\",\n                                    \"min-width\": \"120\"\n                                  }, {\n                                    default: _withCtx(({ row }) => [\n                                      _createTextVNode(\" 0x\" + _toDisplayString(row.id.toString(16).toUpperCase().padStart(3, '0')), 1)\n                                    ]),\n                                    _: 1\n                                  }),\n                                  _createVNode(_component_el_table_column, {\n                                    prop: \"name\",\n                                    label: \"Name\",\n                                    \"min-width\": \"180\"\n                                  }),\n                                  _createVNode(_component_el_table_column, {\n                                    label: \"DLC\",\n                                    \"min-width\": \"80\"\n                                  }, {\n                                    default: _withCtx(({ row }) => [\n                                      _createTextVNode(\" 0x\" + _toDisplayString(row.dlc.toString(16).toUpperCase()), 1)\n                                    ]),\n                                    _: 1\n                                  }),\n                                  _createVNode(_component_el_table_column, {\n                                    label: \"Ext Flag\",\n                                    \"min-width\": \"120\"\n                                  }, {\n                                    default: _withCtx(({ row }) => [\n                                      _createTextVNode(_toDisplayString(row.isExt ? 'CAN Extended' : 'CAN Standard'), 1)\n                                    ]),\n                                    _: 1\n                                  }),\n                                  _createVNode(_component_el_table_column, {\n                                    prop: \"transmitter\",\n                                    label: \"Transmitter\",\n                                    \"min-width\": \"120\"\n                                  }),\n                                  _createVNode(_component_el_table_column, {\n                                    label: \"Receivers\",\n                                    \"min-width\": \"180\"\n                                  }, {\n                                    default: _withCtx(({ row }) => [\n                                      _createTextVNode(_toDisplayString(row.receivers.join(', ')), 1)\n                                    ]),\n                                    _: 1\n                                  })\n                                ]),\n                                _: 1\n                              }, 8, [\"data\"]))\n                      ]),\n                      _: 1\n                    }, 8, [\"label\"])\n                  ]),\n                  _: 1\n                }, 8, [\"model\"])\n              ]),\n              _: 1\n            })\n          ]),\n          _createElementVNode(\"div\", _hoisted_12, [\n            _createVNode(_component_el_collapse_item, {\n              title: \"NM Wake Up Setting\",\n              name: \"nmWakeup\",\n              class: \"custom-card\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_form, {\n                  model: config.value,\n                  \"label-width\": \"160px\",\n                  \"label-position\": \"top\"\n                }, {\n                  default: _withCtx(() => [\n                    _createVNode(_component_el_form_item, { label: \"Enable NM Wake Up\" }, {\n                      default: _withCtx(() => [\n                        _createVNode(_component_el_switch, {\n                          modelValue: config.value.enableNmWakeup,\n                          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = ($event: any) => ((config.value.enableNmWakeup) = $event))\n                        }, null, 8, [\"modelValue\"])\n                      ]),\n                      _: 1\n                    }),\n                    _createElementVNode(\"div\", {\n                      class: _normalizeClass({ 'disabled-form-content': !config.value.enableNmWakeup })\n                    }, [\n                      _createVNode(_component_el_table, {\n                        data: [{}],\n                        border: \"\",\n                        style: {\"width\":\"100%\",\"margin-bottom\":\"15px\"},\n                        \"show-header\": true,\n                        class: \"nm-table\"\n                      }, {\n                        default: _withCtx(() => [\n                          _createVNode(_component_el_table_column, {\n                            label: \"ID\",\n                            width: \"220\"\n                          }, {\n                            default: _withCtx(() => [\n                              _createVNode(_component_el_input, {\n                                modelValue: nmWakeupIdInput.value,\n                                \"onUpdate:modelValue\": _cache[6] || (_cache[6] = ($event: any) => ((nmWakeupIdInput).value = $event)),\n                                placeholder: \"Enter ID in hex\",\n                                onChange: handleNmWakeupIdChange,\n                                disabled: !config.value.enableNmWakeup\n                              }, null, 8, [\"modelValue\", \"disabled\"])\n                            ]),\n                            _: 1\n                          }),\n                          _createVNode(_component_el_table_column, {\n                            label: \"Ext Flag\",\n                            width: \"100\"\n                          }, {\n                            default: _withCtx(() => [\n                              _createVNode(_component_el_switch, {\n                                modelValue: config.value.nmWakeupIsExt,\n                                \"onUpdate:modelValue\": _cache[7] || (_cache[7] = ($event: any) => ((config.value.nmWakeupIsExt) = $event)),\n                                disabled: !config.value.enableNmWakeup\n                              }, null, 8, [\"modelValue\", \"disabled\"])\n                            ]),\n                            _: 1\n                          }),\n                          _createVNode(_component_el_table_column, {\n                            label: \"Frame Type\",\n                            width: \"200\"\n                          }, {\n                            default: _withCtx(() => [\n                              _createVNode(_component_el_select, {\n                                modelValue: nmWakeupFrameType.value,\n                                \"onUpdate:modelValue\": _cache[8] || (_cache[8] = ($event: any) => ((nmWakeupFrameType).value = $event)),\n                                disabled: !config.value.enableNmWakeup\n                              }, {\n                                default: _withCtx(() => [\n                                  _createVNode(_component_el_option, {\n                                    value: \"CAN Frame\",\n                                    label: \"CAN Frame\"\n                                  }),\n                                  _createVNode(_component_el_option, {\n                                    value: \"CANFD Frame\",\n                                    label: \"CANFD Frame\"\n                                  })\n                                ]),\n                                _: 1\n                              }, 8, [\"modelValue\", \"disabled\"])\n                            ]),\n                            _: 1\n                          }),\n                          _createVNode(_component_el_table_column, {\n                            label: \"DLC\",\n                            width: \"150\"\n                          }, {\n                            default: _withCtx(() => [\n                              _createVNode(_component_el_input, {\n                                modelValue: nmWakeupDlcInput.value,\n                                \"onUpdate:modelValue\": _cache[9] || (_cache[9] = ($event: any) => ((nmWakeupDlcInput).value = $event)),\n                                placeholder: \"Enter DLC\",\n                                onChange: handleNmWakeupDlcChange,\n                                disabled: !config.value.enableNmWakeup\n                              }, null, 8, [\"modelValue\", \"disabled\"])\n                            ]),\n                            _: 1\n                          }),\n                          _createVNode(_component_el_table_column, { label: \"Data\" }, {\n                            default: _withCtx(() => [\n                              _createVNode(_component_el_input, {\n                                modelValue: nmWakeupDataInput.value,\n                                \"onUpdate:modelValue\": _cache[10] || (_cache[10] = ($event: any) => ((nmWakeupDataInput).value = $event)),\n                                placeholder: \"Enter data bytes, e.g. 0x3F,0x50,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF\",\n                                onChange: handleNmWakeupDataChange,\n                                disabled: !config.value.enableNmWakeup\n                              }, null, 8, [\"modelValue\", \"disabled\"])\n                            ]),\n                            _: 1\n                          })\n                        ]),\n                        _: 1\n                      }),\n                      _createElementVNode(\"div\", _hoisted_13, [\n                        _createVNode(_component_el_form_item, {\n                          label: \"Cycle (ms)\",\n                          class: \"nm-wakeup-item\"\n                        }, {\n                          default: _withCtx(() => [\n                            _createVNode(_component_el_input_number, {\n                              modelValue: config.value.nmWakeupCycleMs,\n                              \"onUpdate:modelValue\": _cache[11] || (_cache[11] = ($event: any) => ((config.value.nmWakeupCycleMs) = $event)),\n                              min: 1,\n                              max: 60000,\n                              style: {\"width\":\"200px\"},\n                              disabled: !config.value.enableNmWakeup\n                            }, null, 8, [\"modelValue\", \"disabled\"])\n                          ]),\n                          _: 1\n                        }),\n                        _createVNode(_component_el_form_item, {\n                          label: \"Wake Up Delay (ms)\",\n                          class: \"nm-wakeup-item\"\n                        }, {\n                          default: _withCtx(() => [\n                            _createVNode(_component_el_input_number, {\n                              modelValue: config.value.nmWakeupDelayMs,\n                              \"onUpdate:modelValue\": _cache[12] || (_cache[12] = ($event: any) => ((config.value.nmWakeupDelayMs) = $event)),\n                              min: 0,\n                              max: 60000,\n                              style: {\"width\":\"200px\"},\n                              disabled: !config.value.enableNmWakeup\n                            }, null, 8, [\"modelValue\", \"disabled\"])\n                          ]),\n                          _: 1\n                        })\n                      ])\n                    ], 2)\n                  ]),\n                  _: 1\n                }, 8, [\"model\"])\n              ]),\n              _: 1\n            })\n          ]),\n          _createElementVNode(\"div\", _hoisted_14, [\n            _createVNode(_component_el_collapse_item, {\n              title: \"UDS Setting\",\n              name: \"uds\",\n              class: \"custom-card\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_form, {\n                  model: config.value,\n                  \"label-width\": \"160px\",\n                  \"label-position\": \"top\"\n                }, {\n                  default: _withCtx(() => [\n                    _createElementVNode(\"div\", _hoisted_15, [\n                      _createVNode(_component_el_form_item, {\n                        label: \"DUT MTU < 4096 bytes\",\n                        class: \"form-item\"\n                      }, {\n                        default: _withCtx(() => [\n                          _createVNode(_component_el_switch, {\n                            modelValue: config.value.isDutMtuLessThan4096,\n                            \"onUpdate:modelValue\": _cache[13] || (_cache[13] = ($event: any) => ((config.value.isDutMtuLessThan4096) = $event))\n                          }, null, 8, [\"modelValue\"])\n                        ]),\n                        _: 1\n                      }),\n                      _createVNode(_component_el_form_item, {\n                        label: \"Enable Retry Request\",\n                        class: \"form-item\"\n                      }, {\n                        default: _withCtx(() => [\n                          _createVNode(_component_el_switch, {\n                            modelValue: config.value.enableDiagRetryRequest,\n                            \"onUpdate:modelValue\": _cache[14] || (_cache[14] = ($event: any) => ((config.value.enableDiagRetryRequest) = $event))\n                          }, null, 8, [\"modelValue\"])\n                        ]),\n                        _: 1\n                      }),\n                      _createVNode(_component_el_form_item, {\n                        label: \"Retry Request Payload\",\n                        class: \"form-item\",\n                        style: {\"width\":\"400px\"}\n                      }, {\n                        default: _withCtx(() => [\n                          _createVNode(_component_el_input, {\n                            modelValue: diagRetryRequestPayloadInput.value,\n                            \"onUpdate:modelValue\": _cache[15] || (_cache[15] = ($event: any) => ((diagRetryRequestPayloadInput).value = $event)),\n                            placeholder: \"Enter data bytes, e.g. 0x10,0x01\",\n                            onChange: handleDiagRetryRequestPayloadChange,\n                            disabled: !config.value.enableDiagRetryRequest\n                          }, null, 8, [\"modelValue\", \"disabled\"])\n                        ]),\n                        _: 1\n                      })\n                    ]),\n                    _createElementVNode(\"div\", _hoisted_16, [\n                      _createVNode(_component_el_icon, null, {\n                        default: _withCtx(() => [\n                          _createVNode(_unref(InfoFilled))\n                        ]),\n                        _: 1\n                      }),\n                      _cache[24] || (_cache[24] = _createTextVNode()),\n                      _cache[25] || (_cache[25] = _createElementVNode(\"span\", null, \"Retry Request will be triggered when UDS diagnostic requests receive no response, using the configured payload for retry attempts.\", -1))\n                    ]),\n                    _createElementVNode(\"div\", null, [\n                      _createVNode(_component_el_form_item, { label: \"Security Access Dll\" }, {\n                        default: _withCtx(() => [\n                          _createElementVNode(\"div\", _hoisted_17, [\n                            _createVNode(_component_el_input, {\n                              modelValue: securityDllDisplay.value,\n                              \"onUpdate:modelValue\": _cache[16] || (_cache[16] = ($event: any) => ((securityDllDisplay).value = $event)),\n                              placeholder: \"No Dll selected\",\n                              readonly: \"\",\n                              class: \"security-dll-input\"\n                            }, null, 8, [\"modelValue\"]),\n                            _createElementVNode(\"div\", _hoisted_18, [\n                              (!config.value.securityInfo?.hasDll)\n                                ? (_openBlock(), _createBlock(_component_el_button, {\n                                    key: 0,\n                                    onClick: selectDll,\n                                    type: \"primary\",\n                                    icon: _unref(Plus),\n                                    title: \"Select DLL\"\n                                  }, null, 8, [\"icon\"]))\n                                : _createCommentVNode(\"\", true),\n                              (config.value.securityInfo?.hasDll)\n                                ? (_openBlock(), _createBlock(_component_el_button, {\n                                    key: 1,\n                                    onClick: removeDll,\n                                    type: \"danger\",\n                                    icon: _unref(Delete),\n                                    title: \"Remove DLL\"\n                                  }, null, 8, [\"icon\"]))\n                                : _createCommentVNode(\"\", true)\n                            ])\n                          ]),\n                          (selectedDllName.value)\n                            ? (_openBlock(), _createElementBlock(\"div\", _hoisted_19, \" DLL selected. Click Save to apply. \"))\n                            : _createCommentVNode(\"\", true),\n                          (shouldRemoveDll.value)\n                            ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, \" DLL marked for removal. Click Save to apply. \"))\n                            : _createCommentVNode(\"\", true)\n                        ]),\n                        _: 1\n                      })\n                    ])\n                  ]),\n                  _: 1\n                }, 8, [\"model\"])\n              ]),\n              _: 1\n            })\n          ]),\n          _createElementVNode(\"div\", _hoisted_21, [\n            _createVNode(_component_el_collapse_item, {\n              title: \"Log Setting\",\n              name: \"log\",\n              class: \"custom-card\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_form, {\n                  model: config.value,\n                  \"label-width\": \"160px\",\n                  \"label-position\": \"top\"\n                }, {\n                  default: _withCtx(() => [\n                    _createElementVNode(\"div\", _hoisted_22, [\n                      _createVNode(_component_el_form_item, {\n                        label: \"Enable log filter\",\n                        class: \"form-item\"\n                      }, {\n                        default: _withCtx(() => [\n                          _createVNode(_component_el_switch, {\n                            modelValue: config.value.enableLogFilter,\n                            \"onUpdate:modelValue\": _cache[17] || (_cache[17] = ($event: any) => ((config.value.enableLogFilter) = $event))\n                          }, null, 8, [\"modelValue\"])\n                        ]),\n                        _: 1\n                      })\n                    ]),\n                    _createElementVNode(\"div\", _hoisted_23, [\n                      _createVNode(_component_el_icon, null, {\n                        default: _withCtx(() => [\n                          _createVNode(_unref(InfoFilled))\n                        ]),\n                        _: 1\n                      }),\n                      _cache[26] || (_cache[26] = _createTextVNode()),\n                      _cache[27] || (_cache[27] = _createElementVNode(\"span\", null, \"When enabled, BLF log files will only record transmitted frames, expected received frames, and error frames to reduce log size and improve performance.\", -1))\n                    ])\n                  ]),\n                  _: 1\n                }, 8, [\"model\"])\n              ]),\n              _: 1\n            })\n          ])\n        ]),\n        _: 1\n      }, 8, [\"modelValue\"])\n    ]),\n    _createElementVNode(\"div\", _hoisted_24, [\n      _createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: handleSave\n      }, {\n        default: _withCtx(() => _cache[28] || (_cache[28] = [\n          _createTextVNode(\"Save\")\n        ])),\n        _: 1\n      }),\n      _createVNode(_component_el_button, {\n        onClick: resetToDefault,\n        style: {\"margin-left\":\"10px\"}\n      }, {\n        default: _withCtx(() => _cache[29] || (_cache[29] = [\n          _createTextVNode(\"Reset\")\n        ])),\n        _: 1\n      })\n    ])\n  ])), [\n    [_directive_loading, loading.value]\n  ])\n}\n}\n\n})", "<template>\r\n  <div class=\"case-setting-container\" v-loading=\"loading\">\r\n    <!-- Top Toolbar -->\r\n    <div class=\"toolbar top-toolbar\">\r\n      <div class=\"toolbar-left\">\r\n        <!-- 展开/收起全部按钮 -->\r\n        <el-button @click=\"expandAll\" type=\"primary\" size=\"small\" class=\"expand-button\">\r\n          <font-awesome-icon icon=\"up-right-and-down-left-from-center\" /><span class=\"button-text\">Expand All</span>\r\n        </el-button>\r\n        <el-button @click=\"collapseAll\" type=\"primary\" size=\"small\" class=\"collapse-button\">\r\n          <font-awesome-icon icon=\"down-left-and-up-right-to-center\" /><span class=\"button-text\">Collapse All</span>\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Content Area -->\r\n    <div class=\"content-area\">\r\n      <el-collapse v-model=\"activeNames\">\r\n        <div class=\"card-container\">\r\n          <el-collapse-item title=\"Case Setting\" name=\"case\" class=\"custom-card\">\r\n            <el-form :model=\"config\" label-width=\"160px\" label-position=\"top\">\r\n              <!-- 第一行：请求ID、Is Ext Flag、响应ID -->\r\n              <div class=\"form-row\">\r\n                <el-form-item label=\"Request ID\" class=\"form-item\">\r\n                  <el-input v-model=\"requestIdInput\" placeholder=\"Enter ID in hex, e.g. 0x731\"\r\n                    @change=\"handleRequestIdChange\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"Ext Flag\" class=\"form-item\" style=\"width: 80px;\">\r\n                  <el-switch v-model=\"config.requestIsExt\" />\r\n                </el-form-item>\r\n              </div>\r\n              <div class=\"form-row\">\r\n                <el-form-item label=\"Response ID\" class=\"form-item\">\r\n                  <el-input v-model=\"responseIdInput\" placeholder=\"Enter ID in hex, e.g. 0x631\"\r\n                    @change=\"handleResponseIdChange\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"Timeout (ms)\" class=\"form-item\">\r\n                  <el-input-number v-model=\"config.timeoutMs\" :min=\"1\" :max=\"60000\" style=\"width: 200px\" />\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <div class=\"comment-row\">\r\n                <el-icon>\r\n                  <InfoFilled />\r\n                </el-icon> <span>ISO-11898 and ISO-14229 protocols share the same Response ID, and Timeout\r\n                  setting.</span>\r\n              </div>\r\n            </el-form>\r\n          </el-collapse-item>\r\n        </div>\r\n\r\n        <div class=\"card-container\">\r\n          <el-collapse-item title=\"Database Setting\" name=\"database\" class=\"custom-card\">\r\n            <el-form :model=\"config\" label-width=\"160px\" label-position=\"top\">\r\n              <!-- Import DBC 按钮 -->\r\n              <el-form-item>\r\n                <el-button @click=\"handleImportDbc\" :loading=\"importLoading\" type=\"primary\" size=\"small\"\r\n                  style=\"margin-bottom: 15px;\">\r\n                  Import DBC\r\n                </el-button>\r\n              </el-form-item>\r\n\r\n              <!-- 节点选择 -->\r\n              <el-form-item label=\"Target ECU (DUT)\" v-if=\"nodeNames.length > 0\">\r\n                <el-select v-model=\"selectedNode\" placeholder=\"Select target ECU\" style=\"width: 100%;\"\r\n                  @change=\"handleNodeChange\">\r\n                  <el-option v-for=\"node in nodeNames\" :key=\"node\" :label=\"node\" :value=\"node\" />\r\n                </el-select>\r\n              </el-form-item>\r\n\r\n              <el-form-item :label=\"`White List Frames (${filteredFrames.length})`\">\r\n                <div class=\"import-note\" v-if=\"allFrames.length === 0\">\r\n                  No frames imported. Please use \"Import DBC\" button above to add frames.\r\n                </div>\r\n                <div v-else-if=\"!selectedNode\" class=\"node-selection-required\">\r\n                  Please select a target ECU to view related frames\r\n                </div>\r\n                <el-table :data=\"filteredFrames\" style=\"width: 100%\" v-else :max-height=\"400\" border>\r\n                  <el-table-column label=\"ID\" min-width=\"120\">\r\n                    <template #default=\"{ row }\">\r\n                      0x{{ row.id.toString(16).toUpperCase().padStart(3, '0') }}\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column prop=\"name\" label=\"Name\" min-width=\"180\" />\r\n                  <el-table-column label=\"DLC\" min-width=\"80\">\r\n                    <template #default=\"{ row }\">\r\n                      0x{{ row.dlc.toString(16).toUpperCase() }}\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"Ext Flag\" min-width=\"120\">\r\n                    <template #default=\"{ row }\">\r\n                      {{ row.isExt ? 'CAN Extended' : 'CAN Standard' }}\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column prop=\"transmitter\" label=\"Transmitter\" min-width=\"120\" />\r\n                  <el-table-column label=\"Receivers\" min-width=\"180\">\r\n                    <template #default=\"{ row }\">\r\n                      {{ row.receivers.join(', ') }}\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n              </el-form-item>\r\n            </el-form>\r\n          </el-collapse-item>\r\n        </div>\r\n\r\n        <div class=\"card-container\">\r\n          <el-collapse-item title=\"NM Wake Up Setting\" name=\"nmWakeup\" class=\"custom-card\">\r\n            <el-form :model=\"config\" label-width=\"160px\" label-position=\"top\">\r\n              <!-- 第一行：Enable NM Wake Up -->\r\n              <el-form-item label=\"Enable NM Wake Up\">\r\n                <el-switch v-model=\"config.enableNmWakeup\" />\r\n              </el-form-item>\r\n\r\n              <div :class=\"{ 'disabled-form-content': !config.enableNmWakeup }\">\r\n                <!-- 表格布局：ID、IsExtFlag、Frame Type、DLC、Data -->\r\n                <el-table :data=\"[{}]\" border style=\"width: 100%; margin-bottom: 15px;\" :show-header=\"true\"\r\n                  class=\"nm-table\">\r\n                  <el-table-column label=\"ID\" width=\"220\">\r\n                    <template #default>\r\n                      <el-input v-model=\"nmWakeupIdInput\" placeholder=\"Enter ID in hex\" @change=\"handleNmWakeupIdChange\"\r\n                        :disabled=\"!config.enableNmWakeup\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"Ext Flag\" width=\"100\">\r\n                    <template #default>\r\n                      <el-switch v-model=\"config.nmWakeupIsExt\" :disabled=\"!config.enableNmWakeup\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"Frame Type\" width=\"200\">\r\n                    <template #default>\r\n                      <el-select v-model=\"nmWakeupFrameType\" :disabled=\"!config.enableNmWakeup\">\r\n                        <el-option value=\"CAN Frame\" label=\"CAN Frame\" />\r\n                        <el-option value=\"CANFD Frame\" label=\"CANFD Frame\" />\r\n                      </el-select>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"DLC\" width=\"150\">\r\n                    <template #default>\r\n                      <el-input v-model=\"nmWakeupDlcInput\" placeholder=\"Enter DLC\" @change=\"handleNmWakeupDlcChange\"\r\n                        :disabled=\"!config.enableNmWakeup\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"Data\">\r\n                    <template #default>\r\n                      <el-input v-model=\"nmWakeupDataInput\"\r\n                        placeholder=\"Enter data bytes, e.g. 0x3F,0x50,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF\"\r\n                        @change=\"handleNmWakeupDataChange\" :disabled=\"!config.enableNmWakeup\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n\r\n                <!-- 第三行：Cycle、Wake Up Delay -->\r\n                <div class=\"nm-wakeup-row\">\r\n                  <!-- Cycle -->\r\n                  <el-form-item label=\"Cycle (ms)\" class=\"nm-wakeup-item\">\r\n                    <el-input-number v-model=\"config.nmWakeupCycleMs\" :min=\"1\" :max=\"60000\" style=\"width: 200px\"\r\n                      :disabled=\"!config.enableNmWakeup\" />\r\n                  </el-form-item>\r\n\r\n                  <!-- Wake Up Delay -->\r\n                  <el-form-item label=\"Wake Up Delay (ms)\" class=\"nm-wakeup-item\">\r\n                    <el-input-number v-model=\"config.nmWakeupDelayMs\" :min=\"0\" :max=\"60000\" style=\"width: 200px\"\r\n                      :disabled=\"!config.enableNmWakeup\" />\r\n                  </el-form-item>\r\n                </div>\r\n              </div>\r\n            </el-form>\r\n          </el-collapse-item>\r\n        </div>\r\n\r\n        <div class=\"card-container\">\r\n          <el-collapse-item title=\"UDS Setting\" name=\"uds\" class=\"custom-card\">\r\n            <el-form :model=\"config\" label-width=\"160px\" label-position=\"top\">\r\n              <!-- 第一行：MTU 和重试设置 -->\r\n              <div class=\"form-row\">\r\n                <el-form-item label=\"DUT MTU < 4096 bytes\" class=\"form-item\">\r\n                  <el-switch v-model=\"config.isDutMtuLessThan4096\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"Enable Retry Request\" class=\"form-item\">\r\n                  <el-switch v-model=\"config.enableDiagRetryRequest\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"Retry Request Payload\" class=\"form-item\" style=\"width: 400px;\">\r\n                  <el-input v-model=\"diagRetryRequestPayloadInput\" placeholder=\"Enter data bytes, e.g. 0x10,0x01\"\r\n                    @change=\"handleDiagRetryRequestPayloadChange\" :disabled=\"!config.enableDiagRetryRequest\" />\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <div class=\"comment-row\">\r\n                <el-icon>\r\n                  <InfoFilled />\r\n                </el-icon> <span>Retry Request will be triggered when UDS diagnostic requests receive no response, using the configured payload for retry attempts.</span>\r\n              </div>\r\n\r\n              <div>\r\n                <el-form-item label=\"Security Access Dll\">\r\n                  <div class=\"security-input-group\">\r\n                    <el-input v-model=\"securityDllDisplay\" placeholder=\"No Dll selected\" readonly\r\n                      class=\"security-dll-input\"></el-input>\r\n                    <div class=\"security-buttons\">\r\n                      <el-button v-if=\"!config.securityInfo?.hasDll\" @click=\"selectDll\" type=\"primary\" :icon=\"Plus\"\r\n                        title=\"Select DLL\" />\r\n                      <el-button v-if=\"config.securityInfo?.hasDll\" @click=\"removeDll\" type=\"danger\" :icon=\"Delete\"\r\n                        title=\"Remove DLL\" />\r\n                    </div>\r\n                  </div>\r\n                  <div v-if=\"selectedDllName\" class=\"selected-dll-info\">\r\n                    DLL selected. Click Save to apply.\r\n                  </div>\r\n                  <div v-if=\"shouldRemoveDll\" class=\"selected-dll-info\">\r\n                    DLL marked for removal. Click Save to apply.\r\n                  </div>\r\n                </el-form-item>\r\n              </div>\r\n            </el-form>\r\n          </el-collapse-item>\r\n        </div>\r\n\r\n        <div class=\"card-container\">\r\n          <el-collapse-item title=\"Log Setting\" name=\"log\" class=\"custom-card\">\r\n            <el-form :model=\"config\" label-width=\"160px\" label-position=\"top\">\r\n              <div class=\"form-row\">\r\n                <el-form-item label=\"Enable log filter\" class=\"form-item\">\r\n                  <el-switch v-model=\"config.enableLogFilter\" />\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <div class=\"comment-row\">\r\n                <el-icon>\r\n                  <InfoFilled />\r\n                </el-icon> <span>When enabled, BLF log files will only record transmitted frames, expected received frames, and error frames to reduce log size and improve performance.</span>\r\n              </div>\r\n            </el-form>\r\n          </el-collapse-item>\r\n        </div>\r\n      </el-collapse>\r\n    </div>\r\n\r\n    <!-- Bottom Toolbar -->\r\n    <div class=\"toolbar bottom-toolbar\">\r\n      <el-button type=\"primary\" @click=\"handleSave\">Save</el-button>\r\n      <el-button @click=\"resetToDefault\" style=\"margin-left: 10px;\">Reset</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, onMounted, computed } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { Delete, InfoFilled, Plus, } from '@element-plus/icons-vue';\r\nimport { caseApi, CaseConfigDto, WhiteListFrame } from '@/api/caseApi';\r\n\r\nconst loading = ref(false);\r\nconst config = ref<CaseConfigDto>({\r\n  whiteListFrames: [],\r\n  enableNmWakeup: true,\r\n  nmWakeupId: 0x53F,\r\n  nmWakeupIsExt: false,\r\n  nmWakeupDlc: 8,\r\n  nmWakeupData: [0x3F, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF],\r\n  nmWakeupCommunicationType: 'Can',\r\n  nmWakeupCycleMs: 100,\r\n  nmWakeupDelayMs: 2000,\r\n  requestId: 0x731,\r\n  requestIsExt: false,\r\n  responseId: 0x631,\r\n  timeoutMs: 500,\r\n  isDutMtuLessThan4096: false,\r\n  enableDiagRetryRequest: false,\r\n  diagRetryRequestPayload: [0x10, 0x01],\r\n  securityInfo: {\r\n    hasDll: false,\r\n    dllFileName: undefined,\r\n    dllSize: 0\r\n  }\r\n});\r\n\r\n// 新增变量\r\nconst nodeNames = ref<string[]>([]);\r\nconst selectedNode = ref<string>('');\r\nconst allFrames = ref<WhiteListFrame[]>([]);\r\n\r\n// 根据选定节点过滤帧\r\nconst filteredFrames = computed(() => {\r\n  if (!selectedNode.value) return [];\r\n\r\n  const txFrames = allFrames.value.filter(frame =>\r\n    frame.transmitter === selectedNode.value\r\n  );\r\n\r\n  const rxFrames = allFrames.value.filter(frame =>\r\n    frame.transmitter !== selectedNode.value &&\r\n    frame.receivers.includes(selectedNode.value)\r\n  );\r\n\r\n  return [...txFrames, ...rxFrames];\r\n});\r\n\r\nconst activeNames = ref(['case', 'database', 'nmWakeup', 'uds', 'log']); // 默认展开所有分组\r\n\r\nconst nmWakeupIdInput = ref('');\r\nconst nmWakeupDlcInput = ref('');\r\nconst nmWakeupDataInput = ref('');\r\nconst requestIdInput = ref('');\r\nconst responseIdInput = ref('');\r\nconst diagRetryRequestPayloadInput = ref('');\r\n\r\n// 用于显示和选择 Frame Type\r\nconst nmWakeupFrameType = computed({\r\n  get: () => {\r\n    return config.value.nmWakeupCommunicationType === 'Can' ? 'CAN Frame' : 'CANFD Frame';\r\n  },\r\n  set: (value: string) => {\r\n    config.value.nmWakeupCommunicationType = value === 'CAN Frame' ? 'Can' : 'Canfd';\r\n  }\r\n});\r\n\r\nconst selectedDllPath = ref<string | null>(null);\r\nconst selectedDllName = ref<string | null>(null);\r\nconst shouldRemoveDll = ref(false);\r\n\r\nconst defaultConfig: CaseConfigDto = {\r\n  whiteListFrames: [], // 修改为空数组\r\n  enableNmWakeup: true,\r\n  nmWakeupId: 0x53F,\r\n  nmWakeupIsExt: false,\r\n  nmWakeupDlc: 8,\r\n  nmWakeupData: [0x3F, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF],\r\n  nmWakeupCommunicationType: 'Can',\r\n  nmWakeupCycleMs: 100,\r\n  nmWakeupDelayMs: 2000,\r\n  requestId: 0x731,\r\n  requestIsExt: false,\r\n  responseId: 0x631,\r\n  timeoutMs: 500,\r\n  isDutMtuLessThan4096: false,\r\n  enableDiagRetryRequest: false,\r\n  diagRetryRequestPayload: [0x10, 0x01],\r\n  securityInfo: {\r\n    hasDll: false,\r\n    dllFileName: undefined,\r\n    dllSize: 0\r\n  }\r\n};\r\n\r\nconst arrayToHexString = (arr?: number[]) => {\r\n  if (!arr) {\r\n    return '';\r\n  }\r\n  return arr.map(n => '0x' + n.toString(16).toUpperCase()).join(',');\r\n};\r\n\r\nconst handleNmWakeupIdChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const id = parseInt(cleanValue, 16);\r\n    if (isNaN(id)) {\r\n      ElMessage.error('Invalid ID format');\r\n      return;\r\n    }\r\n    config.value.nmWakeupId = id;\r\n    // 确保显示带有 0x 前缀\r\n    nmWakeupIdInput.value = '0x' + id.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleNmWakeupDlcChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const dlc = parseInt(cleanValue, 16);\r\n    if (isNaN(dlc)) {\r\n      ElMessage.error('Invalid DLC format');\r\n      return;\r\n    }\r\n    if (dlc < 0 || dlc > 64) {\r\n      ElMessage.warning('DLC should be between 0 and 64');\r\n      return;\r\n    }\r\n    config.value.nmWakeupDlc = dlc;\r\n    // 确保显示带有 0x 前缀\r\n    nmWakeupDlcInput.value = '0x' + dlc.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleNmWakeupDataChange = (value: string) => {\r\n  try {\r\n    const bytes = value.split(',')\r\n      .map(s => s.trim())\r\n      .filter(s => s)\r\n      .map(s => parseInt(s, 16));\r\n\r\n    if (bytes.length > 8) {\r\n      ElMessage.warning('Maximum 8 bytes allowed');\r\n      return;\r\n    }\r\n    if (bytes.some(b => isNaN(b) || b < 0 || b > 0xFF)) {\r\n      ElMessage.warning('Each byte must be between 0x00 and 0xFF');\r\n      return;\r\n    }\r\n    config.value.nmWakeupData = bytes;\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input format');\r\n  }\r\n};\r\n\r\nconst handleRequestIdChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const id = parseInt(cleanValue, 16);\r\n    if (isNaN(id)) {\r\n      ElMessage.error('Invalid ID format');\r\n      return;\r\n    }\r\n    config.value.requestId = id;\r\n    // 确保显示带有 0x 前缀\r\n    requestIdInput.value = '0x' + id.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleResponseIdChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const id = parseInt(cleanValue, 16);\r\n    if (isNaN(id)) {\r\n      ElMessage.error('Invalid ID format');\r\n      return;\r\n    }\r\n    config.value.responseId = id;\r\n    // 确保显示带有 0x 前缀\r\n    responseIdInput.value = '0x' + id.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleDiagRetryRequestPayloadChange = (value: string) => {\r\n  try {\r\n    const bytes = value.split(',')\r\n      .map(s => s.trim())\r\n      .filter(s => s)\r\n      .map(s => parseInt(s, 16));\r\n\r\n    if (bytes.some(b => isNaN(b) || b < 0 || b > 0xFF)) {\r\n      ElMessage.warning('Each byte must be between 0x00 and 0xFF');\r\n      return;\r\n    }\r\n    config.value.diagRetryRequestPayload = bytes;\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input format');\r\n  }\r\n};\r\n\r\nconst resetToDefault = () => {\r\n  config.value = JSON.parse(JSON.stringify(defaultConfig));\r\n  nmWakeupIdInput.value = '0x' + defaultConfig.nmWakeupId.toString(16).toUpperCase();\r\n  nmWakeupDlcInput.value = '0x' + defaultConfig.nmWakeupDlc.toString(16).toUpperCase();\r\n  nmWakeupDataInput.value = arrayToHexString(defaultConfig.nmWakeupData);\r\n  requestIdInput.value = '0x' + defaultConfig.requestId.toString(16).toUpperCase();\r\n  responseIdInput.value = '0x' + defaultConfig.responseId.toString(16).toUpperCase();\r\n  diagRetryRequestPayloadInput.value = arrayToHexString(defaultConfig.diagRetryRequestPayload);\r\n  selectedDllPath.value = null;\r\n  selectedDllName.value = null;\r\n  shouldRemoveDll.value = false;\r\n\r\n  // 重置节点和帧相关状态\r\n  nodeNames.value = [];\r\n  selectedNode.value = '';\r\n  allFrames.value = [];\r\n};\r\n\r\nconst loadConfig = async () => {\r\n  loading.value = true;\r\n  try {\r\n    const response = await caseApi.getCaseConfig();\r\n    config.value = response.data;\r\n    nmWakeupIdInput.value = '0x' + response.data.nmWakeupId.toString(16).toUpperCase();\r\n    nmWakeupDataInput.value = arrayToHexString(response.data.nmWakeupData);\r\n    requestIdInput.value = '0x' + response.data.requestId.toString(16).toUpperCase();\r\n    responseIdInput.value = '0x' + response.data.responseId.toString(16).toUpperCase();\r\n\r\n    // 设置备用诊断请求数据\r\n    if (response.data.diagRetryRequestPayload) {\r\n      diagRetryRequestPayloadInput.value = arrayToHexString(response.data.diagRetryRequestPayload);\r\n    } else {\r\n      diagRetryRequestPayloadInput.value = '0x10,0x01';\r\n    }\r\n\r\n    // 确保新增字段有默认值\r\n    if (response.data.nmWakeupIsExt === undefined) {\r\n      config.value.nmWakeupIsExt = false;\r\n    }\r\n    if (response.data.nmWakeupDlc === undefined) {\r\n      config.value.nmWakeupDlc = 8;\r\n    }\r\n    if (response.data.requestIsExt === undefined) {\r\n      config.value.requestIsExt = false;\r\n    }\r\n    if (response.data.enableDiagRetryRequest === undefined) {\r\n      config.value.enableDiagRetryRequest = false;\r\n    }\r\n    if (response.data.diagRetryRequestPayload === undefined) {\r\n      config.value.diagRetryRequestPayload = [0x10, 0x01];\r\n    }\r\n\r\n    // 设置 DLC 输入框的值\r\n    nmWakeupDlcInput.value = '0x' + config.value.nmWakeupDlc.toString(16).toUpperCase();\r\n\r\n    // 如果已存在帧，尝试提取节点列表\r\n    if (response.data.whiteListFrames.length > 0) {\r\n      const frames = response.data.whiteListFrames;\r\n      allFrames.value = frames;\r\n\r\n      // 提取所有唯一的节点名\r\n      const nodes = new Set<string>();\r\n      frames.forEach(frame => {\r\n        if (frame.transmitter) nodes.add(frame.transmitter);\r\n        frame.receivers?.forEach(r => nodes.add(r));\r\n      });\r\n\r\n      nodeNames.value = Array.from(nodes);\r\n\r\n      // 如果保存了选中的节点，恢复选中状态\r\n      if (response.data.selectedNodeName && nodeNames.value.includes(response.data.selectedNodeName)) {\r\n        selectedNode.value = response.data.selectedNodeName;\r\n      }\r\n      // 否则，如果只有一个节点，自动选择\r\n      else if (nodeNames.value.length === 1) {\r\n        selectedNode.value = nodeNames.value[0];\r\n      }\r\n    }\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load configuration');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 处理节点改变\r\nconst handleNodeChange = (value: string) => {\r\n  // 更新过滤后的帧列表 (通过 computed 自动实现)\r\n  if (!value) {\r\n    ElMessage.warning('Please select a target ECU');\r\n  }\r\n};\r\n\r\nconst handleSave = async () => {\r\n  loading.value = true;\r\n  try {\r\n    // 准备提交数据\r\n    const submitData = { ...config.value };\r\n\r\n    // 修改: 保存所有帧数据，而不只是筛选后的数据\r\n    submitData.whiteListFrames = allFrames.value;\r\n\r\n    // 保存选中的节点名称\r\n    submitData.selectedNodeName = selectedNode.value;\r\n\r\n    if (selectedDllPath.value) {\r\n      submitData.securityDllPath = selectedDllPath.value;\r\n    }\r\n\r\n    if (shouldRemoveDll.value) {\r\n      submitData.removeSecurityDll = true;\r\n    }\r\n\r\n    const result = await caseApi.updateCaseConfig(submitData);\r\n    config.value = result.data;\r\n\r\n    selectedDllPath.value = null;\r\n    selectedDllName.value = null;\r\n    shouldRemoveDll.value = false;\r\n\r\n    ElMessage.success('Case settings saved.');\r\n  } catch (error) {\r\n    ElMessage.error('Save failed');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst importLoading = ref(false);\r\n\r\nconst handleImportDbc = async () => {\r\n  importLoading.value = true;\r\n  try {\r\n    const response = await caseApi.importDbc();\r\n\r\n    // 保存所有帧和节点信息\r\n    allFrames.value = response.data.whiteListFrames;\r\n    nodeNames.value = response.data.nodeNames;\r\n\r\n    // 重置选择的节点\r\n    selectedNode.value = '';\r\n\r\n    // 清空当前已保存的帧列表，等待用户选择节点\r\n    config.value.whiteListFrames = [];\r\n\r\n    ElMessage.success('DBC file imported successfully. Please select a target ECU.');\r\n  } catch (error: any) {\r\n    if (error.response?.data === 'UserCanceled') {\r\n      return;\r\n    }\r\n    ElMessage.error(error.response?.data === 'InvalidFileFormat'\r\n      ? 'Invalid DBC file format'\r\n      : 'Failed to import DBC file');\r\n  } finally {\r\n    importLoading.value = false;\r\n  }\r\n};\r\n\r\nconst formatFileSize = (bytes?: number): string => {\r\n  if (!bytes) return '0 B';\r\n\r\n  const units = ['B', 'KB', 'MB', 'GB'];\r\n  let size = bytes;\r\n  let unitIndex = 0;\r\n\r\n  while (size >= 1024 && unitIndex < units.length - 1) {\r\n    size /= 1024;\r\n    unitIndex++;\r\n  }\r\n\r\n  return `${size.toFixed(2)} ${units[unitIndex]}`;\r\n};\r\n\r\nconst selectDll = async () => {\r\n  try {\r\n    const response = await caseApi.selectSecurityDll();\r\n    const dllPath = response.data.path;\r\n    selectedDllPath.value = dllPath;\r\n    selectedDllName.value = dllPath.split('\\\\').pop() || dllPath;\r\n  } catch (error: any) {\r\n    if (error.response?.data === 'UserCanceled') {\r\n      return;\r\n    }\r\n    ElMessage.error(error.response?.data === 'InvalidFileFormat'\r\n      ? 'Invalid DLL file format'\r\n      : 'Failed to select DLL file');\r\n  }\r\n};\r\n\r\nconst removeDll = () => {\r\n  shouldRemoveDll.value = true;\r\n};\r\n\r\nconst securityDllDisplay = computed(() => {\r\n  if (selectedDllName.value) {\r\n    return selectedDllName.value;\r\n  }\r\n\r\n  if (shouldRemoveDll.value) {\r\n    return 'DLL will be removed after save';\r\n  }\r\n\r\n  return config.value.securityInfo?.hasDll\r\n    ? `${config.value.securityInfo.dllFileName} (${formatFileSize(config.value.securityInfo.dllSize)})`\r\n    : '';\r\n});\r\n\r\n\r\n\r\n// 展开全部面板\r\nconst expandAll = () => {\r\n  activeNames.value = ['case', 'database', 'nmWakeup', 'uds', 'log'];\r\n};\r\n\r\n// 收起全部面板\r\nconst collapseAll = () => {\r\n  activeNames.value = [];\r\n};\r\n\r\nonMounted(() => {\r\n  loadConfig();\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.case-setting-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex: 1;\r\n  background-color: #ffffff;\r\n  border-radius: 8px;\r\n  padding: 15px 20px;\r\n}\r\n\r\n.toolbar {\r\n  display: flex;\r\n  background-color: #ffffff;\r\n  z-index: 10;\r\n}\r\n\r\n.top-toolbar {\r\n  border-bottom: 1px solid #e4e7ed;\r\n  padding-bottom: 10px;\r\n  position: sticky;\r\n  top: 0;\r\n}\r\n\r\n.bottom-toolbar {\r\n  border-top: 1px solid #e4e7ed;\r\n  padding-top: 10px;\r\n  position: sticky;\r\n  bottom: 0;\r\n}\r\n\r\n.content-area {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 10px 0;\r\n}\r\n\r\n/* 卡片容器样式 */\r\n.card-container {\r\n  margin-bottom: 15px;\r\n  margin-right: 10px;\r\n}\r\n\r\n/* 自定义卡片样式 */\r\n.custom-card {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n  background-color: #fff;\r\n}\r\n\r\n:deep(.el-collapse-item__header) {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  background-color: #f5f7fa;\r\n  border-bottom: 1px solid #e4e7ed;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n:deep(.el-collapse) {\r\n  border: none;\r\n}\r\n\r\n:deep(.el-collapse-item__wrap) {\r\n  padding: 15px;\r\n  border: none;\r\n  background-color: #ffffff;\r\n}\r\n\r\n.disabled-form-content {\r\n  opacity: 0.6;\r\n  pointer-events: none;\r\n}\r\n\r\n.disabled-form-content :deep(.el-input__wrapper),\r\n.disabled-form-content :deep(.el-input-number__wrapper),\r\n.disabled-form-content :deep(.el-select) {\r\n  cursor: not-allowed;\r\n}\r\n\r\n.security-config {\r\n  padding: 10px;\r\n}\r\n\r\n.dll-info {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.security-input-group {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.security-dll-input {\r\n  width: 400px;\r\n}\r\n\r\n.security-buttons {\r\n  margin-left: 10px;\r\n  display: flex;\r\n  gap: 5px;\r\n}\r\n\r\n.selected-dll-info {\r\n  margin-left: 10px;\r\n  margin-top: 5px;\r\n  font-size: 12px;\r\n  color: var(--el-color-warning);\r\n}\r\n\r\n.warning-text {\r\n  color: #E6A23C;\r\n}\r\n\r\n.node-selection-required {\r\n  color: #E6A23C;\r\n  text-align: center;\r\n  padding: 20px;\r\n  border: 1px dashed #dcdfe6;\r\n  border-radius: 4px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.import-note {\r\n  color: #909399;\r\n  font-style: italic;\r\n  text-align: center;\r\n}\r\n\r\n.white-list-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.toolbar-left {\r\n  display: flex;\r\n}\r\n\r\n.expand-button,\r\n.collapse-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n}\r\n\r\n.button-text {\r\n  margin-left: 8px;\r\n}\r\n\r\n/* NM 唤醒表格样式 */\r\n.nm-table {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n:deep(.nm-table .el-table__header) {\r\n  font-weight: bold;\r\n}\r\n\r\n:deep(.nm-table .el-table__cell) {\r\n  padding: 8px;\r\n}\r\n\r\n:deep(.nm-table .el-input__wrapper),\r\n:deep(.nm-table .el-select) {\r\n  width: 100%;\r\n}\r\n\r\n/* NM Wake Up 设置的行布局 */\r\n.nm-wakeup-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  margin-bottom: 10px;\r\n  justify-content: flex-start;\r\n  /* 左对齐 */\r\n}\r\n\r\n.nm-wakeup-item {\r\n  flex: 0 0 auto;\r\n  /* 不自动拉伸，保持原始大小 */\r\n}\r\n\r\n/* ID、Is Ext Flag、Frame Type、DLC 的宽度 */\r\n.nm-wakeup-item:nth-child(1) {\r\n  width: 180px;\r\n}\r\n\r\n.nm-wakeup-item:nth-child(2) {\r\n  width: 80px;\r\n}\r\n\r\n.nm-wakeup-item:nth-child(3) {\r\n  width: 200px;\r\n}\r\n\r\n.nm-wakeup-item:nth-child(4) {\r\n  width: 150px;\r\n}\r\n\r\n/* Data 字段随页面大小变化 */\r\n.nm-wakeup-item:nth-child(5) {\r\n  flex: 1 1 auto;\r\n  /* 可以拉伸和收缩 */\r\n  min-width: 300px;\r\n}\r\n\r\n/* 第三行的元素左对齐 */\r\n.nm-wakeup-row:nth-child(2) .nm-wakeup-item {\r\n  width: 180px;\r\n}\r\n\r\n/* UDS Setting 设置的行布局 */\r\n.form-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  margin-bottom: 10px;\r\n  justify-content: flex-start;\r\n}\r\n\r\n.form-item {\r\n  flex: 0 0 auto;\r\n  width: 200px;\r\n}\r\n\r\n.comment-row {\r\n  color: #888;\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  gap: 6px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.el-form-item--default {\r\n  margin-bottom: 4px;\r\n}\r\n</style>\r\n", "import script from \"./CaseSetting.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./CaseSetting.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./CaseSetting.vue?vue&type=style&index=0&id=d7f9939a&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-d7f9939a\"]])\n\nexport default __exports__"], "names": ["BASE_URL", "caseApi", "getCaseConfig", "USE_MOCK", "mockApi", "case", "axios", "get", "updateCaseConfig", "config", "post", "importDbc", "selectSecurityDll", "_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "key", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_defineComponent", "__name", "setup", "__props", "loading", "ref", "whiteList<PERSON>rames", "enableNmWakeup", "nmWakeupId", "nmWakeupIsExt", "nmWakeupDlc", "nmWakeupData", "nmWakeupCommunicationType", "nmWakeupCycleMs", "nmWakeupDelayMs", "requestId", "requestIsExt", "responseId", "timeoutMs", "isDutMtuLessThan4096", "enableDiagRetryRequest", "diagRetryRequestPayload", "securityInfo", "hasDll", "dllFileName", "undefined", "dllSize", "nodeNames", "selectedNode", "allFrames", "filteredFrames", "computed", "value", "txFrames", "filter", "frame", "transmitter", "rxFrames", "receivers", "includes", "activeNames", "nmWakeupIdInput", "nmWakeupDlcInput", "nmWakeupDataInput", "requestIdInput", "responseIdInput", "diagRetryRequestPayloadInput", "nmWakeupFrameType", "set", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedDllName", "shouldRemoveDll", "defaultConfig", "arrayToHexString", "arr", "map", "n", "toString", "toUpperCase", "join", "handleNmWakeupIdChange", "cleanValue", "replace", "id", "parseInt", "isNaN", "ElMessage", "error", "handleNmWakeupDlcChange", "dlc", "warning", "handleNmWakeupDataChange", "bytes", "split", "s", "trim", "length", "some", "b", "handleRequestIdChange", "handleResponseIdChange", "handleDiagRetryRequestPayloadChange", "resetToDefault", "JSON", "parse", "stringify", "loadConfig", "async", "response", "data", "frames", "nodes", "Set", "for<PERSON>ach", "add", "r", "Array", "from", "selectedNodeName", "handleNodeChange", "handleSave", "submitData", "securityDllPath", "removeSecurityDll", "result", "success", "importLoading", "handleImportDbc", "formatFileSize", "units", "size", "unitIndex", "toFixed", "selectDll", "dll<PERSON><PERSON>", "path", "pop", "removeDll", "securityDllDisplay", "expandAll", "collapseAll", "onMounted", "_ctx", "_cache", "_component_font_awesome_icon", "_resolveComponent", "_component_el_button", "_component_el_input", "_component_el_form_item", "_component_el_switch", "_component_el_input_number", "_component_el_icon", "_component_el_form", "_component_el_collapse_item", "_component_el_option", "_component_el_select", "_component_el_table_column", "_component_el_table", "_component_el_collapse", "_directive_loading", "_resolveDirective", "_withDirectives", "_openBlock", "_createElementBlock", "_createElementVNode", "_createVNode", "onClick", "type", "default", "_withCtx", "icon", "_", "modelValue", "$event", "title", "name", "model", "label", "placeholder", "onChange", "style", "min", "max", "_unref", "InfoFilled", "_createTextVNode", "_createBlock", "_Fragment", "_renderList", "node", "_createCommentVNode", "border", "row", "_toDisplayString", "padStart", "prop", "isExt", "_normalizeClass", "width", "disabled", "readonly", "Plus", "Delete", "enableLogFilter", "__exports__"], "sourceRoot": ""}