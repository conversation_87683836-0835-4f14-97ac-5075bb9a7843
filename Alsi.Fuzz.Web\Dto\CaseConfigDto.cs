using Alsi.App.Devices.Core;
using Alsi.Fuzz.Core.Models.TestPlans;
using System;

namespace Alsi.Fuzz.Web.Dto
{
    public class CaseConfigDto
    {
        public WhiteListFrame[] WhiteListFrames { get; set; } = Array.Empty<WhiteListFrame>();

        // 新增：保存选中的目标节点名称
        public string SelectedNodeName { get; set; }

        public bool EnableNmWakeup { get; set; }
        public int NmWakeupId { get; set; }
        public CommunicationType NmWakeupCommunicationType { get; set; }
        public bool NmWakeupIsExt { get; set; }
        public int NmWakeupDlc { get; set; }
        public int[] NmWakeupData { get; set; }
        public int NmWakeupCycleMs { get; set; }
        public int NmWakeupDelayMs { get; set; }

        public int RequestId { get; set; }
        public int ResponseId { get; set; }
        public bool RequestIsExt { get; set; }
        public int TimeoutMs { get; set; }
        public CommunicationType DiagCommunicationType { get; set; }
        public bool IsDutMtuLessThan4096 { get; set; }
        public bool EnableDiagRetryRequest { get; set; }
        public int[] DiagRetryRequestPayload { get; set; }

        /// <summary>
        /// 记录日志时，是否使用 Log 过滤器
        /// </summary>
        public bool EnableLogFilter { get; set; }

        #region 安全配置相关

        /// <summary>
        /// 新选择的 DLL 路径
        /// </summary>
        public string SecurityDllPath { get; set; }

        /// <summary>
        /// 是否移除现有 DLL
        /// </summary>
        public bool RemoveSecurityDll { get; set; }

        /// <summary>
        /// Security 配置信息，只读
        /// </summary>
        public SecurityConfigInfoDto SecurityInfo { get; set; }
        #endregion
    }

    public class SecurityConfigInfoDto
    {
        public bool HasDll { get; set; }
        public string DllFileName { get; set; }
        public int DllSize { get; set; }
    }
}
