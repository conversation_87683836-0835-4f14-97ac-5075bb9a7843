using Alsi.App.Devices.Core;
using Alsi.Fuzz.Core.Service.Results;
using System;
using System.Linq;

namespace Alsi.Fuzz.Tester.Testers
{
    public class ErrorFrameChecker
    {
        private CanFrame ErrorFrame { get; set; }

        public ErrorFrameChecker()
        {
            DataBus.OnSent += RecordDataFrame;
            DataBus.OnReceived += RecordDataFrame;
        }

        public void Release()
        {
            DataBus.OnSent -= RecordDataFrame;
            DataBus.OnReceived -= RecordDataFrame;
        }

        private DataCache Cache = new DataCache();

        private void RecordDataFrame(object _, CanFrame frame)
        {
            Cache.Add(frame);
        }

        public void Reset()
        {
            Cache.Clear();
        }

        public void Check(int caseResultId, out CaseStep errorFrameCaseStep)
        {
            errorFrameCaseStep = null;

            InternalCheck();

            if (IsPassed(out var errorMessage, out var errorFrame))
            {
                return;
            }

            var now = DateTime.Now;
            errorFrameCaseStep = new CaseStep
            {
                Id = Guid.NewGuid(),
                Name = "ErrorFrame",
                CaseResultId = caseResultId,
                Timestamp = DataBus.Timestamp,
                FrameTimestamp = errorFrame.TimeUS,
                State = CaseStepState.Failure,
                Begin = now,
                End = now,
                Detail = errorMessage
            };
        }

        private ErrorFrameChecker InternalCheck()
        {
            if (ErrorFrame != null)
            {
                return this;
            }

            var frames = Cache.GetAll();
            var firstTxFrame = frames.FirstOrDefault(x => x.IsTx && !x.IsErrorFrame);
            if (firstTxFrame != null)
            {
                var lastRxFrame = frames.LastOrDefault(x => !x.IsTx && !x.IsErrorFrame);
                CanFrame errorFrame = null;
                if (lastRxFrame == null)
                {
                    errorFrame = frames.FirstOrDefault(x => x.TimeUS > firstTxFrame.TimeUS && x.IsErrorFrame);
                }
                else
                {
                    errorFrame = frames.FirstOrDefault(x =>
                        x.TimeUS > firstTxFrame.TimeUS
                        && x.TimeUS < lastRxFrame.TimeUS
                        && x.IsErrorFrame);
                }

                if (errorFrame != null)
                {
                    ErrorFrame = errorFrame;
                }
            }

            return this;
        }

        private bool IsPassed(out string errorMessage, out CanFrame errorFrame)
        {
            errorMessage = string.Empty;
            errorFrame = ErrorFrame;
            if (ErrorFrame == null)
            {
                return true;
            }
            errorMessage = $"Error frame detected: Timestamp={TimestampUtils.GetString(ErrorFrame.TimeUS)}";
            return false;
        }
    }
}
