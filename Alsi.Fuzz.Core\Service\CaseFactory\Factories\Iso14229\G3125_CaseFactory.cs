using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts;
using Alsi.Fuzz.Core.Service.CaseFactory.Iso14229;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G3125_CaseFactory : CaseFactoryBase
    {
        public override void Generate(MutationOptions options, Action<CreateCaseInfo> createCase)
        {
            var xmlServices = options.XmlServices;
            var supportedXmlServicesWithoutSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => !x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历没有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == false)
                .ToArray();

            foreach (var xmlService in supportedXmlServicesWithoutSubfunction)
            {
                var sid = xmlService.Id;
                var serviceName = xmlService.IsoUdsServiceDisplayName;

                var dataList = new List<byte[]>();
                if (xmlService.Parameter2k.Length == 1)
                {
                    for (var i = 0; i <= 255; ++i)
                    {
                        dataList.Add(new byte[] { (byte)i });
                    }
                }
                else
                {
                    var sampleCount = options.Coverage == CoverageType.Normal ? 512 : 1024;
                    var sampleBytesArray = UniformSampleBytes(xmlService.Parameter2k.Length, sampleCount);
                    dataList = sampleBytesArray.ToList();
                }

                for (var i = 0; i < dataList.Count; i++)
                {
                    byte[] data = dataList[i];
                    var payload = new List<byte> { sid, };
                    // 别人的参数
                    payload.AddRange(data);
                    var groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                                    .GetService(serviceName)
                                    .Invalid()
                                    .Parameter1K("RandomValue")
                                    .Path;
                    var name = $"Sid{sid:X2}-RandomValue -{groupPath}";

                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G3125-Sid{sid:X2}-Lhs{i + 1:D3}/{dataList.Count}", caseMutation, sid, xmlService.SubfunctionId));
                }
            }
        }

        /// <summary>
        /// 生成均匀分布的字节数组样本
        /// </summary>
        /// <param name="byteArraySize">字节数组大小（如512）</param>
        /// <param name="sampleCount">要生成的样本数量（如512或1024）</param>
        /// <returns>包含样本的数组</returns>
        public static byte[][] UniformSampleBytes(int byteArraySize, int sampleCount)
        {
            if (sampleCount < 2)
                throw new ArgumentException("样本数量必须至少为2");

            byte[][] samples = new byte[sampleCount][];

            // 创建最大值 (2^(byteArraySize*8) - 1)，即全1
            byte[] maxValueBytes = Enumerable.Repeat((byte)0xFF, byteArraySize).ToArray();
            BigInteger maxValue = new BigInteger(maxValueBytes.Reverse().Concat(new byte[] { 0 }).ToArray());

            // 第一个样本是全0
            samples[0] = new byte[byteArraySize]; // 默认为0

            // 最后一个样本是全1
            samples[sampleCount - 1] = maxValueBytes;

            // 计算间隔
            BigInteger interval = BigInteger.Divide(maxValue, sampleCount - 1);

            // 生成中间的样本
            for (int i = 1; i < sampleCount - 1; i++)
            {
                BigInteger value = BigInteger.Multiply(interval, i);
                samples[i] = ConvertToByteArray(value, byteArraySize);
            }

            return samples;
        }

        /// <summary>
        /// 将BigInteger转换为固定长度的字节数组（大端格式）
        /// </summary>
        private static byte[] ConvertToByteArray(BigInteger value, int length)
        {
            // 获取BigInteger的字节表示（小端格式）
            byte[] bytes = value.ToByteArray();
            byte[] result = new byte[length];

            // 去除可能存在的符号位
            int sourceLength = bytes.Length;
            if (bytes.Length > 0 && bytes[bytes.Length - 1] == 0)
            {
                sourceLength--;
            }

            // 将字节复制到结果数组，同时反转字节顺序以获得大端格式
            int sourceToCopy = Math.Min(sourceLength, length);
            for (int i = 0; i < sourceToCopy; i++)
            {
                result[length - i - 1] = bytes[i];
            }

            return result;
        }
    }
}
