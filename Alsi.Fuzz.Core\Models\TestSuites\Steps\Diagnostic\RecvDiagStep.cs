using System.Collections.Generic;
using System.Xml.Serialization;

namespace Alsi.Fuzz.Core.Models.TestSuites.Steps.Diagnostic
{
    public class RecvDiagStep : StepBase, ICommRecvStep
    {
        [XmlElement("match")]
        public List<MatchDiag> MatchDiags { get; set; } = new List<MatchDiag>();

        private bool? _ignoreErrorFrame;

        [XmlIgnore]
        public bool IgnoreErrorFrame
        {
            get => _ignoreErrorFrame == true;
            set => _ignoreErrorFrame = value;
        }

        [XmlAttribute("ignore-error-frame")]
        public string IgnoreErrorFrameString
        {
            get => _ignoreErrorFrame.HasValue ? _ignoreErrorFrame.Value.ToString() : null;
            set => _ignoreErrorFrame = bool.TryParse(value ?? string.Empty, out var boolValue) ? boolValue : false;
        }
    }
}
