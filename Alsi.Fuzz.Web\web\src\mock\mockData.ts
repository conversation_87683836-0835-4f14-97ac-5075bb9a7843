// 导入类型定义
import { TestPlan } from '@/api/testPlanApi';
import { TestPlanHistory } from '@/api/testPlanHistoryApi';
import { ErrorData } from '@/api/appApi';

// 生成随机ID
const generateId = () => Math.random().toString(36).substring(2, 15);

// 生成当前日期时间的ISO字符串
const getCurrentISOTime = () => new Date().toISOString();

// 生成过去随机时间
const getRandomPastTime = () => {
  const now = new Date();
  const pastTime = new Date(now.getTime() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000);
  return pastTime.toISOString();
};

// 预设测试计划数据
export const testPlans: TestPlan[] = [
  {
    path: "D:\\TestPlans\\CAN总线测试计划.fzp",
    manifest: {
      name: "CAN总线测试计划",
      description: "针对汽车CAN总线通信的模糊测试",
      created: getRandomPastTime(),
      modified: getCurrentISOTime()
    },
    config: {
      targetDevice: "ECM",
      protocol: "CAN",
      testCases: ["TC001", "TC002", "TC003"]
    }
  },
  {
    path: "D:\\TestPlans\\UDS协议测试.fzp",
    manifest: {
      name: "UDS协议测试",
      description: "针对UDS协议的诊断服务测试",
      created: getRandomPastTime(),
      modified: getRandomPastTime()
    },
    config: {
      targetDevice: "BCM",
      protocol: "UDS",
      testCases: ["UDS001", "UDS002"]
    }
  },
  {
    path: "D:\\TestPlans\\CANFD高速协议测试.fzp",
    manifest: {
      name: "CANFD高速协议测试",
      description: "针对CANFD高速数据传输协议的稳定性测试",
      created: getRandomPastTime(),
      modified: getRandomPastTime()
    },
    config: {
      targetDevice: "Gateway",
      protocol: "CANFD",
      testCases: ["FD001", "FD002", "FD003", "FD004"]
    }
  }
];

// 测试计划历史记录数据
export const testPlanHistoryItems: TestPlanHistory[] = [
  {
    id: generateId(),
    filePath: "D:\\TestPlans\\can_test.fzp",
    planName: "CAN总线测试计划",
    lastAccessTime: getRandomPastTime(),
    lastModified: getRandomPastTime(),
    isDeleted: false
  },
  {
    id: generateId(),
    filePath: "D:\\TestPlans\\uds_protocol.fzp",
    planName: "UDS协议测试",
    lastAccessTime: getRandomPastTime(),
    lastModified: getRandomPastTime(),
    isDeleted: false
  },
  {
    id: generateId(),
    filePath: "D:\\TestPlans\\canfd_test.fzp",
    planName: "CANFD高速协议测试",
    lastAccessTime: getCurrentISOTime(),
    lastModified: getRandomPastTime(),
    isDeleted: false
  }
];

// 用于模拟内存中的错误日志存储
export const errorLogs: ErrorData[] = [];
