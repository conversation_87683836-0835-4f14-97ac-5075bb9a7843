using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts;
using Alsi.Fuzz.Core.Service.CaseFactory.Iso14229;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G3124_CaseFactory : CaseFactoryBase
    {
        public override void Generate(MutationOptions options, Action<CreateCaseInfo> createCase)
        {
            var xmlServices = options.XmlServices;
            var supportedXmlServicesWithoutSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => !x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历没有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == false)
                .ToArray();

            foreach (var xmlService in supportedXmlServicesWithoutSubfunction)
            {
                var sid = xmlService.Id;
                var serviceName = xmlService.IsoUdsServiceDisplayName;

                foreach (var otherXmlService in supportedXmlServicesWithoutSubfunction)
                {
                    if (otherXmlService.Id == xmlService.Id)
                    {
                        continue;
                    }
                    var otherServiceName = otherXmlService.IsoUdsServiceDisplayName;
                    var payload = new List<byte> { sid, };
                    // 别人的参数
                    payload.AddRange(otherXmlService.Parameter2k);

                    var groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                                    .GetService(serviceName)
                                    .Invalid()
                                    .Parameter1K(otherServiceName)
                                    .Path;
                    var name = $"Sid{sid:X2}-otherServiceParam2k{otherXmlService.Id:X} -{groupPath}";

                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G3124-Sid{sid:X2}-OtherSid{otherXmlService.Id:X2}", caseMutation, sid, xmlService.SubfunctionId));
                }
            }
        }
    }
}
