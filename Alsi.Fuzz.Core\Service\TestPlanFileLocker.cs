using System;
using System.IO;

namespace Alsi.Fuzz.Core.Service
{
    public static class TestPlanFileLocker
    {
        private static FileStream _currentLock;
        private static string _currentFilePath;

        public static void LockFile(string filePath)
        {
            UnlockFile();

            _currentLock = new FileStream(
                filePath,
                FileMode.Open,
                FileAccess.Read,
                FileShare.Read
            );

            _currentFilePath = filePath;
        }

        public static void UnlockFile()
        {
            if (_currentLock != null)
            {
                _currentLock.Close();
                _currentLock.Dispose();
                _currentLock = null;
                _currentFilePath = null;
            }
        }

        public static bool IsLocked(string filePath)
        {
            return filePath.Equals(_currentFilePath, StringComparison.OrdinalIgnoreCase);
        }

        public static FileLockHandler BeginWrite(string filePath)
        {
            return new FileLockHandler(filePath);
        }
    }

    public class FileLockHandler : IDisposable
    {
        private string _filePath;
        private bool _isLocked;

        public FileLockHandler(string filePath)
        {
            _isLocked = TestPlanFileLocker.IsLocked(filePath);
            if (_isLocked)
            {
                TestPlanFileLocker.UnlockFile();
            }

            _filePath = filePath;
        }

        public void Dispose()
        {
            if (_isLocked)
            {
                TestPlanFileLocker.LockFile(_filePath);
            }
        }
    }
}
