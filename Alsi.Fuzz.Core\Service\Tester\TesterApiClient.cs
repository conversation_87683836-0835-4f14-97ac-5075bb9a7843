using Alsi.Common.Utils;
using Alsi.Fuzz.Core.Contracts.Tester;
using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service.Results;
using Alsi.Fuzz.Core.Storage;
using Alsi.Fuzz.Core.Utils;
using Newtonsoft.Json;
using System;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace Alsi.Fuzz.Core.Service.Tester
{
    /// <summary>
    /// Tester进程API客户端实现
    /// </summary>
    public class TesterApiClient : ITesterApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly int _port;
        private bool _disposed = false;

        public TesterApiClient(int port = 5000, string hostname = "localhost", int timeoutMs = 10_000)
        {
            _port = port;
            _httpClient = new HttpClient
            {
                BaseAddress = new Uri($"http://{hostname}:{port}/"),
                Timeout = TimeSpan.FromMilliseconds(timeoutMs)
            };
        }

        /// <summary>
        /// 获取Tester是否正在运行
        /// </summary>
        public async Task<bool> IsRunningAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("api/tester");
                if (!response.IsSuccessStatusCode)
                {
                    return false;
                }

                var content = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeAnonymousType(content, new { IsRunning = false });
                return result?.IsRunning ?? false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取Tester状态失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 请求Tester进程退出
        /// </summary>
        public async Task<ApiResponse> ExitAsync()
        {
            var response = await _httpClient.PostAsync("api/tester/exit", null);
            return await HandleResponseAsync(response);
        }

        public async Task<ApiResponse> PauseAsync()
        {
            var response = await _httpClient.PostAsync("api/tester/pause", null);
            return await HandleResponseAsync(response);
        }

        public async Task<ApiResponse> ResumeAsync()
        {
            var response = await _httpClient.PostAsync("api/tester/resume", null);
            return await HandleResponseAsync(response);
        }

        /// <summary>
        /// 检查Tester API是否可访问
        /// </summary>
        public async Task<ApiResponse> PingAsync()
        {
            var response = await _httpClient.GetAsync("api/tester");
            return await HandleResponseAsync(response);
        }

        public async Task<TesterSnapshot> GetTesterSnapshotAsync(ReqeustTesterSnapshot reqeust)
        {
            var content = new StringContent(JsonConvert.SerializeObject(reqeust), System.Text.Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync("api/tester/snapshot", content);

            if (!response.IsSuccessStatusCode)
            {
                return null;
            }

            var json = await response.Content.ReadAsStringAsync();
            var snapshotResponse = JsonConvert.DeserializeObject<TesterSnapshot>(json);
            return snapshotResponse;
        }

        public async Task<ApiResponse> StartTestCasesAsync(TestResult testResult, CaseEntry[] caseEntries)
        {
            var currentPlan = TestPlanManager.Instance.GetCurrentPlan();
            var selectedSequence = currentPlan?.Config?.SequenceConfigList.FirstOrDefault(x => x.IsSelected);
            if (selectedSequence == null)
            {
                throw new Exception("Cannot start interoperation: current sequence config is empty.");
            }

            // 获取序列配置
            if (string.IsNullOrWhiteSpace(selectedSequence.TestSuiteName) ||
                string.IsNullOrWhiteSpace(selectedSequence.SequencePackageName))
            {
                throw new Exception("Cannot start interoperation: test suite or package name is empty.");
            }


            var selectedDeviceChannelName = currentPlan.Config.HardwareConfig?.GetSelectedDeviceChannelName();
            if (string.IsNullOrWhiteSpace(selectedDeviceChannelName))
            {
                throw new Exception("Cannot start interoperation: no device channel selected.");
            }

            var builtInTestSuiteService = new BuiltInTestSuiteService();
            var testSuite = builtInTestSuiteService.GetByName(selectedSequence.TestSuiteName);
            var package = testSuite.Packages.FirstOrDefault(x => x.Name == selectedSequence.SequencePackageName);
            if (package == null)
            {
                throw new Exception($"Cannot start interoperation: sequence package not found: {selectedSequence.SequencePackageName}");
            }

            var testPlanSequenceService = new TestPlanSequenceService(new TestPlanStorage());
            if (!testPlanSequenceService.TryGetSelectedSequenceXml(currentPlan, out var isoType, out var sequencePackageXml))
            {
                throw new AppException("Please select a test sequence, failed to load selected sequence XML.");
            }

            var securityAccessDllBytes = currentPlan.Config.SecurityConfig.DllBytes;
            SecurityAccessUtils.PrepareDll(securityAccessDllBytes);

            var request = new ExecutionRequest
            {
                TestResult = testResult,
                HardwareConfig = currentPlan.Config.HardwareConfig,
                CaseConfig = currentPlan.Config.CaseConfig,
                SequencePackageXml = sequencePackageXml,
                CaseEntries = caseEntries
            };
            var requestBody = JsonConvert.SerializeObject(request);

            var content = new StringContent(requestBody, System.Text.Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync("api/tester/execute", content);
            return await HandleResponseAsync(response);
        }

        /// <summary>
        /// 启动互操作测试
        /// </summary>
        public async Task<ApiResponse> StartInteroperationTestAsync()
        {
            // 获取当前测试计划
            var currentPlan = TestPlanManager.Instance.GetCurrentPlan();

            // 检查是否选择硬件
            var selectedDeviceChannelName = currentPlan?.Config?.HardwareConfig?.GetSelectedDeviceChannelName();
            if (string.IsNullOrWhiteSpace(selectedDeviceChannelName))
            {
                throw new Exception("Cannot start interoperation: no device channel selected.");
            }

            var interoperationService = TestPlanManager.Instance.Interoperation;
            var interoperationModel = interoperationService.BuildInteroperationModel();
            var testResult = interoperationModel.TestResult;

            var securityAccessDllBytes = currentPlan.Config.SecurityConfig.DllBytes;
            SecurityAccessUtils.PrepareDll(securityAccessDllBytes);

            var request = new ExecutionRequest
            {
                TestResult = testResult,
                CaseResults = interoperationModel.CaseResults,
                HardwareConfig = currentPlan.Config.HardwareConfig,
                CaseConfig = currentPlan.Config.CaseConfig,
                SequencePackageXml = interoperationModel.SequencePackageXml
            };
            var requestBody = JsonConvert.SerializeObject(request);

            var content = new StringContent(requestBody, System.Text.Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync("api/tester/execute", content);
            return await HandleResponseAsync(response);
        }

        public async Task<ApiResponse> StopAsync()
        {
            var response = await _httpClient.PostAsync("api/tester/stop", null);
            return await HandleResponseAsync(response);
        }

        public async Task<ApiResponse> HandleResponseAsync(HttpResponseMessage response)
        {
            if (response.IsSuccessStatusCode)
            {
                return new ApiResponse(true, string.Empty);
            }
            else
            {
                var json = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeAnonymousType(json, new { message = string.Empty });
                return new ApiResponse(false, result.message);
            }
        }

        #region IDisposable Support
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _httpClient?.Dispose();
                }
                _disposed = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        #endregion
    }
}
