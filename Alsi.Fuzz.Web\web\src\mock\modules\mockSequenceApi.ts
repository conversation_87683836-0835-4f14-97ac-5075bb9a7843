import { mockSuccess } from '../mockApi';

// 存储当前的序列配置数据（模拟数据库）
let currentSequenceConfig = {
  testSuiteName: 'CAN-Bus Test Suite',
  sequencePackageName: 'CAN Frame examples',
  sequencePackageXml: '<sequence><name>CAN Frame examples</name><description>Example sequence</description></sequence>',
  isBuiltIn: true,
  customName: '',
  basePackageName: '',
  lastModified: '',
  creationTime: ''
};

export const mockSequenceApi = {
  // 获取序列配置
  getSequenceConfig: () => {
    return mockSuccess(currentSequenceConfig);
  },

  // 更新序列配置
  updateSequenceConfig: (config: any) => {
    // 更新当前配置
    currentSequenceConfig = {
      ...currentSequenceConfig,
      ...config
    };
    return mockSuccess(currentSequenceConfig);
  },

  // 删除自定义序列包
  deleteCustomSequencePackage: (testSuiteName: string, customName: string) => {
    // 模拟删除操作，重置为默认内建包
    currentSequenceConfig = {
      testSuiteName: 'CAN-Bus Test Suite',
      sequencePackageName: 'CAN Frame examples',
      sequencePackageXml: '<sequence><name>CAN Frame examples</name><description>Example sequence</description></sequence>',
      isBuiltIn: true,
      customName: '',
      basePackageName: '',
      lastModified: '',
      creationTime: ''
    };
    return mockSuccess(currentSequenceConfig);
  },

  // 获取自定义包列表
  getCustomPackages: (testSuiteName: string) => {
    // 模拟返回一些自定义包
    const mockCustomPackages = [
      {
        name: 'CAN Frame examples',
        customName: 'My Custom CAN Package',
        basePackageName: 'CAN Frame examples',
        isBuiltIn: false,
        testSuiteName: testSuiteName,
        lastModified: '2024-08-07T14:30:00',
        creationTime: '2024-08-07T10:15:00'
      }
    ];
    return mockSuccess(mockCustomPackages);
  }
};
