<package name="CAN Frame sequences (ISO 11898-1)">
  <set-var name="$BusType" value="canfd" />
  <set-var name="$SendFrameId" value="0x200" />
  <set-var name="$SendFrameData" value="11 22 33 44 55 66 77 88" />

  <set-var name="$RecvFrameDlc" value="15" />

  <sequence name="Basic Test">
    <send name="Send Frame">
      <frame type="$BusType" id="$SendFrameId" is-ext="false">
        <hex-data>$SendFrameData</hex-data>
      </frame>
    </send>

    <recv name="Receive Frame">
      <!--<match-frame type="$BusType" dlc="$RecvFrameDlc" />-->
      <match-frame type="$BusType" />
    </recv>
  </sequence>
</package>
