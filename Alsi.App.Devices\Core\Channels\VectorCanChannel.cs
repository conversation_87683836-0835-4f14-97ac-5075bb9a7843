using Alsi.App.Devices.Vector;

namespace Alsi.App.Devices.Core.Channels
{
    public class VectorCanChannel : ICanChannel
    {
        public VectorCanChannel(DeviceChannel deviceChannel, ChannelConfig deviceChannelConfig)
        {
            _deviceChannel = deviceChannel;
            _config = deviceChannelConfig;
        }

        private ulong? timestampDeviation = null;
        private readonly DeviceChannel _deviceChannel;
        private readonly ChannelConfig _config;

        private EncapsulationVectorCan.VectorConnectInfo VectorConnectInfo { get; set; } = new EncapsulationVectorCan.VectorConnectInfo();

        public void Initialize(bool filterErrorFrame)
        {
            VectorDeviceApi.InitializeData();

            EncapsulationVectorCan.filterErrorFrame = filterErrorFrame;
        }

        public void Release()
        {
            VectorDeviceApi.FinalizeData();
        }

        public void Start()
        {
            EncapsulationVectorCan.smttEvent += OnVectorCanReceived;

            VectorConnectInfo = EncapsulationVectorCan.TSAppStart(_deviceChannel.VectorInfo, _config, _deviceChannel.Channel);
        }

        public void Stop()
        {
            EncapsulationVectorCan.smttEvent -= OnVectorCanReceived;
            EncapsulationVectorCan.TSAppStop(_deviceChannel.VectorInfo);
        }


        public void Send(CanFrame frame)
        {
            if (frame.IsCanFd)
            {
                EncapsulationVectorCan.SendCanFDFrameData(_deviceChannel.VectorInfo, frame.Id, frame.Dlc, frame.Data, frame.Rtr, frame.IsExt);
            }
            else
            {
                EncapsulationVectorCan.SendCanFrameData(_deviceChannel.VectorInfo, frame.Id, frame.Dlc, frame.Data, frame.Rtr, frame.IsExt);
            }
        }

        private void OnVectorCanReceived(int vectorChannelIndex, CanFrame frame)
        {
            if (VectorConnectInfo.VectorChannelIndex != vectorChannelIndex)
            {
                return;
            }

            // Vector 通道，转换为应用通道
            frame.Channel = _deviceChannel.Channel;

            if (!DataBus.HardwareTimeBaseChannel.HasValue || DataBus.HardwareTimeBaseChannel == frame.Channel)
            {
                // 如当前通道，是时间戳基准通道，使用硬件时间戳，同步当前总线时间戳
                DataBus.SyncTimeBaseDeviation(frame.TimeUS, frame.Channel);
            }
            else
            {
                // 如当前通道，不是时间戳基准通道，收到首帧时，用当前总线时间戳计算当前通道时间偏差
                if (!timestampDeviation.HasValue)
                {
                    timestampDeviation = DataBus.Timestamp - frame.TimeUS;
                    AppEnv.Logger.Debug($"Initialize timestamp deviation of channel [{frame.Channel} {_deviceChannel.CommunicationType} {_deviceChannel.Name}]: {timestampDeviation.Value}");
                }

                // 用当前通道的首帧时间偏差，更新当前通道所有数据的时间戳
                frame.TimeUS += timestampDeviation.Value;
            }

            DataBus.InvokeOnTransmitted(frame);
        }
    }
}
