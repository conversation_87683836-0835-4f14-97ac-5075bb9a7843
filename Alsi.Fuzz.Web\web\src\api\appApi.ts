import axios, { AxiosResponse } from 'axios';
import { USE_MOCK, mockApi } from '@/mock/mockApi';
import { CaseResult, GroupTreeNode, MyGroupResponse } from './interoperationApi';

// 定义错误数据结构
export interface ErrorData {
  message: string;
  stack?: string;
  url: string;
  type?: string;
  vueHookInfo?: string;
  codeInfo?: string;
}

// 定义应用信息接口
export interface AppInfo {
  dataFolder: string;
  logFolder: string;
}

// 覆盖率选项枚举
export enum CoverageType {
  Normal = 0,
  High = 1
}

// 生成测试用例请求对象
export interface GenerateCasesRequest {
  coverage: CoverageType;
  sequenceNames: string[]; // 修改为与后端一致的名称
}

// 执行状态枚举 - 与后端保持一致
export enum ExecutionState {
  Paused = 'Paused',
  Pending = 'Pending',
  Running = 'Running',
  Success = 'Success',
  Failure = 'Failure',
  Completed = 'Completed'
}

// 测试结果接口
export interface TestResult {
  id: string;
  resultFolderName: string;
  testType: string;
  creationTime: string;
  completionTime?: string;
  totalCount: number;
  successCount: number;
  failureCount: number;
}

// 测试器快照接口 - 与后端 TesterSnapshot 保持一致
export interface TesterSnapshot {
  processState: ExecutionState;
  currentOperation: string;
  testResult: TestResult;
  caseResults: CaseResult[];
}

// 提供一个工具函数来实现isCompleted方法的逻辑
export function isTesterCompleted(snapshot: TesterSnapshot): boolean {
  return snapshot.processState === ExecutionState.Success ||
    snapshot.processState === ExecutionState.Failure;
}

// 新增 CaseStep 接口定义
export interface CaseStep {
  id: number;
  name: string;
  caseResultId: number;
  timestamp: number; // ulong in C#
  frameTimestamp?: number; // ulong? in C#
  state: string; // ExecutionState enum
  begin?: string; // DateTime? in C#
  end?: string; // DateTime? in C#
  detail?: string;
}





const BASE_URL = '/api/app'

export const appApi = {
  // 获取应用信息
  getAppInfo(): Promise<AxiosResponse<AppInfo>> {
    if (USE_MOCK) {
      return mockApi.app.getAppInfo();
    }
    return axios.get(`${BASE_URL}/appInfo`);
  },

  // 记录错误日志
  logError: (errorData: ErrorData) => {
    if (USE_MOCK) {
      return mockApi.app.logError(errorData);
    }
    return axios.post(`${BASE_URL}/logError`, errorData);
  },

  // 退出应用程序
  exit: () => {
    if (USE_MOCK) {
      return mockApi.app.exit();
    }
    return axios.post(`${BASE_URL}/exit`);
  },

  // 获取最新的互操作测试结果
  getLatestInteroperationCaseResults(): Promise<AxiosResponse<CaseResult[]>> {
    if (USE_MOCK) {
      return mockApi.app.getLatestInteroperationCaseResults();
    }
    return axios.get('/api/case/latest-interoperation-case-results');
  },

  // 生成测试用例 - 更新为接受sequenceNames参数
  generateCases(coverage: CoverageType, sequenceNames: string[]): Promise<AxiosResponse<CaseResult[]>> {
    const request: GenerateCasesRequest = {
      coverage,
      sequenceNames
    };

    if (USE_MOCK) {
      // mockApi.app.generateCases 接口更新为支持两个参数
      return mockApi.app.generateCases(coverage, sequenceNames);
    }

    // 后端 API 使用 SequenceNames (首字母大写)
    return axios.post('/api/case/generate-cases', {
      coverage: request.coverage,
      SequenceNames: request.sequenceNames
    });
  },

  // 获取已保存的测试用例和分组树
  getCasesWithGroup(): Promise<AxiosResponse<MyGroupResponse>> {
    if (USE_MOCK) {
      return mockApi.app.getCasesWithGroup();
    }
    return axios.get('/api/case/cases-with-group');
  },

  // 生成测试用例 - 更新为接受sequenceNames参数
  generateCasesWithGroup(coverage: CoverageType, sequenceNames: string[]): Promise<AxiosResponse<MyGroupResponse>> {
    const request: GenerateCasesRequest = {
      coverage,
      sequenceNames
    };

    if (USE_MOCK) {
      // mockApi.app.generateCasesWithGroup 接口更新为支持两个参数
      return mockApi.app.generateCasesWithGroup(coverage, sequenceNames);
    }

    // 后端 API 使用 SequenceNames (首字母大写)
    return axios.post('/api/case/generate-cases-with-group', {
      coverage: request.coverage,
      SequenceNames: request.sequenceNames
    });
  },

  // TODO: 点击根节点时，展开并生成测试用例 - 追加参数根节点id作为检索条件，可能考虑追加分页作为检索条件
  expandCases(coverage: CoverageType, sequenceNames: string[]): Promise<AxiosResponse<CaseResult[]>> {
    const request: GenerateCasesRequest = {
      coverage,
      sequenceNames
    };

    if (USE_MOCK) {
      // mockApi.app.generateCases 接口更新为支持两个参数
      return mockApi.app.generateCases(coverage, sequenceNames);
    }

    // 后端 API 使用 SequenceNames (首字母大写)
    return axios.post('/api/case/generate-cases', {
      coverage: request.coverage,
      SequenceNames: request.sequenceNames
    });
  },

  // 保存测试用例
  saveCases(cases: CaseResult[]): Promise<AxiosResponse<any>> {
    if (USE_MOCK) {
      return mockApi.case.saveCases(cases);
    }
    return axios.post('/api/case/save-cases', { cases });
  },



  // 获取保存的测试用例
  getSavedCases(): Promise<AxiosResponse<CaseResult[]>> {
    if (USE_MOCK) {
      return mockApi.app.getSavedCases();
    }
    return axios.get('/api/case/cases');
  },

  // 获取保存的测试用例
  getSavedCaseCount(): Promise<AxiosResponse<number>> {
    if (USE_MOCK) {
      return mockApi.app.getSavedCaseCount();
    }
    return axios.get('/api/case/case-count');
  },

  // 开始执行测试
  startTest(): Promise<AxiosResponse<void>> {
    if (USE_MOCK) {
      return mockApi.app.startTest();
    }
    return axios.post('/api/case/start');
  },

  // 停止测试
  stopTest(): Promise<AxiosResponse<void>> {
    if (USE_MOCK) {
      return mockApi.app.stopTest();
    }
    return axios.post('/api/case/stop');
  },

  // 暂停测试
  pauseTest(): Promise<AxiosResponse<void>> {
    if (USE_MOCK) {
      return mockApi.app.pauseTest();
    }
    return axios.post('/api/case/pause');
  },

  // 恢复测试
  resumeTest(): Promise<AxiosResponse<void>> {
    if (USE_MOCK) {
      return mockApi.app.resumeTest();
    }
    return axios.post('/api/case/resume');
  },

  // 获取测试状态
  getTestStatus(): Promise<AxiosResponse<TesterSnapshot>> {
    if (USE_MOCK) {
      return mockApi.app.getTestStatus();
    }
    return axios.get('/api/case/status');
  },

  // 获取测试结果列表
  getTestResults(): Promise<AxiosResponse<TestResult[]>> {
    if (USE_MOCK) {
      return mockApi.app.getTestResults();
    }
    return axios.get('/api/case/test-results');
  },

  // 获取测试用例列表
  getCases(testResultId: string): Promise<AxiosResponse<CaseResult[]>> {
    if (USE_MOCK) {
      return mockApi.app.getCases(testResultId);
    }
    return axios.get(`/api/case/cases?testResultId=${testResultId}`);
  },

  // 获取用例步骤列表
  getCaseSteps(testResultId: string, caseResultId: number): Promise<AxiosResponse<CaseStep[]>> {
    if (USE_MOCK) {
      return mockApi.app.getCaseSteps(testResultId, caseResultId);
    }
    return axios.get(`/api/case/case-steps?testResultId=${testResultId}&caseResultId=${caseResultId}`);
  },

  // 获取用例步骤列表
  getCaseResult(testResultId: string, caseResultId: number): Promise<AxiosResponse<CaseResult>> {
    if (USE_MOCK) {
      return mockApi.case.getCaseResult(testResultId, caseResultId);
    }
    return axios.get(`/api/case/case-result?testResultId=${testResultId}&caseResultId=${caseResultId}`);
  },

  // 删除测试结果
  deleteTestResult(testResultId: string): Promise<AxiosResponse<void>> {
    if (USE_MOCK) {
      return mockApi.app.deleteTestResult(testResultId);
    }
    return axios.delete(`/api/case/test-result?testResultId=${testResultId}`);
  },

  // 下载HTML报告
  downloadHtmlReport(testResultId: string): Promise<void> {
    if (USE_MOCK) {
      return mockApi.app.downloadHtmlReport(testResultId);
    }

    // 使用浏览器直接下载功能，而不是处理axios响应
    const url = `/api/case/generate-report?testResultId=${testResultId}`;
    return axios.post(url);
  },



  // 获取当前生成的用例数量
  getGeneratingProgress(): Promise<AxiosResponse<string>> {
    if (USE_MOCK) {
      return mockApi.case.getGeneratingProgress();
    }
    return axios.get('/api/case/generating-progress');
  },
}

export default appApi

// 导出其他API模块
export { default as sequenceApi } from './sequenceApi';
