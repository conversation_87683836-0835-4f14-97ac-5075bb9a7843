# Alsi.Fuzz 项目指南

## 项目架构

```
Alsi.Fuzz Solution
├── 主要项目
│   ├── Alsi.Fuzz - 主项目
│   ├── Alsi.Fuzz.Web - Web界面
│   ├── Alsi.Fuzz.Core - 核心库
│   └── Alsi.Fuzz.Tester - 测试工具
├── Common 文件夹
│   ├── Alsi.App.Desktop - 桌面应用公共库
│   ├── Alsi.App - 通用应用库
│   └── Alsi.App.Database - 数据库访问库
└── UnitTests 文件夹
    └── Alsi.Fuzz.UnitTests - 单元测试项目
```

## 项目配置

- 支持 Debug/Release 配置
- 支持 AnyCPU/x64 平台配置

## 项目依赖关系

- Alsi.Fuzz 可能依赖于 Alsi.Fuzz.Core
- Alsi.Fuzz.Web 可能为 Alsi.Fuzz 提供Web界面
- 公共库位于 ../Alsi.Common/ 目录

## 开发指南

开发新功能时，请遵循以下原则:
1. 核心业务逻辑应放在 Alsi.Fuzz.Core 项目中
2. UI相关代码放在 Alsi.Fuzz.Web 项目中
3. 所有核心库功能应编写单元测试

## 领域模型

### 核心概念
- **TestPlan**: 测试计划，包含测试配置和输入参数，管理多个测试序列文件的执行
- **TestSuite**: 测试套件，如CAN/CANFD有独立的测试套件，包含多个测试序列文件
- **SequencePackage**: 测试序列包，包含多个测试序列的集合
- **Sequence**: 测试序列，对应XML中的单个sequence元素，包含一系列测试步骤
- **OperationStep**: 操作步骤，如发送(send)或接收(recv)指令，是Sequence中的基本操作单位
- **TestCase**: 测试用例，每个Sequence执行生成一个测试用例结果

### 数据流
```
TestPlan -> 选择SequencePackage -> 执行Sequence -> 执行OperationStep -> 生成TestCase结果 -> 存储本地结果
```

### 序列执行模型
- **序列包(SequencePackage)**: 逻辑上关联的测试序列集合，可来源于文件或其他数据源
- **测试序列(Sequence)**: 针对特定功能的测试定义，如"DiagnosticSessionControl"
- **操作步骤(OperationStep)**: 序列中的具体操作，主要包括:
  - **发送步骤(SendStep)**: 向被测设备发送指令
  - **接收步骤(ReceiveStep)**: 从被测设备接收响应
  - **导入步骤(ImportStep)**: 引入其他序列包中的步骤
  - **前导步骤(PreambleStep)**: 在序列开始执行的预备步骤

### 测试模型
- **预测试**: 检测设备支持的服务并标记
- **用例生成**: 根据预测试结果生成针对性测试用例
- **用例执行**: 执行测试用例并收集结果
- **结果分析**: 分析测试结果，支持失败重试

### 关键接口
- **通信接口**: 与CAN/CANFD设备通信
- **存储接口**: 本地存储测试结果和日志
- **用户界面**: 配置测试参数，展示测试进度和结果

## 功能需求

### 通信协议支持
- 支持 CAN/CANFD 协议
- 暂不支持 LIN 和 ETH 协议

### 测试用例
- ISO 11898: 预置23-24个用例，使用固定的non-white list数据
- ISO 14229: 预置1个用例，需指定SID进行数据异变

### 测试流程
- 使用TestPlan管理测试输入和配置（不包含测试结果）
- TestPlan从多个.seq文件中选择执行
- 每个.seq包含多个顺序执行的sequence，每次执行生成一个用例结果
- 预测试后筛选支持的服务并标记，展示测试用例列表
- 用户可对测试用例列表进行筛选，选择需要执行的用例
- 系统根据筛选后的列表生成针对性测试用例并执行
- 测试失败后支持重试
- 测试结果存储在本地，默认名称为"时间+预测试名称"
- 只存储关联ID的日志

### 系统性能
- 约6万条常见用例，完整运行约需13小时
