<template>
  <div class="test-suite-container">
    <!-- 左侧测试套件列表 -->
    <div class="suites-list">
      <el-card v-for="suite in testSuites" :key="suite.name" class="suite-card"
        :class="{ 'active': selectedSuite?.name === suite.name }" @click="handleSuiteClick(suite)">
        <template #header>
          <div class="suite-header">
            <h3>{{ suite.name }}</h3>
            <span class="version">v{{ suite.version }}</span>
          </div>
        </template>
        <div class="packages-list">
          <div v-for="(sequencePackage, index) in suite.packages" :key="index" class="package-item"
            :class="{ 'active': selectedSuite?.name === suite.name && selectedPackageIndex === index }"
            @click.stop="handlePackageClick(suite, index, sequencePackage)">
            <el-icon>
              <Document />
            </el-icon>
            <span>{{ sequencePackage.name }}</span>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 右侧XML查看器 -->
    <XmlViewer readonly v-if="selectedSuite" :title="`${selectedSuite.name} - ${selectedPackageName}`" :content="xmlContent" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import XmlViewer from '@/components/TestSuite/XmlViewer.vue'
import { testSuiteApi, type TestSuite, type SequencePackage } from '@/api/testSuiteApi'

const testSuites = ref<TestSuite[]>([])
const selectedSuite = ref<TestSuite | null>(null)
const xmlContent = ref('')
const selectedPackageIndex = ref<number>(-1)
const selectedPackageName = ref('')

const loadTestSuites = async () => {
  try {
    const response = await testSuiteApi.getBuiltIn()
    testSuites.value = response.data
  } catch (error) {
    ElMessage.error('Failed to load test suites')
    console.error('Error loading test suites:', error)
  }
}

const handleSuiteClick = async (suite: TestSuite) => {
  if (selectedSuite.value?.name === suite.name) return
  selectedSuite.value = suite
  selectedPackageIndex.value = -1
  selectedPackageName.value = ''
  xmlContent.value = ''
}

const handlePackageClick = async (suite: TestSuite, index: number, sequencePackage: SequencePackage) => {
  selectedSuite.value = suite
  selectedPackageIndex.value = index
  selectedPackageName.value = sequencePackage.name
  try {
    const response = await testSuiteApi.getBuiltInXml(suite.name, sequencePackage.name)
    xmlContent.value = response.data
  } catch (error) {
    ElMessage.error('Failed to load XML content')
    console.error('Error loading XML:', error)
  }
}

onMounted(() => {
  loadTestSuites()
})
</script>

<style scoped lang="scss">
.test-suite-container {
  padding: 20px;
  display: flex;
  gap: 20px;
  height: calc(100vh - 80px);
}

.suites-list {
  width: 300px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.suite-card {
  cursor: pointer;

  &.active {
    border-color: var(--el-color-primary);
  }
}

.suite-header {
  display: flex;
  align-items: center;
  gap: 10px;

  h3 {
    margin: 0;
    flex: 1;
  }

  .version {
    color: var(--el-text-color-secondary);
    font-size: 0.9em;
  }
}

.packages-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.package-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 4px;

  &:hover {
    background-color: var(--el-fill-color-light);
  }

  .el-icon {
    color: var(--el-text-color-secondary);
  }

  &.active {
    background-color: var(--el-color-primary-light-9);
    color: var(--el-color-primary);

    .el-icon {
      color: var(--el-color-primary);
    }
  }
}
</style>
