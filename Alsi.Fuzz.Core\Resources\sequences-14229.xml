<package name="CAN Unified Diagnostic Services (ISO 14229-1)">
  <!-- DiagnosticSessionControl (0x10) service - Default Session -->
  <sequence name="DiagnosticSessionControl (0x10) service - Default Session">
    <send-diag hex-payload="10 01" />
    <recv-diag>
      <match hex-payload="50 01 ** ** ** **" />
    </recv-diag>
  </sequence>

  <!-- DiagnosticSessionControl (0x10) service - Programming Session -->
  <sequence name="DiagnosticSessionControl (0x10) service - Programming Session">
    <send-diag hex-payload="10 02" />
    <recv-diag>
      <match hex-payload="50 02 ** ** ** **" />
    </recv-diag>
  </sequence>

  <!-- ECUReset (0x11) service -->
  <sequence name="ECUReset (0x11) service">
    <send-diag hex-payload="11 01" />
    <recv-diag>
      <match hex-payload="51 01" />
    </recv-diag>
    <wait timeout-ms="2000" />
  </sequence>

  <!-- ClearDiagnosticInformation (0x14) Service -->
  <sequence name="ClearDiagnosticInformation (0x14) Service">
    <send-diag hex-payload="14 FF FF FF" />
    <recv-diag>
      <match hex-payload="54" />
    </recv-diag>
  </sequence>

  <!-- ReadDTCInformation (0x19) Service -->
  <sequence name="ReadDTCInformation (0x19) Service">
    <send-diag hex-payload="19 02 00" />
    <recv-diag>
      <match hex-payload="59 02 **" />
    </recv-diag>
  </sequence>

  <!-- ReadDataByIdentifier (0x22) service -->
  <sequence name="ReadDataByIdentifier (0x22) service">
    <send-diag hex-payload="22 F1 86" />
    <recv-diag>
      <match hex-payload="62 F1 86 **" />
    </recv-diag>
  </sequence>

  <!-- ReadMemoryByAddress (0x23) service -->
  <sequence name="ReadMemoryByAddress (0x23) service">
    <send-diag hex-payload="23 44 00 00 00 01 00 00 00 01" />
    <recv-diag>
      <match hex-payload="63 **" />
    </recv-diag>
  </sequence>

  <!-- ReadScalingDataByIdentifier (0x24) service -->
  <sequence name="ReadScalingDataByIdentifier (0x24) service">
    <send-diag hex-payload="24 F1 90" />
    <recv-diag>
      <match hex-payload="64 F1 90 6F 62" />
    </recv-diag>
  </sequence>

  <!-- calculate key by the seed-->
  <set-var name="$SaSeedLevel" value="01" />
  <set-var name="$SaKeyLevel" value="02" />
  <set-var name="$SaSeed" value="" />
  <set-var name="$SaKey" value="" />
  <set-var name="$KeyLength" value="3" />

  <!-- SecurityAccess (0x27) service: request seed -->
  <sequence name="SecurityAccess (0x27) service: request seed">
    <send-diag hex-payload="10 02" mutate="false" />
    <recv-diag>
      <match hex-payload="50 02 ** ** ** **" />
    </recv-diag>

    <send-diag hex-payload="27 $SaSeedLevel" />
    <recv-diag>
      <match hex-payload="67 $SaSeedLevel ** ** **">
        <set-var name="$SaSeed" index="2" length="$KeyLength" />
      </match>
    </recv-diag>

    <print text="The security access seed: $SaSeed" />
  </sequence>

  <!-- SecurityAccess (0x27) service: send key -->
  <sequence name="SecurityAccess (0x27) service: send key">
    <send-diag hex-payload="10 02" mutate="false" />
    <recv-diag>
      <match hex-payload="50 02 ** ** ** **" />
    </recv-diag>

    <send-diag hex-payload="27 $SaSeedLevel" mutate="false" />
    <recv-diag>
      <match hex-payload="67 $SaSeedLevel ** ** **">
        <set-var name="$SaSeed" index="2" length="$KeyLength" />
      </match>
    </recv-diag>

    <print text="The security access seed: $SaSeed" />

    <calc-key seed="$SaSeed" hex-level="$SaSeedLevel">
      <set-var name="$SaKey" index="0" />
    </calc-key>

    <print text="The security access key: $SaKey" />

    <!-- send key -->
    <send-diag hex-payload="27 $SaKeyLevel $SaKey" />
    <recv-diag>
      <match hex-payload="67 $SaKeyLevel" />
    </recv-diag>
  </sequence>

  <!-- CommunicationControl (0x28) service -->
  <sequence name="CommunicationControl (0x28) service">
    <send-diag hex-payload="28 01 02" />
    <recv-diag>
      <match hex-payload="68 01" />
    </recv-diag>
  </sequence>

  <!-- Authentication (0x29) service -->
  <sequence name="Authentication (0x29) service">
    <send-diag hex-payload="29 00" />
    <recv-diag>
      <match hex-payload="69 00 **" />
    </recv-diag>
  </sequence>

  <!-- ReadDataByPeriodicIdentifier (0x2A) service -->
  <sequence name="ReadDataByPeriodicIdentifier (0x2A) service">
    <send-diag hex-payload="2A 02 E3 24" />
    <recv-diag>
      <match hex-payload="6A" />
    </recv-diag>
  </sequence>

  <!-- DynamicallyDefineDataIdentifier (0x2C) service -->
  <sequence name="DynamicallyDefineDataIdentifier (0x2C) service">
    <send-diag hex-payload="2C 03 F3 03" />
    <recv-diag>
      <match hex-payload="6C 03 F3 03" />
    </recv-diag>
  </sequence>

  <!-- WriteDataByIdentifier (0x2E) service -->
  <sequence name="WriteDataByIdentifier (0x2E) service">
    <send-diag hex-payload="2E D0 36 CC" />
    <recv-diag>
      <match hex-payload="6E ** **" />
    </recv-diag>
  </sequence>

  <!-- InputOutputControlByIdentifier (0x2F) service -->
  <sequence name="InputOutputControlByIdentifier (0x2F) service">
    <send-diag hex-payload="2F 9B 03 3C" />
    <recv-diag>
      <match hex-payload="6F 9B 03 0C" />
    </recv-diag>
  </sequence>

  <!-- RoutineControl (0x31) Service -->
  <sequence name="RoutineControl (0x31) Service">
    <send-diag hex-payload="31 01 02 06" />
    <recv-diag>
      <match hex-payload="71 01 02 06 10 01" />
    </recv-diag>
  </sequence>

  <!-- WriteMemoryByAddress (0x3D) service -->
  <sequence name="WriteMemoryByAddress (0x3D) service">
    <send-diag hex-payload="3D 12 20 48 02 00 8C" />
    <recv-diag>
      <match hex-payload="7D 12 20 48 02" />
    </recv-diag>
  </sequence>

  <!-- TesterPresent (0x3E) service -->
  <sequence name="TesterPresent (0x3E) service">
    <send-diag hex-payload="10 02" mutate="false" />
    <recv-diag>
      <match hex-payload="50 02 ** ** ** **" />
    </recv-diag>

    <send-diag hex-payload="3E 00" />
    <recv-diag>
      <match hex-payload="7E **" />
    </recv-diag>
  </sequence>

  <!-- SecuredDataTransmission (0x84) service -->
  <!-- If the service is supported, request and response customize according to the DUT context is strongly recommanded -->
  <sequence name="SecuredDataTransmission (0x84) service">
    <send-diag hex-payload="84 00" />
    <recv-diag>
      <match hex-payload="7F 84 7F" />
    </recv-diag>
  </sequence>

  <!-- ControlDTCSetting (0x85) service -->
  <sequence name="ControlDTCSetting (0x85) service">
    <send-diag hex-payload="85 01" />
    <recv-diag>
      <match hex-payload="C5 01" />
    </recv-diag>
  </sequence>

  <!-- ResponseOnEvent (0x86) service -->
  <sequence name="ResponseOnEvent (0x86) service">
    <send-diag hex-payload="86 05 08" />
    <recv-diag>
      <match hex-payload="C6 01 00 08" />
    </recv-diag>
  </sequence>

  <!-- LinkControl (0x87) service -->
  <sequence name="LinkControl (0x87) service">
    <send-diag hex-payload="87 01 05" />
    <recv-diag>
      <match hex-payload="C7 01" />
    </recv-diag>
  </sequence>
</package>
