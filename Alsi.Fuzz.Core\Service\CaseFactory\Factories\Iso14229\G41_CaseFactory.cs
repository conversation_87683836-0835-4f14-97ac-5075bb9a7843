using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts;
using Alsi.Fuzz.Core.Service.CaseFactory.Iso14229;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using static Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.G41_CaseFactory;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G41_CaseFactory : CaseFactoryBase
    {
        public class IdExt
        {
            public IdExt(int id, bool isExt)
            {
                Id = id;
                IsExt = isExt;
            }

            public int Id { get; set; }
            public bool IsExt { get; set; }
        }

        private List<IdExt> GenerateIdExtList(MutationOptions options)
        {
            var list = new List<IdExt>();
            // 11bit
            for (var id = 0; id <= 0x7FF; ++id)
            {
                list.Add(new IdExt(id, false));
            }

            // 29bit
            var ids = new UniformSampler(options.Random)
                .UniformSample(0x10000000, 0x1FFFFFFF, 512);
            foreach (var id in ids)
            {
                list.Add(new IdExt(id, true));
            }

            // 29bit
            for (var id = 0; id <= 0x7FF; ++id)
            {
                list.Add(new IdExt(id, true));
            }

            list.Add(new IdExt(0x1FFFFFFF, true));
            return list;
        }

        public override void Generate(MutationOptions options, Action<CreateCaseInfo> createCase)
        {
            var xmlServices = options.XmlServices;
            var supportedXmlServicesWithSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == true)
                .ToArray();

            var testedSidList = new List<byte>();

            foreach (var xmlServiceWithSubfunction in supportedXmlServicesWithSubfunction)
            {
                var sid = xmlServiceWithSubfunction.Id;
                var subfunctionId = xmlServiceWithSubfunction.SubfunctionId.Value;
                var parameter2k = xmlServiceWithSubfunction.Parameter2k;
                var serviceName = xmlServiceWithSubfunction.IsoUdsServiceDisplayName;
                
                testedSidList.Add(sid);

                var payload = new List<byte> { sid, subfunctionId };
                payload.AddRange(parameter2k);

                foreach (var idExt in GenerateIdExtList(options))
                {
                    var groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat.GetService(serviceName).ArbitraryField().isExtendedCanId(idExt.IsExt).Path;
                    var name = $"xmlServiceWithSubfunction {serviceName}-{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .MutateId(idExt.Id)
                        .MutateExt(idExt.IsExt)
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G411-Sid{sid:X2}", caseMutation, sid, subfunctionId));
                }
            }

            var supportedXmlServicesWithoutSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => !x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历没有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == false)
                .ToArray();

            foreach (var xmlServiceWithoutSubfunction in supportedXmlServicesWithoutSubfunction)
            {
                var sid = xmlServiceWithoutSubfunction.Id;
                var parameter2k = xmlServiceWithoutSubfunction.Parameter2k;
                var serviceName = xmlServiceWithoutSubfunction.IsoUdsServiceDisplayName;

                testedSidList.Add(sid);

                var payload = new List<byte> { sid };
                payload.AddRange(parameter2k);

                foreach (var idExt in GenerateIdExtList(options))
                {
                    var groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat.GetService(serviceName).ArbitraryField().isExtendedCanId(idExt.IsExt).Path;
                    var name = $"xmlServiceWithoutSubfunction {serviceName}-{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .MutateId(idExt.Id)
                        .MutateExt(idExt.IsExt)
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G412-Sid{sid:X2}", caseMutation, sid, xmlServiceWithoutSubfunction.SubfunctionId));
                }
            }

            for (var otherSid = 0; otherSid <= 0xFF; otherSid++)
            {
                if (!IsoUdsConsts.Services.Any(service => otherSid == service.Id))
                {
                    continue;
                }

                if (testedSidList.Contains((byte)otherSid))
                {
                    continue;
                }

                var payload = new List<byte> { (byte)otherSid };

                foreach (var idExt in GenerateIdExtList(options))
                {
                    var groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat.GetService("OtherSid").ArbitraryField().isExtendedCanId(idExt.IsExt).Path;
                    var name = $"otherSid {otherSid:X2}-{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .MutateId(idExt.Id)
                        .MutateExt(idExt.IsExt)
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G413-Sid{otherSid:X2}", caseMutation, (byte)otherSid, null));
                }
            }

            foreach (var xmlServiceWithSubfunction in supportedXmlServicesWithSubfunction)
            {
                var sid = xmlServiceWithSubfunction.Id;
                var subfunctionId = xmlServiceWithSubfunction.SubfunctionId.Value;
                var parameter2k = xmlServiceWithSubfunction.Parameter2k;
                var serviceName = xmlServiceWithSubfunction.IsoUdsServiceDisplayName;
                var groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat.GetService(serviceName).ArbitraryField().StandardCanId().Path;
                var payload = new List<byte> { sid, subfunctionId };
                payload.AddRange(parameter2k);

                foreach (var repeat in new[] { 10, 50, 500 })
                {       
                    var name = $"xmlServiceWithSubfunction {serviceName} repeat{repeat}-{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .MutateId(1)
                        .MutateExt(false)
                        .Mutate(MutationFieldType.UDS_Repeat_Frame, repeat.ToString())
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G414-Sid{sid:X2}", caseMutation, sid, subfunctionId));
                }
            }

            foreach (var xmlServiceWithoutSubfunction in supportedXmlServicesWithoutSubfunction)
            {
                var sid = xmlServiceWithoutSubfunction.Id;
                var parameter2k = xmlServiceWithoutSubfunction.Parameter2k;
                var serviceName = xmlServiceWithoutSubfunction.IsoUdsServiceDisplayName;
                testedSidList.Add(sid);

                var payload = new List<byte> { sid };
                payload.AddRange(parameter2k);

                foreach (var repeat in new[] { 10, 50, 500 })
                {
                    var groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat.GetService(serviceName).ArbitraryField().StandardCanId().Path;
                    var name = $"xmlServiceWithoutSubfunction {serviceName} repeat{repeat}-{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .MutateId(1)
                        .MutateExt(false)
                        .Mutate(MutationFieldType.UDS_Repeat_Frame, repeat.ToString())
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G415-Sid{sid:X2}", caseMutation, sid, xmlServiceWithoutSubfunction.SubfunctionId));
                }
            }
        }

    }
}
