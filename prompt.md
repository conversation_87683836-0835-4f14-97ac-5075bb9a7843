# 指令一览

我和你的沟通，遵循以下标准：
- 【xxx】格式表示这是一条指令，如【分析代码库】
- 每个指令都有其特定的输入和输出格式要求
- 指令执行时需要严格遵循相应的规范

以下是指令列表

## 【分析代码库】
以下是代码分析的具体要求：

1. 准备工作
   - 确认能访问 copilot.md 文档，并解析其中的：
     * 项目架构和目录结构
     * 项目配置和依赖关系
     * 领域模型和核心概念
     * 功能需求和约束
   - 确认能访问所有源代码文件
   - 如有文件无法访问，立即报告

2. 分析范围
   - 项目架构：
     * 对照copilot.md中的架构设计进行验证
     * 核心模块结构和依赖关系确认
     * 项目间的引用关系核实
     * 共享库使用情况对比
     * 版本控制策略
   - 代码实现：
     * 关键设计模式和规范
     * 代码组织方式
   - 技术栈：
     * 框架选型和开发工具
     * 第三方库依赖
     * 开发环境要求

3. 分析输出
   - 基础信息
     * 项目名称和版本
     * 核心功能描述（基于copilot.md中的定义）
     * 支持的平台和环境
   - 技术评估
     * 对照copilot.md的需求完成度评估
     * 关键发现（最多3点）
     * 技术栈清单
   - 代码质量
     * 代码组织评估（对照copilot.md规范）

注意事项：
1. 仅分析代码，不要修改任何文件，包括 prompt.md
2. 文件访问报告格式：
   ```
   文件访问状态：
   - copilot.md: [可访问/不可访问]
   - 源代码文件: [可访问/不可访问]
     * 可访问目录：[目录列表]
     * 文件类型：[.cs/.md等]
   ```
3. 保持分析结果简洁，突出重点
4. 优先关注核心业务代码
