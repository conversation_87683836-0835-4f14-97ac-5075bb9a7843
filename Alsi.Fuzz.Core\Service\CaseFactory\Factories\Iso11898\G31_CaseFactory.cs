using Alsi.Common.Utils.Autosar;
using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Iso11898;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso11898
{
    public class G31_CaseFactory
    {
        public CaseMutation[] Generate(MutationOptions options, WhiteListFrame whiteListFrame)
        {
            var list = new List<CaseMutation>();

            if (whiteListFrame == null || whiteListFrame.WhiteListFrameSignals == null)
            {
                return list.ToArray();
            }

            foreach (var WhiteListFrameSignal in whiteListFrame.WhiteListFrameSignals)
            {
                var bitLength = WhiteListFrameSignal.Length;
                var startBit = WhiteListFrameSignal.StartBit;
                var sampleCount = 0;

                if (bitLength < 3)
                {
                    sampleCount = 1 << bitLength;
                }
                else if (bitLength <= 8)
                {
                    if (options.Coverage == CoverageType.Normal)
                    {
                        sampleCount = 1 << (bitLength - 1);
                    }
                    else if (options.Coverage == CoverageType.High)
                    {
                        sampleCount = 1 << bitLength;
                    }
                    else
                    {
                        //do nothing;
                    }
                }
                else
                {
                    if (options.Coverage == CoverageType.Normal)
                    {
                        sampleCount = 1 << 8;
                    }
                    else if (options.Coverage == CoverageType.High)
                    {
                        sampleCount = 1 << 9;
                    }
                    else
                    {
                        //do nothing;
                    }
                }

                var sampleBitsArrayInPayload = UniformSampleBitsInPayload(WhiteListFrameSignal.Length, sampleCount, WhiteListFrameSignal.StartBit, DlcUtils.GetDataLength(whiteListFrame.Dlc));
                var j = 0;
                foreach (var signalInPayload in sampleBitsArrayInPayload)
                {
                    j++;
                    var mutation = CaseMutation.Create($"G31-ID_0x{whiteListFrame.Id:X}_Signal_{WhiteListFrameSignal.Name}_StartBit_{WhiteListFrameSignal.StartBit}_Length_{WhiteListFrameSignal.Length}_index_{j}")
                        .MutateId(whiteListFrame.Id)
                        .MutateDlc(whiteListFrame.Dlc)
                        .MutateRtr(0)
                        .MutateExt(whiteListFrame.IsExt)
                        .MutateData(signalInPayload);

                    list.Add(mutation);
                }
            }

            return list.ToArray();
        }
        public (CaseMutation[],List<string> groupPaths) GenerateWithGroup(MutationOptions options, WhiteListFrame whiteListFrame)
        {
            var list = new List<CaseMutation>();
            var groupPaths = new List<string>();

            if (whiteListFrame == null || whiteListFrame.WhiteListFrameSignals == null)
            {
                return (list.ToArray(), groupPaths);
            }
            var isTx = whiteListFrame.Transmitter == options.SelectedNodeName;

            foreach (var WhiteListFrameSignal in whiteListFrame.WhiteListFrameSignals)
            {
                var bitLength = WhiteListFrameSignal.Length;
                var startBit = WhiteListFrameSignal.StartBit;
                var sampleCount = 0;


                if (bitLength < 3)
                {
                    sampleCount = 1 << bitLength;
                }
                else if (bitLength <= 8)
                {
                    if (options.Coverage == CoverageType.Normal)
                    {
                        sampleCount = 1 << (bitLength - 1);
                    }
                    else if (options.Coverage == CoverageType.High)
                    {
                        sampleCount = 1 << bitLength;
                    }
                    else
                    {
                        //do nothing;
                    }
                }
                else
                {
                    if (options.Coverage == CoverageType.Normal)
                    {
                        sampleCount = 1 << 8;
                    }
                    else if (options.Coverage == CoverageType.High)
                    {
                        sampleCount = 1 << 9;
                    }
                    else
                    {
                        //do nothing;
                    }
                }

                var sampleBitsArrayInPayload = UniformSampleBitsInPayload(WhiteListFrameSignal.Length, sampleCount, WhiteListFrameSignal.StartBit, DlcUtils.GetDataLength(whiteListFrame.Dlc));
                var j = 0;
                var groupPath = Iso11898CaseGroupConsts.WhiteListMessages.GetTxOrRxMessages(isTx).SignalPath(whiteListFrame.Name, 0, WhiteListFrameSignal.Name);
                groupPaths.Add(groupPath);

                foreach (var signalInPayload in sampleBitsArrayInPayload)
                {
                    j++;
                    var name = $"index_{j:X}";
                    var mutation = CaseMutation.Create(name,groupPath)
                        .MutateId(whiteListFrame.Id)
                        .MutateDlc(whiteListFrame.Dlc)
                        .MutateRtr(0)
                        .MutateExt(whiteListFrame.IsExt)
                        .MutateData(signalInPayload);
                    list.Add(mutation);
                }
            }

            return (list.ToArray(), groupPaths);
        }

        // 生成均匀分布的比特数组样本,并存储于特定位置
        public static byte[][] UniformSampleBitsInPayload(int bitArraySize, int sampleCount, int startBit, int payloadLength)
        {
            if (sampleCount < 2)
            {
                throw new ArgumentException("样本数量必须至少为2");
            }

            int byteArraySize = (bitArraySize + 7) / 8;
            byte[][] samples = new byte[sampleCount][];
            for (var i = 0; i < samples.Length; i++)
            {
                samples[i] = new byte[payloadLength];
            }

            // 最大值 (2^bitArraySize - 1)
            int startBitFixed = (startBit / 8 * 8) + (7 - startBit % 8);
            int shiftNum = payloadLength * 8 - (startBitFixed + bitArraySize);
            BigInteger maxValue = ((BigInteger.One << bitArraySize) - 1);
            BigInteger maxValueInPayload = maxValue << shiftNum;
            // 第一个样本是全0
            samples[0] = new byte[payloadLength];

            // 最后一个样本是全1（高位多余bit清零）
            var maxBytesInPayload = ConvertToByteArray(maxValueInPayload, payloadLength);
            samples[sampleCount - 1] = maxBytesInPayload;

            // 计算间隔
            BigInteger interval = maxValue / (sampleCount - 1);

            // 生成中间的样本
            for (var i = 1; i < sampleCount - 1; i++)
            {
                var signalValue = interval * i;
                var payloadValue = signalValue << shiftNum;

                var bytes = ConvertToByteArray(payloadValue, payloadLength);
                samples[i] = bytes;
            }
            return samples;
        }
        private static byte[] ConvertToByteArray(BigInteger value, int length)
        {
            // 获取BigInteger的字节表示（小端格式）
            byte[] bytes = value.ToByteArray();
            byte[] result = new byte[length];

            // 去除可能存在的符号位
            var sourceLength = bytes.Length;
            if (bytes.Length > 0 && bytes[bytes.Length - 1] == 0)
            {
                sourceLength--;
            }

            // 将字节复制到结果数组，同时反转字节顺序以获得大端格式
            var sourceToCopy = Math.Min(sourceLength, length);

            // 先截取需要的字节，逆序后复制到 result 的末尾
            var reversed = bytes.Take(sourceToCopy).Reverse().ToArray();
            Array.Copy(reversed, 0, result, length - sourceToCopy, sourceToCopy);

            return result;
        }
    }
}
