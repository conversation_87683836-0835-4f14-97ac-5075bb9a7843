using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Models.TestSuites;
using Alsi.Fuzz.Core.Service.Results;
using System.Collections.Generic;

namespace Alsi.Fuzz.Tester.Testers
{
    public class CaseContext
    {
        public CaseContext(List<EnvVar> envVars, TestResult testResult, CaseResult caseResult, CaseConfig caseConfig, HardwareConfig hardwareConfig)
        {
            EnvVars = envVars;
            TestResult = testResult;
            CaseResult = caseResult;
            CaseConfig = caseConfig;
            HardwareConfig = hardwareConfig;
        }

        public List<EnvVar> EnvVars { get; }
        public TestResult TestResult { get; }
        public CaseResult CaseResult { get; }
        public CaseConfig CaseConfig { get; }
        public HardwareConfig HardwareConfig { get; }
        public List<CaseStep> CaseSteps { get; } = new List<CaseStep>();
    }
}
