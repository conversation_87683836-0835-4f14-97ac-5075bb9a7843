"use strict";(self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[]).push([[734],{2971:function(e,t,s){s.d(t,{A:function(){return o}});var l=s(6768),a=s(4232),n=s(1021),i=(0,l.pM)({__name:"CaseStateTag",props:{state:{}},setup(e){const t=e,s=(0,l.EW)((()=>{switch(t.state){case n.si.Success:return"success";case n.si.Running:return"warning";case n.si.Failure:return"danger";case n.si.Pending:default:return"info"}})),i=e=>{switch(e){case n.si.Running:return"Running";case n.si.Pending:return"Not Run";case n.si.Success:return"Passed";case n.si.Failure:return"Failed";default:return"Unknown"}},c=(0,l.EW)((()=>i(t.state)));return(e,t)=>{const n=(0,l.g2)("el-tag");return(0,l.uX)(),(0,l.Wv)(n,{type:s.value,size:"small",style:{"min-width":"60px"}},{default:(0,l.k6)((()=>[(0,l.eW)((0,a.v_)(c.value),1)])),_:1},8,["type"])}}});const c=i;var o=c},3855:function(e,t,s){s.d(t,{A:function(){return z}});var l=s(6768),a=s(4232),n=s(144),i=s(1219),c=s(1021);const o=e=>{switch(e){case c.si.Success:return"success";case c.si.Running:return"primary";case c.si.Failure:return"danger";case c.si.Pending:default:return"info"}};var r=s(2971),u=s(4441);const d={key:0,class:"loading"},h={key:1,class:"case-detail-content"},m={class:"basic-info"},v={class:"info-grid"},p={key:0,class:"info-item"},g={class:"value"},k={key:1,class:"info-item"},f={class:"value"},b={class:"info-item"},C={class:"value"},y={class:"info-item"},L={class:"value status-combined"},w={key:2,class:"info-item full-width"},R=["title"],x={key:3,class:"info-item full-width"},S=["title"],E={class:"steps-section"},_={key:0,class:"no-steps"},F={class:"step-content"},I={class:"step-row"},H={class:"step-left"},N={class:"step-timestamp"},T=["title"],X=["title"],D={class:"step-right"},W={class:"dialog-footer"};var M=(0,l.pM)({__name:"CaseDetailDialog",props:{visible:{type:Boolean},testResultId:{},caseResultId:{}},emits:["update:visible","close"],setup(e,{emit:t}){const s=e,M=t,K=(0,n.KR)(s.visible),$=(0,n.KR)(null),z=(0,n.KR)([]),V=(0,n.KR)(!1);(0,l.wB)((()=>s.visible),(e=>{K.value=e,e&&s.testResultId&&s.caseResultId&&P()})),(0,l.wB)((()=>K.value),(e=>{M("update:visible",e),e||M("close")}));const P=async()=>{if(s.testResultId&&s.caseResultId){V.value=!0;try{const[e,t]=await Promise.all([c.GQ.getCaseResult(s.testResultId,s.caseResultId),c.GQ.getCaseSteps(s.testResultId,s.caseResultId)]);$.value=e.data,z.value=t.data}catch(e){console.error("Failed to load case data:",e),i.nk.error("Failed to load case details")}finally{V.value=!1}}else i.nk.warning("Missing required parameters")},Q=()=>{K.value=!1,$.value=null,z.value=[]},A=e=>{if(!e&&0!==e)return"N/A";const t=e/1e6;return`${t.toFixed(6)}`};return(0,l.sV)((()=>{K.value&&s.testResultId&&s.caseResultId&&P()})),(e,t)=>{const s=(0,l.g2)("el-skeleton"),i=(0,l.g2)("el-empty"),M=(0,l.g2)("el-timeline-item"),P=(0,l.g2)("el-timeline"),B=(0,l.g2)("el-button"),U=(0,l.g2)("el-dialog");return(0,l.uX)(),(0,l.Wv)(U,{modelValue:K.value,"onUpdate:modelValue":t[0]||(t[0]=e=>K.value=e),title:`${$.value?.name||$.value?.sequenceName||""}`,width:"60%","destroy-on-close":""},{footer:(0,l.k6)((()=>[(0,l.Lk)("span",W,[(0,l.bF)(B,{onClick:Q},{default:(0,l.k6)((()=>t[9]||(t[9]=[(0,l.eW)("Close")]))),_:1})])])),default:(0,l.k6)((()=>[V.value?((0,l.uX)(),(0,l.CE)("div",d,[(0,l.bF)(s,{rows:10,animated:""})])):((0,l.uX)(),(0,l.CE)("div",h,[(0,l.Lk)("div",m,[t[7]||(t[7]=(0,l.Lk)("h4",null,"Information",-1)),(0,l.Lk)("div",v,[$.value?.name?((0,l.uX)(),(0,l.CE)("div",p,[t[1]||(t[1]=(0,l.Lk)("div",{class:"label"},"Case Name:",-1)),(0,l.Lk)("div",g,(0,a.v_)($.value?.name),1)])):(0,l.Q3)("",!0),$.value?.name?((0,l.uX)(),(0,l.CE)("div",k,[t[2]||(t[2]=(0,l.Lk)("div",{class:"label"},"Sequence Name:",-1)),(0,l.Lk)("div",f,(0,a.v_)($.value?.sequenceName),1)])):(0,l.Q3)("",!0),(0,l.Lk)("div",b,[t[3]||(t[3]=(0,l.Lk)("div",{class:"label"},"Start Time:",-1)),(0,l.Lk)("div",C,(0,a.v_)((0,n.R1)(u.r)($.value?.begin)),1)]),(0,l.Lk)("div",y,[t[4]||(t[4]=(0,l.Lk)("div",{class:"label"},"End Time / Status:",-1)),(0,l.Lk)("div",L,[(0,l.eW)((0,a.v_)((0,n.R1)(u.r)($.value?.end))+" ",1),(0,l.bF)(r.A,{state:$.value?.state||"",class:"status-tag"},null,8,["state"])])]),$.value?.parameter?((0,l.uX)(),(0,l.CE)("div",w,[t[5]||(t[5]=(0,l.Lk)("div",{class:"label"},"Parameter:",-1)),(0,l.Lk)("div",{class:"value",title:$.value?.parameter},(0,a.v_)($.value?.parameter),9,R)])):(0,l.Q3)("",!0),$.value?.detail?((0,l.uX)(),(0,l.CE)("div",x,[t[6]||(t[6]=(0,l.Lk)("div",{class:"label"},"Detail:",-1)),(0,l.Lk)("div",{class:"value",title:$.value?.detail},(0,a.v_)($.value.detail),9,S)])):(0,l.Q3)("",!0)])]),(0,l.Lk)("div",E,[t[8]||(t[8]=(0,l.Lk)("h4",null,"Steps",-1)),0===z.value.length?((0,l.uX)(),(0,l.CE)("div",_,[(0,l.bF)(i,{description:"No steps available"})])):((0,l.uX)(),(0,l.Wv)(P,{key:1},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(z.value,(e=>((0,l.uX)(),(0,l.Wv)(M,{key:e.id,type:(0,n.R1)(o)(e.state),hollow:"Success"!==e.state},{default:(0,l.k6)((()=>[(0,l.Lk)("div",F,[(0,l.Lk)("div",I,[(0,l.Lk)("div",H,[(0,l.Lk)("span",N,(0,a.v_)(A(e.timestamp)),1),(0,l.Lk)("span",{class:"step-name",title:e.name},(0,a.v_)(e.name),9,T),e.detail?((0,l.uX)(),(0,l.CE)("span",{key:0,title:e.detail,class:"step-detail-inline"},(0,a.v_)(e.detail),9,X)):(0,l.Q3)("",!0)]),(0,l.Lk)("div",D,[e.state!=(0,n.R1)(c.si).Completed?((0,l.uX)(),(0,l.Wv)(r.A,{key:0,state:e.state},null,8,["state"])):(0,l.Q3)("",!0)])])])])),_:2},1032,["type","hollow"])))),128))])),_:1}))])]))])),_:1},8,["modelValue","title"])}}}),K=s(1241);const $=(0,K.A)(M,[["__scopeId","data-v-0bc3c0cc"]]);var z=$},7489:function(e,t,s){s.d(t,{$:function(){return l}});
/**
 * js-booster - High-performance frontend library
 * VirtualScroll - Virtual scrolling implementation
 * @version 1.1.3
 * <AUTHOR>
 * @license MIT
 */
class l{constructor(e){this.container=e.container,this.items=e.items||[],this.itemHeight=e.itemHeight||20,this.bufferSize=e.bufferSize||10,this.customRenderItem=e.renderItem,this.customRenderHeader=e.renderHeader,this.maxHeight=e.maxHeight||2684e4,this.visibleStartIndex=0,this.visibleEndIndex=0,this.scrollContainer=null,this.contentWrapper=null,this.contentContainer=null,this.totalHeight=this.items.length*this.itemHeight,this.heightScale=1,this.totalHeight>this.maxHeight&&(this.heightScale=this.maxHeight/this.totalHeight),this.initialize()}initialize(){if(this.container.innerHTML="",this.scrollContainer=document.createElement("div"),Object.assign(this.scrollContainer.style,{flex:"1",overflow:"auto",position:"relative",minHeight:"0",height:"100%",boxSizing:"border-box"}),this.customRenderHeader){const e=this.customRenderHeader();e&&this.scrollContainer.appendChild(e)}this.contentWrapper=document.createElement("div"),Object.assign(this.contentWrapper.style,{position:"relative",width:"100%"});const e=this.totalHeight*this.heightScale;this.contentWrapper.style.height=`${e}px`,this.contentContainer=document.createElement("div"),Object.assign(this.contentContainer.style,{position:"absolute",width:"100%",left:"0"}),this.scrollContainer.addEventListener("scroll",this.handleScroll.bind(this)),this.contentWrapper.appendChild(this.contentContainer),this.scrollContainer.appendChild(this.contentWrapper),this.container.appendChild(this.scrollContainer),this.renderVisibleItems(0,Math.min(100,this.items.length))}handleScroll(){const e=this.scrollContainer.scrollTop,t=this.scrollContainer.clientHeight,s=e/this.heightScale,l=Math.max(0,Math.floor(s/this.itemHeight)-this.bufferSize),a=Math.min(this.items.length,Math.ceil((s+t/this.heightScale)/this.itemHeight)+this.bufferSize);l===this.visibleStartIndex&&a===this.visibleEndIndex&&0!==a||(this.renderVisibleItems(l,a),this.visibleStartIndex=l,this.visibleEndIndex=a)}renderVisibleItems(e,t){this.contentContainer.innerHTML="",this.contentContainer.style.transform=`translateY(${e*this.itemHeight*this.heightScale}px)`;for(let s=e;s<t;s++){const e=this.items[s];if(this.customRenderItem){const t=this.customRenderItem(e,s);t&&(t.style.height=this.itemHeight*this.heightScale+"px",t.style.boxSizing="border-box",t.style.width="100%",this.contentContainer.appendChild(t))}else{const t=document.createElement("div");Object.assign(t.style,{height:this.itemHeight*this.heightScale+"px",width:"100%",boxSizing:"border-box",padding:"8px",borderBottom:"1px solid #eee"}),t.textContent=JSON.stringify(e),this.contentContainer.appendChild(t)}}}updateItems(e){this.items=e||[],this.totalHeight=this.items.length*this.itemHeight,this.heightScale=1,this.totalHeight>this.maxHeight&&(this.heightScale=this.maxHeight/this.totalHeight),this.contentWrapper&&(this.contentWrapper.style.height=this.totalHeight*this.heightScale+"px"),this.visibleStartIndex=0,this.visibleEndIndex=0,this.handleScroll()}scrollToIndex(e){e>=0&&e<this.items.length&&(this.scrollContainer.scrollTop=e*this.itemHeight*this.heightScale)}destroy(){this.scrollContainer&&this.scrollContainer.removeEventListener("scroll",this.handleScroll),this.container&&(this.container.innerHTML=""),this.items=null,this.container=null,this.scrollContainer=null,this.contentWrapper=null,this.contentContainer=null}refresh(){this.handleScroll()}getScrollContainer(){return this.scrollContainer}}
/**
 * js-booster - High-performance frontend library
 * @version 1.1.3
 * <AUTHOR>
 * @license MIT
 */"undefined"!==typeof window&&(window.JsBooster={VirtualScroll:l})},9734:function(e,t,s){s.r(t),s.d(t,{default:function(){return J}});s(8111),s(2489),s(116);var l=s(6768),a=s(4232),n=s(5130),i=s(144),c=s(1219),o=s(2933),r=s(1021),u=s(7489);const d={class:"cases-list-container case-list"},h=36,m=20,v=40;var p=(0,l.pM)({__name:"CaseList",props:{cases:{}},emits:["view-detail"],setup(e,{emit:t}){const s=e,a=t,n=(0,i.KR)(null),c=(0,i.KR)(null);let o=null;const p=()=>{if(!c.value)return;c.value.innerHTML="";const e=document.createElement("div");e.className="header-row",e.style.height=`${v}px`;const t=document.createElement("div");t.textContent="ID",t.className="header-cell-id",e.appendChild(t);const s=document.createElement("div");s.textContent="Name",s.className="header-cell-name",e.appendChild(s);const l=document.createElement("div");l.textContent="Parameter",l.className="header-cell-param",e.appendChild(l);const a=document.createElement("div");a.textContent="Detail",a.className="header-cell-detail",e.appendChild(a);const n=document.createElement("div");n.textContent="Status",n.className="header-cell-status",e.appendChild(n),c.value.appendChild(e)},g=()=>{n.value&&s.cases.length&&(p(),o&&o.destroy(),o=new u.$({container:n.value,items:s.cases,itemHeight:h,bufferSize:m,renderItem:(e,t)=>{const l=document.createElement("div");l.className="case-row",l.onclick=()=>a("view-detail",e),l.style.height=`${h}px`,l.style.lineHeight=`${h}px`,l.style.borderBottom=t===s.cases.length-1?"none":"1px solid #ebeef5",l.style.backgroundColor=e.id%2===0?"#fff":"#fafafa",l.onmouseover=()=>{l.style.backgroundColor="#f5f7fa"},l.onmouseout=()=>{l.style.backgroundColor=e.id%2===0?"#fff":"#fafafa",l.style.borderBottom=t===s.cases.length-1?"none":"1px solid #ebeef5"};const n=document.createElement("div");n.className="case-row-content";const i=document.createElement("div");i.textContent=`#${e.id}`,i.className="case-cell-id",n.appendChild(i);const c=document.createElement("div");c.textContent=e.name,c.className="case-cell-name",n.appendChild(c);const o=document.createElement("div");o.textContent=e.parameter,o.title=e.parameter,o.className="case-cell-param",n.appendChild(o);const r=document.createElement("div");r.textContent=e.detail||"-",r.title=e.detail||"",r.className="case-cell-detail",n.appendChild(r);const u=document.createElement("div");u.className="case-cell-status";const d=f(e.state),m=b(e.state),v=document.createElement("span");return v.className=`el-tag el-tag--${d} el-tag--small case-status-tag`,v.textContent=m,u.appendChild(v),n.appendChild(u),l.appendChild(n),l}}))},k=()=>{o?o.updateItems(s.cases):(0,l.dY)((()=>{g()}))},f=e=>{switch(e){case r.si.Success:return"success";case r.si.Running:return"warning";case r.si.Failure:return"danger";case r.si.Pending:default:return"info"}},b=e=>{switch(e){case r.si.Success:return"Passed";case r.si.Failure:return"Failed";case r.si.Running:return"Running";case r.si.Pending:return"Pending";default:return"Unknown"}};return(0,l.wB)((()=>s.cases),(()=>{(0,l.dY)((()=>{k()}))}),{deep:!0}),(0,l.sV)((()=>{(0,l.dY)((()=>{g()}))})),(0,l.hi)((()=>{o&&(o.destroy(),o=null)})),(e,t)=>((0,l.uX)(),(0,l.CE)("div",d,[(0,l.Lk)("div",{class:"cases-header",ref_key:"headerContainer",ref:c},null,512),(0,l.Lk)("div",{ref_key:"casesContainer",ref:n,class:"cases-content"},null,512)]))}}),g=s(1241);const k=(0,g.A)(p,[["__scopeId","data-v-89a1292a"]]);var f=k,b=s(3855),C=s(7477);const y={class:"test-results-container"},L={class:"content-area"},w={key:0,class:"test-results-page"},R={class:"results-table-container"},x={key:0,class:"loading-container"},S={key:1,class:"empty-container"},E={key:2,class:"results-table"},_=["onClick"],F={class:"column-name"},I={class:"result-name text-ellipsis"},H={class:"column-start-time"},N={class:"column-duration"},T={class:"column-passed"},X={class:"success-count"},D={class:"column-failed"},W={class:"failure-count"},M={class:"column-total"},K={class:"total-count"},$={class:"column-actions"},z={class:"action-buttons"},V={key:0},P={key:1,class:"test-cases-page"},Q={class:"page-header"},A={class:"case-list-container","element-loading-text":"Loading test cases..."},B={class:"filters"},U={class:"filter-left"},G={class:"checkbox-label"},Y={class:"checkbox-label"},O={class:"checkbox-label"};var j=(0,l.pM)({__name:"TestResults",setup(e){const t=(0,i.KR)([]),s=(0,i.KR)([]),u=(0,i.KR)(null),d=(0,i.KR)(null),h=(0,i.KR)(!1),m=(0,i.KR)(!0),v=(0,i.KR)(!1),p=(0,i.KR)(!1),g=(0,i.KR)(!1),k=(0,i.KR)(!1),j=(0,i.KR)(!1),q=(0,i.KR)(!1),J=(0,l.EW)((()=>{if(!u.value)return"";const e=t.value.find((e=>e.id===u.value));return e?e.resultFolderName:""})),Z=(0,l.EW)((()=>j.value||q.value?s.value.filter((e=>{const t="Success"===e.state,s="Failure"===e.state;return t&&j.value||s&&q.value})):s.value)),ee=async()=>{m.value=!0;try{const e=await r.GQ.getTestResults();t.value=e.data}catch(e){console.error("获取测试结果列表失败:",e),c.nk.error("Failed to fetch test results")}finally{m.value=!1}},te=e=>{se(e.id)},se=async e=>{u.value=e,h.value=!0,await le(e)},le=async e=>{v.value=!0,s.value=[];try{const t=await r.GQ.getCases(e);s.value=t.data}catch(t){console.error("获取测试用例列表失败:",t),c.nk.error("Failed to fetch case results")}finally{v.value=!1}},ae=e=>{o.s.confirm(`Are you sure you want to delete test result "${e.resultFolderName}"?`,"Warning",{confirmButtonText:"Delete",cancelButtonText:"Cancel",type:"warning"}).then((()=>{ne(e.id)})).catch((()=>{}))},ne=async e=>{try{await r.GQ.deleteTestResult(e),c.nk.success("Test result deleted successfully"),await ee(),u.value===e&&(u.value=null,s.value=[],h.value=!1)}catch(t){console.error("删除测试结果失败:",t),c.nk.error("Failed to delete test result")}},ie=async e=>{if(e){p.value=!0;try{await r.GQ.downloadHtmlReport(e),c.nk.success("Report downloaded successfully")}catch(t){console.error("Download report failed:",t),c.nk.error("Failed to download test report")}finally{p.value=!1}}else c.nk.warning("No test result selected for report generation")},ce=e=>{d.value=e.id,g.value=!0},oe=()=>{g.value=!1,d.value=null},re=e=>{if(!e)return null;try{const t=new Date(e);return t.toLocaleString()}catch(t){return e}},ue=e=>{if(!e)return null;try{const t=new Date(e),s=new Date,l=t.getDate()===s.getDate()&&t.getMonth()===s.getMonth()&&t.getFullYear()===s.getFullYear();return l?t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit",second:"2-digit"}):t.toLocaleDateString([],{month:"2-digit",day:"2-digit"})+" "+t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}catch(t){return e}},de=(e,t)=>{if(!e||!t)return"-";try{const s=new Date(e),l=new Date(t),a=l.getTime()-s.getTime();if(a<0)return"-";if(a<1e3)return`${a}ms`;if(a<6e4){const e=Math.floor(a/1e3);return`${e}s`}if(a<36e5){const e=Math.floor(a/6e4),t=Math.floor(a%6e4/1e3);return`${e}m ${t}s`}{const e=Math.floor(a/36e5),t=Math.floor(a%36e5/6e4);return`${e}h ${t}m`}}catch(s){return console.error("Error calculating duration:",s),"-"}},he=()=>{j.value=k.value,q.value=k.value},me=()=>{k.value=j.value&&q.value};return(0,l.sV)((()=>{ee()})),(e,s)=>{const c=(0,l.g2)("el-skeleton"),o=(0,l.g2)("el-empty"),r=(0,l.g2)("el-tooltip"),p=(0,l.g2)("el-icon"),ee=(0,l.g2)("el-button"),se=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",y,[(0,l.Lk)("div",L,[h.value?((0,l.uX)(),(0,l.CE)("div",P,[(0,l.Lk)("div",Q,[(0,l.bF)(ee,{type:"primary",size:"small",onClick:s[0]||(s[0]=e=>h.value=!1),class:"back-button"},{default:(0,l.k6)((()=>[(0,l.bF)(p,null,{default:(0,l.k6)((()=>[(0,l.bF)((0,i.R1)(C.Back))])),_:1}),s[8]||(s[8]=(0,l.eW)(" Back "))])),_:1}),(0,l.Lk)("h3",null,(0,a.v_)(J.value),1)]),(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",A,[(0,l.Lk)("div",B,[(0,l.Lk)("div",U,[(0,l.Lk)("label",G,[(0,l.bo)((0,l.Lk)("input",{type:"checkbox",id:"filter-all","onUpdate:modelValue":s[1]||(s[1]=e=>k.value=e),onChange:he},null,544),[[n.lH,k.value]]),s[9]||(s[9]=(0,l.Lk)("span",{class:"filter-checkbox filter-all"},"All",-1))]),(0,l.Lk)("label",Y,[(0,l.bo)((0,l.Lk)("input",{type:"checkbox",id:"filter-passed","onUpdate:modelValue":s[2]||(s[2]=e=>j.value=e),onChange:me},null,544),[[n.lH,j.value]]),s[10]||(s[10]=(0,l.Lk)("span",{class:"filter-checkbox filter-passed"},"Passed",-1))]),(0,l.Lk)("label",O,[(0,l.bo)((0,l.Lk)("input",{type:"checkbox",id:"filter-failed","onUpdate:modelValue":s[3]||(s[3]=e=>q.value=e),onChange:me},null,544),[[n.lH,q.value]]),s[11]||(s[11]=(0,l.Lk)("span",{class:"filter-checkbox filter-failed"},"Failed",-1))])])]),(0,l.bF)(f,{cases:Z.value,onViewDetail:ce},null,8,["cases"])])),[[se,v.value]])])):((0,l.uX)(),(0,l.CE)("div",w,[s[7]||(s[7]=(0,l.Lk)("div",{class:"page-header"},[(0,l.Lk)("h3",null,"Test Results")],-1)),(0,l.Lk)("div",R,[m.value?((0,l.uX)(),(0,l.CE)("div",x,[(0,l.bF)(c,{rows:5,animated:""})])):0===t.value.length?((0,l.uX)(),(0,l.CE)("div",S,[(0,l.bF)(o,{description:"No test results found"})])):((0,l.uX)(),(0,l.CE)("table",E,[s[6]||(s[6]=(0,l.Lk)("thead",null,[(0,l.Lk)("tr",null,[(0,l.Lk)("th",{class:"column-name"},"Name"),(0,l.Lk)("th",{class:"column-start-time"},"Start Time"),(0,l.Lk)("th",{class:"column-duration"},"Duration"),(0,l.Lk)("th",{class:"column-passed"},"Passed"),(0,l.Lk)("th",{class:"column-failed"},"Failed"),(0,l.Lk)("th",{class:"column-total"},"Total"),(0,l.Lk)("th",{class:"column-actions"},"Actions")])],-1)),(0,l.Lk)("tbody",null,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(t.value,((e,t)=>((0,l.uX)(),(0,l.CE)("tr",{key:e.id,class:(0,a.C4)({"row-stripe":t%2===1}),onClick:t=>te(e)},[(0,l.Lk)("td",F,[(0,l.bF)(r,{content:e.resultFolderName,placement:"top","show-after":500},{default:(0,l.k6)((()=>[(0,l.Lk)("span",I,(0,a.v_)(e.resultFolderName),1)])),_:2},1032,["content"])]),(0,l.Lk)("td",H,[(0,l.bF)(r,{content:re(e.creationTime),placement:"top","show-after":500},{default:(0,l.k6)((()=>[(0,l.Lk)("span",null,(0,a.v_)(ue(e.creationTime)),1)])),_:2},1032,["content"])]),(0,l.Lk)("td",N,[(0,l.Lk)("span",null,(0,a.v_)(de(e.creationTime,e.end)),1)]),(0,l.Lk)("td",T,[(0,l.Lk)("span",X,(0,a.v_)(e.successCount),1)]),(0,l.Lk)("td",D,[(0,l.Lk)("span",W,(0,a.v_)(e.failureCount),1)]),(0,l.Lk)("td",M,[(0,l.Lk)("span",K,(0,a.v_)(e.totalCount),1)]),(0,l.Lk)("td",$,[(0,l.Lk)("div",z,[(0,l.bF)(ee,{type:"primary",size:"small",onClick:(0,n.D$)((t=>ie(e.id)),["stop"]),title:"Download Report"},{default:(0,l.k6)((()=>[(0,l.bF)(p,null,{default:(0,l.k6)((()=>[(0,l.bF)((0,i.R1)(C.Download))])),_:1})])),_:2},1032,["onClick"]),(0,l.bF)(ee,{type:"danger",size:"small",onClick:(0,n.D$)((t=>ae(e)),["stop"]),title:"Delete Result"},{default:(0,l.k6)((()=>[(0,l.bF)(p,null,{default:(0,l.k6)((()=>[(0,l.bF)((0,i.R1)(C.Delete))])),_:1})])),_:2},1032,["onClick"])])])],10,_)))),128)),0===t.value.length?((0,l.uX)(),(0,l.CE)("tr",V,s[5]||(s[5]=[(0,l.Lk)("td",{colspan:"7",class:"empty-row"},"No data",-1)]))):(0,l.Q3)("",!0)])]))])]))]),(0,l.bF)(b.A,{visible:g.value,"onUpdate:visible":s[4]||(s[4]=e=>g.value=e),testResultId:u.value,caseResultId:d.value,onClose:oe},null,8,["visible","testResultId","caseResultId"])])}}});const q=(0,g.A)(j,[["__scopeId","data-v-5bb9d170"]]);var J=q}}]);
//# sourceMappingURL=734.7a325e9b.js.map