using Alsi.App;
using Alsi.App.Devices.Core;
using Alsi.Common.Utils.Timers;
using Alsi.Fuzz.Core;
using Alsi.Fuzz.Core.Contracts.Tester;
using Alsi.Fuzz.Core.Models.TestSuites;
using Alsi.Fuzz.Core.Models.TestSuites.Steps.Diagnostic;
using Alsi.Fuzz.Core.Service.Results;
using System;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using SuiteSequence = Alsi.Fuzz.Core.Models.TestSuites.Sequence;

namespace Alsi.Fuzz.Tester.Testers
{
    public class PackageTester
    {
        public bool NeedRelease { get; set; }
        public bool IsReleased { get; set; }

        public void Start(ExecutionRequest request, DeviceChannel deviceChannel, SequencePackage package)
        {
            var _ = Task.Run(async () =>
            {
                Exception exception = null;
                try
                {
                    var channelConfig = new ChannelConfig();
                    var hardwareConfig = request.HardwareConfig;
                    channelConfig.CanBitrate = hardwareConfig.CanConfig.DataBitrate;
                    channelConfig.CanFdDataBitRate = hardwareConfig.CanFdConfig.DataBitrate;
                    channelConfig.CanFdArbitrationBitRate = hardwareConfig.CanFdConfig.ArbitrationBitrate;
                    channelConfig.CommunicationType = hardwareConfig.CommunicationType;

                    DataBus.Start(deviceChannel, channelConfig);

                    DataBus.OnSent += RecordDataFrame;
                    DataBus.OnReceived += RecordDataFrame;

                    NeedRelease = true;

                    await InternalStartAsync(request, package);
                }
                catch (Exception e)
                {
                    exception = e;
                    AppEnv.Logger.Error(e, "Unexpected exception in execution");
                }
                finally
                {
                    try
                    {
                        DataBus.OnSent -= RecordDataFrame;
                        DataBus.OnReceived -= RecordDataFrame;

                        DataBus.Stop();

                        TesterEnv.EndTester(TesterEnv.Snapshot.TestResult);
                    }
                    catch (Exception e)
                    {
                        AppEnv.Logger.Error(e, "Unexpected exception in releasing");
                        exception = e;
                    }

                    IsReleased = true;
                }

                if (exception != null)
                {
                    TesterEnv.Snapshot.ProcessState = ExecutionState.Failure;
                }
                else
                {
                    TesterEnv.Snapshot.ProcessState = ExecutionState.Success;
                }
            });
        }

        private async Task InternalStartAsync(
            ExecutionRequest request, SequencePackage package)
        {
            var testResult = request.TestResult;
            if (package == null)
            {
                throw new ArgumentNullException(nameof(package));
            }

            TesterEnv.Update("Sequence package execution started");

            if (request.CaseConfig.EnableNmWakeup
                && request.CaseConfig.NmWakeupCycleMs > 0)
            {
                _ = Task.Run(() =>
                {
                    AppEnv.Logger.Info("NM Wake Up begin");

                    try
                    {
                        while (Program.IsRunning)
                        {
                            HighResolutionTimer.Sleep(request.CaseConfig.NmWakeupCycleMs);
                            var frame = new CanFrame
                            {
                                Id = request.CaseConfig.NmWakeupId,
                                IsTx = true,
                                IsExt = request.CaseConfig.NmWakeupIsExt,
                                IsCanFd = request.CaseConfig.NmWakeupCommunicationType == CommunicationType.CanFd,
                                Dlc = (byte)request.CaseConfig.NmWakeupDlc,
                                Data = request.CaseConfig.NmWakeupData.Select(x => (byte)x).ToArray()
                            };
                            DataBus.Send(frame);
                        }
                    }
                    catch (Exception e)
                    {
                        AppEnv.Logger.Error(e, "Unexpected exception");
                    }
                });

                if (request.CaseConfig.NmWakeupDelayMs > 0)
                {
                    HighResolutionTimer.Sleep(request.CaseConfig.NmWakeupDelayMs);
                }
            }

            foreach (var caseResult in TesterEnv.Snapshot.CaseResults)
            {
                if (!Program.IsRunning)
                {
                    break;
                }

                while (Program.IsPaused)
                {
                    await Task.Delay(10);
                }

                var displayName = $"CASE #{caseResult.Id} {caseResult.Name}";

                CaseContext caseContext = null;
                try
                {
                    SuiteSequence sequence = null;
                    if (package.Name == FuzzConsts.PackageName11898)
                    {
                        sequence = package.Sequences.FirstOrDefault(x => x.Name == FuzzConsts.SequenceName11898Basic);
                        if (sequence == null)
                        {
                            throw new Exception($"Can't find sequence: {FuzzConsts.SequenceName11898Basic}");
                        }
                    }
                    else
                    {
                        if (caseResult.SequenceName == FuzzConsts.DefaultUdsSequenceName)
                        {
                            var defaultSequence = new SuiteSequence() { Name = FuzzConsts.DefaultUdsSequenceName };
                            defaultSequence.Steps.Add(new SendDiagStep { HexPayload = "" });
                            defaultSequence.Steps.Add(new RecvDiagStep());
                            sequence = defaultSequence;
                        }
                        else
                        {
                            sequence = package.Sequences.FirstOrDefault(x => x.Name == caseResult.SequenceName);
                        }

                        if (sequence == null)
                        {
                            throw new Exception($"Can't find sequence: {caseResult.SequenceName}");
                        }
                    }

                    caseResult.State = ExecutionState.Running;
                    caseResult.Begin = DateTime.Now;
                    TesterEnv.Update($"{displayName} started");
                    TesterEnv.ResultWriter.AddOrUpdateCaseResult(caseResult);

                    var envVars = package.SetVars.Select(x => new EnvVar(x.Name, x.Value)).ToList();
                    caseContext = new CaseContext(envVars, request.TestResult, caseResult, request.CaseConfig, request.HardwareConfig);
                    await ExecuteCaseAsync(sequence, caseContext);

                    caseResult.State = ExecutionState.Success;
                    caseResult.End = DateTime.Now;

                    TesterEnv.Update($"{displayName} completed");
                    TesterEnv.ResultWriter.AddOrUpdateCaseResult(caseResult);
                }
                catch (Exception e)
                {
                    caseResult.State = ExecutionState.Failure;
                    caseResult.End = DateTime.Now;
                    caseResult.Detail = e.Message;

                    TesterEnv.Update($"{displayName} failed: {e.Message}");
                    TesterEnv.ResultWriter.AddOrUpdateCaseResult(caseResult);
                }
                finally
                {
                    TesterEnv.ResultWriter.AddCaseSteps(caseContext?.CaseSteps.ToArray());
                    if (request.TestResult.TestType == TestType.Interoperation)
                    {
                        TesterEnv.Snapshot.RefreshCaseSteps(TesterEnv.ResultWriter.GetCaseSteps());
                    }

                    TesterEnv.Snapshot.UpdateCaseStatistics();
                }
            }
        }

        private void RecordDataFrame(object _, CanFrame frame)
        {
            TesterEnv.DataLogStorage.WriteFrame(frame);
        }

        private async Task ExecuteCaseAsync(SuiteSequence sequence, CaseContext caseContext)
        {
            using (var stepTester = new StepTester())
            {
                foreach (var step in sequence.Steps)
                {
                    await stepTester.ExecuteStepAsync(step, caseContext);
                }
            }

            var lastFailureStep = caseContext.CaseSteps.LastOrDefault(x => x.State == CaseStepState.Failure);
            if (lastFailureStep != null)
            {
                caseContext.CaseResult.Detail = lastFailureStep.Detail;
            }
            else
            {
                var lastSuccessStep = caseContext.CaseSteps.LastOrDefault(x =>
                    x.State == CaseStepState.Success && !IsIgnoreDetail(x.Detail));
                if (lastSuccessStep != null)
                {
                    caseContext.CaseResult.Detail = lastSuccessStep.Detail;
                }
            }
        }

        private static bool IsIgnoreDetail(string detail)
        {
            return ignoredDetailPatterns.Any(x => Regex.Match(detail, x).Success);
        }

        private static string[] ignoredDetailPatterns = new[] { @"Wait for \d+ ms" };
    }
}
