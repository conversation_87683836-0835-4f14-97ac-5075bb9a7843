using System;

namespace Alsi.Fuzz.Core.Models.TestPlans
{
    public class SequenceConfig
    {
        public bool IsSelected { get; set; }
        public string TestSuiteName { get; set; }
        public string SequencePackageName { get; set; }
        public string SequencePackageXml { get; set; }

        /// <summary>
        /// 标识是否为内建包。内建包不可修改，XML内容始终从嵌入资源读取
        /// </summary>
        public bool IsBuiltIn { get; set; }

        /// <summary>
        /// 自定义包的名称。仅当IsBuiltIn=false时使用，用于显示和标识自定义包
        /// </summary>
        public string CustomName { get; set; }

        /// <summary>
        /// 基于哪个内建包创建的自定义包。可选，用于追踪来源
        /// </summary>
        public string BasePackageName { get; set; }

        /// <summary>
        /// 最后修改时间。仅自定义包有值
        /// </summary>
        public DateTime? LastModified { get; set; }

        /// <summary>
        /// 创建时间。仅自定义包有值
        /// </summary>
        public DateTime? CreationTime { get; set; }
    }
}
