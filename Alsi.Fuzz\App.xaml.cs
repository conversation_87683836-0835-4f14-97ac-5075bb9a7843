using Alsi.App;
using Alsi.App.Cef;
using Alsi.App.Database;
using Alsi.App.Desktop;
using Alsi.App.Desktop.Utils;
using Alsi.App.Devices;
using Alsi.Fuzz.Core;
using Alsi.Fuzz.Core.Service.Tester;
using Alsi.Fuzz.Views;
using Alsi.Fuzz.Web;
using System.ComponentModel;
using System.Windows;

namespace Alsi.Fuzz
{
    public partial class App : Application
    {
        private WebHostApp _webHostApp;
        private void Application_Startup(object sender, StartupEventArgs e)
        {
            var fuzzCoreAssembly = new FuzzWebAssembly();

            var options = new AppOptions
            {
                OnClosing = OnClosing,
                ApiLogOptions = new ApiLogOptions
                {
                    SilentApiPaths = { "api/case/status", "api/interoperation/status" }
                }
            };

            _webHostApp = WebHostApp
                .Create(appName: "Fuzz", appFolderName: "Alsi.Atts", productFolderName: "Fuzz")
                .UseOptions(options)
                .UseAppExceptionHandler(this)
                .UseSerilog("Fuzz")
                .UseProcessMutex(FuzzConsts.ProcessMutexName)
                .UseDevice()
                .UseCef()
                .UseFreeSql("fuzz.sqlite")
                .UseApiHost(fuzzCoreAssembly)
                .Build();

            var mainWindow = WindowUtils.ShowWindow<MainView>("Fuzz", null, size: new Size(1400, 800));
            mainWindow.MinWidth = 800;
            mainWindow.MinHeight = 600;

            Current.MainWindow = mainWindow;
            Current.MainWindow.Closing += MainWindow_Closing;
        }

        private void MainWindow_Closing(object sender, CancelEventArgs e)
        {
            e.Cancel = true;

            if (_webHostApp != null)
            {
                _webHostApp.Exit();
            }
        }

        private bool OnClosing()
        {
            if (TesterManager.Instance.IsRunning)
            {
                var result = MessageBox.Show(
                    "The tester is running, are you sure you want to exit the application?",
                    "Exit",
                    MessageBoxButton.OKCancel,
                    MessageBoxImage.Warning);

                if (result != MessageBoxResult.OK)
                {
                    return false;
                }
            }

            return true;
        }
    }
}
