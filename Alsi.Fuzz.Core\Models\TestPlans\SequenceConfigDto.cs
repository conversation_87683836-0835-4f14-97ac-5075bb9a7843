using System;

namespace Alsi.Fuzz.Core.Models.TestPlans
{
    public class SequenceConfigDto
    {
        public string TestSuiteName { get; set; }
        public string SequencePackageName { get; set; }
        public string SequencePackageXml { get; set; }

        /// <summary>
        /// 标识是否为内建包
        /// </summary>
        public bool IsBuiltIn { get; set; }

        /// <summary>
        /// 自定义包的名称
        /// </summary>
        public string CustomName { get; set; }

        /// <summary>
        /// 基于哪个内建包创建的自定义包
        /// </summary>
        public string BasePackageName { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LastModified { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreationTime { get; set; }
    }
}
