using Alsi.App;
using Alsi.Fuzz.Core.Contracts.Tester;
using Alsi.Fuzz.Core.Service.Results;
using Alsi.Fuzz.Core.Service.Tester;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Alsi.Fuzz.Core.Service
{
    public class StatusPollingService
    {
        public bool IsTesterRunning { get; set; } = false;
        public TesterSnapshot TesterSnapshot = new TesterSnapshot();

        private Timer _statusPollingTimer;

        private bool _isInteroperation = false;

        public void Reset()
        {
            TesterSnapshot = new TesterSnapshot();
        }

        /// <summary>
        /// 开始状态轮询
        /// </summary>
        public void StartStatusPolling(bool isInteroperation)
        {
            _isInteroperation = isInteroperation;

            // 每1秒轮询一次状态
            _statusPollingTimer = new Timer(
                async _ => await UpdateStatusAsync(),
                null,
                TimeSpan.Zero,
                TimeSpan.FromMilliseconds(1000));
        }

        /// <summary>
        /// 停止状态轮询
        /// </summary>
        public void StopStatusPolling()
        {
            _statusPollingTimer?.Dispose();
            _statusPollingTimer = null;
        }

        /// <summary>
        /// 更新状态（用于定时轮询）
        /// </summary>
        public async Task UpdateStatusAsync()
        {
            try
            {
                var request = new ReqeustTesterSnapshot { IsInteroperation = _isInteroperation };
                var snapshot = await TesterManager.Instance.ApiClient.GetTesterSnapshotAsync(request);

                TesterSnapshot = snapshot;

                IsTesterRunning = !TesterSnapshot?.IsCompleted() ?? false;

                if (TesterSnapshot?.TestResult != null)
                {
                    var resultReader = new TestResultReaderService();
                    resultReader.UpdateTestResult(TesterSnapshot.TestResult);
                }

                if (TesterSnapshot?.IsCompleted() ?? false)
                {
                    StopStatusPolling();
                    await TesterManager.Instance.ApiClient.ExitAsync();
                }
            }
            catch (Exception e)
            {
                AppEnv.Logger.Error(e, "error in tester snapshot polling");
            }
        }
    }
}
