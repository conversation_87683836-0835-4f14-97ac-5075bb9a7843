<template>
  <div class="interoperation-container">
    <div class="toolbar">
      <h3>Interoperation Test</h3>
      <div class="action-buttons">
        <el-button type="success" size="small" :loading="starting" @click="startTestExecution" :disabled="isRunning">
          Start
        </el-button>
        <el-button type="danger" size="small" :loading="stopping" @click="stopTestExecution" :disabled="!isRunning">
          Stop
        </el-button>
      </div>
    </div>

    <!-- 使用TestMonitor组件显示执行状态进度 -->
    <TestMonitor :run-status="runStatus" :visible="hasEverStarted" />

    <el-scrollbar v-if="displayedCases.length" height="100%">
      <div class="cases-list">
        <div v-for="(item, index) in displayedCases" :key="index" class="case-item">
          <div class="case-header">
            <div class="case-sequence">
              <div>{{ item.sequenceName }}</div>
              <div v-if="item.detail" class="case-detail">
                <div class="detail-content">{{ item.detail }}</div>
              </div>
            </div>
            <div class="case-actions">
              <div class="case-status">
                <CaseStateTag :state="item.state" />
              </div>
              <el-button 
                type="primary" 
                size="small" 
                plain 
                @click="viewCaseDetail(item)">
                Open
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>

    <el-empty v-else description="No items" :image-size="150" />

    <!-- 添加用例详情对话框组件 -->
    <CaseDetailDialog
      v-model:visible="detailDialogVisible"
      :testResultId="testResultId"
      :caseResultId="selectedCaseId"
      @close="closeDetailDialog"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, defineComponent } from 'vue';
import { ElMessage, ElEmpty } from 'element-plus';
import { CaseResult, interoperationApi, isTesterCompleted } from '@/api/interoperationApi';
import CaseStateTag from '@/components/common/CaseStateTag.vue';
import TestMonitor from '@/components/test/TestMonitor.vue';
import { TesterSnapshot, ExecutionState } from '@/api/appApi';
// 导入用例详情对话框组件
import CaseDetailDialog from '@/components/test/CaseDetailDialog.vue';

// 定义组件名称
defineComponent({
  name: 'InteroperationView'
});

// 状态变量
const loading = ref(true);
const starting = ref(false);
const stopping = ref(false);
const hasEverStarted = ref(false);
const runStatus = ref<TesterSnapshot>({
  processState: ExecutionState.Pending,
  currentOperation: '',
  testResult: {
    id: '',
    resultFolderName: '',
    testType: '',
    creationTime: '',
    totalCount: 0,
    successCount: 0,
    failureCount: 0
  },
  caseResults: []
});

// 用例详情对话框相关
const detailDialogVisible = ref(false);
const selectedCaseId = ref<number | null>(null);
const testResultId = ref<string | null>(null);

// 状态轮询定时器
let statusPollingTimer: number | null = null;

// 计算属性
const isRunning = computed(() => {
  return runStatus.value.processState === ExecutionState.Running;
});

const displayedCases = computed(() => {
  return runStatus.value.caseResults || [];
});

// 查看用例详情
const viewCaseDetail = (caseResult: CaseResult) => {
  testResultId.value = caseResult.testResultId;
  selectedCaseId.value = caseResult.id;
  detailDialogVisible.value = true;
};

// 关闭详情对话框
const closeDetailDialog = () => {
  detailDialogVisible.value = false;
  selectedCaseId.value = null;
};

// 开始测试执行
const startTestExecution = async () => {
  starting.value = true;
  try {
    await interoperationApi.startTest();
    hasEverStarted.value = true;
    ElMessage.success('Interoperation test started');

    // 立即获取状态并开始轮询
    await fetchTestStatus();
    startStatusPolling();
  } catch (error) {
    console.error('Failed to start interoperation test:', error);
    ElMessage.error('Failed to start interoperation test');
  } finally {
    starting.value = false;
  }
};

// 停止测试执行
const stopTestExecution = async () => {
  stopping.value = true;
  try {
    await interoperationApi.stopTest();
    ElMessage.success('Interoperation test stopped');

    // 立即更新状态
    await fetchTestStatus();
  } catch (error) {
    console.error('Failed to stop interoperation test:', error);
    ElMessage.error('Failed to stop interoperation test');
  } finally {
    stopping.value = false;
  }
};

// 获取测试状态
const fetchTestStatus = async () => {
  try {
    const response = await interoperationApi.getStatus();
    runStatus.value = response.data;

    // 如果测试完成则停止轮询
    if (isTesterCompleted(runStatus.value) && statusPollingTimer) {
      stopStatusPolling();
    }
    loading.value = false;
  } catch (error) {
    console.error('Failed to fetch interoperation test status:', error);
  }
};

// 开始状态轮询
const startStatusPolling = () => {
  // 清除可能存在的轮询定时器
  stopStatusPolling();
  statusPollingTimer = window.setInterval(fetchTestStatus, 300);
};

// 停止状态轮询
const stopStatusPolling = () => {
  if (statusPollingTimer) {
    clearInterval(statusPollingTimer);
    statusPollingTimer = null;
  }
};

// 组件挂载时获取测试状态
onMounted(() => {
  fetchTestStatus().then(() => {
    // 如果测试正在运行，开始轮询
    if (isRunning.value) {
      hasEverStarted.value = true;
      startStatusPolling();
    }
  });
});

// 组件卸载时停止轮询
onUnmounted(() => {
  stopStatusPolling();
});
</script>

<style scoped lang="scss">
.interoperation-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 15px 20px;
}

/* 顶部工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 10px;
  margin-bottom: 10px;
  height: 30px;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
  }
}

.cases-list {
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.case-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 6px 10px;
  background-color: #fff;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  gap: 10px;
  font-size: 13px;

  &:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}

.case-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .case-index {
    font-weight: bold;
    width: 40px;
    color: #909399;
  }

  .case-sequence {
    display: flex;
    flex-direction: row;
    flex: 1;
    font-weight: 500;
    color: #303133;
  }
}

.case-actions {
  display: flex;
  align-items: center;
  gap: 8px; /* 按钮和状态标签之间的间距 */
}

.case-status {
  display: flex;
  align-items: center;
}

.case-body {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .case-parameter {
    flex: 1;
    color: #606266;
    font-size: 13px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 60vw;
  }

  .case-group {
    margin-left: 8px;
  }
}

.case-detail {
  font-size: 12px;
  margin-left: 10px;
  display: flex;
  align-items: center;

  .detail-content {
    color: #909399;
    word-break: break-word;
    flex: 1;
  }
}
</style>
