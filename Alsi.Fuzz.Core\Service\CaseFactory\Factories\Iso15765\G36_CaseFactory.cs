using Alsi.App.Devices.Core;
using Alsi.Common.Utils.Autosar;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using System.Collections.Generic;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso15765
{
    public class G36_CaseFactory : CaseFactoryBase
    {
        public override CaseMutation[] Generate(MutationOptions options)
        {
            var list = new List<CaseMutation>();
            const int dlc = 8;

            // 1. G361 - FlowStatus=0, BlockSize=Random(0,0x100), Stmin=00-7F
            var groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FlowControl().Dlc8().FlowStatus(0);
            foreach (var stMin in UniformSampleBytes(0x00, 0x7F, options.Coverage == CoverageType.Normal ? 32 : 128))
            {
                byte blockSize = (byte)random.Next(0, 0x100); // 随机生成 0 到 0xFF 之间的 BlockSize
                var bytes = new byte[] { 0x30, blockSize, stMin, 0x55, 0x55, 0x55, 0x55, 0x55 };
                var caseMutation = CaseMutation.Create($"G361-BS_{blockSize:X2}-STmin_{stMin:X2}", groupPath)
                    .MutateDlc(dlc)
                    .MutateTpFc(bytes);
                list.Add(caseMutation);
            }

            // 2. G362 - FlowStatus=0, BlockSize=Random(0,0x100), Stmin=F1-F9
            foreach (var stMin in UniformSampleBytes(0xF1, 0xF9, options.Coverage == CoverageType.Normal ? 2 : 9))
            {
                byte blockSize = (byte)random.Next(0, 0x100); // 随机生成 0 到 0xFF 之间的 BlockSize
                var bytes = new byte[] { 0x30, blockSize, stMin, 0x55, 0x55, 0x55, 0x55, 0x55 };
                var caseMutation = CaseMutation.Create($"G362-BS_{blockSize:X2}-STmin_{stMin:X2}", groupPath)
                    .MutateDlc(dlc)
                    .MutateTpFc(bytes);
                list.Add(caseMutation);
            }

            // 3. G363 - FlowStatus=0, BlockSize=Random(0,0x100), Stmin=80-F0 (Invalid)
            foreach (var stMin in UniformSampleBytes(0x80, 0xF0, options.Coverage == CoverageType.Normal ? 28 : 113))
            {
                byte blockSize = (byte)random.Next(0, 0x100);
                var bytes = new byte[] { 0x30, blockSize, stMin, 0x55, 0x55, 0x55, 0x55, 0x55 };
                var caseMutation = CaseMutation.Create($"G363-BS_{blockSize:X2}-STmin_{stMin:X2}", groupPath)
                    .MutateDlc(dlc)
                    .MutateTpFc(bytes);
                list.Add(caseMutation);
            }

            // 4. G364 - FlowStatus=0, BlockSize=Random(0,0x100), Stmin=FA-FF (Invalid)
            if (options.Coverage == CoverageType.Normal)
            {
                // 通常覆盖: Random(FA,0x100):1
                byte stMin = (byte)random.Next(0xFA, 0x100);
                byte blockSize = (byte)random.Next(0, 0x100);
                var bytes = new byte[] { 0x30, blockSize, stMin, 0x55, 0x55, 0x55, 0x55, 0x55 };
                var caseMutation = CaseMutation.Create($"G364-BS_{blockSize:X2}-STmin_{stMin:X2}", groupPath)
                    .MutateDlc(dlc)
                    .MutateTpFc(bytes);
                list.Add(caseMutation);
            }
            else
            {
                // 加强覆盖: Repeat all: 5
                for (var stMin = 0xFA; stMin <= 0xFF; stMin++)
                {
                    byte blockSize = (byte)random.Next(0, 0x100);
                    var bytes = new byte[] { 0x30, blockSize, (byte)stMin, 0x55, 0x55, 0x55, 0x55, 0x55 };
                    var caseMutation = CaseMutation.Create($"G364-BS_{blockSize:X2}-STmin_{(byte)stMin:X2}", groupPath)
                        .MutateDlc(dlc)
                        .MutateTpFc(bytes);
                    list.Add(caseMutation);
                }
            }

            // 5. G365 - FlowStatus=1 (Wait), BlockSize=Random(0,0x100), Stmin=00-7F
            groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FlowControl().Dlc8().FlowStatus(1);
            foreach (var stMin in UniformSampleBytes(0x00, 0x7F, options.Coverage == CoverageType.Normal ? 32 : 128))
            {
                byte blockSize = (byte)random.Next(0, 0x100);
                var bytes = new byte[] { 0x31, blockSize, stMin, 0x55, 0x55, 0x55, 0x55, 0x55 }; // 0x31 表示 FlowStatus=1 (Wait)
                var caseMutation = CaseMutation.Create($"G365-BS_{blockSize:X2}-STmin_{stMin:X2}", groupPath)
                    .MutateDlc(dlc)
                    .MutateTpFc(bytes);
                list.Add(caseMutation);
            }

            // 6. G366 - FlowStatus=1 (Wait), BlockSize=Random(0,0x100), Stmin=F1-F9
            foreach (var stMin in UniformSampleBytes(0xF1, 0xF9, options.Coverage == CoverageType.Normal ? 2 : 9))
            {
                byte blockSize = (byte)random.Next(0, 0x100);
                var bytes = new byte[] { 0x31, blockSize, stMin, 0x55, 0x55, 0x55, 0x55, 0x55 };
                var caseMutation = CaseMutation.Create($"G366-BS_{blockSize:X2}-STmin_{stMin:X2}", groupPath)
                    .MutateDlc(dlc)
                    .MutateTpFc(bytes);
                list.Add(caseMutation);
            }

            // 7. G367 - FlowStatus=1 (Wait), BlockSize=Random(0,0x100), Stmin=80-F0 (Invalid)
            foreach (var stMin in UniformSampleBytes(0x80, 0xF0, options.Coverage == CoverageType.Normal ? 28 : 113))
            {
                byte blockSize = (byte)random.Next(0, 0x100);
                var bytes = new byte[] { 0x31, blockSize, stMin, 0x55, 0x55, 0x55, 0x55, 0x55 };
                var caseMutation = CaseMutation.Create($"G367-BS_{blockSize:X2}-STmin_{stMin:X2}", groupPath)
                    .MutateDlc(dlc)
                    .MutateTpFc(bytes);
                list.Add(caseMutation);
            }

            // 8. G368 - FlowStatus=1 (Wait), BlockSize=Random(0,0x100), Stmin=FA-FF (Invalid)
            if (options.Coverage == CoverageType.Normal)
            {
                // 通常覆盖: Random(FA,0x100):1
                byte stMin = (byte)random.Next(0xFA, 0x100);
                byte blockSize = (byte)random.Next(0, 0x100);
                var bytes = new byte[] { 0x31, blockSize, stMin, 0x55, 0x55, 0x55, 0x55, 0x55 };
                var caseMutation = CaseMutation.Create($"G368-BS_{blockSize:X2}-STmin_{stMin:X2}", groupPath)
                    .MutateDlc(dlc)
                    .MutateTpFc(bytes);
                list.Add(caseMutation);
            }
            else
            {
                // 加强覆盖: Repeat all: 5
                for (var stMin = 0xFA; stMin <= 0xFF; stMin++)
                {
                    byte blockSize = (byte)random.Next(0, 0x100);
                    var bytes = new byte[] { 0x31, blockSize, (byte)stMin, 0x55, 0x55, 0x55, 0x55, 0x55 };
                    var caseMutation = CaseMutation.Create($"G368-BS_{blockSize:X2}-STmin_{(byte)stMin:X2}", groupPath)
                        .MutateDlc(dlc)
                        .MutateTpFc(bytes);
                    list.Add(caseMutation);
                }
            }

            // 9. G369 - FlowStatus=2 (Overflow), BlockSize=Random(0,0x100), Stmin=00-7F
            groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FlowControl().Dlc8().FlowStatus(2);
            foreach (var stMin in UniformSampleBytes(0x00, 0x7F, options.Coverage == CoverageType.Normal ? 32 : 128))
            {
                byte blockSize = (byte)random.Next(0, 0x100);
                var bytes = new byte[] { 0x32, blockSize, stMin, 0x55, 0x55, 0x55, 0x55, 0x55 }; // 0x32 表示 FlowStatus=2 (Overflow)
                var caseMutation = CaseMutation.Create($"G369-BS_{blockSize:X2}-STmin_{stMin:X2}", groupPath)
                    .MutateDlc(dlc)
                    .MutateTpFc(bytes);
                list.Add(caseMutation);
            }

            // 10. G36A - FlowStatus=2 (Overflow), BlockSize=Random(0,0x100), Stmin=F1-F9
            foreach (var stMin in UniformSampleBytes(0xF1, 0xF9, options.Coverage == CoverageType.Normal ? 2 : 9))
            {
                byte blockSize = (byte)random.Next(0, 0x100);
                var bytes = new byte[] { 0x32, blockSize, stMin, 0x55, 0x55, 0x55, 0x55, 0x55 };
                var caseMutation = CaseMutation.Create($"G36A-BS_{blockSize:X2}-STmin_{stMin:X2}", groupPath)
                    .MutateDlc(dlc)
                    .MutateTpFc(bytes);
                list.Add(caseMutation);
            }

            // 11. G36B - FlowStatus=2 (Overflow), BlockSize=Random(0,0x100), Stmin=80-F0 (Invalid)
            foreach (var stMin in UniformSampleBytes(0x80, 0xF0, options.Coverage == CoverageType.Normal ? 28 : 113))
            {
                byte blockSize = (byte)random.Next(0, 0x100);
                var bytes = new byte[] { 0x32, blockSize, stMin, 0x55, 0x55, 0x55, 0x55, 0x55 };
                var caseMutation = CaseMutation.Create($"G36B-BS_{blockSize:X2}-STmin_{stMin:X2}", groupPath)
                    .MutateDlc(dlc)
                    .MutateTpFc(bytes);
                list.Add(caseMutation);
            }

            // 12. G36C - FlowStatus=2 (Overflow), BlockSize=Random(0,0x100), Stmin=FA-FF (Invalid)
            if (options.Coverage == CoverageType.Normal)
            {
                // 通常覆盖: Random(FA,0x100):1
                byte stMin = (byte)random.Next(0xFA, 0x100);
                byte blockSize = (byte)random.Next(0, 0x100);
                var bytes = new byte[] { 0x32, blockSize, stMin, 0x55, 0x55, 0x55, 0x55, 0x55 };
                var caseMutation = CaseMutation.Create($"G36C-BS_{blockSize:X2}-STmin_{stMin:X2}", groupPath)
                    .MutateDlc(dlc)
                    .MutateTpFc(bytes);
                list.Add(caseMutation);
            }
            else
            {
                // 加强覆盖: Repeat all: 5
                for (var stMin = 0xFA; stMin <= 0xFF; stMin++)
                {
                    byte blockSize = (byte)random.Next(0, 0x100);
                    var bytes = new byte[] { 0x32, blockSize, (byte)stMin, 0x55, 0x55, 0x55, 0x55, 0x55 };
                    var caseMutation = CaseMutation.Create($"G36C-BS_{blockSize:X2}-STmin_{(byte)stMin:X2}", groupPath)
                        .MutateDlc(dlc)
                        .MutateTpFc(bytes);
                    list.Add(caseMutation);
                }
            }

            // 13. G36D - DLC=3, FlowStatus=0, BlockSize=Random(0,0x100), Stmin=Random(0-80)
            groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FlowControl().Dlc3().Path;
            {
                byte blockSize = (byte)random.Next(0, 0x100);
                byte stMin = (byte)random.Next(0, 0x81);
                var bytes = new byte[] { 0x30, blockSize, stMin }; // 只有3个字节，没有填充
                var caseMutation = CaseMutation.Create($"G36D-DLC_3-BS_{blockSize:X2}-STmin_{stMin:X2}", groupPath)
                    .MutateTpFc(bytes)
                    .MutateDlc(3); // 设置DLC=3
                list.Add(caseMutation);
            }

            // 14. G36E - DLC=9,A,B,C,D,E,F (仅CANFD), FlowStatus=0, BlockSize=Random(0,0x100), Stmin=Random(0-80)
            groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FlowControl().CanfdDlcMoreThan8().Path;
            if (options.CommunicationType == CommunicationType.CanFd)
            {
                // DLC=9,A,B,C,D,E,F 对应的数据长度
                int[] dlcValues = { 9, 10, 11, 12, 13, 14, 15 };

                foreach (var dlcValue in dlcValues)
                {
                    byte blockSize = (byte)random.Next(0, 0x100);
                    byte stMin = (byte)random.Next(0, 0x81);

                    // 根据DLC计算实际数据长度
                    int dataLength = DlcUtils.GetDataLength(dlcValue);
                    var bytes = new byte[dataLength];

                    // 设置前3个字节
                    bytes[0] = 0x30; // FlowStatus=0
                    bytes[1] = blockSize;
                    bytes[2] = stMin;

                    // 填充剩余字节为0x55
                    for (int i = 3; i < dataLength; i++)
                    {
                        bytes[i] = 0x55;
                    }

                    var caseMutation = CaseMutation.Create($"G36E-DLC_{dlcValue:X}-BS_{blockSize:X2}-STmin_{stMin:X2}", groupPath)
                        .MutateTpFc(bytes)
                        .MutateDlc(dlcValue);
                    list.Add(caseMutation);
                }
            }

            return list.ToArray();
        }
    }
}
