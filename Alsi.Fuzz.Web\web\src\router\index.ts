import { createRouter, create<PERSON>eb<PERSON><PERSON><PERSON>, RouteRecordRaw } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import TestSuiteView from '@/views/TestSuiteView.vue'
import { testPlanService } from '@/services/testPlanService';
import { ElMessage } from 'element-plus';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'home',
    component: HomeView
  },
  {
    path: '/test-plan',
    name: 'test-plan',
    component: () => import('../views/TestPlanView.vue'),
    props: route => ({ path: route.query.path }),
    children: [
      {
        path: '',
        name: 'test-plan.basic-setting',
        component: () => import('../views/testplan/BasicSetting.vue')
      },
      {
        path: 'basic-settings',
        name: 'test-plan.basic-settings',
        component: () => import('../views/testplan/BasicSetting.vue')
      },
      {
        path: 'hardware',
        name: 'test-plan.hardware',
        component: () => import('../views/testplan/HardwareSetting.vue')
      },
      {
        path: 'case-setting',
        name: 'test-plan.case-setting',
        component: () => import('../views/testplan/CaseSetting.vue')
      },
      {
        path: 'interoperation',
        name: 'test-plan.interoperation',
        component: () => import('../views/testplan/Interoperation.vue')
      },
      {
        path: 'test-cases',
        name: 'test-plan.test-cases',
        component: () => import('../views/testplan/TestCases.vue')
      },
      {
        path: 'test-run',
        name: 'test-plan.test-run',
        component: () => import('../views/testplan/TestRun.vue')
      },
      {
        path: 'test-results',
        name: 'test-plan.test-results',
        component: () => import('../views/testplan/TestResults.vue')
      },
      {
        path: 'sequence-setting',
        name: 'test-plan.sequence-setting',
        component: () => import('../views/testplan/SequenceSetting.vue')
      }
    ]
  },
  {
    path: '/test-suite',
    name: 'test-suite',
    component: TestSuiteView
  },
  {
    path: '/about',
    name: 'about',
    component: () => import('../views/AboutView.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 添加路由守卫
router.beforeEach(async (to, from, next) => {
  // 初始化路由
  testPlanService.setRouter(router);
  
  // 访问测试计划相关页面时检查是否有当前测试计划
  if (to.path.startsWith('/test-plan') || to.path.startsWith('/testplan')) {
    const state = testPlanService.getState();
    if (!state.currentPlan) {
      const currentPlan = await testPlanService.getCurrentPlan();
      if (!currentPlan) {
        next('/');
        ElMessage.warning("No test plan is currently open");
        return;
      }
    }
  }
  next();
});

export default router
