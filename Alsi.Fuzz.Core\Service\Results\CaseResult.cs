using FreeSql.DataAnnotations;
using System;

namespace Alsi.Fuzz.Core.Service.Results
{
    public class ResultProps
    {
        public bool HasMultipleFrameRequest { get; set; }
    }

    [Index("index_TestResultId", "TestResultId")]
    public class CaseResult
    {
        [Column(IsIdentity = true, IsPrimary = true)]
        public int Id { get; set; }
        public Guid TestResultId { get; set; }
        public string Name { get; set; }
        public string SequenceName { get; set; }
        public string Parameter { get; set; }
        public ExecutionState State { get; set; }
        public string ResultProps { get; set; }
        public DateTime? Begin { get; set; }
        public DateTime? End { get; set; }
        public string Detail { get; set; }
        public string GroupPath { get; set; }
        public string TesterGroup { get; set; } = string.Empty;

        public override string ToString()
        {
            return $"Id={Id} {Name} {State} {Begin} {End}";
        }
    }
}
