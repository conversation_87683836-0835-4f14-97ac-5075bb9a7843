using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts;
using Alsi.Fuzz.Core.Service.CaseFactory.Iso14229;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G3131_G3132_CaseFactory : CaseFactoryBase
    {
        public override void Generate(MutationOptions options, Action<CreateCaseInfo> createCase)
        {
            var xmlServices = options.XmlServices;
            var groupPath = "";

            var supportedXmlServicesWithSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == true)
                .ToArray();

            var supportedXmlServicesWithoutSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => !x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历没有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == false)
                .ToArray();

            foreach (var xmlServiceWithSubfunction in supportedXmlServicesWithSubfunction)
            {
                var sid = xmlServiceWithSubfunction.Id;
                var serviceWithSubName = xmlServiceWithSubfunction.IsoUdsServiceDisplayName;


                foreach (var xmlServiceWithoutSubfunction in supportedXmlServicesWithoutSubfunction)
                {
                    var payload = new List<byte> { sid, };
                    payload.AddRange(xmlServiceWithoutSubfunction.Parameter2k);

                    var serviceWithoutName = xmlServiceWithoutSubfunction.IsoUdsServiceDisplayName;
                    groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                        .GetService(serviceWithSubName)
                        .Invalid()
                        .Parameter1K(serviceWithoutName)
                        .Path;
                    var name = $"ServiceWith & ServiceWithout -{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G3131-Sid{sid:X2}-OtherSid{xmlServiceWithoutSubfunction.Id:X2}", caseMutation, sid, xmlServiceWithSubfunction.Id));
                }
            }

            foreach (var xmlServiceWithoutSubfunction in supportedXmlServicesWithoutSubfunction)
            {
                var sid = xmlServiceWithoutSubfunction.Id;
                var serviceWithoutName = xmlServiceWithoutSubfunction.IsoUdsServiceDisplayName;
                foreach (var xmlServiceWithSubfunction in supportedXmlServicesWithSubfunction)
                {
                    // 2025/7/1 调整， Parameter1（subfunction） 取值范围 0~255
                    for (var i = 0; i <= 255; i++)
                    {
                        var subfunctionId = i;
                        var payload = new List<byte> { sid, (byte)subfunctionId };
                        payload.AddRange(xmlServiceWithSubfunction.Parameter2k);
                        var serviceWithSubName = xmlServiceWithSubfunction.IsoUdsServiceDisplayName;
                        groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                            .GetService(serviceWithSubName)
                            .Invalid()
                            .Parameter1K(serviceWithoutName)
                            .Path;
                        var name = $"ServiceWithout & ServiceWith -{groupPath}";
                        var caseMutation = CaseMutation.Create(name, groupPath)
                            .MutatePayload(payload.ToArray())
                            .MutatePayloadLength(payload.Count);
                        createCase(new CreateCaseInfo($"G3132-Sid{sid:X2}-OtherSid{xmlServiceWithSubfunction.Id:X2}", caseMutation, sid, xmlServiceWithSubfunction.Id));
                    }
                }
            }
        }
    }
}
