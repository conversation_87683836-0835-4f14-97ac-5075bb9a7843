using Alsi.Fuzz.Core.Contracts.Tester;
using Alsi.Fuzz.Core.Service;
using Alsi.Fuzz.Core.Service.Tester;
using System;
using System.Threading.Tasks;
using System.Web.Http;

namespace Alsi.Fuzz.Web.Controllers
{
    public class InteroperationController : WebControllerBase
    {
        public StatusPollingService StatusPollingService
            => TestPlanManager.Instance.Interoperation.StatusPollingService;

        [HttpPost]
        [ActionName("start")]
        public async Task<IHttpActionResult> Start()
        {
            if (StatusPollingService.IsTesterRunning)
            {
                return Ok();
            }

            StatusPollingService.TesterSnapshot = new TesterSnapshot();

            var testerManager = TesterManager.Instance;
            // 启动Tester进程并传递参数
            var success = await testerManager.StartAsync();
            if (!success)
            {
                throw new Exception("Failed to start tester");
            }

            var apiClient = testerManager.ApiClient;
            if (apiClient == null)
            {
                throw new Exception("The API client is null");
            }

            // 等待 tester 启动
            await testerManager.WaitTesterAsync(expectIsRunning: true);

            // 通过API启动互操作测试
            var apiResponse = await apiClient.StartInteroperationTestAsync();
            if (!apiResponse.Success)
            {
                throw new Exception(apiResponse.Message);
            }

            await StatusPollingService.UpdateStatusAsync();
            StatusPollingService.StartStatusPolling(true);

            return Ok();
        }

        [HttpPost]
        [ActionName("stop")]
        public async Task<IHttpActionResult> Stop()
        {
            await TesterManager.Instance.ApiClient.StopAsync();
            await StatusPollingService.UpdateStatusAsync();
            StatusPollingService.StopStatusPolling();
            await TesterManager.Instance.ApiClient.ExitAsync();
            return Ok();
        }

        [HttpGet]
        [ActionName("status")]
        public IHttpActionResult GetStatus()
        {
            var snapshot = StatusPollingService.TesterSnapshot;
            if (snapshot.TestResult == null
                && (snapshot.CaseResults?.Length ?? 0) == 0)
            {
                // 当互操作未运行时，也返回互操作对应的用例列表
                snapshot = new TesterSnapshot
                {
                    CurrentOperation = "",
                    ProcessState = 0
                };

                var interoperationService = TestPlanManager.Instance.Interoperation;
                var interoperationModel = interoperationService.BuildInteroperationModel();
                snapshot.TestResult = interoperationModel.TestResult;
                snapshot.CaseResults = interoperationModel.CaseResults;
            }

            return Ok(snapshot);
        }
    }
}
