using Alsi.Fuzz.Core.Contracts.Tester;
using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Models.TestSuites;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Iso11898;
using Alsi.Fuzz.Core.Service.Results;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.Interoperation
{
    public class InteroperationService
    {
        private TesterSnapshot LatestTesterSnapshot = null;
        private TestPlan TestPlan => TestPlanManager.Instance.GetCurrentPlan();
        public StatusPollingService StatusPollingService { get; } = new StatusPollingService();

        private TesterSnapshot RefreshTesterSnapshot(InteroperationModel interoperationModel)
        {
            var snapshot = new TesterSnapshot
            {
                CurrentOperation = "",
                ProcessState = 0
            };

            snapshot.TestResult = interoperationModel.TestResult;
            snapshot.CaseResults = interoperationModel.CaseResults;

            LatestTesterSnapshot = snapshot;

            return snapshot;
        }

        public CaseResult GetCaseResult(Guid testResultId, int caseResultId)
        {
            return LatestTesterSnapshot?.CaseResults
                .FirstOrDefault(x => x.TestResultId == testResultId && x.Id == caseResultId);
        }

        public InteroperationModel BuildInteroperationModel()
        {
            // 获取当前测试计划
            if (TestPlan == null)
            {
                throw new Exception("Please open a test plan");
            }

            var selectedSequence = TestPlan.Config?.SequenceConfigList.FirstOrDefault(x => x.IsSelected);
            if (selectedSequence == null)
            {
                throw new Exception("No active sequence package, please create or select a sequence package.");
            }

            // 检查测试序列配置 - 测试套件名称
            if (string.IsNullOrWhiteSpace(selectedSequence.TestSuiteName))
            {
                throw new Exception("The selected test suite name is empty.");
            }

            // 检查测试序列配置 - 序列包名称
            if (string.IsNullOrWhiteSpace(selectedSequence.SequencePackageName))
            {
                throw new Exception("The selected package name is empty.");
            }

            var xml = selectedSequence.SequencePackageXml;
            if (selectedSequence.IsBuiltIn)
            {
                var builtInTestSuiteService = new BuiltInTestSuiteService();
                xml = builtInTestSuiteService.GetBuiltInXml(selectedSequence.TestSuiteName, selectedSequence.SequencePackageName);
            }

            var testPlanName = Path.GetFileNameWithoutExtension(TestPlanManager.Instance.GetCurrentPlanPath());
            var sequencePackage = SequencePackageUtils.LoadFromString(xml);
            var caseConfig = TestPlan.Config.CaseConfig;

            var testType = TestType.Interoperation;
            var resultFolderName = $"{testPlanName}_{testType}_{DateTime.Now:yyyyMMdd_HHmmss}";

            var testResultId = Guid.NewGuid();
            var testResult = new TestResult
            {
                Id = testResultId,
                TestType = testType,
                TestPlanName = testPlanName,
                ResultFolderName = resultFolderName,
                TotalCount = 0,
                SuccessCount = 0,
                FailureCount = 0,
                CreationTime = DateTime.Now
            };

            var caseResults = CreateCaseResults(testResultId, sequencePackage, caseConfig);
            var interoperationModel = new InteroperationModel
            {
                TestResult = testResult,
                CaseResults = caseResults,
                SequencePackageXml = xml
            };

            RefreshTesterSnapshot(interoperationModel);
            return interoperationModel;
        }

        private CaseResult[] CreateCaseResults(Guid testResultId, SequencePackage sequencePackage, CaseConfig caseConfig)
        {
            var id = 1;
            CaseResult CreateCaseResult(string name)
            {
                var caseResult = new CaseResult
                {
                    Id = id,
                    TestResultId = testResultId,
                    SequenceName = name,
                    Parameter = string.Empty,
                    State = ExecutionState.Pending,
                    Begin = null,
                    End = null,
                    Detail = string.Empty
                };
                ++id;
                return caseResult;
            }

            var caseResults = new List<CaseResult>();

            // ISO-11898 情况下，生成更多互操作测试用例
            if (sequencePackage.Name == FuzzConsts.PackageName11898)
            {
                var frames = new List<WhiteListFrame>();
                frames.AddRange(caseConfig.WhiteListFrames
                    .Where(x => x.Transmitter == caseConfig.SelectedNodeName));
                frames.AddRange(caseConfig.WhiteListFrames
                    .Where(x => x.Receivers.Contains(caseConfig.SelectedNodeName)));

                foreach (var frame in frames)
                {
                    var name = Iso11898_CaseFactoryUtils.BuildSequenceName(frame);
                    var caseResult = CreateCaseResult(name);
                    var parameter = CaseMutation.Create(name)
                        .MutateId(frame.Id)
                        .MutateDlc(frame.Dlc);
                    caseResult.Parameter = parameter.Serialize();
                    caseResults.Add(caseResult);
                }

                caseResults.Add(CreateCaseResult(FuzzConsts.SequenceName11898Extension));
            }
            else
            {
                caseResults.AddRange(sequencePackage.Sequences.Select(x => CreateCaseResult(x.Name)));
            }

            return caseResults.ToArray();
        }
    }
}
