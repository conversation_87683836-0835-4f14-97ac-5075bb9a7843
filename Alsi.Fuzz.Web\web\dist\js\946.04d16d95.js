"use strict";(self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[]).push([[946],{9857:function(e,a,t){t.d(a,{A:function(){return c}});var s=t(6768),l=t(4232),u=t(1021),n=(0,s.pM)({__name:"TestStateTag",props:{state:{}},setup(e){const a=e,t=(0,s.EW)((()=>{switch(a.state){case u.si.Success:return"success";case u.si.Running:return"warning";case u.si.Failure:return"danger";case u.si.Pending:default:return"info"}})),n=e=>{switch(e){case u.si.Running:return"Running";case u.si.Pending:return"Not Run";case u.si.Success:return"Completed";case u.si.Failure:return"Faulted";case u.si.Paused:return"Paused";default:return"Unknown"}},i=(0,s.EW)((()=>n(a.state)));return(e,a)=>{const u=(0,s.g2)("el-tag");return(0,s.uX)(),(0,s.Wv)(u,{type:t.value,size:"small"},{default:(0,s.k6)((()=>[(0,s.eW)((0,l.v_)(i.value),1)])),_:1},8,["type"])}}});const i=n;var c=i},9946:function(e,a,t){t.r(a),t.d(a,{default:function(){return J}});var s=t(6768),l=t(144),u=t(4232),n=t(1219),i=t(1021),c=t(7477),r=t(9857),o=t(4441);const v={class:"test-run-container"},d={class:"toolbar"},k={class:"action-buttons"},p={class:"content-area"},g={class:"overview-layout"},f={class:"progress-section panel"},m={class:"progress-circle-container"},L={class:"test-state"},R={class:"stats-section"},y={class:"stats-grid"},w={class:"stat-card total panel"},C={class:"stat-icon"},b={class:"stat-data"},_={class:"stat-value"},h={class:"stat-card completion panel"},F={class:"stat-icon"},T={class:"stat-data"},W={class:"stat-value"},E={class:"stat-card success panel"},Q={class:"stat-icon"},S={class:"stat-data"},x={class:"stat-value"},P={class:"stat-card failure panel"},X={class:"stat-icon"},D={class:"stat-data"},z={class:"stat-value"},K={key:0,class:"operation-panel panel"},G={class:"current-operation"},I={class:"operation-value"},A={class:"footer-panel panel"},M={class:"timing-info"},N={key:0,class:"time-item"},O={class:"time-value"},V={key:1,class:"time-item"},$={class:"time-value"},q={key:2,class:"time-item"},H={class:"time-value"},U={key:0,class:"last-updated"};var Y=(0,s.pM)({__name:"TestRun",setup(e){const a=(0,l.KR)(0),t=(0,l.KR)(!0),Y=(0,l.KR)(!1),j=(0,l.KR)(!1),B=(0,l.KR)(!1),J=(0,l.KR)(!1),Z=(0,l.KR)(""),ee=(0,l.KR)({processState:i.si.Pending,currentOperation:"",testResult:{id:"",resultFolderName:"",testType:"",creationTime:"",totalCount:0,successCount:0,failureCount:0},caseResults:[]}),ae=(0,l.KR)("");let te=null;const se=(0,s.EW)((()=>ee.value.processState===i.si.Running)),le=(0,s.EW)((()=>ee.value.processState===i.si.Paused)),ue=(0,s.EW)((()=>ee.value.testResult&&0!==ee.value.testResult.totalCount?ee.value.testResult.totalCount:a.value)),ne=(0,s.EW)((()=>ee.value.testResult?(ee.value.testResult.successCount||0)+(ee.value.testResult.failureCount||0):0)),ie=(0,s.EW)((()=>0===ue.value?0:Math.round(ne.value/ue.value*100))),ce=(0,s.EW)((()=>0===ue.value?0:(ne.value/ue.value*100).toFixed(2))),re=e=>{const a=`${ne.value} / ${ue.value}`;return(0,s.dY)((()=>{const e=document.querySelector(".el-progress__text");e&&(e.classList.remove("font-small","font-medium","font-large"),a.length<=11?e.classList.add("font-large"):a.length<=14?e.classList.add("font-medium"):e.classList.add("font-small"))})),a},oe=(0,s.EW)((()=>{if(!Z.value||0===ue.value||0===ne.value||!se.value)return 0;const e=new Date(Z.value),a=Date.now()-e.getTime(),t=a/ne.value,s=ue.value-ne.value;return t*s})),ve=(0,s.EW)((()=>{if(!Z.value||0===ue.value||0===ne.value||!se.value)return"";const e=new Date(Z.value),a=Date.now()-e.getTime(),t=a/ne.value,s=ue.value-ne.value,l=t*s;if(l<=0)return"Completing...";const u=new Date(Date.now()+l);return(0,o.r)(u)})),de=async()=>{t.value=!0;try{const e=await i.GQ.getSavedCaseCount();a.value=e.data}catch(e){console.error("获取保存的测试用例数量失败:",e),n.nk.error("Failed to fetch test cases count")}finally{t.value=!1}},ke=async()=>{if(0!==a.value){Y.value=!0;try{await i.GQ.startTest(),Z.value=(0,o.r)(new Date),n.nk.success("Test execution started"),await me(),Le()}catch(e){console.error("启动测试失败:",e),n.nk.error("Failed to start test execution")}finally{Y.value=!1}}else n.nk.warning("No test cases available to execute")},pe=async()=>{B.value=!0;try{await i.GQ.pauseTest(),n.nk.success("Test execution paused"),await me()}catch(e){console.error("暂停测试失败:",e),n.nk.error("Failed to pause test execution")}finally{B.value=!1}},ge=async()=>{J.value=!0;try{await i.GQ.resumeTest(),n.nk.success("Test execution resumed"),await me()}catch(e){console.error("恢复测试失败:",e),n.nk.error("Failed to resume test execution")}finally{J.value=!1}},fe=async()=>{j.value=!0;try{await i.GQ.stopTest(),n.nk.success("Test execution stopped"),await me()}catch(e){console.error("停止测试失败:",e),n.nk.error("Failed to stop test execution")}finally{j.value=!1}},me=async()=>{try{const e=await i.GQ.getTestStatus();ee.value=e.data,ee.value.testResult||(ee.value.processState=i.si.Pending),ae.value=(0,o.r)(new Date),(0,i.xh)(ee.value)&&te&&(de(),Re())}catch(e){console.error("获取测试状态失败:",e)}},Le=()=>{Re(),te=window.setInterval(me,500)},Re=()=>{te&&(clearInterval(te),te=null)};return(0,s.sV)((()=>{de(),me().then((()=>{se.value&&(ee.value.testResult?.creationTime?Z.value=(0,o.r)(new Date(ee.value.testResult.creationTime)):Z.value=(0,o.r)(new Date),Le())}))})),(0,s.hi)((()=>{Re()})),(e,t)=>{const n=(0,s.g2)("el-button"),i=(0,s.g2)("el-progress"),te=(0,s.g2)("el-icon");return(0,s.uX)(),(0,s.CE)("div",v,[(0,s.Lk)("div",d,[t[4]||(t[4]=(0,s.Lk)("h3",null,"Test Execution",-1)),(0,s.Lk)("div",k,[se.value||le.value?(0,s.Q3)("",!0):((0,s.uX)(),(0,s.Wv)(n,{key:0,type:"success",size:"small",icon:(0,l.R1)(c.CaretRight),loading:Y.value,onClick:ke,disabled:0===a.value},{default:(0,s.k6)((()=>t[0]||(t[0]=[(0,s.eW)(" Start ")]))),_:1},8,["icon","loading","disabled"])),se.value&&!le.value?((0,s.uX)(),(0,s.Wv)(n,{key:1,type:"warning",size:"small",icon:(0,l.R1)(c.VideoPause),loading:B.value,onClick:pe},{default:(0,s.k6)((()=>t[1]||(t[1]=[(0,s.eW)(" Pause ")]))),_:1},8,["icon","loading"])):(0,s.Q3)("",!0),le.value?((0,s.uX)(),(0,s.Wv)(n,{key:2,type:"info",size:"small",icon:(0,l.R1)(c.CaretRight),loading:J.value,onClick:ge},{default:(0,s.k6)((()=>t[2]||(t[2]=[(0,s.eW)(" Resume ")]))),_:1},8,["icon","loading"])):(0,s.Q3)("",!0),se.value||le.value?((0,s.uX)(),(0,s.Wv)(n,{key:3,type:"danger",size:"small",icon:(0,l.R1)(c.VideoPlay),loading:j.value,onClick:fe},{default:(0,s.k6)((()=>t[3]||(t[3]=[(0,s.eW)(" Stop ")]))),_:1},8,["icon","loading"])):(0,s.Q3)("",!0)])]),(0,s.Lk)("div",p,[(0,s.Lk)("div",g,[(0,s.Lk)("div",f,[(0,s.Lk)("div",m,[(0,s.bF)(i,{type:"dashboard",percentage:ie.value,width:180,"stroke-width":12,format:re},null,8,["percentage"]),(0,s.Lk)("div",L,[(0,s.bF)(r.A,{state:ee.value.processState},null,8,["state"])])])]),(0,s.Lk)("div",R,[(0,s.Lk)("div",y,[(0,s.Lk)("div",w,[(0,s.Lk)("div",C,[(0,s.bF)(te,null,{default:(0,s.k6)((()=>[(0,s.bF)((0,l.R1)(c.InfoFilled))])),_:1})]),(0,s.Lk)("div",b,[(0,s.Lk)("div",_,(0,u.v_)(ue.value),1),t[5]||(t[5]=(0,s.Lk)("div",{class:"stat-label"},"Total Test Cases",-1))])]),(0,s.Lk)("div",h,[(0,s.Lk)("div",F,[(0,s.bF)(te,null,{default:(0,s.k6)((()=>[(0,s.bF)((0,l.R1)(c.Histogram))])),_:1})]),(0,s.Lk)("div",T,[(0,s.Lk)("div",W,(0,u.v_)(ce.value)+"%",1),t[6]||(t[6]=(0,s.Lk)("div",{class:"stat-label"},"Completion Rate",-1))])]),(0,s.Lk)("div",E,[(0,s.Lk)("div",Q,[(0,s.bF)(te,null,{default:(0,s.k6)((()=>[(0,s.bF)((0,l.R1)(c.CircleCheckFilled))])),_:1})]),(0,s.Lk)("div",S,[(0,s.Lk)("div",x,(0,u.v_)(ee.value.testResult?.successCount||0),1),t[7]||(t[7]=(0,s.Lk)("div",{class:"stat-label"},"Passed Test Cases",-1))])]),(0,s.Lk)("div",P,[(0,s.Lk)("div",X,[(0,s.bF)(te,null,{default:(0,s.k6)((()=>[(0,s.bF)((0,l.R1)(c.CircleCloseFilled))])),_:1})]),(0,s.Lk)("div",D,[(0,s.Lk)("div",z,(0,u.v_)(ee.value.testResult?.failureCount||0),1),t[8]||(t[8]=(0,s.Lk)("div",{class:"stat-label"},"Failed Test Cases",-1))])])])])]),ee.value.currentOperation&&ve.value?((0,s.uX)(),(0,s.CE)("div",K,[(0,s.Lk)("div",G,[t[9]||(t[9]=(0,s.Lk)("span",{class:"operation-label"},"Status:",-1)),(0,s.Lk)("span",I,(0,u.v_)(ee.value.currentOperation),1)])])):(0,s.Q3)("",!0),(0,s.Lk)("div",A,[(0,s.Lk)("div",M,[Z.value?((0,s.uX)(),(0,s.CE)("div",N,[t[10]||(t[10]=(0,s.Lk)("span",{class:"time-label"},"Start:",-1)),(0,s.Lk)("span",O,(0,u.v_)(Z.value),1)])):(0,s.Q3)("",!0),ve.value?((0,s.uX)(),(0,s.CE)("div",V,[t[11]||(t[11]=(0,s.Lk)("span",{class:"time-label"},"Estimated End:",-1)),(0,s.Lk)("span",$,(0,u.v_)(ve.value),1)])):(0,s.Q3)("",!0),oe.value>0?((0,s.uX)(),(0,s.CE)("div",q,[t[12]||(t[12]=(0,s.Lk)("span",{class:"time-label"},"Remaining:",-1)),(0,s.Lk)("span",H,(0,u.v_)((0,l.R1)(o.a)(oe.value)),1)])):(0,s.Q3)("",!0)]),ae.value?((0,s.uX)(),(0,s.CE)("div",U," Last updated: "+(0,u.v_)(ae.value),1)):(0,s.Q3)("",!0)])])])}}}),j=t(1241);const B=(0,j.A)(Y,[["__scopeId","data-v-b43f6810"]]);var J=B}}]);
//# sourceMappingURL=946.04d16d95.js.map