using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Utils;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso15765
{
    public class G33_G34_CaseFactory : CaseFactoryBase
    {
        public override CaseMutation[] Generate(MutationOptions options)
        {
            var list = new List<CaseMutation>();
            var dlc = 8;

            // G331
            var groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FirstFrame().FfDlLessThan4096().ValidNpci().GivenParameters().Path;
            {
                var validFfdl12b = 8;
                var caseMutation = CaseMutation.Create($"G331-FF_DL_12b_{validFfdl12b:X}",groupPath)
                    .MutateDlc(dlc)
                    .MutateTpFfHighNibble(1)
                    .MutateTpFfDl12b(validFfdl12b)
                    .MutateTpParameters(GetTpParameters(options.TpParameters, validFfdl12b));
                list.Add(caseMutation);
            }

            // G332
            groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FirstFrame().FfDlLessThan4096().ValidNpci().GivenParameters().Path;
            var sampleCountG332 = 256;
            if (options.Coverage == CoverageType.High)
            {
                sampleCountG332 = 1024;
            }

            var ffDlArray = UniformSample(9, 4095, sampleCountG332);
            foreach (var ffDl12b in ffDlArray)
            {
                var caseMutation = CaseMutation.Create($"G332-FF_DL_12b_{ffDl12b:X}", groupPath)
                    .MutateDlc(dlc)
                    .MutateTpFfHighNibble(1)
                    .MutateTpFfDl12b(ffDl12b)
                    .MutateTpParameters(GetTpParameters(options.TpParameters, ffDl12b));
                list.Add(caseMutation);
            }

            // G333
            groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FirstFrame().FfDlLessThan4096().ValidNpci().RandomParameters().Path;
           
            var sampleCountG333 = 32;
            if (options.Coverage == CoverageType.High)
            {
                sampleCountG333 = 128;
            }

            var ffDlArrayG333 = UniformSample(8, 4095, sampleCountG333);


            foreach (var ffDl in ffDlArrayG333)
            {
                var lhsSampler = new LhsSampler();
                var tpParameters = new List<byte>();

                var byteCount = ffDl;
                while (byteCount > 0)
                {
                    if (byteCount > 16)
                    {
                        var bytesGroup = lhsSampler.GenerateByteBasedSamples(16, 1);
                        tpParameters.AddRange(bytesGroup.First());
                        byteCount -= 16;
                    }
                    else
                    {
                        var bytesGroup = lhsSampler.GenerateByteBasedSamples(byteCount, 1);
                        tpParameters.AddRange(bytesGroup.First());
                        byteCount = 0;
                    }
                }

                var caseMutation = CaseMutation.Create($"G333-FF_DL_12b_{ffDl:X}", groupPath)
                    .MutateDlc(dlc)
                    .MutateTpFfHighNibble(1)
                    .MutateTpFfDl12b(ffDl)
                    .MutateTpParameters(tpParameters.ToArray());
                list.Add(caseMutation);
            }

            // G334
            groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FirstFrame().FfDlLessThan4096().InvalidNpci().ValidByte1HighInvalidFfDl();
            for (var invalidFfDl12b = 0; invalidFfDl12b <= 7; invalidFfDl12b++)
            {
                var caseMutation = CaseMutation.Create($"G334-FF_DL_12b_{invalidFfDl12b:X}", groupPath)
                    .MutateDlc(dlc)
                    .MutateTpFfHighNibble(1)
                    .MutateTpFfDl12b(invalidFfDl12b)
                    // 当FFDL_12bit取值为invalid时， 只需要发送单帧即可
                    .MutateTpParameters(options.TpParameters);
                list.Add(caseMutation);
            }

            // G335
            groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FirstFrame().FfDlLessThan4096().InvalidNpci().InvalidByte1HighValidFfDl();
            for (var invalidHighNibble = 4; invalidHighNibble <= 0xF; invalidHighNibble++)
            {
                var randomCount = 2;
                if (options.Coverage == CoverageType.High)
                {
                    randomCount = 8;
                }
                var uniqueInts = RandomUniqueInts(8, 4095, randomCount);
                foreach (var ffDl12b in uniqueInts)
                {
                    var caseMutation = CaseMutation.Create($"G335-HighNibble_{invalidHighNibble:X}-FF_DL_12b_{ffDl12b:X}", groupPath)
                        .MutateDlc(dlc)
                        .MutateTpFfHighNibble((byte)invalidHighNibble)
                        .MutateTpFfDl12b(ffDl12b)
                        .MutateTpParameters(GetTpParameters(options.TpParameters, ffDl12b));
                    list.Add(caseMutation);
                }
            }

            // G336
            groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FirstFrame().FfDlLessThan4096().InvalidNpci().InvalidByte1HighInvalidFfDl();
            for (var invalidHighNibble = 4; invalidHighNibble <= 0xF; invalidHighNibble++)
            {
                var uniqueInts = Enumerable.Range(0, 7 + 1);
                foreach (var ffDl12b in uniqueInts)
                {
                    var caseMutation = CaseMutation.Create($"G336-HighNibble_{invalidHighNibble:X}-FF_DL_12b_{ffDl12b:X}", groupPath)
                        .MutateDlc(dlc)
                        .MutateTpFfHighNibble((byte)invalidHighNibble)
                        .MutateTpFfDl12b(ffDl12b)
                        // 当FFDL_12bit取值为invalid时， 只需要发送单帧即可
                        .MutateTpParameters(options.TpParameters);
                    list.Add(caseMutation);
                }
            }
            
            
            if (options.IsDutMtuLessThan4096)
            {
                // G3411
                groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FirstFrame().FfDlMoreThan4095().ValidNpci().GivenParameters().Path;
                var randomCountG3411 = 1;
                if (options.Coverage == CoverageType.High)
                {
                    randomCountG3411 = 4;
                }
                var ffDl32bList = RandomUniqueInts(4096, 4351, randomCountG3411);
                foreach (var ffDl32b in ffDl32bList)
                {
                    var caseMutation = CaseMutation.Create($"G3411-FF_DL_32b_{ffDl32b:X}", groupPath)
                        .MutateDlc(dlc)
                        .MutateTpFfByte1(0x10)
                        .MutateTpFfByte2(0)
                        .MutateTpFfDl32b((uint)ffDl32b)
                        .MutateTpParameters(GetTpParameters(options.TpParameters, ffDl32b));
                    list.Add(caseMutation);
                }

                // G3412
                groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FirstFrame().FfDlMoreThan4095().InvalidNpci().InvalidByte1();
                var invalidByte1ArrayG3412 = Enumerable.Range(0x33, 0x3F - 0x33 + 1).Select(x => (byte)x).ToArray();
                foreach (var invalidByte1 in invalidByte1ArrayG3412)
                {
                    var ffDl32b = random.Next(4096, 4352);
                    var caseMutation = CaseMutation.Create($"G3412-FF_DL_32b_{ffDl32b:X}", groupPath)
                        .MutateDlc(dlc)
                        .MutateTpFfByte1(invalidByte1)
                        .MutateTpFfByte2(0)
                        .MutateTpFfDl32b((uint)ffDl32b)
                        .MutateTpParameters(GetTpParameters(options.TpParameters, ffDl32b));
                    list.Add(caseMutation);
                }

                // G3413
                groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FirstFrame().FfDlMoreThan4095().InvalidNpci().InvalidByte1();
                var sampleCountG3413 = 48;
                if (options.Coverage == CoverageType.High)
                {
                    sampleCountG3413 = 193;
                }
                var invalidByte1ArrayG3413 = UniformSampleBytes(0x40, 0xFF, sampleCountG3413);
                foreach (var invalidByte1 in invalidByte1ArrayG3413)
                {
                    var ffDl32b = random.Next(4096, 4352);
                    var caseMutation = CaseMutation.Create($"G3413-FF_DL_32b_{ffDl32b:X}", groupPath)
                        .MutateDlc(dlc)
                        .MutateTpFfByte1(invalidByte1)
                        .MutateTpFfByte2(0)
                        .MutateTpFfDl32b((uint)ffDl32b)
                        .MutateTpParameters(GetTpParameters(options.TpParameters, ffDl32b));
                    list.Add(caseMutation);
                }

                // G3414
                groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FirstFrame().FfDlMoreThan4095().InvalidNpci().InvalidFFDL();
                var randomCountG3414 = 1;
                if (options.Coverage == CoverageType.High)
                {
                    randomCountG3414 = 4;
                }

                foreach (var ffDl32b in RandomUniqueInts(0, 4095, randomCountG3414))
                {
                    var byte1 = random.Next(0x40, 0x100);
                    var caseMutation = CaseMutation.Create($"G3414-FF_DL_32b_{ffDl32b:X}", groupPath)
                        .MutateDlc(dlc)
                        .MutateTpFfByte1((byte)byte1)
                        .MutateTpFfByte2(0)
                        .MutateTpFfDl32b((uint)ffDl32b)
                        .MutateTpParameters(GetTpParameters(options.TpParameters, ffDl32b));
                    list.Add(caseMutation);
                }

                // G3415
                groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FirstFrame().FfDlMoreThan4095().InvalidNpci().InvalidByte2();
                var sampleCountG3415 = 4;
                if (options.Coverage == CoverageType.High)
                {
                    sampleCountG3415 = 16;
                }

                foreach (var byte2 in UniformSampleBytes(1, 0xFF, sampleCountG3415))
                {
                    var byte1 = random.Next(0x33, 0x100);
                    var ffDl32b = random.Next(4096, 4352);
                    var caseMutation = CaseMutation.Create($"G3415-FF_DL_32b_{ffDl32b:X}",groupPath)
                        .MutateDlc(dlc)
                        .MutateTpFfByte1((byte)byte1)
                        .MutateTpFfByte2(byte2)
                        .MutateTpFfDl32b((uint)ffDl32b)
                        .MutateTpParameters(GetTpParameters(options.TpParameters, ffDl32b));
                    list.Add(caseMutation);
                }
            }
            else
            {
                // G3421
                groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FirstFrame().FfDlMoreThan4095().ValidNpci().GivenParameters().Path;
                var randomCountG3421 = 16;
                if (options.Coverage == CoverageType.High)
                {
                    randomCountG3421 = 64;
                }

                foreach (var ffDl32b in RandomUniqueInts(4095, 8192, randomCountG3421))
                {
                    var caseMutation = CaseMutation.Create($"G3421-FF_DL_32b_{ffDl32b:X}",groupPath)
                        .MutateDlc(dlc)
                        .MutateTpFfByte1(0x10)
                        .MutateTpFfByte2(0)
                        .MutateTpFfDl32b((uint)ffDl32b)
                        .MutateTpParameters(GetTpParameters(options.TpParameters, ffDl32b));
                    list.Add(caseMutation);
                }

                // G3422
                groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FirstFrame().FfDlMoreThan4095().InvalidNpci().InvalidFFDL();
                var sampleCountG3422 = 32;
                if (options.Coverage == CoverageType.High)
                {
                    sampleCountG3422 = 128;
                }

                foreach (var ffDl32b in UniformSample(0, 4095, sampleCountG3422))
                {
                    var caseMutation = CaseMutation.Create($"G3422-FF_DL_32b_{ffDl32b:X}", groupPath)
                        .MutateDlc(dlc)
                        .MutateTpFfByte1(0x10)
                        .MutateTpFfByte2(0)
                        .MutateTpFfDl32b((uint)ffDl32b)
                        .MutateTpParameters(GetTpParameters(options.TpParameters, ffDl32b));
                    list.Add(caseMutation);
                }

                // G3423
                groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FirstFrame().FfDlMoreThan4095().InvalidNpci().InvalidByte1();
                var sampleCountG3423 = 51;
                if (options.Coverage == CoverageType.High)
                {
                    sampleCountG3423 = 205;
                }

                foreach (var byte1 in UniformSampleBytes(51, 0xFF, sampleCountG3423))
                {
                    var ffDl32b = random.Next(4096, 4352);
                    var caseMutation = CaseMutation.Create($"G3423-Byte1_{byte1:X}" ,groupPath)
                        .MutateDlc(dlc)
                        .MutateTpFfByte1(byte1)
                        .MutateTpFfByte2(0)
                        .MutateTpFfDl32b((uint)ffDl32b)
                        .MutateTpParameters(GetTpParameters(options.TpParameters, ffDl32b));
                    list.Add(caseMutation);
                }

                // G3424
                groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FirstFrame().FfDlMoreThan4095().InvalidNpci().InvalidByte1();
                var sampleCountG3424 = 4;
                if (options.Coverage == CoverageType.High)
                {
                    sampleCountG3424 = 16;
                }

                foreach (var ffDl32b in UniformSample(0, 4095, sampleCountG3424))
                {
                    var byte1 = random.Next(51, 256);
                    var caseMutation = CaseMutation.Create($"G3424-FF_DL_32b_{ffDl32b:X}", groupPath)
                        .MutateDlc(dlc)
                        .MutateTpFfByte1((byte)byte1)
                        .MutateTpFfByte2(0)
                        .MutateTpFfDl32b((uint)ffDl32b)
                        .MutateTpParameters(GetTpParameters(options.TpParameters, ffDl32b));
                    list.Add(caseMutation);
                }

                // G3425
                groupPath = Iso15765CaseGroupConsts.General().ProtocolFormat().FirstFrame().FfDlMoreThan4095().InvalidNpci().InvalidByte2();
                var sampleCountG3425 = 4;
                if (options.Coverage == CoverageType.High)
                {
                    sampleCountG3425 = 16;
                }

                foreach (var byte2 in UniformSampleBytes(0, 0xFF, sampleCountG3425))
                {
                    var byte1 = random.Next(51, 256);
                    var ffDl32b = random.Next(4096, 4352);
                    var caseMutation = CaseMutation.Create($"G3425-FF_DL_32b_{ffDl32b:X}", groupPath)
                        .MutateDlc(dlc)
                        .MutateTpFfByte1((byte)byte1)
                        .MutateTpFfByte2(byte2)
                        .MutateTpFfDl32b((uint)ffDl32b)
                        .MutateTpParameters(GetTpParameters(options.TpParameters, ffDl32b));
                    list.Add(caseMutation);
                }
            }

            return list.ToArray();
        }
    }
}
