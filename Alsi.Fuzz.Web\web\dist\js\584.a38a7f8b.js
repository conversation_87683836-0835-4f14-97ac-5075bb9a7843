"use strict";(self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[]).push([[584],{3584:function(e,a,l){l.r(a),l.d(a,{default:function(){return K}});l(8111),l(2489),l(7588),l(1701),l(3579),l(7642),l(8004),l(3853),l(5876),l(2475),l(5024),l(1698);var t=l(6768),u=l(144),n=l(4232),s=l(1219),o=l(7477),d=l(4373),r=l(3144);const i="/api/caseconfig",m={getCaseConfig(){return r.Xo?r.Z0.case.getCaseConfig():d.A.get(`${i}`)},updateCaseConfig(e){return r.Xo?r.Z0.case.updateCaseConfig(e):d.A.post(`${i}/update`,e)},importDbc(){return r.Xo?r.Z0.case.importDbc():d.A.get(`${i}/import-dbc`)},selectSecurityDll(){return r.Xo?r.Z0.case.selectSecurityDll():d.A.post(`${i}/select-security-dll`)}};const c={class:"case-setting-container"},p={class:"toolbar top-toolbar"},v={class:"toolbar-left"},b={class:"content-area"},k={class:"card-container"},f={class:"form-row"},F={class:"form-row"},g={class:"comment-row"},y={class:"card-container"},h={key:0,class:"import-note"},C={key:1,class:"node-selection-required"},x={class:"card-container"},D={class:"nm-wakeup-row"},w={class:"card-container"},I={class:"form-row"},W={class:"comment-row"},R={class:"security-input-group"},L={class:"security-buttons"},V={key:0,class:"selected-dll-info"},_={key:1,class:"selected-dll-info"},N={class:"card-container"},U={class:"form-row"},E={class:"comment-row"},S={class:"toolbar bottom-toolbar"};var q=(0,t.pM)({__name:"CaseSetting",setup(e){const a=(0,u.KR)(!1),l=(0,u.KR)({whiteListFrames:[],enableNmWakeup:!0,nmWakeupId:1343,nmWakeupIsExt:!1,nmWakeupDlc:8,nmWakeupData:[63,80,255,255,255,255,255,255],nmWakeupCommunicationType:"Can",nmWakeupCycleMs:100,nmWakeupDelayMs:2e3,requestId:1841,requestIsExt:!1,responseId:1585,timeoutMs:500,isDutMtuLessThan4096:!1,enableDiagRetryRequest:!1,diagRetryRequestPayload:[16,1],securityInfo:{hasDll:!1,dllFileName:void 0,dllSize:0}}),d=(0,u.KR)([]),r=(0,u.KR)(""),i=(0,u.KR)([]),q=(0,t.EW)((()=>{if(!r.value)return[];const e=i.value.filter((e=>e.transmitter===r.value)),a=i.value.filter((e=>e.transmitter!==r.value&&e.receivers.includes(r.value)));return[...e,...a]})),M=(0,u.KR)(["case","database","nmWakeup","uds","log"]),A=(0,u.KR)(""),K=(0,u.KR)(""),T=(0,u.KR)(""),P=(0,u.KR)(""),X=(0,u.KR)(""),z=(0,u.KR)(""),B=(0,t.EW)({get:()=>"Can"===l.value.nmWakeupCommunicationType?"CAN Frame":"CANFD Frame",set:e=>{l.value.nmWakeupCommunicationType="CAN Frame"===e?"Can":"Canfd"}}),$=(0,u.KR)(null),Q=(0,u.KR)(null),O=(0,u.KR)(!1),Z={whiteListFrames:[],enableNmWakeup:!0,nmWakeupId:1343,nmWakeupIsExt:!1,nmWakeupDlc:8,nmWakeupData:[63,80,255,255,255,255,255,255],nmWakeupCommunicationType:"Can",nmWakeupCycleMs:100,nmWakeupDelayMs:2e3,requestId:1841,requestIsExt:!1,responseId:1585,timeoutMs:500,isDutMtuLessThan4096:!1,enableDiagRetryRequest:!1,diagRetryRequestPayload:[16,1],securityInfo:{hasDll:!1,dllFileName:void 0,dllSize:0}},j=e=>e?e.map((e=>"0x"+e.toString(16).toUpperCase())).join(","):"",J=e=>{try{const a=e.replace(/^0x/i,""),t=parseInt(a,16);if(isNaN(t))return void s.nk.error("Invalid ID format");l.value.nmWakeupId=t,A.value="0x"+t.toString(16).toUpperCase()}catch(a){s.nk.error("Invalid input value")}},G=e=>{try{const a=e.replace(/^0x/i,""),t=parseInt(a,16);if(isNaN(t))return void s.nk.error("Invalid DLC format");if(t<0||t>64)return void s.nk.warning("DLC should be between 0 and 64");l.value.nmWakeupDlc=t,K.value="0x"+t.toString(16).toUpperCase()}catch(a){s.nk.error("Invalid input value")}},H=e=>{try{const a=e.split(",").map((e=>e.trim())).filter((e=>e)).map((e=>parseInt(e,16)));if(a.length>8)return void s.nk.warning("Maximum 8 bytes allowed");if(a.some((e=>isNaN(e)||e<0||e>255)))return void s.nk.warning("Each byte must be between 0x00 and 0xFF");l.value.nmWakeupData=a}catch(a){s.nk.error("Invalid input format")}},Y=e=>{try{const a=e.replace(/^0x/i,""),t=parseInt(a,16);if(isNaN(t))return void s.nk.error("Invalid ID format");l.value.requestId=t,P.value="0x"+t.toString(16).toUpperCase()}catch(a){s.nk.error("Invalid input value")}},ee=e=>{try{const a=e.replace(/^0x/i,""),t=parseInt(a,16);if(isNaN(t))return void s.nk.error("Invalid ID format");l.value.responseId=t,X.value="0x"+t.toString(16).toUpperCase()}catch(a){s.nk.error("Invalid input value")}},ae=e=>{try{const a=e.split(",").map((e=>e.trim())).filter((e=>e)).map((e=>parseInt(e,16)));if(a.some((e=>isNaN(e)||e<0||e>255)))return void s.nk.warning("Each byte must be between 0x00 and 0xFF");l.value.diagRetryRequestPayload=a}catch(a){s.nk.error("Invalid input format")}},le=()=>{l.value=JSON.parse(JSON.stringify(Z)),A.value="0x"+Z.nmWakeupId.toString(16).toUpperCase(),K.value="0x"+Z.nmWakeupDlc.toString(16).toUpperCase(),T.value=j(Z.nmWakeupData),P.value="0x"+Z.requestId.toString(16).toUpperCase(),X.value="0x"+Z.responseId.toString(16).toUpperCase(),z.value=j(Z.diagRetryRequestPayload),$.value=null,Q.value=null,O.value=!1,d.value=[],r.value="",i.value=[]},te=async()=>{a.value=!0;try{const e=await m.getCaseConfig();if(l.value=e.data,A.value="0x"+e.data.nmWakeupId.toString(16).toUpperCase(),T.value=j(e.data.nmWakeupData),P.value="0x"+e.data.requestId.toString(16).toUpperCase(),X.value="0x"+e.data.responseId.toString(16).toUpperCase(),e.data.diagRetryRequestPayload?z.value=j(e.data.diagRetryRequestPayload):z.value="0x10,0x01",void 0===e.data.nmWakeupIsExt&&(l.value.nmWakeupIsExt=!1),void 0===e.data.nmWakeupDlc&&(l.value.nmWakeupDlc=8),void 0===e.data.requestIsExt&&(l.value.requestIsExt=!1),void 0===e.data.enableDiagRetryRequest&&(l.value.enableDiagRetryRequest=!1),void 0===e.data.diagRetryRequestPayload&&(l.value.diagRetryRequestPayload=[16,1]),K.value="0x"+l.value.nmWakeupDlc.toString(16).toUpperCase(),e.data.whiteListFrames.length>0){const a=e.data.whiteListFrames;i.value=a;const l=new Set;a.forEach((e=>{e.transmitter&&l.add(e.transmitter),e.receivers?.forEach((e=>l.add(e)))})),d.value=Array.from(l),e.data.selectedNodeName&&d.value.includes(e.data.selectedNodeName)?r.value=e.data.selectedNodeName:1===d.value.length&&(r.value=d.value[0])}}catch(e){s.nk.error("Failed to load configuration")}finally{a.value=!1}},ue=e=>{e||s.nk.warning("Please select a target ECU")},ne=async()=>{a.value=!0;try{const e={...l.value};e.whiteListFrames=i.value,e.selectedNodeName=r.value,$.value&&(e.securityDllPath=$.value),O.value&&(e.removeSecurityDll=!0);const a=await m.updateCaseConfig(e);l.value=a.data,$.value=null,Q.value=null,O.value=!1,s.nk.success("Case settings saved.")}catch(e){s.nk.error("Save failed")}finally{a.value=!1}},se=(0,u.KR)(!1),oe=async()=>{se.value=!0;try{const e=await m.importDbc();i.value=e.data.whiteListFrames,d.value=e.data.nodeNames,r.value="",l.value.whiteListFrames=[],s.nk.success("DBC file imported successfully. Please select a target ECU.")}catch(e){if("UserCanceled"===e.response?.data)return;s.nk.error("InvalidFileFormat"===e.response?.data?"Invalid DBC file format":"Failed to import DBC file")}finally{se.value=!1}},de=e=>{if(!e)return"0 B";const a=["B","KB","MB","GB"];let l=e,t=0;while(l>=1024&&t<a.length-1)l/=1024,t++;return`${l.toFixed(2)} ${a[t]}`},re=async()=>{try{const e=await m.selectSecurityDll(),a=e.data.path;$.value=a,Q.value=a.split("\\").pop()||a}catch(e){if("UserCanceled"===e.response?.data)return;s.nk.error("InvalidFileFormat"===e.response?.data?"Invalid DLL file format":"Failed to select DLL file")}},ie=()=>{O.value=!0},me=(0,t.EW)((()=>Q.value?Q.value:O.value?"DLL will be removed after save":l.value.securityInfo?.hasDll?`${l.value.securityInfo.dllFileName} (${de(l.value.securityInfo.dllSize)})`:"")),ce=()=>{M.value=["case","database","nmWakeup","uds","log"]},pe=()=>{M.value=[]};return(0,t.sV)((()=>{te()})),(e,s)=>{const m=(0,t.g2)("font-awesome-icon"),$=(0,t.g2)("el-button"),Z=(0,t.g2)("el-input"),j=(0,t.g2)("el-form-item"),te=(0,t.g2)("el-switch"),de=(0,t.g2)("el-input-number"),ve=(0,t.g2)("el-icon"),be=(0,t.g2)("el-form"),ke=(0,t.g2)("el-collapse-item"),fe=(0,t.g2)("el-option"),Fe=(0,t.g2)("el-select"),ge=(0,t.g2)("el-table-column"),ye=(0,t.g2)("el-table"),he=(0,t.g2)("el-collapse"),Ce=(0,t.gN)("loading");return(0,t.bo)(((0,t.uX)(),(0,t.CE)("div",c,[(0,t.Lk)("div",p,[(0,t.Lk)("div",v,[(0,t.bF)($,{onClick:ce,type:"primary",size:"small",class:"expand-button"},{default:(0,t.k6)((()=>[(0,t.bF)(m,{icon:"up-right-and-down-left-from-center"}),s[19]||(s[19]=(0,t.Lk)("span",{class:"button-text"},"Expand All",-1))])),_:1}),(0,t.bF)($,{onClick:pe,type:"primary",size:"small",class:"collapse-button"},{default:(0,t.k6)((()=>[(0,t.bF)(m,{icon:"down-left-and-up-right-to-center"}),s[20]||(s[20]=(0,t.Lk)("span",{class:"button-text"},"Collapse All",-1))])),_:1})])]),(0,t.Lk)("div",b,[(0,t.bF)(he,{modelValue:M.value,"onUpdate:modelValue":s[18]||(s[18]=e=>M.value=e)},{default:(0,t.k6)((()=>[(0,t.Lk)("div",k,[(0,t.bF)(ke,{title:"Case Setting",name:"case",class:"custom-card"},{default:(0,t.k6)((()=>[(0,t.bF)(be,{model:l.value,"label-width":"160px","label-position":"top"},{default:(0,t.k6)((()=>[(0,t.Lk)("div",f,[(0,t.bF)(j,{label:"Request ID",class:"form-item"},{default:(0,t.k6)((()=>[(0,t.bF)(Z,{modelValue:P.value,"onUpdate:modelValue":s[0]||(s[0]=e=>P.value=e),placeholder:"Enter ID in hex, e.g. 0x731",onChange:Y},null,8,["modelValue"])])),_:1}),(0,t.bF)(j,{label:"Ext Flag",class:"form-item",style:{width:"80px"}},{default:(0,t.k6)((()=>[(0,t.bF)(te,{modelValue:l.value.requestIsExt,"onUpdate:modelValue":s[1]||(s[1]=e=>l.value.requestIsExt=e)},null,8,["modelValue"])])),_:1})]),(0,t.Lk)("div",F,[(0,t.bF)(j,{label:"Response ID",class:"form-item"},{default:(0,t.k6)((()=>[(0,t.bF)(Z,{modelValue:X.value,"onUpdate:modelValue":s[2]||(s[2]=e=>X.value=e),placeholder:"Enter ID in hex, e.g. 0x631",onChange:ee},null,8,["modelValue"])])),_:1}),(0,t.bF)(j,{label:"Timeout (ms)",class:"form-item"},{default:(0,t.k6)((()=>[(0,t.bF)(de,{modelValue:l.value.timeoutMs,"onUpdate:modelValue":s[3]||(s[3]=e=>l.value.timeoutMs=e),min:1,max:6e4,style:{width:"200px"}},null,8,["modelValue"])])),_:1})]),(0,t.Lk)("div",g,[(0,t.bF)(ve,null,{default:(0,t.k6)((()=>[(0,t.bF)((0,u.R1)(o.InfoFilled))])),_:1}),s[21]||(s[21]=(0,t.eW)()),s[22]||(s[22]=(0,t.Lk)("span",null,"ISO-11898 and ISO-14229 protocols share the same Response ID, and Timeout setting.",-1))])])),_:1},8,["model"])])),_:1})]),(0,t.Lk)("div",y,[(0,t.bF)(ke,{title:"Database Setting",name:"database",class:"custom-card"},{default:(0,t.k6)((()=>[(0,t.bF)(be,{model:l.value,"label-width":"160px","label-position":"top"},{default:(0,t.k6)((()=>[(0,t.bF)(j,null,{default:(0,t.k6)((()=>[(0,t.bF)($,{onClick:oe,loading:se.value,type:"primary",size:"small",style:{"margin-bottom":"15px"}},{default:(0,t.k6)((()=>s[23]||(s[23]=[(0,t.eW)(" Import DBC ")]))),_:1},8,["loading"])])),_:1}),d.value.length>0?((0,t.uX)(),(0,t.Wv)(j,{key:0,label:"Target ECU (DUT)"},{default:(0,t.k6)((()=>[(0,t.bF)(Fe,{modelValue:r.value,"onUpdate:modelValue":s[4]||(s[4]=e=>r.value=e),placeholder:"Select target ECU",style:{width:"100%"},onChange:ue},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(d.value,(e=>((0,t.uX)(),(0,t.Wv)(fe,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})):(0,t.Q3)("",!0),(0,t.bF)(j,{label:`White List Frames (${q.value.length})`},{default:(0,t.k6)((()=>[0===i.value.length?((0,t.uX)(),(0,t.CE)("div",h,' No frames imported. Please use "Import DBC" button above to add frames. ')):r.value?((0,t.uX)(),(0,t.Wv)(ye,{key:2,data:q.value,style:{width:"100%"},"max-height":400,border:""},{default:(0,t.k6)((()=>[(0,t.bF)(ge,{label:"ID","min-width":"120"},{default:(0,t.k6)((({row:e})=>[(0,t.eW)(" 0x"+(0,n.v_)(e.id.toString(16).toUpperCase().padStart(3,"0")),1)])),_:1}),(0,t.bF)(ge,{prop:"name",label:"Name","min-width":"180"}),(0,t.bF)(ge,{label:"DLC","min-width":"80"},{default:(0,t.k6)((({row:e})=>[(0,t.eW)(" 0x"+(0,n.v_)(e.dlc.toString(16).toUpperCase()),1)])),_:1}),(0,t.bF)(ge,{label:"Ext Flag","min-width":"120"},{default:(0,t.k6)((({row:e})=>[(0,t.eW)((0,n.v_)(e.isExt?"CAN Extended":"CAN Standard"),1)])),_:1}),(0,t.bF)(ge,{prop:"transmitter",label:"Transmitter","min-width":"120"}),(0,t.bF)(ge,{label:"Receivers","min-width":"180"},{default:(0,t.k6)((({row:e})=>[(0,t.eW)((0,n.v_)(e.receivers.join(", ")),1)])),_:1})])),_:1},8,["data"])):((0,t.uX)(),(0,t.CE)("div",C," Please select a target ECU to view related frames "))])),_:1},8,["label"])])),_:1},8,["model"])])),_:1})]),(0,t.Lk)("div",x,[(0,t.bF)(ke,{title:"NM Wake Up Setting",name:"nmWakeup",class:"custom-card"},{default:(0,t.k6)((()=>[(0,t.bF)(be,{model:l.value,"label-width":"160px","label-position":"top"},{default:(0,t.k6)((()=>[(0,t.bF)(j,{label:"Enable NM Wake Up"},{default:(0,t.k6)((()=>[(0,t.bF)(te,{modelValue:l.value.enableNmWakeup,"onUpdate:modelValue":s[5]||(s[5]=e=>l.value.enableNmWakeup=e)},null,8,["modelValue"])])),_:1}),(0,t.Lk)("div",{class:(0,n.C4)({"disabled-form-content":!l.value.enableNmWakeup})},[(0,t.bF)(ye,{data:[{}],border:"",style:{width:"100%","margin-bottom":"15px"},"show-header":!0,class:"nm-table"},{default:(0,t.k6)((()=>[(0,t.bF)(ge,{label:"ID",width:"220"},{default:(0,t.k6)((()=>[(0,t.bF)(Z,{modelValue:A.value,"onUpdate:modelValue":s[6]||(s[6]=e=>A.value=e),placeholder:"Enter ID in hex",onChange:J,disabled:!l.value.enableNmWakeup},null,8,["modelValue","disabled"])])),_:1}),(0,t.bF)(ge,{label:"Ext Flag",width:"100"},{default:(0,t.k6)((()=>[(0,t.bF)(te,{modelValue:l.value.nmWakeupIsExt,"onUpdate:modelValue":s[7]||(s[7]=e=>l.value.nmWakeupIsExt=e),disabled:!l.value.enableNmWakeup},null,8,["modelValue","disabled"])])),_:1}),(0,t.bF)(ge,{label:"Frame Type",width:"200"},{default:(0,t.k6)((()=>[(0,t.bF)(Fe,{modelValue:B.value,"onUpdate:modelValue":s[8]||(s[8]=e=>B.value=e),disabled:!l.value.enableNmWakeup},{default:(0,t.k6)((()=>[(0,t.bF)(fe,{value:"CAN Frame",label:"CAN Frame"}),(0,t.bF)(fe,{value:"CANFD Frame",label:"CANFD Frame"})])),_:1},8,["modelValue","disabled"])])),_:1}),(0,t.bF)(ge,{label:"DLC",width:"150"},{default:(0,t.k6)((()=>[(0,t.bF)(Z,{modelValue:K.value,"onUpdate:modelValue":s[9]||(s[9]=e=>K.value=e),placeholder:"Enter DLC",onChange:G,disabled:!l.value.enableNmWakeup},null,8,["modelValue","disabled"])])),_:1}),(0,t.bF)(ge,{label:"Data"},{default:(0,t.k6)((()=>[(0,t.bF)(Z,{modelValue:T.value,"onUpdate:modelValue":s[10]||(s[10]=e=>T.value=e),placeholder:"Enter data bytes, e.g. 0x3F,0x50,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF",onChange:H,disabled:!l.value.enableNmWakeup},null,8,["modelValue","disabled"])])),_:1})])),_:1}),(0,t.Lk)("div",D,[(0,t.bF)(j,{label:"Cycle (ms)",class:"nm-wakeup-item"},{default:(0,t.k6)((()=>[(0,t.bF)(de,{modelValue:l.value.nmWakeupCycleMs,"onUpdate:modelValue":s[11]||(s[11]=e=>l.value.nmWakeupCycleMs=e),min:1,max:6e4,style:{width:"200px"},disabled:!l.value.enableNmWakeup},null,8,["modelValue","disabled"])])),_:1}),(0,t.bF)(j,{label:"Wake Up Delay (ms)",class:"nm-wakeup-item"},{default:(0,t.k6)((()=>[(0,t.bF)(de,{modelValue:l.value.nmWakeupDelayMs,"onUpdate:modelValue":s[12]||(s[12]=e=>l.value.nmWakeupDelayMs=e),min:0,max:6e4,style:{width:"200px"},disabled:!l.value.enableNmWakeup},null,8,["modelValue","disabled"])])),_:1})])],2)])),_:1},8,["model"])])),_:1})]),(0,t.Lk)("div",w,[(0,t.bF)(ke,{title:"UDS Setting",name:"uds",class:"custom-card"},{default:(0,t.k6)((()=>[(0,t.bF)(be,{model:l.value,"label-width":"160px","label-position":"top"},{default:(0,t.k6)((()=>[(0,t.Lk)("div",I,[(0,t.bF)(j,{label:"DUT MTU < 4096 bytes",class:"form-item"},{default:(0,t.k6)((()=>[(0,t.bF)(te,{modelValue:l.value.isDutMtuLessThan4096,"onUpdate:modelValue":s[13]||(s[13]=e=>l.value.isDutMtuLessThan4096=e)},null,8,["modelValue"])])),_:1}),(0,t.bF)(j,{label:"Enable Retry Request",class:"form-item"},{default:(0,t.k6)((()=>[(0,t.bF)(te,{modelValue:l.value.enableDiagRetryRequest,"onUpdate:modelValue":s[14]||(s[14]=e=>l.value.enableDiagRetryRequest=e)},null,8,["modelValue"])])),_:1}),(0,t.bF)(j,{label:"Retry Request Payload",class:"form-item",style:{width:"400px"}},{default:(0,t.k6)((()=>[(0,t.bF)(Z,{modelValue:z.value,"onUpdate:modelValue":s[15]||(s[15]=e=>z.value=e),placeholder:"Enter data bytes, e.g. 0x10,0x01",onChange:ae,disabled:!l.value.enableDiagRetryRequest},null,8,["modelValue","disabled"])])),_:1})]),(0,t.Lk)("div",W,[(0,t.bF)(ve,null,{default:(0,t.k6)((()=>[(0,t.bF)((0,u.R1)(o.InfoFilled))])),_:1}),s[24]||(s[24]=(0,t.eW)()),s[25]||(s[25]=(0,t.Lk)("span",null,"Retry Request will be triggered when UDS diagnostic requests receive no response, using the configured payload for retry attempts.",-1))]),(0,t.Lk)("div",null,[(0,t.bF)(j,{label:"Security Access Dll"},{default:(0,t.k6)((()=>[(0,t.Lk)("div",R,[(0,t.bF)(Z,{modelValue:me.value,"onUpdate:modelValue":s[16]||(s[16]=e=>me.value=e),placeholder:"No Dll selected",readonly:"",class:"security-dll-input"},null,8,["modelValue"]),(0,t.Lk)("div",L,[l.value.securityInfo?.hasDll?(0,t.Q3)("",!0):((0,t.uX)(),(0,t.Wv)($,{key:0,onClick:re,type:"primary",icon:(0,u.R1)(o.Plus),title:"Select DLL"},null,8,["icon"])),l.value.securityInfo?.hasDll?((0,t.uX)(),(0,t.Wv)($,{key:1,onClick:ie,type:"danger",icon:(0,u.R1)(o.Delete),title:"Remove DLL"},null,8,["icon"])):(0,t.Q3)("",!0)])]),Q.value?((0,t.uX)(),(0,t.CE)("div",V," DLL selected. Click Save to apply. ")):(0,t.Q3)("",!0),O.value?((0,t.uX)(),(0,t.CE)("div",_," DLL marked for removal. Click Save to apply. ")):(0,t.Q3)("",!0)])),_:1})])])),_:1},8,["model"])])),_:1})]),(0,t.Lk)("div",N,[(0,t.bF)(ke,{title:"Log Setting",name:"log",class:"custom-card"},{default:(0,t.k6)((()=>[(0,t.bF)(be,{model:l.value,"label-width":"160px","label-position":"top"},{default:(0,t.k6)((()=>[(0,t.Lk)("div",U,[(0,t.bF)(j,{label:"Enable log filter",class:"form-item"},{default:(0,t.k6)((()=>[(0,t.bF)(te,{modelValue:l.value.enableLogFilter,"onUpdate:modelValue":s[17]||(s[17]=e=>l.value.enableLogFilter=e)},null,8,["modelValue"])])),_:1})]),(0,t.Lk)("div",E,[(0,t.bF)(ve,null,{default:(0,t.k6)((()=>[(0,t.bF)((0,u.R1)(o.InfoFilled))])),_:1}),s[26]||(s[26]=(0,t.eW)()),s[27]||(s[27]=(0,t.Lk)("span",null,"When enabled, BLF log files will only record transmitted frames, expected received frames, and error frames to reduce log size and improve performance.",-1))])])),_:1},8,["model"])])),_:1})])])),_:1},8,["modelValue"])]),(0,t.Lk)("div",S,[(0,t.bF)($,{type:"primary",onClick:ne},{default:(0,t.k6)((()=>s[28]||(s[28]=[(0,t.eW)("Save")]))),_:1}),(0,t.bF)($,{onClick:le,style:{"margin-left":"10px"}},{default:(0,t.k6)((()=>s[29]||(s[29]=[(0,t.eW)("Reset")]))),_:1})])])),[[Ce,a.value]])}}}),M=l(1241);const A=(0,M.A)(q,[["__scopeId","data-v-d7f9939a"]]);var K=A}}]);
//# sourceMappingURL=584.a38a7f8b.js.map