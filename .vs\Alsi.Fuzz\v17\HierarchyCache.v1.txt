﻿++解决方案 'Alsi.Fuzz' ‎ (13 个项目，共 13 个)
i:{00000000-0000-0000-0000-000000000000}:Alsi.Fuzz.sln
++Alsi.Fuzz.Web
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{00000000-0000-0000-0000-000000000000}:Alsi.Fuzz.Web
++Properties
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\properties\
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\properties\
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.securityaccess.x86\properties\
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\properties\
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\properties\
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.cef\properties\
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\properties\
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\properties\
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\properties\
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\properties\
++引用
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
++Controllers
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\controllers\
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\controllers\
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\controllers\
++CaseConfigController.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\caseconfigcontroller.cs
++CaseController.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\casecontroller.cs
++HardwareConfigController.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\hardwareconfigcontroller.cs
++InteroperationController.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\interoperationcontroller.cs
++SequenceConfigController.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\sequenceconfigcontroller.cs
++TestPlanController.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\testplancontroller.cs
++TestPlanHistoryController.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\testplanhistorycontroller.cs
++TestSuiteController.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\testsuitecontroller.cs
++WebControllerBase.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\webcontrollerbase.cs
++Dto
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\dto\
++Mapping
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\mapping\
++app.config
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\app.config
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\app.config
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\app.config
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\app.config
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\app.config
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\app.config
++build-web.ps1
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\build-web.ps1
++FuzzWebAssembly.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\fuzzwebassembly.cs
++packages.config
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\packages.config
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\packages.config
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\packages.config
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\packages.config
++web.zip
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\web.zip
++AssemblyInfo.cs
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\properties\assemblyinfo.cs
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\properties\assemblyinfo.cs
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.securityaccess.x86\properties\assemblyinfo.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\properties\assemblyinfo.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\properties\assemblyinfo.cs
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.cef\properties\assemblyinfo.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\properties\assemblyinfo.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\properties\assemblyinfo.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\properties\assemblyinfo.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\properties\assemblyinfo.cs
++Alsi.App
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{d36982c1-9f69-48a3-8af6-1f018cff1ee5}:Alsi.App
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>3042
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
++Alsi.App.Database
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{d36982c1-9f69-48a3-8af6-1f018cff1ee5}:Alsi.App.Database
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>3053
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
++Alsi.App.Desktop
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{d36982c1-9f69-48a3-8af6-1f018cff1ee5}:Alsi.App.Desktop
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
++Alsi.App.Devices
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{d36982c1-9f69-48a3-8af6-1f018cff1ee5}:Alsi.App.Devices
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>3048
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
++Alsi.Common.Parsers
i:{d36982c1-9f69-48a3-8af6-1f018cff1ee5}:Alsi.Common.Parsers
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
++Alsi.Common.Utils
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:>3041
i:{d36982c1-9f69-48a3-8af6-1f018cff1ee5}:Alsi.Common.Utils
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>3051
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
++Alsi.Fuzz.Core
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>3056
i:{00000000-0000-0000-0000-000000000000}:Alsi.Fuzz.Core
++AutoMapper
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
++Microsoft.CSharp
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
++Microsoft.Owin
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
++Newtonsoft.Json
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
++Newtonsoft.Json.Bson
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
++Owin
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
++System
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:>3062
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
++System.Buffers
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
++System.Core
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:>3060
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.core.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
++System.Data
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:>3054
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.data.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
++System.Data.DataSetExtensions
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
++System.Memory
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
++System.Net.Http.Formatting
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
++System.Numerics
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:>3052
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.numerics.dll
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
++System.Numerics.Vectors
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
++System.Runtime.CompilerServices.Unsafe
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
++System.Threading.Tasks.Extensions
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
++System.ValueTuple
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
++System.Web.Http
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
++System.Web.Http.Owin
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
++System.Xml
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:>3058
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.xml.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
++System.Xml.Linq
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:>3050
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.xml.linq.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
++CaseConfigDto.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\dto\caseconfigdto.cs
++HardwareConfigDto.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\dto\hardwareconfigdto.cs
++TestPlanDto.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\dto\testplandto.cs
++TestPlanHistoryDto.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\dto\testplanhistorydto.cs
++TestPlanManifestDto.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\dto\testplanmanifestdto.cs
++MappingProfile.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\mapping\mappingprofile.cs
++Alsi.Fuzz.Tester
i:{00000000-0000-0000-0000-000000000000}:Alsi.Fuzz.Tester
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>3046
++Testers
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\testers\
++App.config
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\app.config
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.securityaccess.x86\app.config
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\app.config
++FuzzTesterAssembly.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\fuzztesterassembly.cs
++Program.cs
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.securityaccess.x86\program.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\program.cs
++TesterEnv.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\testerenv.cs
++FreeSql
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
++FreeSql.Provider.Sqlite
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
++System.Data.SQLite
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
++TesterController.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\controllers\testercontroller.cs
++CaseContext.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\testers\casecontext.cs
++DataCache.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\testers\datacache.cs
++DiagRetryTester.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\testers\diagretrytester.cs
++DiagStepTester.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\testers\diagsteptester.cs
++ErrorFrameChecker.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\testers\errorframechecker.cs
++IsotpStepTester.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\testers\isotpsteptester.cs
++MultipleFrameSender.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\testers\multipleframesender.cs
++PackageTester.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\testers\packagetester.cs
++StepTester.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\testers\steptester.cs
++Alsi.Fuzz
i:{00000000-0000-0000-0000-000000000000}:Alsi.Fuzz
++Views
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\views\
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\views\
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.cef\views\
++MainView.xaml
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\views\mainview.xaml
++app.manifest
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\app.manifest
++App.xaml
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\app.xaml
++favicon.ico
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\favicon.ico
++Resources.resx
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\properties\resources.resx
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\properties\resources.resx
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.cef\properties\resources.resx
++Settings.settings
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\properties\settings.settings
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\properties\settings.settings
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.cef\properties\settings.settings
++Alsi.App.Cef
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{d36982c1-9f69-48a3-8af6-1f018cff1ee5}:Alsi.App.Cef
++PresentationCore
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:
++PresentationFramework
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:
++System.Xaml
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:
++WindowsBase
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:
++MainView.xaml.cs
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\views\mainview.xaml.cs
++App.xaml.cs
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\app.xaml.cs
++Resources.Designer.cs
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\properties\resources.designer.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\properties\resources.designer.cs
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.cef\properties\resources.designer.cs
++Settings.Designer.cs
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\properties\settings.designer.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\properties\settings.designer.cs
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.cef\properties\settings.designer.cs
++UnitTests
i:{00000000-0000-0000-0000-000000000000}:UnitTests
++Alsi.Fuzz.UnitTests
i:{4c1f4725-d3ce-4a1d-859a-f93e638bcc76}:Alsi.Fuzz.UnitTests
++依赖项
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:>3035
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:>3036
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>3034
++Hardware
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\hardware\
++Log
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\log\
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\log\
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\log\
++Results
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\results\
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\
++Suites
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\suites\
++TestPlans
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\testplans\
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\
++Utils
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\utils\
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\utils\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\utils\
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.cef\utils\
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\utils\
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\utils\
++SerialUnitTestCollection.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\serialunittestcollection.cs
++UnitTestAppContext.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\unittestappcontext.cs
++UnitTestBase.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\unittestbase.cs
++包
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:>3043
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:>3077
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:>3089
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>3069
++分析器
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>3057
++框架
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>3067
++项目
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:>3040
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>3039
++DataBusTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\hardware\databustests.cs
++DeviceTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\hardware\devicetests.cs
++BlfLogTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\log\blflogtests.cs
++ResultContextTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\results\resultcontexttests.cs
++TestResultServiceTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\results\testresultservicetests.cs
++PackageSerializationTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\suites\packageserializationtests.cs
++SequenceConfigTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\testplans\sequenceconfigtests.cs
++TestPlanTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\testplans\testplantests.cs
++TestPlanUpdateTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\testplans\testplanupdatetests.cs
++LhsSamplerTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\utils\lhssamplertests.cs
++SequenceUtilsTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\utils\sequenceutilstests.cs
++coverlet.collector (3.2.0)
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>3074
++FreeSql.Provider.Sqlite (3.5.106)
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>3075
++Microsoft.NET.Test.Sdk (17.5.0)
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>3071
++Shouldly (4.3.0)
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>3070
++xunit (2.9.3)
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>3072
++xunit.runner.visualstudio (3.0.2)
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>3073
++Xunit.SkippableFact (1.5.23)
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>3076
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++System.Text.Json.SourceGeneration
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\6.0.36\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++xunit.analyzers
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\nuget_packages\xunit.analyzers\1.18.0\analyzers\dotnet\cs\xunit.analyzers.dll
++xunit.analyzers.fixes
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\nuget_packages\xunit.analyzers\1.18.0\analyzers\dotnet\cs\xunit.analyzers.fixes.dll
++Microsoft.NETCore.App
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>3068
++Contracts
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\contracts\
++Models
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\cin\models\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\
++Resources
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\resources\
++Service
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\
++CaseFactory
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\
++Tester
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\contracts\tester\
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\tester\
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\tester\
++BuiltInTestSuiteService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\builtintestsuiteservice.cs
++CaseService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\caseservice.cs
++DeviceScanner.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\devicescanner.cs
++FileLocker.cs
++GenerationProgressService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\generationprogressservice.cs
++HardwareService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\hardwareservice.cs
++IHardwareService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\ihardwareservice.cs
++ITestPlanHistoryService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\itestplanhistoryservice.cs
++ReportTemplateService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\reporttemplateservice.cs
++StatusPollingService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\statuspollingservice.cs
++TestPlanHistoryService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\testplanhistoryservice.cs
++TestPlanManager.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\testplanmanager.cs
++TestPlanSequenceService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\testplansequenceservice.cs
++TestPlanService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\testplanservice.cs
++Storage
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\storage\
++FuzzConsts.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\fuzzconsts.cs
++ICSharpCode.SharpZipLib
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
++History
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\history\
++TestSuites
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\
++report-template.html
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\resources\report-template.html
++sequences-11898.xml
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\resources\sequences-11898.xml
++sequences-14229.xml
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\resources\sequences-14229.xml
++sequences-15765.xml
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\resources\sequences-15765.xml
++Core
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\core\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ccdb\core\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\openissue\core\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\td\core\
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\core\
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\core\
++Factories
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\
++Iso15765_CaseFactoryUtils.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\iso15765_casefactoryutils.cs
++CaseResult.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\caseresult.cs
++CaseStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\casestep.cs
++ExecutionState.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\executionstate.cs
++ITestResultReaderService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\itestresultreaderservice.cs
++ITestResultWriterService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\itestresultwriterservice.cs
++ResultContext.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\resultcontext.cs
++SaveProgressService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\saveprogressservice.cs
++Sequence.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\sequence.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\sequence.cs
++TestResult.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\testresult.cs
++TestResultReaderService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\testresultreaderservice.cs
++TestResultWriterService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\testresultwriterservice.cs
++TestType.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\testtype.cs
++ApiResponse.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\tester\apiresponse.cs
++ITesterApiClient.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\tester\itesterapiclient.cs
++ITesterManager.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\tester\itestermanager.cs
++TesterApiClient.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\tester\testerapiclient.cs
++TesterConsts.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\tester\testerconsts.cs
++TesterManager.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\tester\testermanager.cs
++DataLogStorage.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\storage\datalogstorage.cs
++TestPlanStorage.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\storage\testplanstorage.cs
++LhsSampler.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\utils\lhssampler.cs
++SecurityAccessUtils.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\utils\securityaccessutils.cs
++SequencePackageUtils.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\utils\sequencepackageutils.cs
++SequenceUtils.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\utils\sequenceutils.cs
++UniformSampler.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\utils\uniformsampler.cs
++ExecutionRequest.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\contracts\tester\executionrequest.cs
++TestPlanHistory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\history\testplanhistory.cs
++EnvVars.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\tester\envvars.cs
++TesterProcess.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\tester\testerprocess.cs
++CaseConfig.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\caseconfig.cs
++HardwareConfig.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\hardwareconfig.cs
++SecurityConfig.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\securityconfig.cs
++SequenceConfig.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\sequenceconfig.cs
++SequenceConfigDto.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\sequenceconfigdto.cs
++TestPlan.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\testplan.cs
++TestPlanConfig.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\testplanconfig.cs
++TestPlanManifest.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\testplanmanifest.cs
++WhiteListFrame.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\whitelistframe.cs
++Steps
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\
++EnvVar.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\envvar.cs
++SequencePackage.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\sequencepackage.cs
++SetVar.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\setvar.cs
++Store.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\store.cs
++TestSuite.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\testsuite.cs
++CaseMutation.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\core\casemutation.cs
++ICaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\core\icasefactory.cs
++IsoType.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\core\isotype.cs
++MutationFactoryDiscoverer.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\core\mutationfactorydiscoverer.cs
++MutationField.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\core\mutationfield.cs
++MutationFieldType.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\core\mutationfieldtype.cs
++MutationOptions.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\core\mutationoptions.cs
++Iso11898
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso11898\
++Iso14229
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\
++Iso15765
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso15765\
++Diagnostic
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\diagnostic\
++Isotp
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\isotp\
++CalcKeyStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\calckeystep.cs
++Frame.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\frame.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\frame.cs
++ICommRecvStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\icommrecvstep.cs
++MatchFrame.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\matchframe.cs
++PrintStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\printstep.cs
++ReceiveStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\receivestep.cs
++SendStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\sendstep.cs
++StepBase.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\stepbase.cs
++WaitStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\waitstep.cs
++G11_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso11898\g11_casefactory.cs
++G12_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso11898\g12_casefactory.cs
++G21_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso11898\g21_casefactory.cs
++G22_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso11898\g22_casefactory.cs
++G31_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso11898\g31_casefactory.cs
++Iso11898_CaseFactoryUtils.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso11898\iso11898_casefactoryutils.cs
++Consts
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\consts\
++CaseFactoryBase.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\casefactorybase.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso15765\casefactorybase.cs
++G3111_G3112_G3113_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g3111_g3112_g3113_casefactory.cs
++G3114_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g3114_casefactory.cs
++G3115_G3116_G3117_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g3115_g3116_g3117_casefactory.cs
++G3121_G3122_G3123_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g3121_g3122_g3123_casefactory.cs
++G3124_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g3124_casefactory.cs
++G3125_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g3125_casefactory.cs
++G3131_G3132_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g3131_g3132_casefactory.cs
++G3141_G3151_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g3141_g3151_casefactory.cs
++G3211_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g3211_casefactory.cs
++G3311_G3312_G3313_G3314_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g3311_g3312_g3313_g3314_casefactory.cs
++G3321_G3322_G3323_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g3321_g3322_g3323_casefactory.cs
++G341_G342_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g341_g342_casefactory.cs
++G41_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g41_casefactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso15765\g41_casefactory.cs
++G42_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g42_casefactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso15765\g42_casefactory.cs
++G431_G432_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g431_g432_casefactory.cs
++Iso14229_CaseFactoryUtils.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\iso14229_casefactoryutils.cs
++G31_G32_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso15765\g31_g32_casefactory.cs
++G33_G34_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso15765\g33_g34_casefactory.cs
++G35_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso15765\g35_casefactory.cs
++G36_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso15765\g36_casefactory.cs
++G37_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso15765\g37_casefactory.cs
++MatchDiag.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\diagnostic\matchdiag.cs
++RecvDiagStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\diagnostic\recvdiagstep.cs
++SendDiagStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\diagnostic\senddiagstep.cs
++RecvIsotpStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\isotp\recvisotpstep.cs
++SendIsotpStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\isotp\sendisotpstep.cs
++IsoUdsConsts.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\consts\isoudsconsts.cs
++Subfunction.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\consts\subfunction.cs
++UdsService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\consts\udsservice.cs
++Common
i:{00000000-0000-0000-0000-000000000000}:Common
++Midwares
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\midwares\
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\midwares\
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\
++AppExtensions.cs
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\appextensions.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\appextensions.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\appextensions.cs
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.cef\appextensions.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\appextensions.cs
++DbEnv.cs
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\dbenv.cs
++FreeSql.DbContext
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
++DbContext.cs
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\midwares\dbcontext.cs
++DbContextManager.cs
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\midwares\dbcontextmanager.cs
++FreeSqlMidware.cs
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\midwares\freesqlmidware.cs
++Alsi.App.SecurityAccess.x86
i:{d36982c1-9f69-48a3-8af6-1f018cff1ee5}:Alsi.App.SecurityAccess.x86
++System.Net.Http
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++Themes
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\themes\
++WindowEnv.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\windowenv.cs
++MahApps.Metro.IconPacks.Material
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++Microsoft.AspNet.WebApi.Owin
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++Microsoft.VisualBasic
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++Ookii.Dialogs.Wpf
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++System.Design
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++System.Drawing
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:>3049
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.drawing.dll
++System.Security
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++ExplorerController.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\controllers\explorercontroller.cs
++AppExceptionHandlerMidware.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\midwares\appexceptionhandlermidware.cs
++ProcessMutexMidware.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\midwares\processmutexmidware.cs
++Generic.xaml
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\themes\generic.xaml
++RecycleBinUtils.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\utils\recyclebinutils.cs
++UiUtils.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\utils\uiutils.cs
++WindowUtils.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\utils\windowutils.cs
++WindowUtils.RemoveIcon.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\utils\windowutils.removeicon.cs
++WindowEx.xaml
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\views\windowex.xaml
++WindowEx.xaml.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\views\windowex.xaml.cs
++AppEnv.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\appenv.cs
++AppOptions.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\appoptions.cs
++WebHostApp.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\webhostapp.cs
++Microsoft.AspNet.WebApi.OwinSelfHost
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++Serilog.Sinks.Console
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++Serilog.Sinks.File
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++SharpZipLib
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++App
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\controllers\app\
++ControllerBase.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\controllers\controllerbase.cs
++ErrorData.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\controllers\errordata.cs
++AppMidwareManager.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\core\appmidwaremanager.cs
++IApiAssembly.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\core\iapiassembly.cs
++IAppMidware.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\core\iappmidware.cs
++ILogger.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\log\ilogger.cs
++LogCategory.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\log\logcategory.cs
++LogConsts.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\log\logconsts.cs
++LogEntry.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\log\logentry.cs
++MemoryLogger.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\log\memorylogger.cs
++ApiHost
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\apihost\
++Serilog
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\serilog\
++ContentTypeUtils.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\utils\contenttypeutils.cs
++PortUtils.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\utils\portutils.cs
++AppController.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\controllers\app\appcontroller.cs
++AppInfo.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\controllers\app\appinfo.cs
++ApiHostMidware.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\apihost\apihostmidware.cs
++ApiHostStartup.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\apihost\apihoststartup.cs
++LoggingHandler.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\apihost\logginghandler.cs
++SerilogLogger.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\serilog\seriloglogger.cs
++SerilogMidware.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\serilog\serilogmidware.cs
++Arxml
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\
++Ccdb
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ccdb\
++Cin
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\cin\
++Dbc
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\
++Ldf
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\
++OpenIssue
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\openissue\
++Sddb
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\sddb\
++Td
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\td\
++Vbf
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\
++程序集
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:>3047
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:>3079
++ModelsParsers
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\modelsparsers\
++ArxmlParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\arxmlparser.cs
++AUTOSAR_4-2-2.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\autosar_4-2-2.cs
++AutosarExtension.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\autosarextension.cs
++AutosarStaticExtension.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\autosarstaticextension.cs
++CcdbParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ccdb\ccdbparser.cs
++Format
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\cin\format\
++CinBuilderUtils.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\cin\cinbuilderutils.cs
++CinParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\cin\cinparser.cs
++CinParserUtils.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\cin\cinparserutils.cs
++Parsers
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\
++DbcParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\dbcparser.cs
++Model
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\model\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\model\
++LdfParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\ldfparser.cs
++OpenIssueParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\openissue\openissueparser.cs
++Project.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\sddb\project.cs
++SddbParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\sddb\sddbparser.cs
++TinyProject.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\sddb\tinyproject.cs
++TdParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\td\tdparser.cs
++TdResult.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\td\tdresult.cs
++ColumnExAttribute.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\utils\columnexattribute.cs
++ColumnExInfo.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\utils\columnexinfo.cs
++VbfParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\vbfparser.cs
++NPOI (2.6.2)
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:>3044
++SharpZipLib (1.4.2)
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:>3045
++System.IO.Compression.FileSystem
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:>3064
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.io.compression.filesystem.dll
++System.Runtime.Serialization
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:>3055
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.runtime.serialization.dll
++ClusterBusType.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\clusterbustype.cs
++CommunicationCluster.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\communicationcluster.cs
++Direction.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\direction.cs
++Pdu.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\pdu.cs
++Signal.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\signal.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\signal.cs
++CommunicationClusterParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\modelsparsers\communicationclusterparser.cs
++CcdbModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ccdb\core\ccdbmodel.cs
++CcdbParameter.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ccdb\core\ccdbparameter.cs
++CcdbRow.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ccdb\core\ccdbrow.cs
++CcdbValue.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ccdb\core\ccdbvalue.cs
++CinFormatUtils.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\cin\format\cinformatutils.cs
++CinPropAttribute.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\cin\format\cinpropattribute.cs
++CinModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\cin\models\cinmodel.cs
++AttributeDef.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\attributedef.cs
++AttributeType.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\attributetype.cs
++CustomProperty.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\customproperty.cs
++DbcModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\dbcmodel.cs
++DbcValueType.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\dbcvaluetype.cs
++EnvAccessibility.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\envaccessibility.cs
++EnvDataType.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\envdatatype.cs
++EnvironmentVariable.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\environmentvariable.cs
++Message.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\message.cs
++Node.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\node.cs
++SignalExtension.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\signalextension.cs
++SignalGroup.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\signalgroup.cs
++StatisticInfo.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\statisticinfo.cs
++ValTable.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\valtable.cs
++AttributeDefDefParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\attributedefdefparser.cs
++AttributeDefParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\attributedefparser.cs
++AttributeParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\attributeparser.cs
++BaudrateParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\baudrateparser.cs
++CommentParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\commentparser.cs
++EnvironmentDataVariableParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\environmentdatavariableparser.cs
++EnvironmentVariableParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\environmentvariableparser.cs
++IParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\iparser.cs
++MessageParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\messageparser.cs
++NewSymbolParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\newsymbolparser.cs
++NodeParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\nodeparser.cs
++ParserContext.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\parsercontext.cs
++SignalGroupParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\signalgroupparser.cs
++SignalParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\signalparser.cs
++SignalValueTypeParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\signalvaluetypeparser.cs
++ValParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\valparser.cs
++ValTableParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\valtableparser.cs
++VersionParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\versionparser.cs
++LdfFrame.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\model\ldfframe.cs
++LdfModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\model\ldfmodel.cs
++LdfNode.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\model\ldfnode.cs
++LdfScheduleTable.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\model\ldfscheduletable.cs
++LdfSignal.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\model\ldfsignal.cs
++OpenIssueModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\openissue\core\openissuemodel.cs
++OpenIssueRow.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\openissue\core\openissuerow.cs
++TdModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\td\core\tdmodel.cs
++TdRow.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\td\core\tdrow.cs
++VbfBlock.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\model\vbfblock.cs
++VbfErase.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\model\vbferase.cs
++VbfModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\model\vbfmodel.cs
++VbfOutputModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\model\vbfoutputmodel.cs
++CefMidware.cs
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.cef\cefmidware.cs
++CefSharp.Wpf
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:
++CefSharpUtils.cs
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.cef\utils\cefsharputils.cs
++CefSharpUtils.CustomKeyboardHandler.cs
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.cef\utils\cefsharputils.customkeyboardhandler.cs
++CefSharpUtils.EmptyContextMenuHandler.cs
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.cef\utils\cefsharputils.emptycontextmenuhandler.cs
++CefBrowser.xaml
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.cef\views\cefbrowser.xaml
++CefBrowser.xaml.cs
i:{d50b6900-a1d7-4c38-b50c-6bfc2dcbdceb}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.cef\views\cefbrowser.xaml.cs
++TsLibCan
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\tslibcan\
++Vector
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\vector\
++x64
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\
++blf
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\blf\
++libTsCan
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\libtscan\
++Interop.TsCANApi.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\libtscan\interop.tscanapi.dll
++libTSCAN.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\libtscan\libtscan.dll
++libTSH.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\libtscan\libtsh.dll
++vector
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\vector\
++DeviceEnv.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\deviceenv.cs
++DeviceMidware.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\devicemidware.cs
++Interop.TsCANApi
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
++vxlapi_NET
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
++Channels
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\channels\
++TransportLayer
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\
++CanFrame.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\canframe.cs
++ChannelConfig.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\channelconfig.cs
++CommunicationType.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\communicationtype.cs
++DataBus.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\databus.cs
++DataBusTimer.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\databustimer.cs
++DeviceChannel.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\devicechannel.cs
++FrameMonitor.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\framemonitor.cs
++ICanChannel.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\icanchannel.cs
++Manufacturer.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\manufacturer.cs
++TimestampUtils.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\timestamputils.cs
++BlfLogReader.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\log\blflogreader.cs
++BlfLogWriter.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\log\blflogwriter.cs
++BlfStructs.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\log\blfstructs.cs
++EncapsulationLibTsCan.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\tslibcan\encapsulationlibtscan.cs
++TosunConsts.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\tslibcan\tosunconsts.cs
++TsLibApi.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\tslibcan\tslibapi.cs
++TsLibCanChannelInfo.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\tslibcan\tslibcanchannelinfo.cs
++EncapsulationVectorCan.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\vector\encapsulationvectorcan.cs
++MemoryProtector.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\vector\memoryprotector.cs
++VectorDeviceApi.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\vector\vectordeviceapi.cs
++VectorInfo.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\vector\vectorinfo.cs
++binlog.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\blf\binlog.dll
++OperateBlf.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\blf\operateblf.dll
++vxlapi.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\vector\vxlapi.dll
++vxlapi_NET.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\vector\vxlapi_net.dll
++vxlapi64.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\vector\vxlapi64.dll
++TsLibCanChannel.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\channels\tslibcanchannel.cs
++VectorCanChannel.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\channels\vectorcanchannel.cs
++Frames
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\frames\
++DiagParams.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\diagparams.cs
++NoFlowControlException.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\noflowcontrolexception.cs
++Request.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\request.cs
++Response.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\response.cs
++ResponseStore.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\responsestore.cs
++TpContext.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\tpcontext.cs
++TpService.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\tpservice.cs
++TpService.SendMultipleFrame.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\tpservice.sendmultipleframe.cs
++TpService.SendSingleFrame.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\tpservice.sendsingleframe.cs
++ConsecutiveFrame.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\frames\consecutiveframe.cs
++FirstFrame.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\frames\firstframe.cs
++FlowControl.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\frames\flowcontrol.cs
++FlowState.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\frames\flowstate.cs
++FrameRecorder.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\frames\framerecorder.cs
++IDiagFrame.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\frames\idiagframe.cs
++MultipleFrame.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\frames\multipleframe.cs
++SingleFrame.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\frames\singleframe.cs
++Autosar
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\autosar\
++Compressions
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\compressions\
++Consoles
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\consoles\
++Cryptography
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\cryptography\
++Filters
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\
++Linq
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\linq\
++Reflection
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\reflection\
++Threads
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\threads\
++Timers
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\timers\
++AppException.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\appexception.cs
++ArrayUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\arrayutils.cs
++ByteUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\byteutils.cs
++DirExtension.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\dirextension.cs
++DisplayAttribute.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\displayattribute.cs
++EnumUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\enumutils.cs
++EnvUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\envutils.cs
++FileBlockUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\fileblockutils.cs
++HashUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\hashutils.cs
++HexExtension.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\hexextension.cs
++JsonUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\jsonutils.cs
++NativeDllUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\nativedllutils.cs
++NumberOptions.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\numberoptions.cs
++NumberUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\numberutils.cs
++PathUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\pathutils.cs
++ProgressValue.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\progressvalue.cs
++StringUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\stringutils.cs
++ValidationUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\validationutils.cs
++XmlUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\xmlutils.cs
++net462
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:>3037
++netstandard2.0
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:>3038
++DlcUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\autosar\dlcutils.cs
++CompressUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\compressions\compressutils.cs
++ConsoleEx.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\consoles\consoleex.cs
++Option.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\consoles\option.cs
++AesUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\cryptography\aesutils.cs
++CryptFile.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\cryptography\cryptfile.cs
++CryptFileIndex.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\cryptography\cryptfileindex.cs
++CryptFileUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\cryptography\cryptfileutils.cs
++Sha256Utils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\cryptography\sha256utils.cs
++FilterUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\filterutils.cs
++IFilter.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\ifilter.cs
++LinqExtension.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\linq\linqextension.cs
++AssemblyLoader.CacheItem.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\reflection\assemblyloader.cacheitem.cs
++AssemblyLoader.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\reflection\assemblyloader.cs
++AssemblyLoader.Dependeny.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\reflection\assemblyloader.dependeny.cs
++TypeUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\reflection\typeutils.cs
++AsyncRelayer.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\threads\asyncrelayer.cs
++ProcessUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\threads\processutils.cs
++ThreadUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\threads\threadutils.cs
++TimeWindow.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\threads\timewindow.cs
++TimeWindowCounter.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\threads\timewindowcounter.cs
++HighResolutionTimer.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\timers\highresolutiontimer.cs
++MultimediaTimer.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\timers\multimediatimer.cs
++NativeMethods.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\timers\nativemethods.cs
++CaseIdFilter.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\core\caseidfilter.cs
++FrameIdFilter.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\core\frameidfilter.cs
++FrameIdRange.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\core\frameidrange.cs
++NameFilter.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\core\namefilter.cs
++Newtonsoft.Json (13.0.3)
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:newtonsoft.json/13.0.3
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:>3090
++NETStandard.Library (2.0.3)
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:>3091
++NewFolder1
++Interoperation
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\interoperation\
++InteroperationService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\interoperation\interoperationservice.cs
++InteroperationModel.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\interoperation\interoperationmodel.cs
++Alsi.Fuzz.Core (已卸载)
++解决方案 'Alsi.Fuzz' ‎ (12 个项目，共 13 个)
++TestPlanFileLocker.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\testplanfilelocker.cs
