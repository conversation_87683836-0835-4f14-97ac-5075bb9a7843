<template>
    <el-dialog v-model="dialogVisible" title="Generate Test Cases" class="app-dialog-full-70"
        :close-on-click-modal="false" :close-on-press-escape="false" @close="handleDialogClose">

        <!-- 对话框主要内容 -->
        <div class="dialog-content" v-loading="generating" :element-loading-text="generatingText">
            <div class="option-row">
                <div>Coverage:</div>
                <el-radio-group v-model="formData.coverage" size="small">
                    <el-radio :label="CoverageType.Normal">Normal</el-radio>
                    <el-radio :label="CoverageType.High">High</el-radio>
                </el-radio-group>
            </div>
            <div class="interoperation-results">
                <InteroperationResultPanel :results="interoperationResults" :loading="interoperationLoading"
                    :average-time="averageTimeOfSelectedSequences"
                    v-model:selectedSequences="formData.selectedSequences" />
            </div>
        </div>

        <!-- 对话框底部按钮 -->
        <template #footer>
            <div class="dialog-footer">
                <div class="button-group">
                    <el-button @click="handleCancel" :disabled="generating">Cancel</el-button>
                    <el-button type="primary" @click="handleGenerate" :loading="generating"
                        :disabled="formData.selectedSequences.length === 0">
                        Generate
                    </el-button>
                </div>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { CaseResult, GroupTreeNode } from '@/api/interoperationApi';
import { CoverageType, appApi, ExecutionState } from '@/api/appApi';
import InteroperationResultPanel from './InteroperationResultPanel.vue';

const props = defineProps<{
    visible: boolean;
    coverage: CoverageType;
    selectedSequences: string[];
    baselineAverageTime: number;
}>();

const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void;
    (e: 'generated', cases: CaseResult[], groupTree: GroupTreeNode): void;
}>();

// 内部状态
const generating = ref(false);
const interoperationResults = ref<CaseResult[]>([]);
const interoperationLoading = ref(false);
const generatingProgressText = ref('');
let generateProgressTimer: number | null = null;

// 表单数据
const formData = ref({
    coverage: CoverageType.Normal,
    selectedSequences: [] as string[]
});

const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
});

// 动态的生成进度文本
const generatingText = computed(() => {
    if (generatingProgressText.value) {
        return generatingProgressText.value;
    }
    return 'Generating Test Cases...';
});

// 计算选中用例的平均时间(毫秒)
const averageTimeOfSelectedSequences = computed(() => {
    if (formData.value.selectedSequences.length === 0 || interoperationResults.value.length === 0) {
        return 0;
    }

    // 计算选中序列的平均执行时间
    let totalExecutionTime = 0;
    let totalExecutionCount = 0;

    interoperationResults.value.forEach(result => {
        if (result.begin && result.end && formData.value.selectedSequences.includes(result.sequenceName)) {
            const executionTime = new Date(result.end).getTime() - new Date(result.begin).getTime();
            totalExecutionTime += executionTime;
            totalExecutionCount += 1;
        }
    });

    return totalExecutionCount > 0 ? totalExecutionTime / totalExecutionCount : 0;
});

// 获取最新互操作测试结果
const fetchInteroperationResults = async () => {
    interoperationLoading.value = true;
    try {
        const response = await appApi.getLatestInteroperationCaseResults();
        interoperationResults.value = response.data;

        // 默认勾选成功的结果
        const successfulSequences = response.data
            .filter(result => result.state === ExecutionState.Success)
            .map(result => result.sequenceName);
        formData.value.selectedSequences = successfulSequences;
    } catch (error) {
        console.error('获取测试结果失败:', error);
        ElMessage.error('Failed to fetch interoperation results');
    } finally {
        interoperationLoading.value = false;
    }
};

// 生成测试用例
const handleGenerate = async () => {
    if (formData.value.selectedSequences.length === 0) {
        ElMessage.warning('Please select at least one sequence');
        return;
    }

    generating.value = true;
    generatingProgressText.value = '';

    // 开始轮询生成进度
    startGenerateProgressPolling();

    try {
        const response = await appApi.generateCasesWithGroup(formData.value.coverage, formData.value.selectedSequences);

        // 生成成功后，发送数据给父组件并关闭对话框
        emit('generated', response.data.cases, response.data.groupTree);
        dialogVisible.value = false;

        ElMessage.success(`Successfully generated ${response.data.cases.length} test cases`);
    } catch (error) {
        console.error('生成测试用例失败:', error);
        ElMessage.error('Failed to generate test cases');
    } finally {
        generating.value = false;
        // 停止轮询生成进度
        stopGenerateProgressPolling();
    }
};

// 开始轮询生成进度
const startGenerateProgressPolling = () => {
    const pollProgress = async () => {
        const response = await appApi.getGeneratingProgress();
        generatingProgressText.value = response.data;

        // 如果还在生成中，继续轮询
        if (generating.value) {
            generateProgressTimer = window.setTimeout(pollProgress, 100);
        }
    };

    // 延迟开始轮询，给后端一点启动时间
    generateProgressTimer = window.setTimeout(pollProgress, 200);
};

// 停止轮询生成进度
const stopGenerateProgressPolling = () => {
    if (generateProgressTimer) {
        window.clearTimeout(generateProgressTimer);
        generateProgressTimer = null;
    }
};

// 取消对话框
const handleCancel = () => {
    dialogVisible.value = false;
};

// 对话框关闭时的处理
const handleDialogClose = () => {
    // 停止轮询
    stopGenerateProgressPolling();

    // 重置表单数据
    formData.value.coverage = props.coverage;
    formData.value.selectedSequences = [...props.selectedSequences];
};



// 监听对话框显示状态，获取互操作结果
watch(() => props.visible, (visible) => {
    if (visible) {
        // 初始化表单数据
        formData.value.coverage = props.coverage;
        formData.value.selectedSequences = [...props.selectedSequences];

        // 获取互操作结果
        fetchInteroperationResults();
    }
});

// 组件挂载时获取互操作结果
onMounted(() => {
    if (props.visible) {
        fetchInteroperationResults();
    }
});
</script>

<style scoped lang="scss">
.dialog-content {
    padding: 0;
    display: flex;
    flex: 1;
    flex-direction: column;
}

.interoperation-results {
    overflow-y: auto;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background: #fff;
    display: flex;
    flex: 1;
    flex-basis: 0;
}

.option-row {
    display: flex;
    flex-direction: row;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;
}
</style>
