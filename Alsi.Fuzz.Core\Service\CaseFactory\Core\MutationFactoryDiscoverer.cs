using System;
using System.Linq;
using System.Reflection;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Core
{
    public static class MutationFactoryDiscoverer
    {
        public static T[] GetFactories<T>()
        {
            var currentAssembly = Assembly.GetExecutingAssembly();
            return currentAssembly.GetTypes()
                .Where(t => typeof(T).IsAssignableFrom(t) && !t.IsAbstract && t.IsClass)
                .Select(type => (T)Activator.CreateInstance(type))
                .ToArray();
        }

        public static ICaseFactory[] GetAllFactories()
        {
            return FindAllTypes()
                .Select(type => (ICaseFactory)Activator.CreateInstance(type))
                .ToArray();
        }

        private static Type[] FindAllTypes()
        {
            var currentAssembly = Assembly.GetExecutingAssembly();

            var mutationFactories = currentAssembly.GetTypes()
                .Where(t => typeof(ICaseFactory).IsAssignableFrom(t) && !t.IsAbstract && t.IsClass)
                .ToArray();

            return mutationFactories;
        }
    }
}
