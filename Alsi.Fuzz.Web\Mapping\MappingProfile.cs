using Alsi.App.Devices.Core;
using Alsi.Fuzz.Core.Models.History;
using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Web.Dto;
using AutoMapper;
using System.IO;

namespace Alsi.Fuzz.Web.Mapping
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            CreateMap<TestPlanHistory, TestPlanHistoryDto>()
                .ForMember(dest => dest.PlanName, opt => opt.MapFrom(src =>
                    Path.GetFileNameWithoutExtension(src.FilePath)));

            CreateMap<TestPlan, TestPlanDto>();
            CreateMap<TestPlanManifest, TestPlanManifestDto>();
            CreateMap<HardwareConfig, TestPlanConfigDto>();

            CreateMap<DeviceChannel, DeviceChannelDto>();

            CreateMap<TestPlanConfig, TestPlanConfigDto>();
            CreateMap<TestPlanConfigDto, TestPlanConfig>();

            CreateMap<CanConfig, CanConfigDto>();
            CreateMap<CanConfigDto, CanConfig>();
            CreateMap<CanFdConfig, CanFdConfigDto>();
            CreateMap<CanFdConfigDto, CanFdConfig>();

            CreateMap<CaseConfig, CaseConfigDto>();
            CreateMap<CaseConfigDto, CaseConfig>();

            CreateMap<SequenceConfig, SequenceConfigDto>();
            CreateMap<SequenceConfigDto, SequenceConfig>();
        }
    }
}
