using System.Xml.Serialization;

namespace Alsi.Fuzz.Core.Models.TestSuites.Steps
{
    public class WaitStep : StepBase
    {
        private int? _timeoutMs;

        [XmlIgnore]
        public int? TimeoutMs
        {
            get => _timeoutMs;
            set => _timeoutMs = value;
        }

        [XmlAttribute("timeout-ms")]
        public string TimeoutMsString
        {
            get => _timeoutMs.HasValue ? _timeoutMs.Value.ToString() : null;
            set => _timeoutMs = string.IsNullOrEmpty(value) ? (int?)null : int.Parse(value);
        }
    }
}
