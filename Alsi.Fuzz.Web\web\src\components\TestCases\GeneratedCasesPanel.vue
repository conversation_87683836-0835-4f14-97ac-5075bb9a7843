<template>
  <div style="display: flex;flex:1; flex-direction: column;">
    <div class="case-info">
      <span>
        Showing {{ cases.length }} cases
      </span>
    </div>
    <div class="case-panel">
      <!-- 保存进度显示 -->
      <div v-if="saving && saveProgress" class="save-progress">
        <div class="progress-header">
          <span>Saving test cases...</span>
        </div>
        <el-progress :percentage="saveProgress.progress" :status="saveProgress.error ? 'exception' : undefined"
          :stroke-width="6">
        </el-progress>
        <div class="progress-message">{{ saveProgress.message }}</div>
      </div>

      <div v-if="generating" class="loading-indicator">
        <el-icon class="is-loading">
          <Loading />
        </el-icon> Generating...
      </div>

      <div v-else-if="cases.length === 0 && showEmptyMessage" class="empty-message">
        Click Generate to create test cases
      </div>

      <div v-else-if="cases.length > 0" class="results-list case-list">
        <!-- 固定表头 -->
        <div class="cases-header" ref="headerContainer"></div>
        <!-- 虚拟滚动内容容器 -->
        <div ref="casesContainer" class="cases-content"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { Loading } from '@element-plus/icons-vue';
import { VirtualScroll } from 'js-booster';
import { CaseResult } from '@/api/interoperationApi';
import type { SaveProgressResponse } from '@/api/appApi';

const props = defineProps<{
  cases: CaseResult[];
  generating: boolean;
  saving?: boolean;
  saveProgress?: SaveProgressResponse | null;
  showEmptyMessage: boolean;
}>();

// 虚拟滚动相关
const casesContainer = ref<HTMLElement | null>(null);
const headerContainer = ref<HTMLElement | null>(null);
let virtualScroll: any = null;
const ITEM_HEIGHT = 36; // 行高
const BUFFER_SIZE = 20; // 缓冲区大小
const HEADER_HEIGHT = 40; // 表头高度

// 渲染固定表头
const renderFixedHeader = () => {
  if (!headerContainer.value) return;

  // 清空表头容器
  headerContainer.value.innerHTML = '';

  // 创建表头
  const header = document.createElement('div');
  header.className = 'header-row';
  header.style.height = `${HEADER_HEIGHT}px`;

  // ID 列
  const idHeader = document.createElement('div');
  idHeader.textContent = 'ID';
  idHeader.className = 'header-cell-id';
  header.appendChild(idHeader);

  // 名称列
  const nameHeader = document.createElement('div');
  nameHeader.textContent = 'Name';
  nameHeader.className = 'header-cell-name';
  header.appendChild(nameHeader);

  // 参数列
  const paramHeader = document.createElement('div');
  paramHeader.textContent = 'Parameter';
  paramHeader.className = 'header-cell-param';
  header.appendChild(paramHeader);

  // 添加到表头容器
  headerContainer.value.appendChild(header);
};

// 初始化虚拟滚动
const initVirtualScroll = () => {
  if (!casesContainer.value || !props.cases.length) return;

  // 渲染固定表头
  renderFixedHeader();

  // 如果已经存在虚拟滚动实例，先销毁
  if (virtualScroll) {
    virtualScroll.destroy();
  }

  virtualScroll = new VirtualScroll({
    container: casesContainer.value,
    items: props.cases,
    itemHeight: ITEM_HEIGHT,
    bufferSize: BUFFER_SIZE,
    renderItem: (item: CaseResult, index: number) => {
      // 创建主容器
      const div = document.createElement('div');
      div.className = 'case-row';

      // 设置动态样式
      div.style.height = `${ITEM_HEIGHT}px`;
      div.style.lineHeight = `${ITEM_HEIGHT}px`;
      div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';
      div.style.backgroundColor = index % 2 === 0 ? '#fff' : '#fafafa';

      // 添加悬停效果
      div.onmouseover = () => {
        div.style.backgroundColor = '#f5f7fa';
      };
      div.onmouseout = () => {
        div.style.backgroundColor = index % 2 === 0 ? '#fff' : '#fafafa';
        div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';
      };

      // 创建行内容
      const rowDiv = document.createElement('div');
      rowDiv.className = 'case-row-content';

      // ID
      const idDiv = document.createElement('div');
      idDiv.textContent = `${item.id || index + 1}`;
      idDiv.className = 'case-cell-id';
      rowDiv.appendChild(idDiv);

      // 名称
      const nameDiv = document.createElement('div');
      nameDiv.textContent = item.name || '-';
      nameDiv.title = item.name || '';
      nameDiv.className = 'case-cell-name';
      rowDiv.appendChild(nameDiv);

      // 参数
      const paramDiv = document.createElement('div');
      paramDiv.textContent = item.parameter || '-';
      paramDiv.title = item.parameter || '';
      paramDiv.className = 'case-cell-param';
      rowDiv.appendChild(paramDiv);

      div.appendChild(rowDiv);

      return div;
    }
  });
};

// 更新虚拟滚动数据
const updateVirtualScroll = () => {
  nextTick(() => {
    initVirtualScroll();
  });
};

// 监听 cases 变化，更新虚拟滚动
watch(() => props.cases, () => {
  nextTick(() => {
    updateVirtualScroll();
  });
}, { deep: true });

// 组件挂载时初始化虚拟滚动
onMounted(() => {
  nextTick(() => {
    initVirtualScroll();
  });
});

// 组件卸载时销毁虚拟滚动
onUnmounted(() => {
  if (virtualScroll) {
    virtualScroll.destroy();
    virtualScroll = null;
  }
});
</script>

<style scoped lang="scss">
.case-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-top: 1px solid #dcdfe6;
  border-radius: 4px;
}

.case-info {
  font-size: 12px;
  color: #909399;
  display: flex;
  padding: 10px 15px;
}

.save-progress {
  padding: 12px;
  background-color: #f0f9ff;
  border-bottom: 1px solid #ebeef5;

  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 13px;
    font-weight: 500;
    color: #303133;
  }

  .progress-message {
    margin-top: 4px;
    font-size: 12px;
    color: #606266;
  }
}

.results-list {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  font-size: 13px;
  min-height: 0;
  flex-basis: 0;
}

.cases-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #f5f7fa;
}

.cases-content {
  flex: 1;
  overflow: auto;
  position: relative;
}

/* 使用全局样式中的表格样式 */
.case-list {
  .cases-header {
    .header-row {
      display: flex;
      align-items: center;
      width: 100%;
      height: 40px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #dcdfe6;
      font-weight: bold;
      font-size: 12px;
      color: #606266;
      padding: 0;
    }

    .header-cell-id {
      width: 60px;
      flex-shrink: 0;
      padding-left: 12px;
    }

    .header-cell-name {
      flex: 1;
      padding-left: 12px;
    }

    .header-cell-param {
      flex: 1;
      padding: 0 10px;
    }
  }

  .case-row {
    border: none;
    padding: 0;
    height: 36px;
    line-height: 36px;
    font-size: 12px;
    transition: all 0.3s;
  }

  .case-row-content {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
  }

  .case-cell-id {
    font-weight: bold;
    color: var(--el-color-primary);
    width: 60px;
    flex-shrink: 0;
    padding-left: 12px;
  }

  .case-cell-name {
    font-weight: 500;
    color: #303133;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 0 0 10px;
  }

  .case-cell-param {
    flex: 1;
    color: #606266;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 0 0 10px;
  }
}

.loading-indicator,
.empty-message {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  color: #909399;
  font-size: 13px;

  .el-icon {
    margin-right: 6px;
  }
}
</style>
