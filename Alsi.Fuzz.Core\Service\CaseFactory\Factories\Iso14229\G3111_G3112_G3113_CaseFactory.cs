using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts;
using Alsi.Fuzz.Core.Service.CaseFactory.Iso14229;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G3111_G3112_G3113_CaseFactory : CaseFactoryBase
    {
        public override void Generate(MutationOptions options, Action<CreateCaseInfo> createCase)
        {
            if (options.Coverage == CoverageType.High)
            {
                GenerateByIsoSubfunctions(options, createCase);
            }
            else if (options.Coverage == CoverageType.Normal)
            {
                GenerateByXmlSubfunctions(options, createCase);
            }
        }

        //public override void GenerateWithGroup(MutationOptions options, Action<CreateCaseInfo> createCase)
        //{
        //    if (options.Coverage == CoverageType.High)
        //    {
        //        GenerateWithGroupByIsoSubfunctions(options, createCase);
        //    }
        //    else if (options.Coverage == CoverageType.Normal)
        //    {
        //        GenerateWithGroupByXmlSubfunctions(options, createCase);
        //    }
        //}

        private void GenerateByXmlSubfunctions(MutationOptions options, Action<CreateCaseInfo> createCase)
        {
            var xmlServices = options.XmlServices;
            var supportedXmlServicesWithSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == true)
                .ToArray();

            foreach (var xmlService in supportedXmlServicesWithSubfunction)
            {
                var sid = xmlService.Id;
                var subfunctionId = xmlService.SubfunctionId.Value;
                var data2kLength = xmlService.Parameter2k.Length;

                var payload = new List<byte> { sid, subfunctionId };
                payload.AddRange(xmlService.Parameter2k);


                var serviceName = xmlService.IsoUdsServiceDisplayName;
                //var otherSIDName = "";
                //var groupPathTest = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                //    .GetService(serviceName)
                //    .Invalid()
                //    .Parameter1KWithSub(otherSIDName)
                //    .Path;

                var groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                    .GetService(serviceName)
                    .Valid()
                    .Path;
                var name = $"XmlSubfunc{subfunctionId:X2} Correct -{groupPath}";

                // G111 - 当前 Subfunction、参数、正确的 DataLength
                var caseMutation = CreateCaseMutation(name, payload, payload.Count, groupPath);
                createCase(new CreateCaseInfo($"G3111-Sid{sid:X2}-Subfunc{subfunctionId:X2}", caseMutation, sid, subfunctionId));

                // G112 - 当前 Subfunction、当前参数、DataLength - 1
                groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                    .GetService(serviceName)
                    .Invalid()
                    .Datalength()
                    .Path;
                //name = $"XmlSubfunc{subfunctionId:X2} Less-{groupPath}";
                for (var payloadLength = 1; payloadLength <= payload.Count - 1; payloadLength++)
                {
                    name = $"XmlSubfunc{subfunctionId:X2} Less: payloadLength={payloadLength} -{groupPath}";
                    caseMutation = CreateCaseMutation(name, payload, payloadLength, groupPath);
                    createCase(new CreateCaseInfo($"G3112-Sid{sid:X2}-Subfunc{subfunctionId:X2}", caseMutation, sid, subfunctionId));
                }

                // G113 - 当前 Subfunction、当前参数、DataLength + 1
                var randomByte = new byte[] { 0, 0x55, 0xAA, 0xCC, 0xFF };
                {
                    var payloadBytes = payload.ToList();
                    payloadBytes.Insert(0, randomByte[options.Random.Next(0, 5)]);
                    name = $"XmlSubfunc{subfunctionId:X2} Large:  head -{groupPath}";
                    caseMutation = CreateCaseMutation(name, payloadBytes.ToArray(), payloadBytes.Count, groupPath);
                    createCase(new CreateCaseInfo($"G3113-Sid{sid:X2}-Subfunc{subfunctionId:X2}", caseMutation, sid, subfunctionId));
                }
                {
                    var payloadBytes = payload.ToList();
                    payloadBytes.Add(randomByte[options.Random.Next(0, 5)]);
                    name = $"XmlSubfunc{subfunctionId:X2} Large:  tail -{groupPath}";
                    caseMutation = CreateCaseMutation(name, payloadBytes.ToArray(), payloadBytes.Count, groupPath);
                    createCase(new CreateCaseInfo($"G3113-Sid{sid:X2}-Subfunc{subfunctionId:X2}", caseMutation, sid, subfunctionId));
                }
            }
        }

        private void GenerateByIsoSubfunctions(MutationOptions options, Action<CreateCaseInfo> createCase)
        {
            foreach (var service in IsoUdsConsts.Services)
            {
                var sid = service.Id;
                var serviceName = service.Name;

                foreach (var subfunction in service.Subfunctions)
                {
                    var data2kLength = subfunction.Length - 2;

                    var payload = new List<byte> { sid, subfunction.Id };
                    payload.AddRange(RandomBytes(data2kLength));

                    // G111 - 当前 Subfunction、自己的参数、正确的 DataLength
                    var groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                        .GetService(serviceName)
                        .Valid()
                        .Path;
                    var name = $"IsoSubfunc{subfunction.Id:X2} Correct -{groupPath}";
                    var caseMutation = CreateCaseMutation(name, payload, payload.Count, groupPath);
                    createCase(new CreateCaseInfo($"G3111-Sid{sid:X2}-Subfunc{subfunction.Id:X2}", caseMutation, sid, subfunction.Id));

                    // G112 - 当前 Subfunction、自己的参数、DataLength - 1
                    groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                        .GetService(serviceName)
                        .Invalid()
                        .Datalength()
                        .Path;
                    for (var payloadLength = 1; payloadLength < payload.Count - 1; payloadLength++)
                    {
                        name = $"IsoSubfunc{subfunction.Id:X2} Less: payloadLength={payloadLength} -{groupPath}";
                        caseMutation = CreateCaseMutation(name, payload, payloadLength, groupPath);
                        createCase(new CreateCaseInfo($"G3112-Sid{sid:X2}-Subfunc{subfunction.Id:X2}", caseMutation, sid, subfunction.Id));
                    }

                    // G113 - 当前 Subfunction、自己的参数、DataLength + 1
                    groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                        .GetService(serviceName)
                        .Invalid()
                        .Datalength()
                        .Path;
                    var randomByte = new byte[] { 0, 0x55, 0xAA, 0xCC, 0xFF };
                    {
                        var payloadBytes = payload.ToList();
                        payloadBytes.Insert(0, randomByte[options.Random.Next(0, 5)]);
                        name = $"IsoSubfunc{subfunction.Id:X2} Large:  head -{groupPath}";
                        caseMutation = CreateCaseMutation(name, payloadBytes, payloadBytes.Count, groupPath);
                        createCase(new CreateCaseInfo($"G3113-Sid{sid:X2}-Subfunc{subfunction.Id:X2}", caseMutation, sid, subfunction.Id));
                    }
                    {
                        var payloadBytes = payload.ToList();
                        payloadBytes.Add(randomByte[options.Random.Next(0, 5)]);
                        name = $"IsoSubfunc{subfunction.Id:X2} Large:  tail -{groupPath}";
                        caseMutation = CreateCaseMutation(name, payloadBytes, payloadBytes.Count, groupPath);
                        createCase(new CreateCaseInfo($"G3113-Sid{sid:X2}-Subfunc{subfunction.Id:X2}", caseMutation, sid, subfunction.Id));
                    }
                }
            }
        }


        private void GenerateWithGroupByXmlSubfunctions(MutationOptions options, Action<CreateCaseInfo> createCase)
        {
            var xmlServices = options.XmlServices;
            var supportedXmlServicesWithSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == true)
                .ToArray();

            foreach (var xmlService in supportedXmlServicesWithSubfunction)
            {
                var sid = xmlService.Id;
                var subfunctionId = xmlService.SubfunctionId.Value;
                var data2kLength = xmlService.Parameter2k.Length;

                var payload = new List<byte> { sid, subfunctionId };
                payload.AddRange(xmlService.Parameter2k);

                var serviceName = xmlService.IsoUdsServiceDisplayName;
                var otherSIDName = "";
                var groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                    .GetService(serviceName)
                    .Invalid()
                    .Parameter1WithSub(otherSIDName)
                    .Path;

                // G111 - 当前 Subfunction、参数、正确的 DataLength
                var caseMutation = CreateCaseMutation($"G3111-Sid{sid:X2}-Subfunc{subfunctionId:X2}", payload, payload.Count);
                createCase(new CreateCaseInfo($"G3111-Sid{sid:X2}-Subfunc{subfunctionId:X2}", caseMutation, sid, subfunctionId));

                // G112 - 当前 Subfunction、当前参数、DataLength - 1
                for (var payloadLength = 1; payloadLength < payload.Count - 1; payloadLength++)
                {
                    caseMutation = CreateCaseMutation($"G3112-Sid{sid:X2}-Subfunc{subfunctionId:X2}", payload, payloadLength);
                    createCase(new CreateCaseInfo($"G3112-Sid{sid:X2}-Subfunc{subfunctionId:X2}", caseMutation, sid, subfunctionId));
                }

                // G113 - 当前 Subfunction、当前参数、DataLength + 1
                var randomByte = new byte[] { 0, 0x55, 0xAA, 0xCC, 0xFF };
                {
                    var payloadBytes = payload.ToList();
                    payloadBytes.Insert(0, randomByte[options.Random.Next(0, 5)]);
                    caseMutation = CreateCaseMutation($"G3113-Sid{sid:X2}-Subfunc{subfunctionId:X2}", payloadBytes.ToArray(), payloadBytes.Count);
                    createCase(new CreateCaseInfo($"G3113-Sid{sid:X2}-Subfunc{subfunctionId:X2}", caseMutation, sid, subfunctionId));
                }
                {
                    var payloadBytes = payload.ToList();
                    payloadBytes.Add(randomByte[options.Random.Next(0, 5)]);
                    caseMutation = CreateCaseMutation($"G3113-Sid{sid:X2}-Subfunc{subfunctionId:X2}", payloadBytes.ToArray(), payloadBytes.Count);
                    createCase(new CreateCaseInfo($"G3113-Sid{sid:X2}-Subfunc{subfunctionId:X2}", caseMutation, sid, subfunctionId));
                }
            }
        }

        private void GenerateWithGroupByIsoSubfunctions(MutationOptions options, Action<CreateCaseInfo> createCase)
        {
            foreach (var service in IsoUdsConsts.Services)
            {
                var sid = service.Id;

                foreach (var subfunction in service.Subfunctions)
                {
                    var data2kLength = subfunction.Length - 2;

                    var payload = new List<byte> { sid, subfunction.Id };
                    payload.AddRange(RandomBytes(data2kLength));

                    // G111 - 当前 Subfunction、自己的参数、正确的 DataLength
                    var caseMutation = CreateCaseMutation($"G3111-Sid{sid:X2}-Subfunc{subfunction.Id:X2}", payload, payload.Count);
                    createCase(new CreateCaseInfo($"G3111-Sid{sid:X2}-Subfunc{subfunction.Id:X2}", caseMutation, sid, subfunction.Id));

                    // G112 - 当前 Subfunction、自己的参数、DataLength - 1
                    for (var payloadLength = 1; payloadLength < payload.Count - 1; payloadLength++)
                    {
                        caseMutation = CreateCaseMutation($"G3112-Sid{sid:X2}-Subfunc{subfunction.Id:X2}", payload, payloadLength);
                        createCase(new CreateCaseInfo($"G3112-Sid{sid:X2}-Subfunc{subfunction.Id:X2}", caseMutation, sid, subfunction.Id));
                    }

                    // G113 - 当前 Subfunction、自己的参数、DataLength + 1
                    var randomByte = new byte[] { 0, 0x55, 0xAA, 0xCC, 0xFF };
                    {
                        var payloadBytes = payload.ToList();
                        payloadBytes.Insert(0, randomByte[options.Random.Next(0, 5)]);
                        caseMutation = CreateCaseMutation($"G3113-Sid{sid:X2}-Subfunc{subfunction.Id:X2}", payloadBytes, payloadBytes.Count);
                        createCase(new CreateCaseInfo($"G3113-Sid{sid:X2}-Subfunc{subfunction.Id:X2}", caseMutation, sid, subfunction.Id));
                    }
                    {
                        var payloadBytes = payload.ToList();
                        payloadBytes.Add(randomByte[options.Random.Next(0, 5)]);
                        caseMutation = CreateCaseMutation($"G3113-Sid{sid:X2}-Subfunc{subfunction.Id:X2}", payloadBytes, payloadBytes.Count);
                        createCase(new CreateCaseInfo($"G3113-Sid{sid:X2}-Subfunc{subfunction.Id:X2}", caseMutation, sid, subfunction.Id));
                    }
                }
            }
        }
    }
}
