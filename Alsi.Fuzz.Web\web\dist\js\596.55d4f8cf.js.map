{"version": 3, "file": "js/596.55d4f8cf.js", "mappings": "2NAGA,MAAMA,EAAa,CAAEC,MAAO,2BACtBC,EAAa,CAAED,MAAO,uBACtBE,EAAa,CAAEF,MAAO,kBACtBG,EAAa,CACjBC,IAAK,EACLJ,MAAO,uBAEHK,EAAa,CAAED,IAAK,GACpBE,EAAa,CACjBF,IAAK,EACLJ,MAAO,oBAEHO,EAAa,CAAEP,MAAO,gBACtBQ,EAAa,CAAER,MAAO,2BACtBS,EAAa,CAAET,MAAO,gBACtBU,EAAc,CAAEV,MAAO,gBACvBW,EAAc,CAAEX,MAAO,gBAQ7B,OAA4BY,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,eACRC,KAAAA,CAAMC,GCwCR,MAAMC,EAAQC,EAAAA,EAAgBC,WACxBC,GAAWC,EAAAA,EAAAA,KAAS,IAAMJ,EAAMK,cAChCC,GAAUF,EAAAA,EAAAA,KAAS,IAAMJ,EAAMO,YAE/BC,GAAYC,EAAAA,EAAAA,KAAI,GAChBC,GAAkBD,EAAAA,EAAAA,IAAI,IACtBE,GAAWF,EAAAA,EAAAA,KAAI,GAEfG,EAAkBC,gBAChBZ,EAAAA,EAAgBa,gBAAgB,EAGlCC,EAAeA,KACnBL,EAAgBM,MAAQb,EAASa,OAAOC,SAASC,aAAe,GAChEV,EAAUQ,OAAQ,CAAI,EAGlBG,EAAgBA,KACpBX,EAAUQ,OAAQ,CAAK,EAGnBI,EAAkBP,UACtB,GAAKV,EAASa,MAAd,CAEAL,EAASK,OAAQ,EACjB,UACQf,EAAAA,EAAgBoB,gBAAgBX,EAAgBM,OACtDR,EAAUQ,OAAQ,C,CAClB,QACAL,EAASK,OAAQ,C,CAPQ,C,EDvB7B,OCkCAM,EAAAA,EAAAA,KAAU,KACRV,GAAiB,IDnCZ,CAACW,EAAUC,KAChB,MAAMC,GAAuBC,EAAAA,EAAAA,IAAkB,aACzCC,GAAyBD,EAAAA,EAAAA,IAAkB,eAC3CE,GAAsBF,EAAAA,EAAAA,IAAkB,YACxCG,GAAsBH,EAAAA,EAAAA,IAAkB,YACxCI,GAAwBJ,EAAAA,EAAAA,IAAkB,cAC1CK,GAAkCL,EAAAA,EAAAA,IAAkB,wBACpDM,GAA6BN,EAAAA,EAAAA,IAAkB,mBAC/CO,GAAqBC,EAAAA,EAAAA,IAAkB,WAE7C,OAAOC,EAAAA,EAAAA,MAAiBC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOtD,EAAY,EAC3EuD,EAAAA,EAAAA,IAAoB,MAAOrD,EAAY,EACrCqD,EAAAA,EAAAA,IAAoB,MAAOpD,EAAY,CACrCsC,EAAO,KAAOA,EAAO,IAAKc,EAAAA,EAAAA,IAAoB,KAAM,KAAM,eAAgB,IACxE9B,EAAUQ,OAcRuB,EAAAA,EAAAA,IAAoB,IAAI,KAbvBH,EAAAA,EAAAA,OAAcI,EAAAA,EAAAA,IAAaf,EAAsB,CAChDrC,IAAK,EACLqD,KAAM,UACNC,KAAM,GACNC,KAAM,QACNC,QAAS7B,EACT8B,MAAMC,EAAAA,EAAAA,IAAOC,EAAAA,OACZ,CACDC,SAASC,EAAAA,EAAAA,KAAS,IAAMzB,EAAO,KAAOA,EAAO,GAAK,EAChD0B,EAAAA,EAAAA,IAAiB,cAEnBC,EAAG,GACF,EAAG,CAAC,YAGZ7C,EAAQU,QACJoB,EAAAA,EAAAA,OAAcI,EAAAA,EAAAA,IAAab,EAAwB,CAClDvC,IAAK,EACLgE,KAAM,EACNC,SAAU,QAEXjB,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoBiB,EAAAA,GAAW,CAAElE,IAAK,GAAK,CACtDoB,EAAUQ,QAUPoB,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAO/C,EAAY,EACpDiE,EAAAA,EAAAA,IAAa1B,EAAqB,CAChC2B,WAAY9C,EAAgBM,MAC5B,sBAAuBQ,EAAO,KAAOA,EAAO,GAAMiC,GAAkB/C,EAAiBM,MAAQyC,GAC7FhB,KAAM,WACNW,KAAM,EACNM,YAAa,8BACbC,UAAW,MACX,kBAAmB,IAClB,KAAM,EAAG,CAAC,gBACbrB,EAAAA,EAAAA,IAAoB,MAAO/C,EAAY,EACrCgE,EAAAA,EAAAA,IAAa9B,EAAsB,CACjCgB,KAAM,UACNG,QAASxB,EACTd,QAASK,EAASK,OACjB,CACDgC,SAASC,EAAAA,EAAAA,KAAS,IAAMzB,EAAO,KAAOA,EAAO,GAAK,EAChD0B,EAAAA,EAAAA,IAAiB,cAEnBC,EAAG,GACF,EAAG,CAAC,aACPI,EAAAA,EAAAA,IAAa9B,EAAsB,CAAEmB,QAASzB,GAAiB,CAC7D6B,SAASC,EAAAA,EAAAA,KAAS,IAAMzB,EAAO,KAAOA,EAAO,GAAK,EAChD0B,EAAAA,EAAAA,IAAiB,cAEnBC,EAAG,WAlCRf,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOlD,EAAY,CACnDgB,EAASa,OAAOC,SAASC,cACrBkB,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,IAAKhD,GAAYuE,EAAAA,EAAAA,IAAiBzD,EAASa,OAAOC,SAASC,aAAc,MAC3GkB,EAAAA,EAAAA,OAAcI,EAAAA,EAAAA,IAAaZ,EAAqB,CAC/CxC,IAAK,EACL8B,YAAa,iBACb,aAAc,WAgCzB,QAEToB,EAAAA,EAAAA,IAAoB,MAAO9C,EAAY,CACrCgC,EAAO,KAAOA,EAAO,IAAKc,EAAAA,EAAAA,IAAoB,KAAM,KAAM,0BAA2B,KACrFiB,EAAAA,EAAAA,IAAavB,EAA4B,CACvC6B,OAAQ,GACRC,OAAQ,GACP,CACDd,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBM,EAAAA,EAAAA,IAAaxB,EAAiC,CAAEgC,MAAO,aAAe,CACpEf,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBM,EAAAA,EAAAA,IAAazB,EAAuB,CAClCkC,QAAS7D,EAASa,OAAOiD,KACzBC,UAAW,OACV,CACDlB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBX,EAAAA,EAAAA,IAAoB,MAAO7C,GAAYmE,EAAAA,EAAAA,IAAiBzD,EAASa,OAAOiD,MAAO,MAEjFd,EAAG,GACF,EAAG,CAAC,eAETA,EAAG,KAELI,EAAAA,EAAAA,IAAaxB,EAAiC,CAAEgC,MAAO,WAAa,CAClEf,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBX,EAAAA,EAAAA,IAAoB,MAAO5C,GAAakE,EAAAA,EAAAA,KAAiBd,EAAAA,EAAAA,IAAOqB,EAAAA,EAAPrB,CAAuB3C,EAASa,OAAOC,SAASmD,UAAW,MAEtHjB,EAAG,KAELI,EAAAA,EAAAA,IAAaxB,EAAiC,CAAEgC,MAAO,YAAc,CACnEf,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBX,EAAAA,EAAAA,IAAoB,MAAO3C,GAAaiE,EAAAA,EAAAA,KAAiBd,EAAAA,EAAAA,IAAOqB,EAAAA,EAAPrB,CAAuB3C,EAASa,OAAOC,SAASoD,WAAY,MAEvHlB,EAAG,OAGPA,EAAG,SAGJ,CACH,CAAClB,EAAoB3B,EAAQU,QAC7B,CAEJ,I,UErLA,MAAMsD,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://fuzz-web/./src/views/testplan/BasicSetting.vue?9517", "webpack://fuzz-web/./src/views/testplan/BasicSetting.vue", "webpack://fuzz-web/./src/views/testplan/BasicSetting.vue?06f2"], "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { createElementVNode as _createElementVNode, unref as _unref, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementBlock as _createElementBlock, createVNode as _createVNode, Fragment as _Fragment, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\"\n\nconst _hoisted_1 = { class: \"basic-setting-container\" }\nconst _hoisted_2 = { class: \"description-section\" }\nconst _hoisted_3 = { class: \"section-header\" }\nconst _hoisted_4 = {\n  key: 0,\n  class: \"description-content\"\n}\nconst _hoisted_5 = { key: 0 }\nconst _hoisted_6 = {\n  key: 1,\n  class: \"description-edit\"\n}\nconst _hoisted_7 = { class: \"edit-actions\" }\nconst _hoisted_8 = { class: \"additional-info-section\" }\nconst _hoisted_9 = { class: \"path-display\" }\nconst _hoisted_10 = { class: \"path-display\" }\nconst _hoisted_11 = { class: \"path-display\" }\n\nimport { ref, onMounted, computed } from 'vue';\r\nimport { Edit } from '@element-plus/icons-vue';\r\nimport { testPlanService } from '@/services/testPlanService';\r\nimport { formatDateTime } from '@/utils/timeUtils';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'BasicSetting',\n  setup(__props) {\n\r\nconst state = testPlanService.getState();\r\nconst testPlan = computed(() => state.currentPlan);\r\nconst loading = computed(() => state.isLoading);\r\n\r\nconst isEditing = ref(false);\r\nconst editDescription = ref('');\r\nconst isSaving = ref(false);\r\n\r\nconst loadCurrentPlan = async () => {\r\n  await testPlanService.getCurrentPlan();\r\n};\r\n\r\nconst startEditing = () => {\r\n  editDescription.value = testPlan.value?.manifest.description || '';\r\n  isEditing.value = true;\r\n};\r\n\r\nconst cancelEditing = () => {\r\n  isEditing.value = false;\r\n};\r\n\r\nconst saveDescription = async () => {\r\n  if (!testPlan.value) return;\r\n  \r\n  isSaving.value = true;\r\n  try {\r\n    await testPlanService.updateBasicInfo(editDescription.value);\r\n    isEditing.value = false;\r\n  } finally {\r\n    isSaving.value = false;\r\n  }\r\n};\r\n\r\nonMounted(() => {\r\n  loadCurrentPlan();\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\")!\n  const _component_el_empty = _resolveComponent(\"el-empty\")!\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_tooltip = _resolveComponent(\"el-tooltip\")!\n  const _component_el_descriptions_item = _resolveComponent(\"el-descriptions-item\")!\n  const _component_el_descriptions = _resolveComponent(\"el-descriptions\")!\n  const _directive_loading = _resolveDirective(\"loading\")!\n\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createElementVNode(\"div\", _hoisted_3, [\n        _cache[2] || (_cache[2] = _createElementVNode(\"h4\", null, \"Description\", -1)),\n        (!isEditing.value)\n          ? (_openBlock(), _createBlock(_component_el_button, {\n              key: 0,\n              type: \"primary\",\n              text: \"\",\n              size: \"small\",\n              onClick: startEditing,\n              icon: _unref(Edit)\n            }, {\n              default: _withCtx(() => _cache[1] || (_cache[1] = [\n                _createTextVNode(\" Edit \")\n              ])),\n              _: 1\n            }, 8, [\"icon\"]))\n          : _createCommentVNode(\"\", true)\n      ]),\n      (loading.value)\n        ? (_openBlock(), _createBlock(_component_el_skeleton, {\n            key: 0,\n            rows: 3,\n            animated: \"\"\n          }))\n        : (_openBlock(), _createElementBlock(_Fragment, { key: 1 }, [\n            (!isEditing.value)\n              ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [\n                  (testPlan.value?.manifest.description)\n                    ? (_openBlock(), _createElementBlock(\"p\", _hoisted_5, _toDisplayString(testPlan.value?.manifest.description), 1))\n                    : (_openBlock(), _createBlock(_component_el_empty, {\n                        key: 1,\n                        description: \"No description\",\n                        \"image-size\": 100\n                      }))\n                ]))\n              : (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [\n                  _createVNode(_component_el_input, {\n                    modelValue: editDescription.value,\n                    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((editDescription).value = $event)),\n                    type: \"textarea\",\n                    rows: 3,\n                    placeholder: \"Enter test plan description\",\n                    maxlength: \"500\",\n                    \"show-word-limit\": \"\"\n                  }, null, 8, [\"modelValue\"]),\n                  _createElementVNode(\"div\", _hoisted_7, [\n                    _createVNode(_component_el_button, {\n                      type: \"primary\",\n                      onClick: saveDescription,\n                      loading: isSaving.value\n                    }, {\n                      default: _withCtx(() => _cache[3] || (_cache[3] = [\n                        _createTextVNode(\" Save \")\n                      ])),\n                      _: 1\n                    }, 8, [\"loading\"]),\n                    _createVNode(_component_el_button, { onClick: cancelEditing }, {\n                      default: _withCtx(() => _cache[4] || (_cache[4] = [\n                        _createTextVNode(\"Cancel\")\n                      ])),\n                      _: 1\n                    })\n                  ])\n                ]))\n          ], 64))\n    ]),\n    _createElementVNode(\"div\", _hoisted_8, [\n      _cache[5] || (_cache[5] = _createElementVNode(\"h4\", null, \"Additional Information\", -1)),\n      _createVNode(_component_el_descriptions, {\n        border: \"\",\n        column: 1\n      }, {\n        default: _withCtx(() => [\n          _createVNode(_component_el_descriptions_item, { label: \"File Path\" }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_tooltip, {\n                content: testPlan.value?.path,\n                placement: \"top\"\n              }, {\n                default: _withCtx(() => [\n                  _createElementVNode(\"div\", _hoisted_9, _toDisplayString(testPlan.value?.path), 1)\n                ]),\n                _: 1\n              }, 8, [\"content\"])\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_descriptions_item, { label: \"Created\" }, {\n            default: _withCtx(() => [\n              _createElementVNode(\"div\", _hoisted_10, _toDisplayString(_unref(formatDateTime)(testPlan.value?.manifest.created)), 1)\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_descriptions_item, { label: \"Modified\" }, {\n            default: _withCtx(() => [\n              _createElementVNode(\"div\", _hoisted_11, _toDisplayString(_unref(formatDateTime)(testPlan.value?.manifest.modified)), 1)\n            ]),\n            _: 1\n          })\n        ]),\n        _: 1\n      })\n    ])\n  ])), [\n    [_directive_loading, loading.value]\n  ])\n}\n}\n\n})", "<template>\r\n  <div class=\"basic-setting-container\" v-loading=\"loading\">\r\n    <div class=\"description-section\">\r\n      <div class=\"section-header\">\r\n        <h4>Description</h4>\r\n        <el-button \r\n          v-if=\"!isEditing\" \r\n          type=\"primary\" \r\n          text \r\n          size=\"small\" \r\n          @click=\"startEditing\"\r\n          :icon=\"Edit\"\r\n        >\r\n          Edit\r\n        </el-button>\r\n      </div>\r\n      \r\n      <el-skeleton :rows=\"3\" animated v-if=\"loading\"></el-skeleton>\r\n      \r\n      <template v-else>\r\n        <div v-if=\"!isEditing\" class=\"description-content\">\r\n          <p v-if=\"testPlan?.manifest.description\">{{ testPlan?.manifest.description }}</p>\r\n          <el-empty v-else description=\"No description\" :image-size=\"100\"></el-empty>\r\n        </div>\r\n        \r\n        <div v-else class=\"description-edit\">\r\n          <el-input\r\n            v-model=\"editDescription\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"Enter test plan description\"\r\n            maxlength=\"500\"\r\n            show-word-limit\r\n          />\r\n          <div class=\"edit-actions\">\r\n            <el-button type=\"primary\" @click=\"saveDescription\" :loading=\"isSaving\">\r\n              Save\r\n            </el-button>\r\n            <el-button @click=\"cancelEditing\">Cancel</el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </div>\r\n    \r\n    <div class=\"additional-info-section\">\r\n      <h4>Additional Information</h4>\r\n      <el-descriptions border :column=\"1\">\r\n        <el-descriptions-item label=\"File Path\">\r\n          <el-tooltip :content=\"testPlan?.path\" placement=\"top\">\r\n            <div class=\"path-display\">{{ testPlan?.path }}</div>\r\n          </el-tooltip>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"Created\">\r\n          <div class=\"path-display\">{{ formatDateTime(testPlan?.manifest.created) }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"Modified\">\r\n          <div class=\"path-display\">{{ formatDateTime(testPlan?.manifest.modified) }}</div>\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, onMounted, computed } from 'vue';\r\nimport { Edit } from '@element-plus/icons-vue';\r\nimport { testPlanService } from '@/services/testPlanService';\r\nimport { formatDateTime } from '@/utils/timeUtils';\r\n\r\nconst state = testPlanService.getState();\r\nconst testPlan = computed(() => state.currentPlan);\r\nconst loading = computed(() => state.isLoading);\r\n\r\nconst isEditing = ref(false);\r\nconst editDescription = ref('');\r\nconst isSaving = ref(false);\r\n\r\nconst loadCurrentPlan = async () => {\r\n  await testPlanService.getCurrentPlan();\r\n};\r\n\r\nconst startEditing = () => {\r\n  editDescription.value = testPlan.value?.manifest.description || '';\r\n  isEditing.value = true;\r\n};\r\n\r\nconst cancelEditing = () => {\r\n  isEditing.value = false;\r\n};\r\n\r\nconst saveDescription = async () => {\r\n  if (!testPlan.value) return;\r\n  \r\n  isSaving.value = true;\r\n  try {\r\n    await testPlanService.updateBasicInfo(editDescription.value);\r\n    isEditing.value = false;\r\n  } finally {\r\n    isSaving.value = false;\r\n  }\r\n};\r\n\r\nonMounted(() => {\r\n  loadCurrentPlan();\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.basic-setting-container {\r\n  margin: 0 auto;\r\n  background-color: #ffffff;\r\n  border-radius: 8px;\r\n  flex: 1;\r\n  padding: 15px 20px;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.section-header h4 {\r\n  margin: 0;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.description-section {\r\n  margin-bottom: 25px;\r\n}\r\n\r\n.description-content {\r\n  min-height: 60px;\r\n  padding: 10px;\r\n  background-color: #f8f8f8;\r\n  border-radius: 4px;\r\n}\r\n\r\n.description-content p {\r\n  margin: 0;\r\n  white-space: pre-line;\r\n  line-height: 1.5;\r\n}\r\n\r\n.description-edit {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.edit-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n  margin-top: 15px;\r\n}\r\n\r\n.additional-info-section {\r\n  margin-top: 25px;\r\n}\r\n\r\n.additional-info-section h4 {\r\n  margin: 0 0 15px 0;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.path-display {\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  max-width: 100%;\r\n}\r\n\r\n</style>\r\n", "import script from \"./BasicSetting.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./BasicSetting.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./BasicSetting.vue?vue&type=style&index=0&id=cf104d54&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-cf104d54\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "key", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_defineComponent", "__name", "setup", "__props", "state", "testPlanService", "getState", "testPlan", "computed", "currentPlan", "loading", "isLoading", "isEditing", "ref", "editDescription", "isSaving", "loadCurrentPlan", "async", "getCurrentPlan", "startEditing", "value", "manifest", "description", "cancelEditing", "saveDescription", "updateBasicInfo", "onMounted", "_ctx", "_cache", "_component_el_button", "_resolveComponent", "_component_el_skeleton", "_component_el_empty", "_component_el_input", "_component_el_tooltip", "_component_el_descriptions_item", "_component_el_descriptions", "_directive_loading", "_resolveDirective", "_withDirectives", "_openBlock", "_createElementBlock", "_createElementVNode", "_createCommentVNode", "_createBlock", "type", "text", "size", "onClick", "icon", "_unref", "Edit", "default", "_withCtx", "_createTextVNode", "_", "rows", "animated", "_Fragment", "_createVNode", "modelValue", "$event", "placeholder", "maxlength", "_toDisplayString", "border", "column", "label", "content", "path", "placement", "formatDateTime", "created", "modified", "__exports__"], "sourceRoot": ""}