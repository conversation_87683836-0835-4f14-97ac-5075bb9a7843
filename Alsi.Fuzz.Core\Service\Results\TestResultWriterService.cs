using Alsi.App.Database.Midwares;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.Results
{
    public class TestResultWriterService : ITestResultWriterService, IDisposable
    {
        public ResultContext ResultContext { get; private set; }

        private TestResult _currentTest;
        private DbContext _resultDbContext;
        private bool _isDisposed;

        public void Begin(TestResult testResult)
        {
            if (_currentTest != null)
            {
                throw new InvalidOperationException("There is already an active test. Call End() before starting a new test.");
            }

            _currentTest = testResult;

            // 创建测试结果上下文
            ResultContext = new ResultContext(_currentTest.TestType, testResult.ResultFolderName);

            // 确保结果目录存在
            Directory.CreateDirectory(ResultContext.ResultFolder);

            // 仅在正式执行用例时，需要初始化数据库
            if (_currentTest.TestType == TestType.Case)
            {
                // 初始化结果数据库
                _resultDbContext = new DbContext(ResultContext.ResultDbPath);
                _resultDbContext.Initialize();

                // 创建必要的表
                var resultFreeSql = _resultDbContext.FreeSql;
                resultFreeSql.CodeFirst.SyncStructure<CaseResult>();
                resultFreeSql.CodeFirst.SyncStructure<CaseStep>();
                //TODO: 追加group tree表便于result从数据库获取
            }
        }

        public void End()
        {
            if (_currentTest == null)
            {
                throw new InvalidOperationException("No active test. Call Begin() before calling End().");
            }

            // 关闭数据库连接
            _resultDbContext?.Dispose();
            _resultDbContext = null;

            // 清除当前测试引用
            _currentTest = null;
            ResultContext = null;
        }
        //get group tree方法
        public CaseResult[] GetGroupTrees()
        {
            EnsureTestActive();
            return _resultDbContext.FreeSql
                .Select<CaseResult>()
                .Where(x => x.TestResultId == _currentTest.Id)
                .OrderBy(x => x.Id)
                .ToList()
                .ToArray();
        }
        //add group tree方法
        public void AddOrUpdateGroupTrees(CaseResult caseResult)
        {
            EnsureTestActive();

            var existedResult = _resultDbContext.FreeSql
                .Select<CaseResult>()
                .Where(x => x.Id == caseResult.Id)
                .First();
            if (existedResult == null)
            {
                _resultDbContext.FreeSql
                    .Insert(caseResult)
                    .ExecuteAffrows();
            }
            else
            {
                _resultDbContext.FreeSql.Update<CaseResult>()
                    .SetSource(caseResult)
                    .ExecuteAffrows();
            }
        }

        public CaseResult[] GetCaseResults()
        {
            EnsureTestActive();
            return _resultDbContext.FreeSql
                .Select<CaseResult>()
                .Where(x => x.TestResultId == _currentTest.Id)
                .OrderBy(x => x.Id)
                .ToList()
                .ToArray();
        }



        public void AddOrUpdateCaseResult(CaseResult caseResult)
        {
            EnsureTestActive();

            if (_currentTest.TestType == TestType.Case)
            {
                var existedResult = _resultDbContext.FreeSql
                    .Select<CaseResult>()
                    .Where(x => x.Id == caseResult.Id)
                    .First();
                if (existedResult == null)
                {
                    _resultDbContext.FreeSql
                        .Insert(caseResult)
                        .ExecuteAffrows();
                }
                else
                {
                    _resultDbContext.FreeSql.Update<CaseResult>()
                        .SetSource(caseResult)
                        .ExecuteAffrows();
                }
            }
        }

        private List<CaseStep> CaseSteps { get; } = new List<CaseStep>();

        public CaseStep[] GetCaseSteps()
        {
            lock (CaseSteps)
            {
                return CaseSteps.ToArray();
            }
        }

        public void AddCaseSteps(CaseStep[] caseSteps)
        {
            EnsureTestActive();

            if (caseSteps == null || !caseSteps.Any())
            {
                return;
            }

            if (_currentTest.TestType == TestType.Interoperation)
            {
                lock (CaseSteps)
                {
                    CaseSteps.AddRange(caseSteps);
                }
            }
            else
            {
                _resultDbContext.FreeSql.Insert(caseSteps).ExecuteAffrows();
            }
        }

        private void EnsureTestActive()
        {
            if (_currentTest == null)
            {
                throw new InvalidOperationException("No active test. Call Begin() first.");
            }

            if (_currentTest.TestType == TestType.Case && _resultDbContext == null)
            {
                throw new InvalidOperationException("The result DB context is null`. Call Begin() first.");
            }
        }

        public void Dispose()
        {
            if (!_isDisposed)
            {
                if (_currentTest != null)
                {
                    End();
                }

                _resultDbContext?.Dispose();

                _isDisposed = true;
            }
        }
    }
}
