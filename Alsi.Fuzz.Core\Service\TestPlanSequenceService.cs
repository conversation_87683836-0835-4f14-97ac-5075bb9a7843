using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Storage;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Alsi.Fuzz.Core.Service
{
    public class TestPlanSequenceService
    {
        public TestPlanSequenceService(TestPlanStorage storage)
        {
            _storage = storage;
            _builtInTestSuiteService = new BuiltInTestSuiteService();
        }

        private readonly TestPlanStorage _storage;
        private readonly BuiltInTestSuiteService _builtInTestSuiteService;

        public bool TryGetSelectedSequenceXml(TestPlan testPlan, out IsoType isoType, out string sequenceXml)
        {
            isoType = IsoType.Unknown;
            sequenceXml = string.Empty;
            var selectedConfig = testPlan.Config.SequenceConfigList.FirstOrDefault(x => x.IsSelected);
            if (selectedConfig == null)
            {
                return false;
            }

            var sequencePackageName = selectedConfig.SequencePackageName;
            if (sequencePackageName == FuzzConsts.PackageName11898)
            {
                isoType = IsoType.Iso11898;
            }
            else if (sequencePackageName == FuzzConsts.PackageName14229)
            {
                isoType = IsoType.Iso14229;
            }
            else if (sequencePackageName == FuzzConsts.PackageName15765)
            {
                isoType = IsoType.Iso15765;
            }
            else
            {
                throw new Exception($"Can't parse ISO type from sequence package name : {sequencePackageName}");
            }

            var isBuiltIn = string.IsNullOrWhiteSpace(selectedConfig.CustomName);
            sequenceXml = isBuiltIn ? _builtInTestSuiteService.GetBuiltInXml(selectedConfig.TestSuiteName, selectedConfig.SequencePackageName)
                : selectedConfig.SequencePackageXml;
            return true;
        }

        public Task<TestPlan> UpdateSequenceConfigAsync(string path, SequenceConfigDto request)
        {
            if (!string.IsNullOrWhiteSpace(request.SequencePackageXml))
            {
                // 检查 XML 是否能被解析为 Sequence
                SequencePackageUtils.LoadFromString(request.SequencePackageXml);
            }

            var testPlan = _storage.Load(path);

            // 验证自定义包名不能与内建包名冲突
            if (!request.IsBuiltIn && !string.IsNullOrWhiteSpace(request.CustomName))
            {
                ValidateCustomPackageName(request.TestSuiteName, request.CustomName);
            }

            // 查找现有配置
            SequenceConfig config = null;
            if (request.IsBuiltIn)
            {
                // 内建包：根据TestSuiteName和SequencePackageName查找
                config = testPlan.Config.SequenceConfigList.FirstOrDefault(
                    x => x.TestSuiteName == request.TestSuiteName
                    && x.SequencePackageName == request.SequencePackageName
                    && x.IsBuiltIn);
            }
            else
            {
                // 自定义包：根据TestSuiteName和CustomName查找
                config = testPlan.Config.SequenceConfigList.FirstOrDefault(
                    x => x.TestSuiteName == request.TestSuiteName
                    && x.CustomName == request.CustomName
                    && !x.IsBuiltIn);
            }

            if (config != null)
            {
                // 更新现有配置
                if (!request.IsBuiltIn)
                {
                    // 只有自定义包才能更新XML内容
                    config.SequencePackageXml = request.SequencePackageXml;
                    config.CustomName = request.CustomName;
                    config.BasePackageName = request.BasePackageName;
                    config.LastModified = DateTime.Now;
                }
            }
            else
            {
                // 创建新配置
                var newConfig = new SequenceConfig
                {
                    TestSuiteName = request.TestSuiteName,
                    SequencePackageName = request.SequencePackageName,
                    IsBuiltIn = request.IsBuiltIn
                };

                if (request.IsBuiltIn)
                {
                    // 内建包：不存储XML内容，运行时从嵌入资源读取
                    newConfig.SequencePackageXml = null;
                    newConfig.LastModified = null;
                }
                else
                {
                    // 自定义包：存储XML内容和自定义信息
                    newConfig.SequencePackageXml = request.SequencePackageXml;
                    newConfig.CustomName = request.CustomName;
                    newConfig.BasePackageName = request.BasePackageName;
                    newConfig.LastModified = DateTime.Now;
                    newConfig.CreationTime = DateTime.Now;
                }

                testPlan.Config.SequenceConfigList.Add(newConfig);
                config = newConfig;
            }

            // 更新选中状态
            foreach (var item in testPlan.Config.SequenceConfigList)
            {
                if (request.IsBuiltIn)
                {
                    item.IsSelected = item.TestSuiteName == request.TestSuiteName
                        && item.SequencePackageName == request.SequencePackageName
                        && item.IsBuiltIn;
                }
                else
                {
                    item.IsSelected = item.TestSuiteName == request.TestSuiteName
                        && item.CustomName == request.CustomName
                        && !item.IsBuiltIn;
                }
            }

            testPlan.Manifest.Modified = DateTime.Now;
            _storage.Save(path, testPlan);

            return Task.FromResult(testPlan);
        }

        /// <summary>
        /// 删除自定义包
        /// </summary>
        public Task<TestPlan> DeleteCustomSequencePackageAsync(string path, string testSuiteName, string customName)
        {
            var testPlan = _storage.Load(path);

            var config = testPlan.Config.SequenceConfigList.FirstOrDefault(
                x => x.TestSuiteName == testSuiteName
                && x.CustomName == customName
                && !x.IsBuiltIn);

            if (config == null)
            {
                throw new InvalidOperationException($"Custom package '{customName}' not found in test suite '{testSuiteName}'");
            }

            testPlan.Config.SequenceConfigList.Remove(config);

            testPlan.Manifest.Modified = DateTime.Now;
            _storage.Save(path, testPlan);

            return Task.FromResult(testPlan);
        }

        /// <summary>
        /// 验证自定义包名不能与内建包名冲突
        /// </summary>
        private void ValidateCustomPackageName(string testSuiteName, string customName)
        {
            if (string.IsNullOrWhiteSpace(customName))
            {
                throw new ArgumentException("Custom package name cannot be empty");
            }

            // 检查是否与内建包名冲突
            var testSuite = _builtInTestSuiteService.GetByName(testSuiteName);
            if (testSuite != null)
            {
                var conflictPackage = testSuite.Packages.FirstOrDefault(p =>
                    string.Equals(p.Name, customName, StringComparison.OrdinalIgnoreCase));

                if (conflictPackage != null)
                {
                    throw new InvalidOperationException($"Custom package name '{customName}' conflicts with built-in package name");
                }
            }
        }
    }
}
