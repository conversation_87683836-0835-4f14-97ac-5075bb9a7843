using Alsi.App.Devices.Core;
using Alsi.Common.Log;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace Alsi.Fuzz.Core.Storage
{
    /// <summary>
    /// 提供CAN/CANFD日志的存储和检索功能，对接BlfLogReader和BlfLogWriter
    /// </summary>
    public class DataLogStorage : IDisposable
    {
        private readonly BlfLogWriter _writer;
        private string _currentLogPath;
        private bool _isDisposed;
        private HashSet<int> _rxFrameFilterIds = new HashSet<int>();

        /// <summary>
        /// 获取当前日志文件路径
        /// </summary>
        public string CurrentLogPath => _currentLogPath;

        /// <summary>
        /// 是否已初始化并准备好写入
        /// </summary>
        public bool IsInitialized => !string.IsNullOrEmpty(_currentLogPath) && BlfLogWriter.IsCreateFile;

        /// <summary>
        /// 创建日志存储实例
        /// </summary>
        public DataLogStorage()
        {
            _writer = new BlfLogWriter();
        }

        /// <summary>
        /// 初始化日志存储，准备写入日志
        /// </summary>
        public string Initialize(string logPath, HashSet<int> rxFrameFilterIds)
        {
            if (_isDisposed)
            {
                throw new ObjectDisposedException(nameof(DataLogStorage));
            }

            _rxFrameFilterIds = rxFrameFilterIds;

            _currentLogPath = logPath;

            // 初始化写入器
            _writer.Initialize(_currentLogPath);

            return _currentLogPath;
        }

        /// <summary>
        /// 写入单个CAN帧到日志
        /// </summary>
        /// <param name="frame">要写入的CAN帧</param>
        public void WriteFrame(CanFrame frame)
        {
            if (_isDisposed)
            {
                throw new ObjectDisposedException(nameof(DataLogStorage));
            }

            if (!IsInitialized)
            {
                throw new InvalidOperationException("LogStorage not initialized. Call Initialize() first.");
            }

            if (!frame.IsTx && _rxFrameFilterIds.Any() && !_rxFrameFilterIds.Contains(frame.Id))
            {
                // 设置了 RX 报文过滤器，且当前报文不在期待 RX 报文中
                return;
            }

            BlfLogWriter.WriteLog(frame);
        }

        /// <summary>
        /// 从BLF日志文件中读取所有CAN帧
        /// </summary>
        /// <param name="logFilePath">日志文件路径</param>
        /// <returns>读取的CAN帧数组</returns>
        public static CanFrame[] ReadFrames(string logFilePath)
        {
            if (!File.Exists(logFilePath))
            {
                throw new FileNotFoundException("Log file not found", logFilePath);
            }

            return BlfLogReader.ReadBlfFile(logFilePath);
        }

        /// <summary>
        /// 关闭当前日志文件
        /// </summary>
        public void Close()
        {
            if (!IsInitialized) return;

            _writer.Release();
            _currentLogPath = null;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed) return;

            try
            {
                Close();
            }
            catch (Exception)
            {
                // 忽略异常，确保不会阻止其他资源的释放
            }

            _isDisposed = true;
            GC.SuppressFinalize(this);
        }
    }
}
