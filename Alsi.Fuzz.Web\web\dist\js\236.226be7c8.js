"use strict";(self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[]).push([[236],{7236:function(e,l,t){t.r(l),t.d(l,{default:function(){return _}});var a=t(6768),o=t(144),s=t(4232),n=t(7477),c=t(1021),i=t(3711);const r={class:"about-container"},u={class:"about-content"},d={class:"feature-list"},f={key:0},p={class:"info-list"},k={class:"info-item"},b={class:"folder-path"},F={class:"info-item"},L={class:"folder-path"};var v={__name:"AboutView",setup(e){const l=(0,o.KR)(null),t=async()=>{try{const e=await c.GQ.getAppInfo();l.value=e.data}catch(e){console.error("Failed to fetch app info:",e)}},v=async e=>{try{await i.m.openExplorer(e)}catch(l){console.error("Failed to open folder:",l)}};return(0,a.sV)(t),(e,t)=>{const c=(0,a.g2)("el-icon"),i=(0,a.g2)("font-awesome-icon"),g=(0,a.g2)("el-button"),h=(0,a.g2)("el-card");return(0,a.uX)(),(0,a.CE)("div",r,[(0,a.bF)(h,{class:"about-card"},{default:(0,a.k6)((()=>[(0,a.Lk)("div",u,[t[11]||(t[11]=(0,a.Lk)("div",null,[(0,a.Lk)("h2",null,"Fuzz"),(0,a.Lk)("p",{class:"description"},"Fuzz is a professional fuzzing tool mainly used for automotive communication protocol testing.")],-1)),(0,a.Lk)("div",null,[t[7]||(t[7]=(0,a.Lk)("h2",null,"Features",-1)),(0,a.Lk)("ul",d,[(0,a.Lk)("li",null,[(0,a.bF)(c,{class:"feature-icon"},{default:(0,a.k6)((()=>[(0,a.bF)((0,o.R1)(n.Check))])),_:1}),t[2]||(t[2]=(0,a.eW)("Support for CAN/CANFD protocol testing"))]),(0,a.Lk)("li",null,[(0,a.bF)(c,{class:"feature-icon"},{default:(0,a.k6)((()=>[(0,a.bF)((0,o.R1)(n.Check))])),_:1}),t[3]||(t[3]=(0,a.eW)("Support for ISO 11898, ISO 14229 and ISO 15765 related testing"))]),(0,a.Lk)("li",null,[(0,a.bF)(c,{class:"feature-icon"},{default:(0,a.k6)((()=>[(0,a.bF)((0,o.R1)(n.Check))])),_:1}),t[4]||(t[4]=(0,a.eW)("Interoperation test to detect services supported by the device"))]),(0,a.Lk)("li",null,[(0,a.bF)(c,{class:"feature-icon"},{default:(0,a.k6)((()=>[(0,a.bF)((0,o.R1)(n.Check))])),_:1}),t[5]||(t[5]=(0,a.eW)("Automatic generation and execution of targeted test cases"))]),(0,a.Lk)("li",null,[(0,a.bF)(c,{class:"feature-icon"},{default:(0,a.k6)((()=>[(0,a.bF)((0,o.R1)(n.Check))])),_:1}),t[6]||(t[6]=(0,a.eW)("Local storage of test results and logs"))])])]),l.value?((0,a.uX)(),(0,a.CE)("div",f,[t[10]||(t[10]=(0,a.Lk)("h2",null,"Application Info",-1)),(0,a.Lk)("div",p,[(0,a.Lk)("div",k,[t[8]||(t[8]=(0,a.Lk)("span",{class:"label"},"Data Folder:",-1)),(0,a.Lk)("span",b,(0,s.v_)(l.value.dataFolder),1),(0,a.bF)(g,{type:"text",size:"small",onClick:t[0]||(t[0]=e=>v(l.value.dataFolder)),title:"Open in File Explorer",class:"folder-btn"},{default:(0,a.k6)((()=>[(0,a.bF)(i,{icon:"folder-open"})])),_:1})]),(0,a.Lk)("div",F,[t[9]||(t[9]=(0,a.Lk)("span",{class:"label"},"Log Folder:",-1)),(0,a.Lk)("span",L,(0,s.v_)(l.value.logFolder),1),(0,a.bF)(g,{type:"text",size:"small",onClick:t[1]||(t[1]=e=>v(l.value.logFolder)),title:"Open in File Explorer",class:"folder-btn"},{default:(0,a.k6)((()=>[(0,a.bF)(i,{icon:"folder-open"})])),_:1})])])])):(0,a.Q3)("",!0),t[12]||(t[12]=(0,a.Lk)("div",{class:"copyright-info"},[(0,a.Lk)("p",null,"Copyright © 2025, ALPS System Integration(Dalian) Co., Ltd."),(0,a.Lk)("p",null,"All rights reserved.")],-1))])])),_:1})])}}},g=t(1241);const h=(0,g.A)(v,[["__scopeId","data-v-6cb96e2c"]]);var _=h}}]);
//# sourceMappingURL=236.226be7c8.js.map