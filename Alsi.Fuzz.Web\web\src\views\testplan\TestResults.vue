<template>
  <div class="test-results-container">
    <!-- 主内容区域 - 单页布局 -->
    <div class="content-area">
      <!-- 测试结果列表页面 -->
      <div v-if="!showCaseDetail" class="test-results-page">
        <div class="page-header">
          <h3>Test Results</h3>
        </div>

        <!-- 测试结果表格 -->
        <div class="results-table-container">
          <div v-if="loadingTestResults" class="loading-container">
            <el-skeleton :rows="5" animated />
          </div>

          <div v-else-if="testResults.length === 0" class="empty-container">
            <el-empty description="No test results found" />
          </div>

          <table v-else class="results-table">
            <thead>
              <tr>
                <th class="column-name">Name</th>
                <th class="column-start-time">Start Time</th>
                <th class="column-duration">Duration</th>
                <th class="column-passed">Passed</th>
                <th class="column-failed">Failed</th>
                <th class="column-total">Total</th>
                <th class="column-actions">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, index) in testResults" :key="row.id" :class="{ 'row-stripe': index % 2 === 1 }"
                @click="handleRowClick(row)">
                <td class="column-name">
                  <el-tooltip :content="row.resultFolderName" placement="top" :show-after="500">
                    <span class="result-name text-ellipsis">{{ row.resultFolderName }}</span>
                  </el-tooltip>
                </td>
                <td class="column-start-time">
                  <el-tooltip :content="formatDateFull(row.creationTime)" placement="top" :show-after="500">
                    <span>{{ formatDateCompact(row.creationTime) }}</span>
                  </el-tooltip>
                </td>
                <td class="column-duration">
                  <span>{{ calculateDuration(row.creationTime, row.end) }}</span>
                </td>
                <td class="column-passed">
                  <span class="success-count">{{ row.successCount }}</span>
                </td>
                <td class="column-failed">
                  <span class="failure-count">{{ row.failureCount }}</span>
                </td>
                <td class="column-total">
                  <span class="total-count">{{ row.totalCount }}</span>
                </td>
                <td class="column-actions">
                  <div class="action-buttons">
                    <el-button type="primary" size="small" @click.stop="downloadHtmlReport(row.id)"
                      :title="'Download Report'">
                      <el-icon>
                        <Download />
                      </el-icon>
                    </el-button>

                    <el-button type="danger" size="small" @click.stop="confirmDelete(row)" :title="'Delete Result'">
                      <el-icon>
                        <Delete />
                      </el-icon>
                    </el-button>
                  </div>
                </td>
              </tr>
              <tr v-if="testResults.length === 0">
                <td colspan="7" class="empty-row">No data</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 测试用例详情页面 -->
      <div v-else class="test-cases-page">
        <div class="page-header">
          <el-button type="primary" size="small" @click="showCaseDetail = false" class="back-button">
            <el-icon>
              <Back />
            </el-icon>
            Back
          </el-button>
          <h3>{{ selectedTestName }}</h3>
        </div>

        <!-- 筛选工具栏 -->
        <div class="case-list-container" v-loading="loadingCases" element-loading-text="Loading test cases...">
          <div class="filters">
            <div class="filter-left">
              <label class="checkbox-label">
                <input type="checkbox" id="filter-all" v-model="filterAll" @change="handleFilterAllChange">
                <span class="filter-checkbox filter-all">All</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" id="filter-passed" v-model="filterPassed" @change="handleFilterChange">
                <span class="filter-checkbox filter-passed">Passed</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" id="filter-failed" v-model="filterFailed" @change="handleFilterChange">
                <span class="filter-checkbox filter-failed">Failed</span>
              </label>
            </div>
          </div>
          <!-- 用例列表 -->
          <CaseList :cases="filteredCaseResults" @view-detail="viewCaseDetail" />
        </div>
      </div>
    </div>

    <!-- 添加用例详情对话框组件 -->
    <CaseDetailDialog v-model:visible="detailDialogVisible" :testResultId="selectedTestId"
      :caseResultId="selectedCaseId" @close="closeDetailDialog" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { appApi, TestResult } from '@/api/appApi';
import { CaseResult } from '@/api/interoperationApi';
import CaseList from '@/components/test/CaseList.vue';
import CaseDetailDialog from '@/components/test/CaseDetailDialog.vue';
import { Delete, Download, Back } from '@element-plus/icons-vue';

// 状态变量
const testResults = ref<TestResult[]>([]);
const caseResults = ref<CaseResult[]>([]);
const selectedTestId = ref<string | null>(null);
const selectedCaseId = ref<number | null>(null);
const showCaseDetail = ref(false);

// 加载状态
const loadingTestResults = ref(true);
const loadingCases = ref(false);
const downloadingReport = ref(false);

// 用例详情对话框相关
const detailDialogVisible = ref(false);

// 筛选条件 - 修改默认值为全部不勾选
const filterAll = ref(false);
const filterPassed = ref(false);
const filterFailed = ref(false);

// 计算属性：获取选中的测试名称
const selectedTestName = computed(() => {
  if (!selectedTestId.value) return '';
  const selectedTest = testResults.value.find(test => test.id === selectedTestId.value);
  return selectedTest ? selectedTest.resultFolderName : '';
});

// 筛选后的测试用例结果 - 修改筛选逻辑
const filteredCaseResults = computed(() => {
  // 如果全部没选中，则显示所有测试用例
  if (!filterPassed.value && !filterFailed.value) {
    return caseResults.value;
  }

  return caseResults.value.filter(caseResult => {
    const isPassed = caseResult.state === "Success";
    const isFailed = caseResult.state === "Failure";

    return (isPassed && filterPassed.value) || (isFailed && filterFailed.value);
  });
});

// 获取测试结果列表
const fetchTestResults = async () => {
  loadingTestResults.value = true;
  try {
    const response = await appApi.getTestResults();
    testResults.value = response.data;
  } catch (error) {
    console.error('获取测试结果列表失败:', error);
    ElMessage.error('Failed to fetch test results');
  } finally {
    loadingTestResults.value = false;
  }
};

// 处理表格行点击
const handleRowClick = (row: TestResult) => {
  selectTestResult(row.id);
};

// 选择测试结果
const selectTestResult = async (testResultId: string) => {
  selectedTestId.value = testResultId;
  showCaseDetail.value = true;
  await fetchCases(testResultId);
};

// 获取测试用例列表
const fetchCases = async (testResultId: string) => {
  loadingCases.value = true;
  caseResults.value = []; // 清空之前的用例

  try {
    const response = await appApi.getCases(testResultId);
    caseResults.value = response.data;
  } catch (error) {
    console.error('获取测试用例列表失败:', error);
    ElMessage.error('Failed to fetch case results');
  } finally {
    loadingCases.value = false;
  }
};

// 确认删除
const confirmDelete = (result: TestResult) => {
  ElMessageBox.confirm(
    `Are you sure you want to delete test result "${result.resultFolderName}"?`,
    'Warning',
    {
      confirmButtonText: 'Delete',
      cancelButtonText: 'Cancel',
      type: 'warning',
    }
  )
    .then(() => {
      deleteTestResult(result.id);
    })
    .catch(() => {
      // 用户取消
    });
};

// 删除测试结果
const deleteTestResult = async (testResultId: string) => {
  try {
    await appApi.deleteTestResult(testResultId);
    ElMessage.success('Test result deleted successfully');

    // 刷新测试结果列表
    await fetchTestResults();

    // 如果删除的是当前选中的测试结果，返回到结果列表
    if (selectedTestId.value === testResultId) {
      selectedTestId.value = null;
      caseResults.value = [];
      showCaseDetail.value = false;
    }
  } catch (error) {
    console.error('删除测试结果失败:', error);
    ElMessage.error('Failed to delete test result');
  }
};

// 下载HTML报告
const downloadHtmlReport = async (testResultId: string) => {
  if (!testResultId) {
    ElMessage.warning('No test result selected for report generation');
    return;
  }

  downloadingReport.value = true;
  try {
    await appApi.downloadHtmlReport(testResultId);
    ElMessage.success('Report downloaded successfully');
  } catch (error) {
    console.error('Download report failed:', error);
    ElMessage.error('Failed to download test report');
  } finally {
    downloadingReport.value = false;
  }
};

// 查看用例详情
const viewCaseDetail = (caseResult: CaseResult) => {
  selectedCaseId.value = caseResult.id;
  detailDialogVisible.value = true;
};

// 关闭详情对话框
const closeDetailDialog = () => {
  detailDialogVisible.value = false;
  selectedCaseId.value = null;
};

// 格式化日期 - 完整格式（用于tooltip）
const formatDateFull = (dateString?: string | null) => {
  if (!dateString) return null;
  try {
    const date = new Date(dateString);
    return date.toLocaleString();
  } catch (e) {
    return dateString;
  }
};

// 格式化日期 - 紧凑格式（用于表格显示）
const formatDateCompact = (dateString?: string | null) => {
  if (!dateString) return null;
  try {
    const date = new Date(dateString);
    // 只显示时间部分，或者只显示日期和时间的小时和分钟
    const today = new Date();
    const isToday = date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();

    if (isToday) {
      // 如果是今天，只显示时间
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
    } else {
      // 否则显示日期和时间，但格式更紧凑
      return date.toLocaleDateString([], { month: '2-digit', day: '2-digit' }) +
        ' ' +
        date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  } catch (e) {
    return dateString;
  }
};

// 计算持续时间
const calculateDuration = (startTime?: string | null, endTime?: string | null) => {
  if (!startTime || !endTime) return '-';

  try {
    const start = new Date(startTime);
    const end = new Date(endTime);
    const durationMs = end.getTime() - start.getTime();

    if (durationMs < 0) return '-'; // 防止负值

    if (durationMs < 1000) {
      return `${durationMs}ms`;
    } else if (durationMs < 60000) {
      const seconds = Math.floor(durationMs / 1000);
      return `${seconds}s`;
    } else if (durationMs < 3600000) {
      const minutes = Math.floor(durationMs / 60000);
      const seconds = Math.floor((durationMs % 60000) / 1000);
      return `${minutes}m ${seconds}s`;
    } else {
      const hours = Math.floor(durationMs / 3600000);
      const minutes = Math.floor((durationMs % 3600000) / 60000);
      return `${hours}h ${minutes}m`;
    }
  } catch (e) {
    console.error('Error calculating duration:', e);
    return '-';
  }
};

// 处理"全部"复选框变更
const handleFilterAllChange = () => {
  filterPassed.value = filterAll.value;
  filterFailed.value = filterAll.value;
};

// 处理其他复选框变更 - 移除强制选中逻辑
const handleFilterChange = () => {
  // 如果全部选中，则"全部"复选框也应该选中
  filterAll.value = filterPassed.value && filterFailed.value;

  // 移除强制选中一项的逻辑，允许全部取消选中
};

// 组件挂载时获取测试结果列表
onMounted(() => {
  fetchTestResults();
});
</script>

<style scoped lang="scss">
.test-results-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 15px 20px;
}

/* 内容区域样式 */
.content-area {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: 0;
  /* 允许容器缩小到小于内容宽度 */

  .test-results-page,
  .test-cases-page {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .page-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    height: 30px;

    h2,
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }

    .back-button {
      margin-right: 16px;
      display: flex;
      align-items: center;

      .el-icon {
        margin-right: 4px;
      }
    }
  }

  .results-table-container {
    flex: 1;
    overflow: auto;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    box-shadow: none;
    width: 100%;
    min-width: 0;
    /* 允许容器缩小到小于内容宽度 */

    .loading-container,
    .empty-container {
      padding: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .result-name {
      font-weight: 500;
      color: var(--el-color-primary);
      font-size: 12px;
    }

    .text-ellipsis {
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
      width: 100%;
      /* 确保占满整个单元格 */
    }

    .success-count {
      color: var(--el-color-success);
      font-weight: 500;
      font-size: 12px;
    }

    .failure-count {
      color: var(--el-color-danger);
      font-weight: 500;
      font-size: 12px;
    }

    .total-count {
      color: var(--el-color-primary);
      font-weight: 500;
      font-size: 12px;
    }

    .action-buttons {
      display: flex;
      justify-content: center;
      gap: 4px;

      .el-button {
        padding: 5px 8px;
      }
    }
  }

  .case-list-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    overflow: hidden;
  }
}

/* 普通表格样式 */
.results-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
  table-layout: auto;

  /* 表头样式 */
  thead {
    tr {
      background-color: #f5f7fa;

      th {
        color: #606266;
        font-size: 12px;
        height: 40px;
        padding: 4px 6px;
        font-weight: bold;
        text-align: left;
        border-bottom: 1px solid #ebeef5;
      }
    }
  }

  /* 表格内容样式 */
  tbody {
    tr {
      cursor: pointer;
      height: 36px;
      border-bottom: 1px solid #ebeef5;

      &:hover {
        background-color: #f5f7fa;
      }

      &.row-stripe {
        background-color: #fafafa;

        &:hover {
          background-color: #f5f7fa;
        }
      }

      td {
        padding: 0 6px;
        text-align: left;
      }
    }
  }

  /* 列对齐方式 */
  .column-name {
    text-align: left;
    min-width: 80px;
  }

  .column-start-time {
    text-align: left;
    min-width: 80px;
  }

  .column-duration {
    text-align: center;
    min-width: 70px;
  }

  .column-passed,
  .column-failed,
  .column-total {
    text-align: center;
    min-width: 50px;
  }

  .column-actions {
    text-align: center;
    min-width: 60px;
  }

  /* 空数据行样式 */
  .empty-row {
    text-align: center;
    padding: 20px;
    color: #909399;
  }

  /* 优化表格在窄屏幕上的显示 */
  @media (max-width: 768px) {
    font-size: 11px;

    thead th,
    tbody td {
      padding: 0 4px;
    }
  }
}

/* 筛选工具栏样式 */
.filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px 15px;
}

.filter-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  font-size: 14px;
  color: #606266;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 5px;
}

.filter-checkbox {
  min-width: 30px;
}

.filter-all {
  color: var(--el-color-primary);
}

.filter-passed {
  color: var(--el-color-success);
}

.filter-failed {
  color: var(--el-color-danger);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-area {
    padding: 8px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .back-button {
        margin-right: 0;
      }
    }
  }
}
</style>
