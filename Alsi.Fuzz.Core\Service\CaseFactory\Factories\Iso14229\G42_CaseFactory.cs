using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts;
using Alsi.Fuzz.Core.Service.CaseFactory.Iso14229;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G42_CaseFactory : CaseFactoryBase
    {
        public override void Generate(MutationOptions options, Action<CreateCaseInfo> createCase)
        {
            var xmlServices = options.XmlServices;
            var supportedXmlServicesWithSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == true)
                .ToArray();

            foreach (var xmlServiceWithSubfunction in supportedXmlServicesWithSubfunction)
            {
                var sid = xmlServiceWithSubfunction.Id;
                var subfunctionId = xmlServiceWithSubfunction.SubfunctionId.Value;
                var parameter2k = xmlServiceWithSubfunction.Parameter2k;
                var serviceName = xmlServiceWithSubfunction.IsoUdsServiceDisplayName;
                var groupPath = Iso14229CaseGroupConsts.UdsServiceId.MessageSequence.RepeatFrameMessage().Path;

                var payload = new List<byte> { sid, subfunctionId };
                payload.AddRange(parameter2k);

                foreach (var repeat in new[] { 2, 4, 8, 16, 64 })
                {
                    var name = $"xmlServiceWithSubfunction {serviceName} repeat{repeat}-{groupPath}";
                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .Mutate(MutationFieldType.UDS_Repeat_Frame, repeat.ToString())
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G421-Sid{sid:X2}", caseMutation, sid, subfunctionId));
                }
            }
        }
    }
}
