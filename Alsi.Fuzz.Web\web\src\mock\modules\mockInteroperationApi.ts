import { AxiosResponse } from 'axios';
import { mockSuccess } from '../mockApi';
import { BaseResponse } from '@/api/interoperationApi';
import { TesterSnapshot, ExecutionState } from '@/api/appApi';

// 默认测试状态
const defaultTesterSnapshot: TesterSnapshot = {
  processState: ExecutionState.Pending,
  currentOperation: '',
  testResult: {
    id: crypto.randomUUID?.() || '1',
    resultFolderName: 'Mock Test',
    testType: 'Interoperation',
    creationTime: new Date().toISOString(),
    totalCount: 20,
    successCount: 0,
    failureCount: 0
  },
  caseResults: []
};

// 测试状态 - 随机生成的测试数据
let mockStatus: TesterSnapshot = { ...defaultTesterSnapshot };

// 用于模拟进度更新的定时器
let progressTimer: number | null = null;

// 简化的测试用例生成函数
function generateTestCases() {
  const sequenceNames = [
    'DiagnosticSessionControl',
    'ECUReset',
    'SecurityAccess',
    'CommunicationControl',
    'ReadDataByIdentifier',
    'can-frames'
  ];

  return sequenceNames.map((name, index) => ({
    id: index + 1,
    testResultId: mockStatus.testResult.id,
    sequenceId: `seq-${index}`,
    sequenceName: name,
    parameter: `param-${index}`,
    name: `Test ${index + 1}`,
    state: Math.random() > 0.7 ? ExecutionState.Success :
      (Math.random() > 0.5 ? ExecutionState.Failure : ExecutionState.Pending),
    begin: new Date(Date.now() - 60000).toISOString(),
    end: new Date().toISOString(),
    detail: 'Test details',
    groupPath: ''
  }));
}

// 停止进度模拟
function stopProgressSimulation() {
  if (progressTimer) {
    clearInterval(progressTimer);
    progressTimer = null;
  }
}

export const mockInteroperationApi = {
  // 启动互操作测试
  startTest: (): Promise<AxiosResponse<BaseResponse>> => {
    stopProgressSimulation();

    // 重置状态为运行中
    mockStatus = {
      processState: ExecutionState.Running,
      currentOperation: '初始化测试环境',
      testResult: {
        id: crypto.randomUUID?.() || '1',
        resultFolderName: 'Mock Interoperation Test',
        testType: 'Interoperation',
        creationTime: new Date().toISOString(),
        totalCount: 20,
        successCount: 0,
        failureCount: 0
      },
      caseResults: []
    };

    // 简单模拟定时更新
    progressTimer = window.setInterval(() => {
      if (mockStatus.processState !== ExecutionState.Running) return;

      mockStatus.testResult.successCount = Math.min(mockStatus.testResult.totalCount,
        mockStatus.testResult.successCount + 1);
      mockStatus.testResult.failureCount = Math.min(mockStatus.testResult.totalCount - mockStatus.testResult.successCount,
        mockStatus.testResult.failureCount + (Math.random() > 0.8 ? 1 : 0));

      mockStatus.caseResults = generateTestCases();
      mockStatus.currentOperation = `执行测试: ${mockStatus.caseResults[Math.floor(Math.random() * mockStatus.caseResults.length)].sequenceName}`;

      // 模拟测试完成
      if (mockStatus.testResult.successCount + mockStatus.testResult.failureCount >= mockStatus.testResult.totalCount) {
        mockStatus.processState = ExecutionState.Success;
        mockStatus.currentOperation = '测试已完成';
        stopProgressSimulation();
      }
    }, 1000);

    return mockSuccess({ success: true, message: '测试已启动' });
  },

  // 停止互操作测试
  stopTest: (): Promise<AxiosResponse<BaseResponse>> => {
    if (mockStatus.processState === ExecutionState.Running) {
      mockStatus.processState = ExecutionState.Failure;
      mockStatus.currentOperation = '测试已停止';
      stopProgressSimulation();
    }

    return mockSuccess({ success: true, message: '测试已停止' });
  },

  // 获取测试状态
  getStatus: (): Promise<AxiosResponse<TesterSnapshot>> => {
    return mockSuccess({ ...mockStatus });
  }
};
