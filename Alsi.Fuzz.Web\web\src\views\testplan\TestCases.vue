<template>
  <div class="test-cases-container" v-loading="loading" :element-loading-text="loadingText">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <!-- 展开/收起全部按钮 -->
        <el-button @click="expandAll" type="primary" size="small" class="expand-button" :disabled="!hasTreeData">
          <font-awesome-icon icon="up-right-and-down-left-from-center" /><span class="button-text">Expand All</span>
        </el-button>
        <el-button @click="collapseAll" type="primary" size="small" class="collapse-button" :disabled="!hasTreeData">
          <font-awesome-icon icon="down-left-and-up-right-to-center" /><span class="button-text">Collapse All</span>
        </el-button>
      </div>
      <div class="action-buttons">
        <el-button type="success" @click="openGenerateDialog" size="small">
          Generate Cases
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 - 显示 Case Groups 和用例列表 -->
    <div class="content-area">
      <!-- 左侧：分组树 -->
      <div class="dialog-left" v-if="!loading">
        <!-- 分组树上方的总览信息 -->
        <div class="group-info">
          <span>Total {{ allCaseCount }} cases</span>
        </div>
        <div class="group-border">
          <div class="tree-header">
            <h4>Case Groups</h4>
          </div>
          <el-tree ref="treeRef" :data="currentTreeData" :props="treeProps" @node-click="handleNodeClick" :key="treeKey"
            class="group-tree" :expand-on-click-node="false" highlight-current node-key="id"
            :default-expand-all="defaultExpandAll">
            <template #default="{ data }">
              <div class="tree-node">
                <div class="case-name">{{ data.name }}</div>
                <div class="case-count">{{ data.count }} cases</div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 右侧：用例列表 -->
      <div class="dialog-right" v-if="!loading">
        <GeneratedCasesPanel :cases="displayedCases" :generating="false" :saving="false" :save-progress="null"
          :show-empty-message="showEmptyMessage" />
      </div>
    </div>

    <!-- 底部工具栏 -->
    <div class="toolbar bottom-toolbar">
      <el-button type="primary" @click="handleSave" :disabled="!canSave">Save</el-button>
    </div>

    <!-- 生成用例对话框 -->
    <GenerateCasesDialog v-model:visible="showGenerateDialog" :coverage="selectedCoverage"
      :selected-sequences="selectedSequences" :baseline-average-time="baselineAverageTime"
      @generated="onCasesGenerated" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { appApi, CoverageType } from '@/api/appApi';
import { CaseResult, GroupTreeNode } from '@/api/interoperationApi';
import GenerateCasesDialog from '@/components/TestCases/GenerateCasesDialog.vue';
import GeneratedCasesPanel from '@/components/TestCases/GeneratedCasesPanel.vue';

// 状态变量
const loading = ref(true);
const loadingText = ref('');
const selectedCoverage = ref(CoverageType.Normal);
const selectedSequences = ref<string[]>([]);
const baselineAverageTime = ref<number>(0); // 生成时的基准平均时间
const showGenerateDialog = ref(false); // 控制生成用例对话框显示

// 测试用例相关状态
const allCases = ref<CaseResult[]>([]);
const displayedCases = ref<CaseResult[]>([]);
const groupTree = ref<GroupTreeNode | null>(null);

// 树形组件引用和状态
const treeRef = ref();
const defaultExpandAll = ref(true); // 控制树的默认展开状态
const currentTreeData = ref<GroupTreeNode[]>([]); // 当前显示的树数据
const treeKey = ref(new Date())

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'name'
};

// 计算属性
const groupTreeData = computed(() => {
  return groupTree.value ? [groupTree.value] : [];
});

const allCaseCount = computed(() => {
  return allCases.value.length;
});

const showEmptyMessage = computed(() => {
  return allCases.value.length === 0;
});

const canSave = computed(() => {
  return allCases.value.length > 0;
});

const hasTreeData = computed(() => {
  return currentTreeData.value.length > 0 && !loading.value;
});

// 获取已保存的测试用例
const fetchSavedCases = async () => {
  loadingText.value = "Loading test cases...";
  loading.value = true;
  try {
    const response = await appApi.getCasesWithGroup();
    allCases.value = response.data.cases;
    groupTree.value = response.data.groupTree;

    // 初始化当前树数据
    currentTreeData.value = groupTreeData.value;

    // 默认点击根节点显示所有用例
    if (groupTree.value) {
      handleNodeClick(groupTree.value);
    }
  } catch (error) {
    console.error('获取测试用例失败:', error);
    ElMessage.error('Failed to fetch test cases');
  } finally {
    loading.value = false;
  }
};

const expandAll = () => {
  defaultExpandAll.value = true;
  treeKey.value = new Date();
};

const collapseAll = () => {
  defaultExpandAll.value = false;
  treeKey.value = new Date();
};

// 节点点击时筛选用例
const handleNodeClick = (node: any) => {
  const path = node.id;
  console.log("getNodePath node", node, "path", path);
  const isRootNode = !path.includes('/');
  displayedCases.value = allCases.value.filter(c => isRootNode || (c.groupPath && c.groupPath.includes(path)));
  console.log("allCases.value", allCases.value);
};

// 打开生成用例对话框
const openGenerateDialog = () => {
  // 打开对话框
  showGenerateDialog.value = true;
};

// 用例生成成功的回调
const onCasesGenerated = (newCases: CaseResult[], newGroupTree: GroupTreeNode) => {
  // 更新主页面的数据
  allCases.value = newCases;
  groupTree.value = newGroupTree;

  // 更新当前树数据
  currentTreeData.value = groupTreeData.value;

  // 默认点击根节点显示所有用例
  if (groupTree.value) {
    handleNodeClick(groupTree.value);
  }
};

// 保存测试用例
const handleSave = async () => {
  try {
    loadingText.value = "Saving test cases...";
    loading.value = true;
    await appApi.saveCases(allCases.value);
    ElMessage.success(`${allCases.value.length} test cases saved successfully`);
  } catch (error) {
    console.error('保存测试用例失败:', error);
    ElMessage.error('Failed to save test cases');
  } finally {
    loading.value = false;
  }
};

// 组件挂载时获取已保存的测试用例
onMounted(() => {
  fetchSavedCases();
});
</script>

<style scoped lang="scss">
.test-cases-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 15px 20px;
}

/* 顶部工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  .toolbar-left {
    display: flex;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
  }
}

/* 底部工具栏样式 */
.bottom-toolbar {
  margin-top: 15px;
  margin-bottom: 0;
  justify-content: flex-start;
}

/* 主要内容区域样式 */
.content-area {
  display: flex;
  flex: 1;
  min-height: 0;
  gap: 15px;
}

/* 左侧分组树样式 */
.dialog-left {
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fff;
  flex: 0.5;
  min-height: 0;
  min-width: 0;
}

.group-info {
  padding: 10px 15px;
  border-bottom: 1px solid #e4e7ed;
  font-size: 12px;
  color: #909399;
}

.group-border {
  flex: 1;
  display: flex;
  flex-direction: column;
  flex-basis: 0;
  min-height: 0;
}

.tree-header {
  padding: 10px 15px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;

  h4 {
    margin: 0;
    font-size: 14px;
    color: #303133;
  }
}

.group-tree {
  flex: 1;
  padding: 10px;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .case-name {
    flex: 1;
    font-size: 13px;
  }

  .case-count {
    font-size: 12px;
    color: #909399;
    margin-left: 10px;
  }
}

/* 右侧用例列表样式 */
.dialog-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fff;
}

.button-text {
  margin-left: 8px;
}
</style>
