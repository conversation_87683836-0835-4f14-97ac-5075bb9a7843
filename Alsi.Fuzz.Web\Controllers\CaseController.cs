using Alsi.App;
using Alsi.App.Desktop.Utils;
using Alsi.App.Devices.Core;
using Alsi.Common.Utils;
using Alsi.Fuzz.Core.Contracts.Tester;
using Alsi.Fuzz.Core.Models.Tester;
using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Models.TestSuites;
using Alsi.Fuzz.Core.Models.TestSuites.Steps.Diagnostic;
using Alsi.Fuzz.Core.Models.TestSuites.Steps.Isotp;
using Alsi.Fuzz.Core.Service;
using Alsi.Fuzz.Core.Service.CaseFactory;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts;
using Alsi.Fuzz.Core.Service.CaseFactory.Iso11898;
using Alsi.Fuzz.Core.Service.CaseFactory.Iso14229;
using Alsi.Fuzz.Core.Service.Interoperation;
using Alsi.Fuzz.Core.Service.Results;
using Alsi.Fuzz.Core.Service.Tester;
using Alsi.Fuzz.Core.Storage;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web.Http;

namespace Alsi.Fuzz.Web.Controllers
{
    public class CaseController : WebControllerBase
    {
        private static StatusPollingService StatusPollingService { get; set; } = new StatusPollingService();

        private InteroperationService Interoperation => TestPlanManager.Instance.Interoperation;

        [HttpGet]
        [ActionName("latest-interoperation-case-results")]
        public IHttpActionResult GetLatestInteroperationCaseResults()
        {
            var caseResults = Interoperation.StatusPollingService?.TesterSnapshot?.CaseResults ?? Array.Empty<CaseResult>();
            return Ok(caseResults);
        }

        public class GenerateCasesRequest
        {
            public CoverageType coverage { get; set; }
            public string[] SequenceNames { get; set; }
        }
        //TODO: 追加展开的请求，追加展开的叶子结点ID
        public class ExpandCasesRequest
        {
            public CoverageType coverage { get; set; }
            public string[] SequenceNames { get; set; }

            public string[] LeafNodeIds { get; set; }
        }

        [HttpDelete]
        [ActionName("test-result")]
        public IHttpActionResult DeleteTestResults(Guid testResultId)
        {
            var resultReader = new TestResultReaderService();
            resultReader.DeleteTestResult(testResultId);
            return Ok();
        }

        [HttpGet]
        [ActionName("test-results")]
        public IHttpActionResult GetTestResults()
        {
            var resultReader = new TestResultReaderService();
            var testResults = resultReader
                .GetTestResults()
                .Where(x => x.TestType == TestType.Case)
                .OrderByDescending(x => x.CreationTime)
                .ToArray();
            return Ok(testResults);
        }

        [HttpGet]
        [ActionName("case-steps")]
        public IHttpActionResult GetCaseSteps(Guid testResultId, int caseResultId)
        {
            // 先尝试从互操作结果中，获取用例步骤
            var snapshot = Interoperation.StatusPollingService.TesterSnapshot;
            var caseResult = snapshot?.CaseResults?.FirstOrDefault(x => x.TestResultId == testResultId && x.Id == caseResultId);
            if (caseResult != null)
            {
                var interoperationCaseSteps = snapshot?.CaseSteps.Where(x => x.CaseResultId == caseResultId).ToArray();
                if (interoperationCaseSteps != null)
                {
                    return Ok(interoperationCaseSteps);
                }
            }

            caseResult = InternalGetCaseResult(testResultId, caseResultId);
            var resultReader = new TestResultReaderService();
            var caseSteps = resultReader.GetCaseSteps(caseResult);
            return Ok(caseSteps);
        }

        [HttpGet]
        [ActionName("case-result")]
        public IHttpActionResult GetCaseResult(Guid testResultId, int caseResultId)
        {
            var caseResult = InternalGetCaseResult(testResultId, caseResultId);
            return Ok(caseResult);
        }

        private CaseResult InternalGetCaseResult(Guid testResultId, int caseResultId)
        {
            // 先尝试从互操作结果中，获取用例结果
            var snapshot = Interoperation.StatusPollingService.TesterSnapshot;
            var caseResult = snapshot?.CaseResults?.FirstOrDefault(x => x.TestResultId == testResultId && x.Id == caseResultId);
            if (caseResult != null)
            {
                return caseResult;
            }

            // 再尝试从互操作用例中，获取用例结果
            var interoperationService = TestPlanManager.Instance.Interoperation;
            caseResult = interoperationService.GetCaseResult(testResultId, caseResultId);
            if (caseResult != null)
            {
                return caseResult;
            }

            // 最后从数据库中读取
            var resultReader = new TestResultReaderService();
            var testResult = resultReader
                .GetTestResults()
                .Where(x => x.Id == testResultId)
                .OrderByDescending(x => x.CreationTime)
                .FirstOrDefault();
            if (testResult == null)
            {
                throw new Exception($"Can't find test result by ID: {testResultId}");
            }

            caseResult = resultReader
                .GetCaseResultById(testResult, caseResultId);
            if (caseResult == null)
            {
                throw new Exception($"Can't find case result by ID: testResultId={testResultId} caseResultId={caseResultId}");
            }
            return caseResult;
        }

        [HttpGet]
        [ActionName("case-count")]
        public IHttpActionResult GetCasesCount(Guid? testResultId = null)
        {
            if (!testResultId.HasValue)
            {
                var caseCount = TestPlanManager.Instance.LoadCaseCollection().CaseEntries.Length;
                return Ok(caseCount);
            }

            var testResult = GetTestResult(testResultId);
            if (testResult == null)
            {
                return Ok(0);
            }
            return Ok(testResult.TotalCount);
        }

        [HttpGet]
        [ActionName("cases")]
        public IHttpActionResult GetCases(Guid? testResultId = null)
        {
            var testResult = GetTestResult(testResultId);
            if (testResult == null)
            {
                return Ok(Array.Empty<CaseResult>());
            }

            ITestResultWriterService resultWriter = new TestResultWriterService();
            try
            {
                resultWriter.Begin(testResult);
                var caseResults = resultWriter.GetCaseResults();
                return Ok(caseResults);
            }
            finally
            {
                resultWriter.End();
            }
        }

        private TestResult GetTestResult(Guid? testResultId = null)
        {
            var resultReader = new TestResultReaderService();
            var testPlanName = Path.GetFileNameWithoutExtension(TestPlanManager.Instance.GetCurrentPlanPath());
            TestResult testResult = null;
            if (testResultId.HasValue)
            {
                testResult = resultReader
                    .GetTestResults()
                    .Where(x => x.TestType == TestType.Case && x.Id == testResultId)
                    .OrderByDescending(x => x.CreationTime)
                    .FirstOrDefault();
            }
            else
            {
                testResult = resultReader
                    .GetTestResults()
                    .Where(x => x.TestType == TestType.Case)
                    .OrderByDescending(x => x.CreationTime)
                    .FirstOrDefault();
            }
            return testResult;
        }



        [HttpPost]
        [ActionName("save-cases")]
        public IHttpActionResult SaveCases([FromBody] SaveCasesRequest request)
        {
            if (_generatedCaseCollection == null)
            {
                throw new Exception("Please generate test cases");
            }

            if (_generatedCaseCollection.GenerationTime != request.GenerationTime)
            {
                throw new Exception("The test cases is expired, please regenerate test cases");
            }

            TestPlanManager.Instance.SaveCaseCollection(_generatedCaseCollection);

            return Ok(new { message = $"Successfully saved {_generatedCaseCollection.CaseEntries.Length} test cases" });
        }

        public class SaveCasesRequest
        {
            public DateTime GenerationTime { get; set; }
        }

        [HttpPost]
        [ActionName("generate-cases")]
        public IHttpActionResult GenerateCases([FromBody] GenerateCasesRequest request)
        {
            // 生成全量case
            var cases = InternalGenerateCases(request);
            return Ok(cases);

            //TODO: 生成树状结构 GroupTree 并返回前台
            //TODO: 是否考虑合并生成和save,因为每次展开树结构时不可能重新生成，需要从数据库查询。或者存在临时结构中用于展示，save时再存数据库
            //var GroupTrees = InternalGenerateGroupTrees(request);
            //return Ok(GroupTrees);
        }

        private CaseResult[] InternalGenerateCases(GenerateCasesRequest request)
        {
            var testPlan = TestPlanManager.Instance.GetCurrentPlan();
            var caseResults = Interoperation.StatusPollingService?.TesterSnapshot?.CaseResults ?? Array.Empty<CaseResult>();

            var caseConfig = testPlan.Config.CaseConfig;

            var hardwareConfig = testPlan.Config.HardwareConfig;
            var isCanFd = hardwareConfig.CommunicationType == CommunicationType.CanFd;
            var isCan = hardwareConfig.CommunicationType == CommunicationType.Can;

            var testPlanSequenceService = new TestPlanSequenceService(new TestPlanStorage());
            if (!testPlanSequenceService.TryGetSelectedSequenceXml(testPlan, out var isoType, out var sequencePackageXml))
            {
                throw new AppException("Please select a test sequence, failed to load selected sequence XML.");
            }

            var xmlServices = new List<XmlService>();
            if (isoType == IsoType.Iso14229)
            {
                var package = SequencePackageUtils.LoadFromString(sequencePackageXml);
                var envVars = package.SetVars.Select(x => new EnvVar(x.Name, x.Value)).ToList();

                foreach (var sequence in package.Sequences)
                {
                    var isSupported = false;
                    var caseResult = caseResults.FirstOrDefault(x => x.SequenceName == sequence.Name);
                    if (caseResult?.State == ExecutionState.Success)
                    {
                        isSupported = true;
                    }

                    var hasMultipleFrameRequest = false;
                    if (!string.IsNullOrWhiteSpace(caseResult?.ResultProps))
                    {
                        try
                        {
                            var resultProps = JsonUtils.Deserialize<ResultProps>(caseResult.ResultProps);
                            hasMultipleFrameRequest = resultProps.HasMultipleFrameRequest;
                        }
                        catch (Exception e)
                        {
                            AppEnv.Logger.Error(e, $"Failed to deserialize: {caseResult.ResultProps}");
                        }
                    }


                    var xmlService = new XmlService
                    {
                        IsSupported = isSupported,
                        HasMultipleFrameRequest = hasMultipleFrameRequest,
                        SequenceName = sequence.Name,
                    };

                    xmlServices.Add(xmlService);
                    var match = Regex.Match(sequence.Name, @".*\((0x[\d|a-f|A-F]+)\).*");
                    if (match.Success)
                    {
                        var sid = SequenceUtils.ParseByte(match.Groups[1].Value);
                        xmlService.Id = sid;
                        xmlService.IsoUdsServiceDisplayName = IsoUdsConsts.GetDisplayServiceName(sid);
                    }

                    var isoService = IsoUdsConsts.Services.FirstOrDefault(x => x.Id == xmlService.Id);
                    var hasSubfunction = isoService != null && isoService.Subfunctions.Any();

                    foreach (var step in sequence.Steps)
                    {
                        if (step is SendDiagStep sendDiagStep && sendDiagStep.Mutate)
                        {
                            var payload = sendDiagStep.HexPayload?.Trim();
                            if (!string.IsNullOrWhiteSpace(payload))
                            {
                                envVars.Eval(ref payload);
                                var payloadBytes = SequenceUtils.ParseBytes(payload);
                                if (payloadBytes[0] == xmlService.Id && payloadBytes.Length > 1)
                                {
                                    if (hasSubfunction)
                                    {
                                        var subfunctionId = payloadBytes[1];
                                        xmlService.SubfunctionId = subfunctionId;
                                        xmlService.Parameter2k = payloadBytes.Skip(2).ToArray();
                                    }
                                    else
                                    {
                                        xmlService.SubfunctionId = null;
                                        xmlService.Parameter2k = payloadBytes.Skip(1).ToArray();
                                    }
                                }
                            }
                        }
                    }
                }
            }

            var tpParameters = Array.Empty<byte>();
            var tpParametersBigDataPayload = Array.Empty<byte>();
            var tpParametersBigDataRepeat = 1;

            if (isoType == IsoType.Iso15765)
            {
                var package = SequencePackageUtils.LoadFromString(sequencePackageXml);
                var envVars = package.SetVars.Select(x => new EnvVar(x.Name, x.Value)).ToList();

                foreach (var sequence in package.Sequences)
                {
                    var isBigData = sequence.Name.Contains("big-data");

                    foreach (var step in sequence.Steps)
                    {
                        if (step is SendIsotpStep sendIsotpStep)
                        {
                            var payload = sendIsotpStep.HexPayload?.Trim();
                            if (!string.IsNullOrWhiteSpace(payload))
                            {
                                envVars.Eval(ref payload);
                                var payloadBytes = SequenceUtils.ParseBytes(payload);
                                var repeat = 1;
                                if (sendIsotpStep.RepeatPayload.HasValue && sendIsotpStep.RepeatPayload.Value > 0)
                                {
                                    repeat = sendIsotpStep.RepeatPayload.Value;
                                }

                                if (isBigData)
                                {
                                    tpParametersBigDataPayload = payloadBytes;
                                    tpParametersBigDataRepeat = repeat;
                                }
                                else
                                {
                                    var repeatPayloadBytes = new List<byte>();
                                    for (var i = 0; i < repeat; i++)
                                    {
                                        repeatPayloadBytes.AddRange(payloadBytes);
                                    }

                                    tpParameters = repeatPayloadBytes.ToArray();
                                }
                            }
                        }
                    }
                }
            }

            var whiteListFrames = caseConfig.WhiteListFrames
                .Where(x => x.Transmitter == caseConfig.SelectedNodeName || x.Receivers.Contains(caseConfig.SelectedNodeName))
                .ToArray();

            var options = new MutationOptions
            {
                IsoType = isoType,
                Coverage = request.coverage,

                InteroperationResults = caseResults,
                SelectedSequenceNames = request.SequenceNames,

                CommunicationType = hardwareConfig.CommunicationType,

                WhiteListFrames = whiteListFrames,
                SelectedNodeName = caseConfig.SelectedNodeName,

                XmlServices = xmlServices.Where(x => request.SequenceNames.Contains(x.SequenceName)).OrderBy(x => x.Id).ToArray(),

                TpParameters = tpParameters,
                TpParametersBigDataPayload = tpParametersBigDataPayload,
                TpParametersBigDataRepeat = tpParametersBigDataRepeat,

                IsDutMtuLessThan4096 = caseConfig.IsDutMtuLessThan4096
            };

            if (isoType == IsoType.Iso11898)
            {
                return Iso11898_CaseFactoryUtils.Generate(options);
            }
            else if (isoType == IsoType.Iso14229)
            {
                return Iso14229_CaseFactoryUtils.Generate(options);
            }

            return Iso15765_CaseFactoryUtils.Generate(options);
        }

        [HttpGet]
        [ActionName("generating-progress")]
        public IHttpActionResult GetGeneratingProgress()
        {
            var message = GenerationProgressService.GetProgressText();
            return Ok(message);
        }

        [HttpGet]
        [ActionName("cases-with-group")]
        public IHttpActionResult GetCasesWithGroup()
        {
            var caseCollection = TestPlanManager.Instance.LoadCaseCollection();
            var cases = caseCollection.CaseEntries.Select(x => x.ToCaseResult(Guid.Empty)).ToArray();

            var groupTree = GroupTreeNode.Create(caseCollection.CaseGroup);

            return Ok(new
            {
                Cases = cases,
                GroupTree = groupTree
            });
        }

        private static CaseCollection _generatedCaseCollection;

        [HttpPost]
        [ActionName("generate-cases-with-group")]
        public IHttpActionResult GenerateCasesWithGroup([FromBody] GenerateCasesRequest request)
        {
            GenerationProgressService.Reset();

            // 生成全量case和group tree
            var (cases, groupTree) = InternalGenerateCasesWithGroup(request);

            var leafNodeCaseCount = 0;
            groupTree.CalculateCaseCount(cases, ref leafNodeCaseCount);

            _generatedCaseCollection = new CaseCollection
            {
                CaseEntries = cases.Select(CaseEntry.Create).ToArray(),
                CaseGroup = groupTree.ToCaseGroup(),
                GenerationTime = DateTime.Now
            };

            // 可以将两个结果一起返回给前端
            return Ok(new
            {
                Cases = cases,
                GroupTree = groupTree
            });

            //TODO: 生成树状结构 GroupTree 并返回前台
            //TODO: 是否考虑合并生成和save,因为每次展开树结构时不可能重新生成，需要从数据库查询。或者存在临时结构中用于展示，save时再存数据库
            //var GroupTrees = InternalGenerateGroupTrees(request);
            //return Ok(GroupTrees);
        }

        private (CaseResult[], GroupTreeNode groupTree) InternalGenerateCasesWithGroup(GenerateCasesRequest request)
        {
            var testPlan = TestPlanManager.Instance.GetCurrentPlan();
            var caseResults = Interoperation.StatusPollingService?.TesterSnapshot?.CaseResults ?? Array.Empty<CaseResult>();

            var caseConfig = testPlan.Config.CaseConfig;

            var hardwareConfig = testPlan.Config.HardwareConfig;
            var isCanFd = hardwareConfig.CommunicationType == CommunicationType.CanFd;
            var isCan = hardwareConfig.CommunicationType == CommunicationType.Can;

            var testPlanSequenceService = new TestPlanSequenceService(new TestPlanStorage());
            if (!testPlanSequenceService.TryGetSelectedSequenceXml(testPlan, out var isoType, out var sequencePackageXml))
            {
                throw new AppException("Please select a test sequence, failed to load selected sequence XML.");
            }

            var xmlServices = new List<XmlService>();

            if (isoType == IsoType.Iso14229)
            {
                var package = SequencePackageUtils.LoadFromString(sequencePackageXml);
                var envVars = package.SetVars.Select(x => new EnvVar(x.Name, x.Value)).ToList();

                foreach (var sequence in package.Sequences)
                {
                    var isSupported = false;
                    var caseResult = caseResults.FirstOrDefault(x => x.SequenceName == sequence.Name);
                    if (caseResult?.State == ExecutionState.Success)
                    {
                        isSupported = true;
                    }

                    var hasMultipleFrameRequest = false;
                    if (!string.IsNullOrWhiteSpace(caseResult?.ResultProps))
                    {
                        try
                        {
                            var resultProps = JsonUtils.Deserialize<ResultProps>(caseResult.ResultProps);
                            hasMultipleFrameRequest = resultProps.HasMultipleFrameRequest;
                        }
                        catch (Exception e)
                        {
                            AppEnv.Logger.Error(e, $"Failed to deserialize: {caseResult.ResultProps}");
                        }
                    }

                    var xmlService = new XmlService
                    {
                        IsSupported = isSupported,
                        HasMultipleFrameRequest = hasMultipleFrameRequest,
                        SequenceName = sequence.Name,
                    };

                    xmlServices.Add(xmlService);
                    var match = Regex.Match(sequence.Name, @".*\((0x[\d|a-f|A-F]+)\).*");
                    if (match.Success)
                    {
                        var sid = SequenceUtils.ParseByte(match.Groups[1].Value);
                        xmlService.Id = sid;
                        xmlService.IsoUdsServiceDisplayName = IsoUdsConsts.GetDisplayServiceName(sid);
                    }

                    var isoService = IsoUdsConsts.Services.FirstOrDefault(x => x.Id == xmlService.Id);
                    var hasSubfunction = isoService != null && isoService.Subfunctions.Any();

                    foreach (var step in sequence.Steps)
                    {
                        if (step is SendDiagStep sendDiagStep && sendDiagStep.Mutate)
                        {
                            var payload = sendDiagStep.HexPayload?.Trim();
                            if (!string.IsNullOrWhiteSpace(payload))
                            {
                                envVars.Eval(ref payload);
                                var payloadBytes = SequenceUtils.ParseBytes(payload);
                                if (payloadBytes[0] == xmlService.Id && payloadBytes.Length > 1)
                                {
                                    if (hasSubfunction)
                                    {
                                        var subfunctionId = payloadBytes[1];
                                        xmlService.SubfunctionId = subfunctionId;
                                        xmlService.Parameter2k = payloadBytes.Skip(2).ToArray();
                                        xmlService.IsoUdsSubfunctionDisplayName = IsoUdsConsts.GetDisplaySubfunctionName(subfunctionId);
                                    }
                                    else
                                    {
                                        xmlService.SubfunctionId = null;
                                        xmlService.Parameter2k = payloadBytes.Skip(1).ToArray();
                                    }
                                }
                            }
                        }
                    }
                }
            }

            var tpParameters = Array.Empty<byte>();
            var tpParametersBigDataPayload = Array.Empty<byte>();
            var tpParametersBigDataRepeat = 1;

            if (isoType == IsoType.Iso15765)
            {
                var package = SequencePackageUtils.LoadFromString(sequencePackageXml);
                var envVars = package.SetVars.Select(x => new EnvVar(x.Name, x.Value)).ToList();

                foreach (var sequence in package.Sequences)
                {
                    var isBigData = sequence.Name.Contains("big-data");

                    foreach (var step in sequence.Steps)
                    {
                        if (step is SendIsotpStep sendIsotpStep)
                        {
                            var payload = sendIsotpStep.HexPayload?.Trim();
                            if (!string.IsNullOrWhiteSpace(payload))
                            {
                                envVars.Eval(ref payload);
                                var payloadBytes = SequenceUtils.ParseBytes(payload);
                                var repeat = 1;
                                if (sendIsotpStep.RepeatPayload.HasValue && sendIsotpStep.RepeatPayload.Value > 0)
                                {
                                    repeat = sendIsotpStep.RepeatPayload.Value;
                                }

                                if (isBigData)
                                {
                                    tpParametersBigDataPayload = payloadBytes;
                                    tpParametersBigDataRepeat = repeat;
                                }
                                else
                                {
                                    var repeatPayloadBytes = new List<byte>();
                                    for (var i = 0; i < repeat; i++)
                                    {
                                        repeatPayloadBytes.AddRange(payloadBytes);
                                    }

                                    tpParameters = repeatPayloadBytes.ToArray();
                                }
                            }
                        }
                    }
                }
            }

            var whiteListFrames = caseConfig.WhiteListFrames
                .Where(x => x.Transmitter == caseConfig.SelectedNodeName || x.Receivers.Contains(caseConfig.SelectedNodeName))
                .ToArray();

            var options = new MutationOptions
            {
                IsoType = isoType,
                Coverage = request.coverage,

                InteroperationResults = caseResults,
                SelectedSequenceNames = request.SequenceNames,

                CommunicationType = hardwareConfig.CommunicationType,

                WhiteListFrames = whiteListFrames,
                SelectedNodeName = caseConfig.SelectedNodeName,

                XmlServices = xmlServices.Where(x => request.SequenceNames.Contains(x.SequenceName)).OrderBy(x => x.Id).ToArray(),

                TpParameters = tpParameters,
                TpParametersBigDataPayload = tpParametersBigDataPayload,
                TpParametersBigDataRepeat = tpParametersBigDataRepeat,

                IsDutMtuLessThan4096 = caseConfig.IsDutMtuLessThan4096
            };

            CaseResult[] caseList = null;
            List<string> groupPaths = new List<string>();

            var groupTree = new GroupTreeNode();


            switch (isoType)
            {
                case IsoType.Iso11898:
                    (caseList, groupPaths) = Iso11898_CaseFactoryUtils.GenerateWithGroup(options);
                    groupTree = BuildGroupTree(groupPaths);
                    break;
                case IsoType.Iso14229:
                    (caseList, groupPaths) = Iso14229_CaseFactoryUtils.GenerateWithGroup(options);
                    groupTree = BuildGroupTree(caseList);
                    break;
                case IsoType.Iso15765:
                    (caseList, groupPaths) = Iso15765_CaseFactoryUtils.GenerateWithGroup(options);
                    groupTree = BuildGroupTree(groupPaths);
                    break;
            }

            return (caseList, groupTree);
        }


        // 新增分组树节点类
        public class GroupTreeNode
        {
            public string Id { get; set; }
            public string Name { get; set; }
            public List<GroupTreeNode> Children { get; set; } = new List<GroupTreeNode>();
            public int Count { get; private set; }

            public void CalculateCaseCount(CaseResult[] caseResults, ref int leafNodeCaseCount)
            {
                foreach (var child in Children)
                {
                    child.CalculateCaseCount(caseResults.Where(x => x.GroupPath == child.Id || x.GroupPath.StartsWith(child.Id + "/")).ToArray(), ref leafNodeCaseCount);
                }

                if (!Children.Any())
                {
                    leafNodeCaseCount += caseResults.Length;
                }

                GenerationProgressService.SetGroupLeafNodeCaseCount(leafNodeCaseCount);
                Count = caseResults.Length;
            }

            public CaseGroup ToCaseGroup()
            {
                return new CaseGroup
                {
                    Path = Id,
                    Name = Name,
                    CaseCount = Count,
                    Children = Children.Select(x => x.ToCaseGroup()).ToArray()
                };
            }

            public static GroupTreeNode Create(CaseGroup caseGroup)
            {
                return new GroupTreeNode
                {
                    Id = caseGroup.Path,
                    Name = caseGroup.Name,
                    Count = caseGroup.CaseCount,
                    Children = caseGroup.Children.Select(Create).ToList()
                };
            }
        }

        private GroupTreeNode BuildGroupTree(IEnumerable<string> groupPaths)
        {
            var roots = new List<GroupTreeNode>();

            foreach (var path in groupPaths)
            {
                var parts = path.Split('/');
                // 查找或创建根节点
                var root = roots.FirstOrDefault(r => r.Name == parts[0]);
                if (root == null)
                {
                    root = new GroupTreeNode { Name = parts[0], Id = parts[0] };
                    roots.Add(root);
                }

                var node = root;
                for (int i = 1; i < parts.Length; i++)
                {
                    var part = parts[i];
                    var child = node.Children.FirstOrDefault(x => x.Name == part);
                    if (child == null)
                    {
                        child = new GroupTreeNode { Name = part, Id = string.Join("/", parts.Take(i + 1)) };
                        node.Children.Add(child);
                    }
                    node = child;
                }
            }
            // 如果只有一个根节点，直接返回；否则可用虚拟根节点包裹
            if (roots.Count == 1)
            {
                return roots[0];
            }
            else
            {
                return new GroupTreeNode { Name = "Root", Id = "Root", Children = roots };
            }
        }

        private GroupTreeNode BuildGroupTree(CaseResult[] caseResults)
        {
            var roots = new List<GroupTreeNode>();

            // 获取所有唯一的 groupPath
            var uniqueGroupPaths = caseResults
                .Where(x => !string.IsNullOrEmpty(x.GroupPath))
                .Select(x => x.GroupPath)
                .Distinct()
                .ToList();

            foreach (var groupPath in uniqueGroupPaths)
            {
                var parts = groupPath.Split('/');

                if (parts.Length <= 0)
                {
                    continue;
                }

                // 查找或创建根节点
                var root = roots.FirstOrDefault(r => r.Name == parts[0]);
                if (root == null)
                {
                    root = new GroupTreeNode { Name = parts[0], Id = parts[0] };
                    roots.Add(root);
                }

                var node = root;
                for (int i = 1; i < parts.Length; i++)
                {
                    var part = parts[i];
                    var child = node.Children.FirstOrDefault(x => x.Name == part);
                    if (child == null)
                    {
                        child = new GroupTreeNode { Name = part, Id = string.Join("/", parts.Take(i + 1)) };
                        node.Children.Add(child);
                    }
                    node = child;
                }
            }

            // 如果只有一个根节点，直接返回；否则可用虚拟根节点包裹
            if (roots.Count == 1)
            {
                return roots[0];
            }
            else
            {
                return new GroupTreeNode { Name = "No case groups", Id = "No case groups", Children = roots };
            }
        }

        [HttpPost]
        [ActionName("start")]
        public async Task<IHttpActionResult> Start()
        {
            if (StatusPollingService.IsTesterRunning)
            {
                return Ok();
            }

            StatusPollingService.TesterSnapshot = new TesterSnapshot();

            var success = await TesterManager.Instance.StartAsync();
            if (!success)
            {
                throw new Exception("Failed to start tester");
            }

            var apiClient = TesterManager.Instance.ApiClient;
            if (apiClient == null)
            {
                throw new Exception("The API client is null");
            }

            // 等待 tester 启动
            await TesterManager.Instance.WaitTesterAsync(expectIsRunning: true);

            var testPlanName = Path.GetFileNameWithoutExtension(TestPlanManager.Instance.GetCurrentPlanPath());
            var resultReader = new TestResultReaderService();
            var testResult = resultReader.CreateTestResult(testPlanName, TestType.Case);

            var caseEntries = TestPlanManager.Instance.LoadCaseCollection().CaseEntries;
            testResult.TotalCount = caseEntries.Length;

            var apiResponse = await TesterManager.Instance.ApiClient.StartTestCasesAsync(testResult, caseEntries);
            if (!apiResponse.Success)
            {
                await TesterManager.Instance.StopAsync();
                throw new Exception(apiResponse.Message);
            }

            await StatusPollingService.UpdateStatusAsync();
            StatusPollingService.StartStatusPolling(false);

            return Ok();
        }

        [HttpPost]
        [ActionName("stop")]
        public async Task<IHttpActionResult> Stop()
        {
            await TesterManager.Instance.ApiClient.StopAsync();
            await StatusPollingService.UpdateStatusAsync();
            StatusPollingService.StopStatusPolling();
            return Ok();
        }

        [HttpPost]
        [ActionName("pause")]
        public async Task<IHttpActionResult> Pause()
        {
            await TesterManager.Instance.ApiClient.PauseAsync();
            return Ok();
        }

        [HttpPost]
        [ActionName("resume")]
        public async Task<IHttpActionResult> Resume()
        {
            await TesterManager.Instance.ApiClient.ResumeAsync();
            return Ok();
        }

        [HttpGet]
        [ActionName("status")]
        public IHttpActionResult GetStatus()
        {
            return Ok(new TesterSnapshot
            {
                ProcessState = StatusPollingService.TesterSnapshot?.ProcessState ?? ExecutionState.Pending,
                CurrentOperation = StatusPollingService.TesterSnapshot?.CurrentOperation ?? string.Empty,
                TestResult = StatusPollingService.TesterSnapshot?.TestResult,
                // 不再返回每条用例的状态 
                CaseResults = Array.Empty<CaseResult>()
            });
        }

        [HttpPost]
        [ActionName("generate-report")]
        public IHttpActionResult GenerateHtmlReport(Guid testResultId)
        {
            var resultReader = new TestResultReaderService();
            var testResult = resultReader
                .GetTestResults()
                .FirstOrDefault(x => x.Id == testResultId);

            if (testResult == null)
            {
                return NotFound();
            }

            // 获取相关的测试用例结果
            var caseResults = resultReader.GetCaseResults(testResult);

            // 使用ReportTemplateService生成HTML报告
            var htmlContent = ReportTemplateService.GenerateHtmlReport(testResult, caseResults);

            if (!UiUtils.SelectFolder(out var folder))
            {
                throw new Exception("Please select a folder");
            }

            var fileName = $"{testResult.ResultFolderName}.html";
            var existedNames = Directory.GetFileSystemEntries(folder).Select(x => Path.GetFileName(x)).ToArray();
            var newFileName = PathUtils.GetAlternativeFileName(fileName, existedNames);
            var filePath = Path.Combine(folder, newFileName);
            File.WriteAllText(filePath, htmlContent);
            UiUtils.RevealInExplorer(filePath);

            return Ok();
        }
    }
}
