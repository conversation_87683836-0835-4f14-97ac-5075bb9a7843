using Alsi.App.Devices.Core;
using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service;
using Alsi.Fuzz.Core.Storage;
using Alsi.Fuzz.Web.Dto;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Http;

namespace Alsi.Fuzz.Web.Controllers
{
    public class HardwareConfigController : WebControllerBase
    {
        private TestPlanService _testPlanService;

        public HardwareConfigController()
        {
            var historyService = new TestPlanHistoryService();
            _testPlanService = new TestPlanService(new TestPlanStorage(), historyService);
        }

        [HttpGet]
        public IHttpActionResult GetHardwareConfig()
        {
            var deviceChannels = TestPlanManager.Instance.GetCachedDeviceChannels();

            var hardwareConfigDto = new HardwareConfigDto();

            var deviceChannelDtos = Mapper.Map<List<DeviceChannelDto>>(deviceChannels).ToList();
            foreach (var deviceChannelDto in deviceChannelDtos)
            {
                deviceChannelDto.IsConnected = true;
            }

            var currentPlan = TestPlanManager.Instance.GetCurrentPlan();
            var selectedDeviceChannelName = currentPlan.Config.HardwareConfig.GetSelectedDeviceChannelName();

            // 如果设备列表不包含以前配置的设备，也显示出来
            if (!string.IsNullOrWhiteSpace(selectedDeviceChannelName)
                && !deviceChannelDtos.Any(x => x.Name == selectedDeviceChannelName))
            {
                DeviceChannelDto deviceChannel = new DeviceChannelDto
                {
                    Name = selectedDeviceChannelName,
                    IsConnected = false,
                    CommunicationType = currentPlan.Config.HardwareConfig.CommunicationType
                };

                deviceChannelDtos.Insert(0, deviceChannel);
            }

            hardwareConfigDto.DeviceChannels = deviceChannelDtos.ToArray();

            if (currentPlan != null && currentPlan.Config.HardwareConfig != null)
            {
                hardwareConfigDto.TestPlanConfig = new TestPlanConfigDto
                {
                    CommunicationType = currentPlan.Config.HardwareConfig.CommunicationType,
                    CanConfig = Mapper.Map<CanConfigDto>(currentPlan.Config.HardwareConfig.CanConfig),
                    CanFdConfig = Mapper.Map<CanFdConfigDto>(currentPlan.Config.HardwareConfig.CanFdConfig)
                };
            }

            return Ok(hardwareConfigDto);
        }

        [HttpPost]
        [ActionName("update")]
        public async Task<IHttpActionResult> UpdateHardwareConfig([FromBody] TestPlanConfigDto config)
        {
            var currentPlan = TestPlanManager.Instance.GetCurrentPlan();
            if (currentPlan == null)
            {
                return NotFound();
            }

            var path = TestPlanManager.Instance.GetCurrentPlanPath();
            if (string.IsNullOrEmpty(path))
            {
                return BadRequest("当前没有打开的测试计划");
            }

            var hardwareConfig = currentPlan.Config.HardwareConfig;

            // 设置通道类型
            hardwareConfig.CommunicationType = config.CommunicationType;

            // 根据所选通道类型更新对应的配置
            if (config.CommunicationType == CommunicationType.Can && config.CanConfig != null)
            {
                hardwareConfig.CanConfig = Mapper.Map<CanConfig>(config.CanConfig);
            }
            else if (config.CommunicationType == CommunicationType.CanFd && config.CanFdConfig != null)
            {
                hardwareConfig.CanFdConfig = Mapper.Map<CanFdConfig>(config.CanFdConfig);
            }

            var updatedTestPlan = await _testPlanService.UpdateHardwareConfigAsync(path, hardwareConfig);

            currentPlan.Config.HardwareConfig = updatedTestPlan.Config.HardwareConfig;

            // 返回更新后的配置
            return GetHardwareConfig();
        }
    }
}
