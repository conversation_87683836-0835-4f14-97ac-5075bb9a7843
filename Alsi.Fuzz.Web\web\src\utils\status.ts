import { ExecutionState } from '@/api/appApi';

/**
 * 获取执行状态对应的时间线项类型
 * @param state 执行状态
 * @returns 时间线项类型
 */
export const getTimelineItemType = (state: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
  switch (state) {
    case ExecutionState.Success:
      return 'success';
    case ExecutionState.Running:
      return 'primary';
    case ExecutionState.Failure:
      return 'danger';
    case ExecutionState.Pending:
    default:
      return 'info';
  }
};