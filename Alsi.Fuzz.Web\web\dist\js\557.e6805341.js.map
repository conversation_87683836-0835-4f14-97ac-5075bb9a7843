{"version": 3, "file": "js/557.e6805341.js", "mappings": "8LAEA,MAAMA,EAAa,CCDZC,MAAM,6BDEPC,EAAa,CCAVD,MAAM,gBDCTE,EAAa,CCSJF,MAAM,sBDRfG,EAAa,CCkBCH,MAAM,eDjBpBI,EAAa,CC+BJJ,MAAM,sBD9BfK,EAAa,CCwCCL,MAAM,eDvCpBM,EAAa,CCgEVN,MAAM,gBD9DT,SAAUO,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,GAAsBC,EAAAA,EAAAA,IAAkB,YACxCC,GAA4BD,EAAAA,EAAAA,IAAkB,kBAC9CE,GAA0BF,EAAAA,EAAAA,IAAkB,gBAC5CG,GAAmCH,EAAAA,EAAAA,IAAkB,yBACrDI,GAAqBJ,EAAAA,EAAAA,IAAkB,WACvCK,GAAoBL,EAAAA,EAAAA,IAAkB,UACtCM,GAAuBN,EAAAA,EAAAA,IAAkB,aACzCO,GAAuBP,EAAAA,EAAAA,IAAkB,aACzCQ,GAAqBR,EAAAA,EAAAA,IAAkB,WACvCS,GAAuBT,EAAAA,EAAAA,IAAkB,aACzCU,GAAqBC,EAAAA,EAAAA,IAAkB,WAE7C,OAAOC,EAAAA,EAAAA,MAAiBC,EAAAA,EAAAA,OCtBxBC,EAAAA,EAAAA,IA0EM,MA1EN9B,EA0EM,EAxEJ+B,EAAAA,EAAAA,IAkEM,MAlEN7B,EAkEM,EAjEJ8B,EAAAA,EAAAA,IAgEUR,EAAA,CAhEAS,MAAOxB,EAAAyB,KAAM,iBAAe,MAAM,cAAY,SDyBrD,CC7BTC,SAAAC,EAAAA,EAAAA,KAKQ,IAKe,EALfJ,EAAAA,EAAAA,IAKed,EAAA,CALDmB,MAAM,YAAU,CALtCF,SAAAC,EAAAA,EAAAA,KAMU,IAGiB,EAHjBJ,EAAAA,EAAAA,IAGiBf,EAAA,CAT3BqB,WAMmC7B,EAAAyB,KAAKK,kBANxC,sBAAA7B,EAAA,KAAAA,EAAA,GAAA8B,GAMmC/B,EAAAyB,KAAKK,kBAAiBC,GAAGC,SAAQhC,EAAAiC,+BD+BnD,CCrCjBP,SAAAC,EAAAA,EAAAA,KAOY,IAAuC,EAAvCJ,EAAAA,EAAAA,IAAuCjB,EAAA,CAA5BsB,MAAO,OAAK,CAPnCF,SAAAC,EAAAA,EAAAA,KAOqC,IAAG1B,EAAA,KAAAA,EAAA,KAPxCiC,EAAAA,EAAAA,IAOqC,WAPrCC,EAAA,KAQYZ,EAAAA,EAAAA,IAA2CjB,EAAA,CAAhCsB,MAAO,SAAO,CARrCF,SAAAC,EAAAA,EAAAA,KAQuC,IAAK1B,EAAA,KAAAA,EAAA,KAR5CiC,EAAAA,EAAAA,IAQuC,aARvCC,EAAA,OAAAA,EAAA,GDqDiB,EAAG,CAAC,aAAc,gBCrDnCA,EAAA,IAYmD,QAA3BnC,EAAAyB,KAAKK,oBD8CdV,EAAAA,EAAAA,OC9CPC,EAAAA,EAAAA,IAsBWe,EAAAA,GAAA,CAlCnBC,IAAA,KAaUf,EAAAA,EAAAA,IAaM,MAbN5B,EAaM,EAZJ6B,EAAAA,EAAAA,IAEed,EAAA,CAFDmB,MAAM,iBAAiBpC,MAAM,aDiDlC,CC/DrBkC,SAAAC,EAAAA,EAAAA,KAec,IAA0F,EAA1FJ,EAAAA,EAAAA,IAA0Fb,EAAA,CAfxGmB,WAe8C7B,EAAAyB,KAAKa,UAAUC,kBAf7D,sBAAAtC,EAAA,KAAAA,EAAA,GAAA8B,GAe8C/B,EAAAyB,KAAKa,UAAUC,kBAAiBR,GAAGS,QAASxC,EAAAyC,YDsDjE,KAAM,EAAG,CAAC,aAAc,eCrEjDN,EAAA,MDyEmBf,EAAAA,EAAAA,OCxDPsB,EAAAA,EAAAA,IAQS9B,EAAA,CARAyB,IAAKrC,EAAA2C,gBAAiBnD,MAAM,oBAAoBoD,KAAK,QAC3DC,KAAM7C,EAAA8C,uBAAuB9C,EAAAyB,KAAKa,UAAUC,mBAAmBQ,YAAc,UAAY,UD4DnF,CC9ErBrB,SAAAC,EAAAA,EAAAA,KAmBc,IAGU,EAHVJ,EAAAA,EAAAA,IAGUZ,EAAA,CAHDnB,MAAM,eAAa,CAnB1CkC,SAAAC,EAAAA,EAAAA,KAoBgB,IAC8G,GD6DnGP,EAAAA,EAAAA,OC9DXsB,EAAAA,EAAAA,KAC8GM,EAAAA,EAAAA,IAAvGhD,EAAA8C,uBAAuB9C,EAAAyB,KAAKa,UAAUC,mBAAmBQ,YAAc,aAAe,oBArB7GZ,EAAA,KAuBcb,EAAAA,EAAAA,IACwC,OADxC3B,GACwCsD,EAAAA,EAAAA,IADXjD,EAAA8C,uBAAuB9C,EAAAyB,KAAKa,UAAUC,mBAAmBQ,YDgEpF,YAAc,gBAAiB,MCvF/CZ,EAAA,GD0FqB,EAAG,CAAC,aC9DfZ,EAAAA,EAAAA,IAKed,EAAA,CALDmB,MAAM,YAAYpC,MAAM,aDmE7B,CC/FnBkC,SAAAC,EAAAA,EAAAA,KA6BY,IAGY,EAHZJ,EAAAA,EAAAA,IAGYT,EAAA,CAhCxBe,WA6BgC7B,EAAAyB,KAAKa,UAAUY,YA7B/C,sBAAAjD,EAAA,KAAAA,EAAA,GAAA8B,GA6BgC/B,EAAAyB,KAAKa,UAAUY,YAAWnB,GAAEoB,YAAY,mBAAmBC,MAAA,gBDyEpE,CCtGvB1B,SAAAC,EAAAA,EAAAA,KA8ByB,IAAyB,GD0EzBP,EAAAA,EAAAA,KAAW,IC1EtBC,EAAAA,EAAAA,IACYe,EAAAA,GAAA,MA/B1BiB,EAAAA,EAAAA,IA8BwCrD,EAAAsD,WAARC,KD2EEnC,EAAAA,EAAAA,OC3EpBsB,EAAAA,EAAAA,IACY7B,EAAA,CAD0BwB,IAAKkB,EAAO3B,MAAU2B,EAAO,IAAZ,UAA4BC,MAAOD,GD+E3E,KAAM,EAAG,CAAC,QAAS,aACpB,SC9G5BpB,EAAA,GDiHuB,EAAG,CAAC,kBCjH3BA,EAAA,KDqHiB,OACFf,EAAAA,EAAAA,OClFPC,EAAAA,EAAAA,IA+BWe,EAAAA,GAAA,CAnEnBC,IAAA,KAqCUf,EAAAA,EAAAA,IAaM,MAbN1B,EAaM,EAZJ2B,EAAAA,EAAAA,IAEed,EAAA,CAFDmB,MAAM,iBAAiBpC,MAAM,aDqFlC,CC3HrBkC,SAAAC,EAAAA,EAAAA,KAuCc,IAA8F,EAA9FJ,EAAAA,EAAAA,IAA8Fb,EAAA,CAvC5GmB,WAuC8C7B,EAAAyB,KAAKgC,YAAYlB,kBAvC/D,sBAAAtC,EAAA,KAAAA,EAAA,GAAA8B,GAuC8C/B,EAAAyB,KAAKgC,YAAYlB,kBAAiBR,GAAGS,QAASxC,EAAA0D,cD0FnE,KAAM,EAAG,CAAC,aAAc,eCjIjDvB,EAAA,KAyCYZ,EAAAA,EAAAA,IAQSX,EAAA,CARDpB,MAAM,oBAAoBoD,KAAK,QACpCC,KAAM7C,EAAA8C,uBAAuB9C,EAAAyB,KAAKgC,YAAYlB,mBAAmBQ,YAAc,UAAY,UD+FrF,CCzIrBrB,SAAAC,EAAAA,EAAAA,KA2Cc,IAGU,EAHVJ,EAAAA,EAAAA,IAGUZ,EAAA,CAHDnB,MAAM,eAAa,CA3C1CkC,SAAAC,EAAAA,EAAAA,KA4CgB,IACgH,GDgGrGP,EAAAA,EAAAA,OCjGXsB,EAAAA,EAAAA,KACgHM,EAAAA,EAAAA,IAAzGhD,EAAA8C,uBAAuB9C,EAAAyB,KAAKgC,YAAYlB,mBAAmBQ,YAAc,aAAe,oBA7C/GZ,EAAA,KA+Ccb,EAAAA,EAAAA,IACwC,OADxCzB,GACwCoD,EAAAA,EAAAA,IADXjD,EAAA8C,uBAAuB9C,EAAAyB,KAAKgC,YAAYlB,mBAAmBQ,YDmGtF,YAAc,gBAAiB,MClJ/CZ,EAAA,GDqJqB,EAAG,CAAC,YCjGfZ,EAAAA,EAAAA,IAMed,EAAA,CANDmB,MAAM,8BAA8BpC,MAAM,aDsG/C,CC1JnBkC,SAAAC,EAAAA,EAAAA,KAqDY,IAIY,EAJZJ,EAAAA,EAAAA,IAIYT,EAAA,CAzDxBe,WAqDgC7B,EAAAyB,KAAKgC,YAAYE,mBArDjD,sBAAA1D,EAAA,KAAAA,EAAA,GAAA8B,GAqDgC/B,EAAAyB,KAAKgC,YAAYE,mBAAkB5B,GAAEoB,YAAY,qCACnEC,MAAA,gBD2GS,CCjKvB1B,SAAAC,EAAAA,EAAAA,KAuDyB,IAAyB,GD4GzBP,EAAAA,EAAAA,KAAW,IC5GtBC,EAAAA,EAAAA,IACYe,EAAAA,GAAA,MAxD1BiB,EAAAA,EAAAA,IAuDwCrD,EAAAsD,WAARC,KD6GEnC,EAAAA,EAAAA,OC7GpBsB,EAAAA,EAAAA,IACY7B,EAAA,CAD0BwB,IAAKkB,EAAO3B,MAAU2B,EAAO,IAAZ,UAA4BC,MAAOD,GDiH3E,KAAM,EAAG,CAAC,QAAS,aACpB,SCzK5BpB,EAAA,GD4KuB,EAAG,CAAC,kBC5K3BA,EAAA,KA4DUZ,EAAAA,EAAAA,IAMed,EAAA,CANDmB,MAAM,uBAAuBpC,MAAM,aDuHxC,CCnLnBkC,SAAAC,EAAAA,EAAAA,KA6DY,IAIY,EAJZJ,EAAAA,EAAAA,IAIYT,EAAA,CAjExBe,WA6DgC7B,EAAAyB,KAAKgC,YAAYP,YA7DjD,sBAAAjD,EAAA,KAAAA,EAAA,GAAA8B,GA6DgC/B,EAAAyB,KAAKgC,YAAYP,YAAWnB,GAAEoB,YAAY,8BAC5DC,MAAA,gBD4HS,CC1LvB1B,SAAAC,EAAAA,EAAAA,KA+DyB,IAA2B,GD6H3BP,EAAAA,EAAAA,KAAW,IC7HtBC,EAAAA,EAAAA,IACYe,EAAAA,GAAA,MAhE1BiB,EAAAA,EAAAA,IA+DwCrD,EAAA4D,aAARL,KD8HEnC,EAAAA,EAAAA,OC9HpBsB,EAAAA,EAAAA,IACY7B,EAAA,CAD4BwB,IAAKkB,EAAO3B,MAAU2B,EAAO,IAAZ,UAA4BC,MAAOD,GDkI7E,KAAM,EAAG,CAAC,QAAS,aACpB,SClM5BpB,EAAA,GDqMuB,EAAG,CAAC,kBCrM3BA,EAAA,KDyMiB,QCzMjBA,EAAA,GD4MS,EAAG,CAAC,aCpITb,EAAAA,EAAAA,IAEM,MAFNxB,EAEM,EADJyB,EAAAA,EAAAA,IAAkFP,EAAA,CAAvE6B,KAAK,UAAWgB,QAAO7D,EAAA8D,WAAaC,QAAS/D,EAAAgE,UD0IrD,CCnNTtC,SAAAC,EAAAA,EAAAA,KAyEwE,IAAI1B,EAAA,KAAAA,EAAA,KAzE5EiC,EAAAA,EAAAA,IAyEwE,YAzExEC,EAAA,GDwNS,EAAG,CAAC,UAAW,iBAEjB,CACH,CAAClB,EC1N+CjB,EAAAiE,YD4NpD,C,4FElLA,MAAMC,EAAW,sBAEJC,EAAc,CAEzBC,kBAAmBA,IACbC,EAAAA,GACKC,EAAAA,GAAQC,SAASH,oBAEnBI,EAAAA,EAAMC,IAAIP,GAInBQ,qBAAuBC,GACjBN,EAAAA,GACKC,EAAAA,GAAQC,SAASG,qBAAqBC,GAExCH,EAAAA,EAAMI,KAAK,GAAGV,WAAmBS,ICzD5C,MAAMpF,EAAa,CCORC,MAAM,iBDNXC,EAAa,CCOLD,MAAM,eDLd,SAAUO,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMO,GAAoBL,EAAAA,EAAAA,IAAkB,UACtCM,GAAuBN,EAAAA,EAAAA,IAAkB,aACzCO,GAAuBP,EAAAA,EAAAA,IAAkB,aAE/C,OAAQa,EAAAA,EAAAA,OCTRsB,EAAAA,EAAAA,IAeY5B,EAAA,CAhBde,WAEa7B,EAAA6E,cAFb,sBAAA5E,EAAA,KAAAA,EAAA,GAAA8B,GAEa/B,EAAA6E,cAAa9C,GACrBoB,YAAanD,EAAAmD,YACd3D,MAAM,wBACL,eAAc,iCACdwC,SAAQhC,EAAA8E,cDWR,CCjBLpD,SAAAC,EAAAA,EAAAA,KAOe,IAAyB,GDYjCP,EAAAA,EAAAA,KAAW,ICZdC,EAAAA,EAAAA,IAQYe,EAAAA,GAAA,MAfhBiB,EAAAA,EAAAA,IAOgCrD,EAAAwC,SAAVuC,KDaN3D,EAAAA,EAAAA,OCbZsB,EAAAA,EAAAA,IAQY7B,EAAA,CAR0BwB,IAAK0C,EAAOC,KAAOpD,MAAOmD,EAAOC,KAAOxB,MAAOuB,EAAOC,KACzFC,UAAWF,EAAOhC,aDiBd,CCzBXrB,SAAAC,EAAAA,EAAAA,KASM,IAKM,EALNL,EAAAA,EAAAA,IAKM,MALN/B,EAKM,EAJJ+B,EAAAA,EAAAA,IAAkD,OAAlD7B,GAAkDwD,EAAAA,EAAAA,IAArB8B,EAAOC,MAAI,IACxCzD,EAAAA,EAAAA,IAESX,EAAA,CAFDgC,KAAK,QAASC,KAAMkC,EAAOhC,YAAc,UAAY,UDqBpD,CChCjBrB,SAAAC,EAAAA,EAAAA,KAYU,IAAuD,EAZjEO,EAAAA,EAAAA,KAAAe,EAAAA,EAAAA,IAYa8B,EAAOhC,YAAc,YAAc,gBAAjB,MAZ/BZ,EAAA,GDqCiB,KAAM,CAAC,cCrCxBA,EAAA,GDyCW,KAAM,CAAC,QAAS,QAAS,gBAC1B,SC1CVA,EAAA,GD6CK,EAAG,CAAC,aAAc,cAAe,YACtC,CCxBA,OAAe+C,EAAAA,EAAAA,IAAgB,CAC7BF,KAAM,sBAENG,MAAO,CACLtD,WAAY,CACVgB,KAAMuC,OACN1D,QAAS,IAEXc,QAAS,CACPK,KAAMwC,MACNC,UAAU,EACV5D,QAASA,IAAM,IAEjByB,YAAa,CACXN,KAAMuC,OACN1D,QAAS,0BAIb6D,MAAO,CAAC,oBAAqB,UAE7BC,KAAAA,CAAML,GAAO,KAAEM,IACb,MAAMZ,GAAgBa,EAAAA,EAAAA,IAAS,CAC7BjB,IAAKA,IAAMU,EAAMtD,WACjB8D,IAAMnC,GAAUiC,EAAK,oBAAqBjC,KAGtCsB,EAAgBtB,IACpBiC,EAAK,SAAUjC,EAAM,EAGvB,MAAO,CACLqB,gBACAC,eAEJ,I,UClDF,MAAMc,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,QJuFA,GAAeV,EAAAA,EAAAA,IAAgB,CAC7BW,WAAY,CACVC,oBAAmB,EACnBC,WAAU,aACVC,YAAWA,EAAAA,aAGbb,MAAO,CACLc,WAAY,CACVpD,KAAMuC,OACNE,UAAU,IAIdE,KAAAA,CAAML,GAEJ,MAAMxC,GAAkBuD,EAAAA,EAAAA,IAAIC,KAAKC,OAG3B3E,GAAOyE,EAAAA,EAAAA,IAAI,CACfpE,kBAAmB,MACnBQ,UAAW,CACTC,kBAAmB,GACnBW,YAAa,KAEfO,YAAa,CACXlB,kBAAmB,GACnBoB,mBAAoB,IACpBT,YAAa,OAKXmD,GAAeH,EAAAA,EAAAA,IAAI,CAAC,GACpBlC,GAAWkC,EAAAA,EAAAA,KAAI,GAGfjC,GAAYiC,EAAAA,EAAAA,KAAI,GAChBI,GAAeJ,EAAAA,EAAAA,KAAI,GACnBK,GAAiBL,EAAAA,EAAAA,IAAS,MAC1BM,GAAiBN,EAAAA,EAAAA,IAAW,IAC5BO,GAAqBP,EAAAA,EAAAA,IAAW,IAGhC5C,GAAY4C,EAAAA,EAAAA,IAAI,CAAC,MAAQ,KAAQ,IAAQ,MACzCtC,GAAcsC,EAAAA,EAAAA,IAAI,CAAC,IAAS,IAAS,IAAS,MAG9CzD,GAAaiD,EAAAA,EAAAA,KAAS,IACnBc,EAAehD,MAAMkD,QAAO3B,GACJ,QAA7BA,EAAOjD,mBAA4D,UAA7BiD,EAAOjD,sBAG3C4B,GAAegC,EAAAA,EAAAA,KAAS,IACrBc,EAAehD,MAAMkD,QAAO3B,GACJ,UAA7BA,EAAOjD,sBAIX,IAAI6E,EAAoB,KACpBC,GAAgB,EAGpB,MAAMC,EAAoBA,KACxB5E,GAA+B,EAI3B6B,EAAagD,UACjB,IACE9C,EAASR,OAAQ,EAEjB,MAAMuD,EAAe,CACnBjF,kBAAmBL,EAAK+B,MAAM1B,kBAC9BQ,UAA4C,QAAjCb,EAAK+B,MAAM1B,kBAA8BL,EAAK+B,MAAMlB,eAAY0E,EAC3EvD,YAA8C,UAAjChC,EAAK+B,MAAM1B,kBAAgCL,EAAK+B,MAAMC,iBAAcuD,SAG7E7C,EAAYO,qBAAqBqC,GAGvCV,EAAa7C,MAAQyD,KAAKC,MAAMD,KAAKE,UAAU1F,EAAK+B,QACpD4D,EAAAA,GAAUC,QAAQ,2B,CAClB,MAAOC,GACPC,QAAQD,MAAM,kCAAmCA,GACjDF,EAAAA,GAAUE,MAAM,wC,CAClB,QACEtD,EAASR,OAAQ,C,GAKfgE,EAAqBV,UACzB,GAAKF,EAEL,IACE3C,EAAUT,OAAQ,EAClB,MAAMiE,QAAiBtD,EAAYC,oBAG7BsD,EAAoBD,EAASE,KAAKnB,gBAAkB,GACpDoB,EAAiBC,EAAqBpB,EAAmBjD,MAAOkE,GAStE,GAPIE,IACFpB,EAAehD,MAAQkE,EACvBjB,EAAmBjD,MAAQyD,KAAKC,MAAMD,KAAKE,UAAUO,IACrD/E,EAAgBa,MAAQ2C,KAAKC,OAI3BqB,EAASE,KAAKG,eAAgB,CAChC,MAAMC,EAAYN,EAASE,KAAKG,eAC1BE,EAAgBC,EAAiB1B,EAAe/C,MAAOuE,GAEzDC,IACFzB,EAAe/C,MAAQuE,EACvBtG,EAAK+B,MAAQ,CACX1B,kBAAmBiG,EAAUjG,kBAC7BQ,UAAW,IACNb,EAAK+B,MAAMlB,aACXyF,EAAUzF,WAEfmB,YAAa,IACRhC,EAAK+B,MAAMC,eACXsE,EAAUtE,cAGjB4C,EAAa7C,MAAQyD,KAAKC,MAAMD,KAAKE,UAAU1F,EAAK+B,Q,EAGxD,MAAO8D,GACPC,QAAQD,MAAM,kCAAmCA,GACjDF,EAAAA,GAAUE,MAAM,wC,CAClB,QACErD,EAAUT,OAAQ,C,GAKhBqE,EAAuBA,CAACK,EAAmBC,KAC/C,IAAKD,IAAeC,EAAY,OAAO,EACvC,GAAID,EAAWE,SAAWD,EAAWC,OAAQ,OAAO,EAGpD,MAAMC,EAAS,IAAIC,IAAIJ,EAAWK,KAAIC,GAAK,CAACA,EAAExD,KAAMwD,EAAEzF,gBACtD,OAAOoF,EAAWM,MAAKD,GAAKH,EAAO5D,IAAI+D,EAAExD,QAAUwD,EAAEzF,aAAY,EAI7DkF,EAAmBA,CAACS,EAAgBX,KACxC,IAAKW,GAAaX,EAAW,OAAO,EACpC,GAAIW,IAAcX,EAAW,OAAO,EACpC,IAAKW,IAAcX,EAAW,OAAO,EAErC,IAEE,MAAMY,EAAe1B,KAAKE,UAAUuB,GAC9BE,EAAe3B,KAAKE,UAAUY,GACpC,OAAOY,IAAiBC,C,CACxB,MAAOC,GAEP,OAAOH,EAAU5G,oBAAsBiG,EAAUjG,mBAC/CmF,KAAKE,UAAUuB,EAAUpG,aAAe2E,KAAKE,UAAUY,EAAUzF,YACjE2E,KAAKE,UAAUuB,EAAUjF,eAAiBwD,KAAKE,UAAUY,EAAUtE,Y,GAKnEqF,EAAsBA,KAE1BxF,EAAUE,MAAQ,CAAC,MAAQ,KAAQ,IAAQ,KAC3CI,EAAYJ,MAAQ,CAAC,IAAS,IAAS,IAAS,IAAQ,EAIpDvB,EAAgCA,KAEpC,GAAqC,QAAjCR,EAAK+B,MAAM1B,kBAA6B,CAE1C,MAAMiH,EAAgBvC,EAAehD,MAAMwF,MACzCR,GAAKA,EAAExD,OAASvD,EAAK+B,MAAMlB,UAAUC,oBAEnCwG,GAAqD,QAApCA,EAAcjH,mBAAmE,UAApCiH,EAAcjH,oBAC9EL,EAAK+B,MAAMlB,UAAUC,kBAAoB,G,KAEtC,CAEL,MAAMwG,EAAgBvC,EAAehD,MAAMwF,MACzCR,GAAKA,EAAExD,OAASvD,EAAK+B,MAAMC,YAAYlB,oBAErCwG,GAAqD,UAApCA,EAAcjH,oBACjCL,EAAK+B,MAAMC,YAAYlB,kBAAoB,G,GAM3CO,GAAyB4C,EAAAA,EAAAA,KAAS,IAC9BuD,IACN,MAAMlE,EAASyB,EAAehD,MAAMwF,MAAKR,GAAKA,EAAExD,OAASiE,IACzD,MAAO,CACLlG,YAAagC,GAAQhC,cAAe,EACrC,IAKCmG,EAAkBnE,GACf,GAAGA,EAAOC,QAAQD,EAAOhC,YAAc,cAAgB,mBAI1DoG,EAAiBrC,UACrB,IACER,EAAa9C,OAAQ,EACrBoD,GAAgB,QACVY,IACNJ,EAAAA,GAAUC,QAAQ,wB,CACpB,QACEf,EAAa9C,OAAQ,C,GAKnB4F,EAAqBA,KACzBzC,EAAe0C,aAAY,KACzB7B,GAAoB,GACnB,IAAK,EAIJ8B,EAAoBA,KACxB1C,GAAgB,EACZD,IACF4C,cAAc5C,GACdA,EAAe,K,EAgCnB,OA3BA6C,EAAAA,EAAAA,KAAU1C,UACRgC,UACMtB,KAENiC,EAAAA,EAAAA,KAAS,KACPpD,EAAa7C,MAAQyD,KAAKC,MAAMD,KAAKE,UAAU1F,EAAK+B,OAAO,IAG7D4F,GAAoB,KAItBM,EAAAA,EAAAA,KAAY,KACVJ,GAAmB,KAIrBK,EAAAA,EAAAA,KAAM,IAAMxE,EAAMc,aAAYa,UACxB8C,UACIpC,KAENiC,EAAAA,EAAAA,KAAS,KACPpD,EAAa7C,MAAQyD,KAAKC,MAAMD,KAAKE,UAAU1F,EAAK+B,OAAO,I,IAK1D,CACL/B,OACAwC,YACAqC,eACAtC,WACAvB,aACAiB,eACAJ,YACAM,cACA3B,gCACA4E,oBACA/C,aACAhB,yBACAoG,iBACAC,iBACAxG,kBAEJ,IKrXF,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS5C,GAAQ,CAAC,YAAY,qBAEzF,QCNA,MAAMR,EAAa,CAAEC,MAAO,8BAO5B,OAA4BqK,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,kBACRtE,KAAAA,CAAMuE,GCAR,MAAMC,EAAQC,EAAAA,EAAgBC,WACxBC,GAAWzE,EAAAA,EAAAA,KAAS,IAAMsE,EAAMI,cAChCrG,GAAU2B,EAAAA,EAAAA,KAAS,IAAMsE,EAAM/F,YAE/BoG,EAAkBvD,gBAChBmD,EAAAA,EAAgBK,gBAAgB,EDSxC,OCNAd,EAAAA,EAAAA,KAAU,KACRa,GAAiB,IDKZ,CAACrK,EAAUC,KAChB,MAAMgB,GAAqBC,EAAAA,EAAAA,IAAkB,WAE7C,OAAOC,EAAAA,EAAAA,MAAiBC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAO9B,EAAY,EAC3EgC,EAAAA,EAAAA,IAAagJ,EAAqB,CAChC,eAAgBJ,EAAS3G,OAAOgH,MAC/B,KAAM,EAAG,CAAC,oBACV,CACH,CAACvJ,EAAoB8C,EAAQP,QAC7B,CAEJ,IE/BA,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://fuzz-web/./src/components/hardware/HardwareConfigPanel.vue?2b97", "webpack://fuzz-web/./src/components/hardware/HardwareConfigPanel.vue", "webpack://fuzz-web/./src/api/hardwareApi.ts", "webpack://fuzz-web/./src/components/hardware/DeviceChannelSelect.vue?4c60", "webpack://fuzz-web/./src/components/hardware/DeviceChannelSelect.vue", "webpack://fuzz-web/./src/components/hardware/DeviceChannelSelect.vue?efd7", "webpack://fuzz-web/./src/components/hardware/HardwareConfigPanel.vue?8445", "webpack://fuzz-web/./src/views/testplan/HardwareSetting.vue?a967", "webpack://fuzz-web/./src/views/testplan/HardwareSetting.vue", "webpack://fuzz-web/./src/views/testplan/HardwareSetting.vue?c8f9"], "sourcesContent": ["import { createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, resolveDynamicComponent as _resolveDynamicComponent, openBlock as _openBlock, createBlock as _createBlock, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\"\n\nconst _hoisted_1 = { class: \"hardware-config-container\" }\nconst _hoisted_2 = { class: \"middle-panel\" }\nconst _hoisted_3 = { class: \"form-item-with-tag\" }\nconst _hoisted_4 = { class: \"status-text\" }\nconst _hoisted_5 = { class: \"form-item-with-tag\" }\nconst _hoisted_6 = { class: \"status-text\" }\nconst _hoisted_7 = { class: \"bottom-panel\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_el_radio = _resolveComponent(\"el-radio\")!\n  const _component_el_radio_group = _resolveComponent(\"el-radio-group\")!\n  const _component_el_form_item = _resolveComponent(\"el-form-item\")!\n  const _component_device_channel_select = _resolveComponent(\"device-channel-select\")!\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n  const _component_el_option = _resolveComponent(\"el-option\")!\n  const _component_el_select = _resolveComponent(\"el-select\")!\n  const _component_el_form = _resolveComponent(\"el-form\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _directive_loading = _resolveDirective(\"loading\")!\n\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createVNode(_component_el_form, {\n        model: _ctx.form,\n        \"label-position\": \"top\",\n        \"label-width\": \"160px\"\n      }, {\n        default: _withCtx(() => [\n          _createVNode(_component_el_form_item, { label: \"Bus Type\" }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_radio_group, {\n                modelValue: _ctx.form.communicationType,\n                \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((_ctx.form.communicationType) = $event)),\n                onChange: _ctx.handleCommunicationTypeChange\n              }, {\n                default: _withCtx(() => [\n                  _createVNode(_component_el_radio, { label: 'Can' }, {\n                    default: _withCtx(() => _cache[6] || (_cache[6] = [\n                      _createTextVNode(\"CAN\")\n                    ])),\n                    _: 1\n                  }),\n                  _createVNode(_component_el_radio, { label: 'CanFd' }, {\n                    default: _withCtx(() => _cache[7] || (_cache[7] = [\n                      _createTextVNode(\"CANFD\")\n                    ])),\n                    _: 1\n                  })\n                ]),\n                _: 1\n              }, 8, [\"modelValue\", \"onChange\"])\n            ]),\n            _: 1\n          }),\n          (_ctx.form.communicationType === 'Can')\n            ? (_openBlock(), _createElementBlock(_Fragment, { key: 0 }, [\n                _createElementVNode(\"div\", _hoisted_3, [\n                  _createVNode(_component_el_form_item, {\n                    label: \"Device Channel\",\n                    class: \"form-item\"\n                  }, {\n                    default: _withCtx(() => [\n                      _createVNode(_component_device_channel_select, {\n                        modelValue: _ctx.form.canConfig.deviceChannelName,\n                        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((_ctx.form.canConfig.deviceChannelName) = $event)),\n                        devices: _ctx.canDevices\n                      }, null, 8, [\"modelValue\", \"devices\"])\n                    ]),\n                    _: 1\n                  }),\n                  (_openBlock(), _createBlock(_component_el_tag, {\n                    key: _ctx.updateTimestamp,\n                    class: \"device-status-tag\",\n                    size: \"small\",\n                    type: _ctx.getDeviceConnectStatus(_ctx.form.canConfig.deviceChannelName).isConnected ? 'success' : 'danger'\n                  }, {\n                    default: _withCtx(() => [\n                      _createVNode(_component_el_icon, { class: \"status-icon\" }, {\n                        default: _withCtx(() => [\n                          (_openBlock(), _createBlock(_resolveDynamicComponent(_ctx.getDeviceConnectStatus(_ctx.form.canConfig.deviceChannelName).isConnected ? 'Connection' : 'CircleClose')))\n                        ]),\n                        _: 1\n                      }),\n                      _createElementVNode(\"span\", _hoisted_4, _toDisplayString(_ctx.getDeviceConnectStatus(_ctx.form.canConfig.deviceChannelName).isConnected ?\r\n                'Connected' : 'Disconnected'), 1)\n                    ]),\n                    _: 1\n                  }, 8, [\"type\"]))\n                ]),\n                _createVNode(_component_el_form_item, {\n                  label: \"Baud Rate\",\n                  class: \"form-item\"\n                }, {\n                  default: _withCtx(() => [\n                    _createVNode(_component_el_select, {\n                      modelValue: _ctx.form.canConfig.dataBitrate,\n                      \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((_ctx.form.canConfig.dataBitrate) = $event)),\n                      placeholder: \"Select Baud Rate\",\n                      style: {\"width\":\"100%\"}\n                    }, {\n                      default: _withCtx(() => [\n                        (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.baudRates, (rate) => {\n                          return (_openBlock(), _createBlock(_component_el_option, {\n                            key: rate,\n                            label: `${rate / 1000} kbit/s`,\n                            value: rate\n                          }, null, 8, [\"label\", \"value\"]))\n                        }), 128))\n                      ]),\n                      _: 1\n                    }, 8, [\"modelValue\"])\n                  ]),\n                  _: 1\n                })\n              ], 64))\n            : (_openBlock(), _createElementBlock(_Fragment, { key: 1 }, [\n                _createElementVNode(\"div\", _hoisted_5, [\n                  _createVNode(_component_el_form_item, {\n                    label: \"Device Channel\",\n                    class: \"form-item\"\n                  }, {\n                    default: _withCtx(() => [\n                      _createVNode(_component_device_channel_select, {\n                        modelValue: _ctx.form.canFdConfig.deviceChannelName,\n                        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = ($event: any) => ((_ctx.form.canFdConfig.deviceChannelName) = $event)),\n                        devices: _ctx.canFdDevices\n                      }, null, 8, [\"modelValue\", \"devices\"])\n                    ]),\n                    _: 1\n                  }),\n                  _createVNode(_component_el_tag, {\n                    class: \"device-status-tag\",\n                    size: \"small\",\n                    type: _ctx.getDeviceConnectStatus(_ctx.form.canFdConfig.deviceChannelName).isConnected ? 'success' : 'danger'\n                  }, {\n                    default: _withCtx(() => [\n                      _createVNode(_component_el_icon, { class: \"status-icon\" }, {\n                        default: _withCtx(() => [\n                          (_openBlock(), _createBlock(_resolveDynamicComponent(_ctx.getDeviceConnectStatus(_ctx.form.canFdConfig.deviceChannelName).isConnected ? 'Connection' : 'CircleClose')))\n                        ]),\n                        _: 1\n                      }),\n                      _createElementVNode(\"span\", _hoisted_6, _toDisplayString(_ctx.getDeviceConnectStatus(_ctx.form.canFdConfig.deviceChannelName).isConnected ?\r\n                'Connected' : 'Disconnected'), 1)\n                    ]),\n                    _: 1\n                  }, 8, [\"type\"])\n                ]),\n                _createVNode(_component_el_form_item, {\n                  label: \"Arbitration Phase Baud Rate\",\n                  class: \"form-item\"\n                }, {\n                  default: _withCtx(() => [\n                    _createVNode(_component_el_select, {\n                      modelValue: _ctx.form.canFdConfig.arbitrationBitrate,\n                      \"onUpdate:modelValue\": _cache[4] || (_cache[4] = ($event: any) => ((_ctx.form.canFdConfig.arbitrationBitrate) = $event)),\n                      placeholder: \"Select Arbitration Phase Baud Rate\",\n                      style: {\"width\":\"100%\"}\n                    }, {\n                      default: _withCtx(() => [\n                        (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.baudRates, (rate) => {\n                          return (_openBlock(), _createBlock(_component_el_option, {\n                            key: rate,\n                            label: `${rate / 1000} kbit/s`,\n                            value: rate\n                          }, null, 8, [\"label\", \"value\"]))\n                        }), 128))\n                      ]),\n                      _: 1\n                    }, 8, [\"modelValue\"])\n                  ]),\n                  _: 1\n                }),\n                _createVNode(_component_el_form_item, {\n                  label: \"Data Phase Baud Rate\",\n                  class: \"form-item\"\n                }, {\n                  default: _withCtx(() => [\n                    _createVNode(_component_el_select, {\n                      modelValue: _ctx.form.canFdConfig.dataBitrate,\n                      \"onUpdate:modelValue\": _cache[5] || (_cache[5] = ($event: any) => ((_ctx.form.canFdConfig.dataBitrate) = $event)),\n                      placeholder: \"Select Data Phase Baud Rate\",\n                      style: {\"width\":\"100%\"}\n                    }, {\n                      default: _withCtx(() => [\n                        (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.fdDataRates, (rate) => {\n                          return (_openBlock(), _createBlock(_component_el_option, {\n                            key: rate,\n                            label: `${rate / 1000} kbit/s`,\n                            value: rate\n                          }, null, 8, [\"label\", \"value\"]))\n                        }), 128))\n                      ]),\n                      _: 1\n                    }, 8, [\"modelValue\"])\n                  ]),\n                  _: 1\n                })\n              ], 64))\n        ]),\n        _: 1\n      }, 8, [\"model\"])\n    ]),\n    _createElementVNode(\"div\", _hoisted_7, [\n      _createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _ctx.handleSave,\n        loading: _ctx.isSaving\n      }, {\n        default: _withCtx(() => _cache[8] || (_cache[8] = [\n          _createTextVNode(\"Save\")\n        ])),\n        _: 1\n      }, 8, [\"onClick\", \"loading\"])\n    ])\n  ])), [\n    [_directive_loading, _ctx.isLoading]\n  ])\n}", "<template>\r\n  <div class=\"hardware-config-container\" v-loading=\"isLoading\">\r\n    <!-- 中间空白区域 -->\r\n    <div class=\"middle-panel\">\r\n      <el-form :model=\"form\" label-position=\"top\" label-width=\"160px\">\r\n        <el-form-item label=\"Bus Type\">\r\n          <el-radio-group v-model=\"form.communicationType\" @change=\"handleCommunicationTypeChange\">\r\n            <el-radio :label=\"'Can'\">CAN</el-radio>\r\n            <el-radio :label=\"'CanFd'\">CANFD</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n\r\n        <template v-if=\"form.communicationType === 'Can'\">\r\n          <div class=\"form-item-with-tag\">\r\n            <el-form-item label=\"Device Channel\" class=\"form-item\">\r\n              <device-channel-select v-model=\"form.canConfig.deviceChannelName\" :devices=\"canDevices\" />\r\n            </el-form-item>\r\n            <el-tag :key=\"updateTimestamp\" class=\"device-status-tag\" size=\"small\"\r\n              :type=\"getDeviceConnectStatus(form.canConfig.deviceChannelName).isConnected ? 'success' : 'danger'\">\r\n              <el-icon class=\"status-icon\">\r\n                <component\r\n                  :is=\"getDeviceConnectStatus(form.canConfig.deviceChannelName).isConnected ? 'Connection' : 'CircleClose'\" />\r\n              </el-icon>\r\n              <span class=\"status-text\">{{ getDeviceConnectStatus(form.canConfig.deviceChannelName).isConnected ?\r\n                'Connected' : 'Disconnected' }}</span>\r\n            </el-tag>\r\n          </div>\r\n\r\n          <el-form-item label=\"Baud Rate\" class=\"form-item\">\r\n            <el-select v-model=\"form.canConfig.dataBitrate\" placeholder=\"Select Baud Rate\" style=\"width: 100%\">\r\n              <el-option v-for=\"rate in baudRates\" :key=\"rate\" :label=\"`${rate / 1000} kbit/s`\" :value=\"rate\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </template>\r\n\r\n        <template v-else>\r\n          <div class=\"form-item-with-tag\">\r\n            <el-form-item label=\"Device Channel\" class=\"form-item\">\r\n              <device-channel-select v-model=\"form.canFdConfig.deviceChannelName\" :devices=\"canFdDevices\" />\r\n            </el-form-item>\r\n            <el-tag class=\"device-status-tag\" size=\"small\"\r\n              :type=\"getDeviceConnectStatus(form.canFdConfig.deviceChannelName).isConnected ? 'success' : 'danger'\">\r\n              <el-icon class=\"status-icon\">\r\n                <component\r\n                  :is=\"getDeviceConnectStatus(form.canFdConfig.deviceChannelName).isConnected ? 'Connection' : 'CircleClose'\" />\r\n              </el-icon>\r\n              <span class=\"status-text\">{{ getDeviceConnectStatus(form.canFdConfig.deviceChannelName).isConnected ?\r\n                'Connected' : 'Disconnected' }}</span>\r\n            </el-tag>\r\n          </div>\r\n\r\n          <el-form-item label=\"Arbitration Phase Baud Rate\" class=\"form-item\">\r\n            <el-select v-model=\"form.canFdConfig.arbitrationBitrate\" placeholder=\"Select Arbitration Phase Baud Rate\"\r\n              style=\"width: 100%\">\r\n              <el-option v-for=\"rate in baudRates\" :key=\"rate\" :label=\"`${rate / 1000} kbit/s`\" :value=\"rate\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"Data Phase Baud Rate\" class=\"form-item\">\r\n            <el-select v-model=\"form.canFdConfig.dataBitrate\" placeholder=\"Select Data Phase Baud Rate\"\r\n              style=\"width: 100%\">\r\n              <el-option v-for=\"rate in fdDataRates\" :key=\"rate\" :label=\"`${rate / 1000} kbit/s`\" :value=\"rate\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </template>\r\n      </el-form>\r\n    </div>\r\n\r\n    <!-- 底部按钮区域 -->\r\n    <div class=\"bottom-panel\">\r\n      <el-button type=\"primary\" @click=\"handleSave\" :loading=\"isSaving\">Save</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport {\r\n  ref,\r\n  computed,\r\n  onMounted,\r\n  onUnmounted,\r\n  watch,\r\n  nextTick,\r\n  defineComponent\r\n} from 'vue';\r\nimport {\r\n  Connection,\r\n  CircleClose\r\n} from '@element-plus/icons-vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { hardwareApi } from '@/api/hardwareApi';\r\nimport DeviceChannelSelect from './DeviceChannelSelect.vue';\r\n\r\nexport default defineComponent({\r\n  components: {\r\n    DeviceChannelSelect,\r\n    Connection,\r\n    CircleClose\r\n  },\r\n\r\n  props: {\r\n    testPlanId: {\r\n      type: String,\r\n      required: false\r\n    }\r\n  },\r\n\r\n  setup(props) {\r\n    // 添加更新时间戳\r\n    const updateTimestamp = ref(Date.now());\r\n\r\n    // 表单数据\r\n    const form = ref({\r\n      communicationType: 'Can',\r\n      canConfig: {\r\n        deviceChannelName: '',\r\n        dataBitrate: 500000\r\n      },\r\n      canFdConfig: {\r\n        deviceChannelName: '',\r\n        arbitrationBitrate: 500000,\r\n        dataBitrate: 2000000\r\n      }\r\n    });\r\n\r\n    // 保存状态管理 - 简化版本\r\n    const originalForm = ref({});\r\n    const isSaving = ref(false);\r\n\r\n    // 状态管理\r\n    const isLoading = ref(false);\r\n    const isRefreshing = ref(false);\r\n    const hardwareConfig = ref<any>(null);\r\n    const deviceChannels = ref<any[]>([]);\r\n    const lastDeviceChannels = ref<any[]>([]);\r\n\r\n    // 添加缺失的响应式变量\r\n    const baudRates = ref([125000, 250000, 500000, 1000000]);\r\n    const fdDataRates = ref([1000000, 2000000, 4000000, 8000000]);\r\n\r\n    // 添加计算属性\r\n    const canDevices = computed(() => {\r\n      return deviceChannels.value.filter(device =>\r\n        device.communicationType === 'Can' || device.communicationType === 'CanFd');\r\n    });\r\n\r\n    const canFdDevices = computed(() => {\r\n      return deviceChannels.value.filter(device =>\r\n        device.communicationType === 'CanFd');\r\n    });\r\n\r\n    // 轮询计时器和状态\r\n    let pollingTimer: any = null;\r\n    let pollingActive = true;\r\n\r\n    // 处理表单值变化\r\n    const handleValueChange = () => {\r\n      handleCommunicationTypeChange();\r\n    };\r\n\r\n    // 保存配置\r\n    const handleSave = async () => {\r\n      try {\r\n        isSaving.value = true;\r\n\r\n        const configToSave = {\r\n          communicationType: form.value.communicationType,\r\n          canConfig: form.value.communicationType === 'Can' ? form.value.canConfig : undefined,\r\n          canFdConfig: form.value.communicationType === 'CanFd' ? form.value.canFdConfig : undefined\r\n        };\r\n\r\n        await hardwareApi.updateHardwareConfig(configToSave);\r\n\r\n        // 更新原始表单状态\r\n        originalForm.value = JSON.parse(JSON.stringify(form.value));\r\n        ElMessage.success('Hardware settings saved.');\r\n      } catch (error) {\r\n        console.error('Failed to save hardware config:', error);\r\n        ElMessage.error('Failed to save hardware configuration');\r\n      } finally {\r\n        isSaving.value = false;\r\n      }\r\n    };\r\n\r\n    // 加载硬件配置\r\n    const loadHardwareConfig = async () => {\r\n      if (!pollingActive) return;\r\n\r\n      try {\r\n        isLoading.value = true;\r\n        const response = await hardwareApi.getHardwareConfig();\r\n\r\n        // 检查设备列表是否有变化\r\n        const newDeviceChannels = response.data.deviceChannels || [];\r\n        const devicesChanged = hasDeviceListChanged(lastDeviceChannels.value, newDeviceChannels);\r\n\r\n        if (devicesChanged) {\r\n          deviceChannels.value = newDeviceChannels;\r\n          lastDeviceChannels.value = JSON.parse(JSON.stringify(newDeviceChannels));\r\n          updateTimestamp.value = Date.now();\r\n        }\r\n\r\n        // 更新配置 - 移除hasUnsavedChanges检查\r\n        if (response.data.testPlanConfig) {\r\n          const newConfig = response.data.testPlanConfig;\r\n          const configChanged = hasConfigChanged(hardwareConfig.value, newConfig);\r\n\r\n          if (configChanged) {\r\n            hardwareConfig.value = newConfig;\r\n            form.value = {\r\n              communicationType: newConfig.communicationType,\r\n              canConfig: {\r\n                ...form.value.canConfig,\r\n                ...newConfig.canConfig\r\n              },\r\n              canFdConfig: {\r\n                ...form.value.canFdConfig,\r\n                ...newConfig.canFdConfig\r\n              }\r\n            };\r\n            originalForm.value = JSON.parse(JSON.stringify(form.value));\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Failed to load hardware config:', error);\r\n        ElMessage.error('Failed to load hardware configuration');\r\n      } finally {\r\n        isLoading.value = false;\r\n      }\r\n    };\r\n\r\n    // 比较设备列表是否有变化\r\n    const hasDeviceListChanged = (oldDevices: any[], newDevices: any[]): boolean => {\r\n      if (!oldDevices || !newDevices) return true;\r\n      if (oldDevices.length !== newDevices.length) return true;\r\n\r\n      // 比较名称和连接状态\r\n      const oldMap = new Map(oldDevices.map(d => [d.name, d.isConnected]));\r\n      return newDevices.some(d => oldMap.get(d.name) !== d.isConnected);\r\n    };\r\n\r\n    // 比较配置是否有变化\r\n    const hasConfigChanged = (oldConfig: any, newConfig: any): boolean => {\r\n      if (!oldConfig && newConfig) return true;\r\n      if (oldConfig && !newConfig) return true;\r\n      if (!oldConfig && !newConfig) return false;\r\n\r\n      try {\r\n        // 使用JSON字符串比较进行浅比较\r\n        const oldConfigStr = JSON.stringify(oldConfig);\r\n        const newConfigStr = JSON.stringify(newConfig);\r\n        return oldConfigStr !== newConfigStr;\r\n      } catch (e) {\r\n        // 如果JSON序列化失败，回退到按属性比较\r\n        return oldConfig.communicationType !== newConfig.communicationType ||\r\n          JSON.stringify(oldConfig.canConfig) !== JSON.stringify(newConfig.canConfig) ||\r\n          JSON.stringify(oldConfig.canFdConfig) !== JSON.stringify(newConfig.canFdConfig);\r\n      }\r\n    };\r\n\r\n    // 硬件配置选项 - 使用固定值代替API调用\r\n    const initHardwareOptions = () => {\r\n      // 使用固定的波特率值\r\n      baudRates.value = [125000, 250000, 500000, 1000000];\r\n      fdDataRates.value = [1000000, 2000000, 4000000, 8000000];\r\n    };\r\n\r\n    // 处理总线类型切换\r\n    const handleCommunicationTypeChange = () => {\r\n      // 如果用户从CanFd切换到Can，需要检查并调整当前选择\r\n      if (form.value.communicationType === 'Can') {\r\n        // 如果之前选择的设备不支持普通Can，则清空选择\r\n        const currentDevice = deviceChannels.value.find(\r\n          d => d.name === form.value.canConfig.deviceChannelName\r\n        );\r\n        if (currentDevice && currentDevice.communicationType !== 'Can' && currentDevice.communicationType !== 'CanFd') {\r\n          form.value.canConfig.deviceChannelName = '';\r\n        }\r\n      } else {\r\n        // 如果从Can切换到CanFd，检查设备是否支持CanFd\r\n        const currentDevice = deviceChannels.value.find(\r\n          d => d.name === form.value.canFdConfig.deviceChannelName\r\n        );\r\n        if (currentDevice && currentDevice.communicationType !== 'CanFd') {\r\n          form.value.canFdConfig.deviceChannelName = '';\r\n        }\r\n      }\r\n    };\r\n\r\n    // 将方法改为计算属性\r\n    const getDeviceConnectStatus = computed(() => {\r\n      return (deviceName: string) => {\r\n        const device = deviceChannels.value.find(d => d.name === deviceName);\r\n        return {\r\n          isConnected: device?.isConnected || false\r\n        };\r\n      };\r\n    });\r\n\r\n    // 获取设备标签，显示连接状态\r\n    const getDeviceLabel = (device: any) => {\r\n      return `${device.name} ${device.isConnected ? '(Connected)' : '(Disconnected)'}`;\r\n    };\r\n\r\n    // 刷新设备列表\r\n    const refreshDevices = async () => {\r\n      try {\r\n        isRefreshing.value = true;\r\n        pollingActive = true;\r\n        await loadHardwareConfig();\r\n        ElMessage.success('Device list refreshed');\r\n      } finally {\r\n        isRefreshing.value = false;\r\n      }\r\n    };\r\n\r\n    // 启动设备轮询\r\n    const startDevicePolling = () => {\r\n      pollingTimer = setInterval(() => {\r\n        loadHardwareConfig();\r\n      }, 2000); // 固定2秒轮询\r\n    };\r\n\r\n    // 停止设备轮询\r\n    const stopDevicePolling = () => {\r\n      pollingActive = false; // 标记轮询为非活跃\r\n      if (pollingTimer) {\r\n        clearInterval(pollingTimer);\r\n        pollingTimer = null;\r\n      }\r\n    };\r\n\r\n    // 组件挂载时加载配置\r\n    onMounted(async () => {\r\n      initHardwareOptions();\r\n      await loadHardwareConfig();\r\n\r\n      nextTick(() => {\r\n        originalForm.value = JSON.parse(JSON.stringify(form.value));\r\n      });\r\n\r\n      startDevicePolling();\r\n    });\r\n\r\n    // 组件卸载时停止轮询\r\n    onUnmounted(() => {\r\n      stopDevicePolling();\r\n    });\r\n\r\n    // 当测试计划ID改变时重新加载配置\r\n    watch(() => props.testPlanId, async (newVal) => {\r\n      if (newVal) {\r\n        await loadHardwareConfig();\r\n\r\n        nextTick(() => {\r\n          originalForm.value = JSON.parse(JSON.stringify(form.value));\r\n        });\r\n      }\r\n    });\r\n\r\n    return {\r\n      form,\r\n      isLoading,\r\n      isRefreshing,\r\n      isSaving,\r\n      canDevices,\r\n      canFdDevices,\r\n      baudRates,\r\n      fdDataRates,\r\n      handleCommunicationTypeChange,\r\n      handleValueChange,\r\n      handleSave,\r\n      getDeviceConnectStatus,\r\n      getDeviceLabel,\r\n      refreshDevices,\r\n      updateTimestamp,\r\n    };\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.hardware-config-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n  height: 100%;\r\n  width: 100%;\r\n}\r\n\r\n.middle-panel {\r\n  flex: 1;\r\n  min-height: 0;\r\n  background-color: #ffffff;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  border-radius: 2px;\r\n\r\n  .form-item {\r\n    margin-bottom: 20px;\r\n    max-width: 400px;\r\n  }\r\n\r\n  .form-item-with-tag {\r\n    display: flex;\r\n    align-items: flex-end;\r\n    gap: 12px;\r\n    margin-bottom: 20px;\r\n\r\n    .form-item {\r\n      flex: 1;\r\n      margin-bottom: 0;\r\n    }\r\n\r\n    .device-status-tag {\r\n      margin-bottom: 8px;\r\n    }\r\n  }\r\n}\r\n\r\n.bottom-panel {\r\n  background-color: #ffffff;\r\n  border-radius: 2px;\r\n  text-align: center;\r\n  display: flex;\r\n  border-top: 1px solid #dcdfe6;\r\n  padding-top: 10px;\r\n}\r\n\r\n.device-channel-container {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.device-status-tag {\r\n  flex-shrink: 0;\r\n  width: 110px;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: none !important;\r\n}\r\n\r\n.status-icon {\r\n  font-size: 14px;\r\n}\r\n\r\n.status-text {\r\n  font-size: 12px;\r\n}\r\n\r\n:deep(.el-form-item:last-child) {\r\n  margin-bottom: 0;\r\n}\r\n\r\n:deep(.el-tag__content) {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  transition: none !important;\r\n}\r\n</style>\r\n", "import axios, { AxiosResponse } from 'axios'\r\nimport { USE_MOCK, mockApi } from '@/mock/mockApi'\r\n\r\n// 定义设备通道数据结构\r\nexport interface DeviceChannel {\r\n  name: string;\r\n  communicationType: string;\r\n  isConnected: boolean;\r\n}\r\n\r\n// 定义CAN配置数据结构\r\nexport interface CanConfig {\r\n  deviceChannelName: string;\r\n  dataBitrate: number;\r\n}\r\n\r\n// 定义CANFD配置数据结构\r\nexport interface CanFdConfig {\r\n  deviceChannelName: string;\r\n  arbitrationBitrate: number;\r\n  dataBitrate: number;\r\n}\r\n\r\n// 定义测试计划配置数据结构\r\nexport interface TestPlanConfig {\r\n  communicationType: string;\r\n  canConfig?: CanConfig;\r\n  canFdConfig?: CanFdConfig;\r\n}\r\n\r\n// 定义硬件配置数据结构\r\nexport interface HardwareConfig {\r\n  deviceChannels: DeviceChannel[];\r\n  testPlanConfig: TestPlanConfig;\r\n}\r\n\r\n// 定义配置选项数据结构\r\nexport interface HardwareOptions {\r\n  canBaudRates: number[];\r\n  canFdArbitrationBaudRates: number[];\r\n  canFdDataBaudRates: number[];\r\n}\r\n\r\nconst BASE_URL = '/api/hardwareConfig'\r\n\r\nexport const hardwareApi = {\r\n  // 获取硬件配置\r\n  getHardwareConfig: (): Promise<AxiosResponse<HardwareConfig>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.hardware.getHardwareConfig();\r\n    }\r\n    return axios.get(BASE_URL);\r\n  },\r\n\r\n  // 更新硬件配置\r\n  updateHardwareConfig: (config: TestPlanConfig): Promise<AxiosResponse<HardwareConfig>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.hardware.updateHardwareConfig(config);\r\n    }\r\n    return axios.post(`${BASE_URL}/update`, config);\r\n  }\r\n}\r\n\r\nexport default hardwareApi\r\n", "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createBlock as _createBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"device-option\" }\nconst _hoisted_2 = { class: \"device-name\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n  const _component_el_option = _resolveComponent(\"el-option\")!\n  const _component_el_select = _resolveComponent(\"el-select\")!\n\n  return (_openBlock(), _createBlock(_component_el_select, {\n    modelValue: _ctx.selectedValue,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((_ctx.selectedValue) = $event)),\n    placeholder: _ctx.placeholder,\n    class: \"device-channel-select\",\n    \"popper-class\": 'device-channel-select-dropdown',\n    onChange: _ctx.handleChange\n  }, {\n    default: _withCtx(() => [\n      (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.devices, (device) => {\n        return (_openBlock(), _createBlock(_component_el_option, {\n          key: device.name,\n          label: device.name,\n          value: device.name,\n          disabled: !device.isConnected\n        }, {\n          default: _withCtx(() => [\n            _createElementVNode(\"div\", _hoisted_1, [\n              _createElementVNode(\"span\", _hoisted_2, _toDisplayString(device.name), 1),\n              _createVNode(_component_el_tag, {\n                size: \"small\",\n                type: device.isConnected ? 'success' : 'danger'\n              }, {\n                default: _withCtx(() => [\n                  _createTextVNode(_toDisplayString(device.isConnected ? 'Connected' : 'Disconnected'), 1)\n                ]),\n                _: 2\n              }, 1032, [\"type\"])\n            ])\n          ]),\n          _: 2\n        }, 1032, [\"label\", \"value\", \"disabled\"]))\n      }), 128))\n    ]),\n    _: 1\n  }, 8, [\"modelValue\", \"placeholder\", \"onChange\"]))\n}", "<template>\r\n  <el-select \r\n    v-model=\"selectedValue\" \r\n    :placeholder=\"placeholder\" \r\n    class=\"device-channel-select\"\r\n    :popper-class=\"'device-channel-select-dropdown'\"\r\n    @change=\"handleChange\">\r\n    <el-option v-for=\"device in devices\" :key=\"device.name\" :label=\"device.name\" :value=\"device.name\"\r\n      :disabled=\"!device.isConnected\">\r\n      <div class=\"device-option\">\r\n        <span class=\"device-name\">{{ device.name }}</span>\r\n        <el-tag size=\"small\" :type=\"device.isConnected ? 'success' : 'danger'\">\r\n          {{ device.isConnected ? 'Connected' : 'Disconnected' }}\r\n        </el-tag>\r\n      </div>\r\n    </el-option>\r\n  </el-select>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { defineComponent, computed } from 'vue';\r\n\r\nexport default defineComponent({\r\n  name: 'DeviceChannelSelect',\r\n  \r\n  props: {\r\n    modelValue: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    devices: {\r\n      type: Array,\r\n      required: true,\r\n      default: () => []\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: 'Select Device Channel'\r\n    }\r\n  },\r\n\r\n  emits: ['update:modelValue', 'change'],\r\n\r\n  setup(props, { emit }) {\r\n    const selectedValue = computed({\r\n      get: () => props.modelValue,\r\n      set: (value) => emit('update:modelValue', value)\r\n    });\r\n\r\n    const handleChange = (value: string) => {\r\n      emit('change', value);\r\n    };\r\n\r\n    return {\r\n      selectedValue,\r\n      handleChange\r\n    };\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.device-channel-select {\r\n  width: 100%;\r\n}\r\n\r\n:global(.device-channel-select-dropdown) {\r\n  min-width: 400px !important;\r\n}\r\n\r\n.device-option {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: 8px;\r\n}\r\n\r\n.device-name {\r\n  font-weight: 500;\r\n  flex-grow: 1;\r\n}\r\n</style>\r\n", "import { render } from \"./DeviceChannelSelect.vue?vue&type=template&id=f26f9cc6&scoped=true&ts=true\"\nimport script from \"./DeviceChannelSelect.vue?vue&type=script&lang=ts\"\nexport * from \"./DeviceChannelSelect.vue?vue&type=script&lang=ts\"\n\nimport \"./DeviceChannelSelect.vue?vue&type=style&index=0&id=f26f9cc6&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-f26f9cc6\"]])\n\nexport default __exports__", "import { render } from \"./HardwareConfigPanel.vue?vue&type=template&id=602982e2&scoped=true&ts=true\"\nimport script from \"./HardwareConfigPanel.vue?vue&type=script&lang=ts\"\nexport * from \"./HardwareConfigPanel.vue?vue&type=script&lang=ts\"\n\nimport \"./HardwareConfigPanel.vue?vue&type=style&index=0&id=602982e2&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-602982e2\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { createVNode as _createVNode, resolveDirective as _resolveDirective, openBlock as _openBlock, createElementBlock as _createElementBlock, withDirectives as _withDirectives } from \"vue\"\n\nconst _hoisted_1 = { class: \"hardware-setting-container\" }\n\nimport { computed, onMounted } from 'vue';\r\nimport { testPlanService } from '@/services/testPlanService';\r\nimport HardwareConfigPanel from '@/components/hardware/HardwareConfigPanel.vue';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'HardwareSetting',\n  setup(__props) {\n\r\nconst state = testPlanService.getState();\r\nconst testPlan = computed(() => state.currentPlan);\r\nconst loading = computed(() => state.isLoading);\r\n\r\nconst loadCurrentPlan = async () => {\r\n  await testPlanService.getCurrentPlan();\r\n};\r\n\r\nonMounted(() => {\r\n  loadCurrentPlan();\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _directive_loading = _resolveDirective(\"loading\")!\n\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createVNode(HardwareConfigPanel, {\n      \"test-plan-id\": testPlan.value?.path\n    }, null, 8, [\"test-plan-id\"])\n  ])), [\n    [_directive_loading, loading.value]\n  ])\n}\n}\n\n})", "<template>\r\n  <div class=\"hardware-setting-container\" v-loading=\"loading\">\r\n    <!-- 硬件配置面板 -->\r\n    <hardware-config-panel :test-plan-id=\"testPlan?.path\" />\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, onMounted } from 'vue';\r\nimport { testPlanService } from '@/services/testPlanService';\r\nimport HardwareConfigPanel from '@/components/hardware/HardwareConfigPanel.vue';\r\n\r\nconst state = testPlanService.getState();\r\nconst testPlan = computed(() => state.currentPlan);\r\nconst loading = computed(() => state.isLoading);\r\n\r\nconst loadCurrentPlan = async () => {\r\n  await testPlanService.getCurrentPlan();\r\n};\r\n\r\nonMounted(() => {\r\n  loadCurrentPlan();\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.hardware-setting-container {\r\n  margin: 0 auto;\r\n  background-color: #ffffff;\r\n  border-radius: 8px;\r\n  flex: 1;\r\n  padding: 15px 20px;\r\n}\r\n\r\n.section-header h2 {\r\n  margin: 0;\r\n  font-size: 1.5rem;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n</style>\r\n", "import script from \"./HardwareSetting.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./HardwareSetting.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./HardwareSetting.vue?vue&type=style&index=0&id=01948689&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-01948689\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_el_radio", "_resolveComponent", "_component_el_radio_group", "_component_el_form_item", "_component_device_channel_select", "_component_el_icon", "_component_el_tag", "_component_el_option", "_component_el_select", "_component_el_form", "_component_el_button", "_directive_loading", "_resolveDirective", "_withDirectives", "_openBlock", "_createElementBlock", "_createElementVNode", "_createVNode", "model", "form", "default", "_withCtx", "label", "modelValue", "communicationType", "$event", "onChange", "handleCommunicationTypeChange", "_createTextVNode", "_", "_Fragment", "key", "canConfig", "deviceChannelName", "devices", "canDevices", "_createBlock", "updateTimestamp", "size", "type", "getDeviceConnectStatus", "isConnected", "_resolveDynamicComponent", "_toDisplayString", "dataBitrate", "placeholder", "style", "_renderList", "baudRates", "rate", "value", "canFdConfig", "canFdDevices", "arbitrationBitrate", "fdDataRates", "onClick", "handleSave", "loading", "isSaving", "isLoading", "BASE_URL", "hardwareApi", "getHardwareConfig", "USE_MOCK", "mockApi", "hardware", "axios", "get", "updateHardwareConfig", "config", "post", "selected<PERSON><PERSON><PERSON>", "handleChange", "device", "name", "disabled", "defineComponent", "props", "String", "Array", "required", "emits", "setup", "emit", "computed", "set", "__exports__", "components", "DeviceChannelSelect", "Connection", "CircleClose", "testPlanId", "ref", "Date", "now", "originalForm", "isRefreshing", "hardwareConfig", "deviceChannels", "lastDeviceChannels", "filter", "pollingTimer", "pollingActive", "handleValueChange", "async", "configToSave", "undefined", "JSON", "parse", "stringify", "ElMessage", "success", "error", "console", "loadHardwareConfig", "response", "newDeviceChannels", "data", "devicesChanged", "hasDeviceListChanged", "testPlanConfig", "newConfig", "config<PERSON><PERSON><PERSON>", "hasConfigChanged", "oldDevices", "newDevices", "length", "oldMap", "Map", "map", "d", "some", "oldConfig", "oldConfigStr", "newConfigStr", "e", "initHardwareOptions", "currentDevice", "find", "deviceName", "getDeviceLabel", "refreshDevices", "startDevicePolling", "setInterval", "stopDevicePolling", "clearInterval", "onMounted", "nextTick", "onUnmounted", "watch", "newVal", "_defineComponent", "__name", "__props", "state", "testPlanService", "getState", "testPlan", "currentPlan", "loadCurrentPlan", "getCurrentPlan", "HardwareConfigPanel", "path"], "sourceRoot": ""}