﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{B5BD0CC8-03EB-4319-8B67-0FA697B43553}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Alsi.App.Devices</RootNamespace>
    <AssemblyName>Alsi.App.Devices</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Interop.TsCANApi">
      <HintPath>3rd_party_libs\libTsCan\Interop.TsCANApi.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="vxlapi_NET, Version=20.30.14.0, Culture=neutral, PublicKeyToken=9b9ef2c94571ded1, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>x64\vector\vxlapi_NET.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AppExtensions.cs" />
    <Compile Include="Core\CommunicationType.cs" />
    <Compile Include="Core\CanFrame.cs" />
    <Compile Include="Core\DataBus.cs" />
    <Compile Include="Core\DataBusTimer.cs" />
    <Compile Include="Core\ChannelConfig.cs" />
    <Compile Include="Core\FrameMonitor.cs" />
    <Compile Include="Core\ICanChannel.cs" />
    <Compile Include="Core\TimestampUtils.cs" />
    <Compile Include="Core\Channels\TsLibCanChannel.cs" />
    <Compile Include="Core\Channels\VectorCanChannel.cs" />
    <Compile Include="Core\TransportLayer\DiagParams.cs" />
    <Compile Include="Core\TransportLayer\Frames\ConsecutiveFrame.cs" />
    <Compile Include="Core\TransportLayer\Frames\FirstFrame.cs" />
    <Compile Include="Core\TransportLayer\Frames\FlowControl.cs" />
    <Compile Include="Core\TransportLayer\Frames\FlowState.cs" />
    <Compile Include="Core\TransportLayer\Frames\FrameRecorder.cs" />
    <Compile Include="Core\TransportLayer\Frames\IDiagFrame.cs" />
    <Compile Include="Core\TransportLayer\Frames\MultipleFrame.cs" />
    <Compile Include="Core\TransportLayer\Frames\SingleFrame.cs" />
    <Compile Include="Core\TransportLayer\NoFlowControlException.cs" />
    <Compile Include="Core\TransportLayer\ResponseStore.cs" />
    <Compile Include="Core\TransportLayer\TpService.SendMultipleFrame.cs" />
    <Compile Include="Core\TransportLayer\TpService.SendSingleFrame.cs" />
    <Compile Include="Core\TransportLayer\TpService.cs" />
    <Compile Include="Core\TransportLayer\Request.cs" />
    <Compile Include="Core\TransportLayer\Response.cs" />
    <Compile Include="Core\TransportLayer\TpContext.cs" />
    <Compile Include="DeviceMidware.cs" />
    <Compile Include="Core\DeviceChannel.cs" />
    <Compile Include="DeviceEnv.cs" />
    <Compile Include="Log\BlfLogReader.cs" />
    <Compile Include="Log\BlfLogWriter.cs" />
    <Compile Include="Log\BlfStructs.cs" />
    <Compile Include="TsLibCan\EncapsulationLibTsCan.cs" />
    <Compile Include="Vector\EncapsulationVectorCan.cs" />
    <Compile Include="Core\Manufacturer.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="TsLibCan\TosunConsts.cs" />
    <Compile Include="TsLibCan\TsLibApi.cs" />
    <Compile Include="TsLibCan\TsLibCanChannelInfo.cs" />
    <Compile Include="Vector\MemoryProtector.cs" />
    <Compile Include="Vector\VectorDeviceApi.cs" />
    <Compile Include="Vector\VectorInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.App\Alsi.App.csproj">
      <Project>{2bf46d86-9704-494a-8998-a478b601df80}</Project>
      <Name>Alsi.App</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.Common.Utils\Alsi.Common.Utils.csproj">
      <Project>{f6626691-d7f8-483c-9423-ed1c6e9214f7}</Project>
      <Name>Alsi.Common.Utils</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="x64\libTsCan\Interop.TsCANApi.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="x64\libTsCan\libTSCAN.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="x64\libTsCan\libTSH.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="x64\vector\vxlapi.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="x64\vector\vxlapi64.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="x64\vector\vxlapi_NET.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="x64\blf\binlog.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="x64\blf\OperateBlf.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>