using Alsi.App;
using Alsi.App.Database.Midwares;
using Alsi.App.Devices.Core;
using Alsi.Fuzz.Core.Service.Results;
using Alsi.Fuzz.Core.Storage;
using Shouldly;

namespace Alsi.Fuzz.UnitTests.Results
{
    public class TestResultServiceTests : UnitTestBase
    {
        private readonly string _testDbPath;
        private readonly DbContext _dbContext;
        private readonly TestResultWriterService _writerService;
        private readonly TestResultReaderService _readerService;
        private readonly string _testFolderName;
        private readonly TestResult _testResult;

        public TestResultServiceTests()
        {
            // 确保数据文件夹存在
            Directory.CreateDirectory(Path.Combine(UnitTestAppContext.WebHostApp.DataFolder, "case_results"));
            Directory.CreateDirectory(Path.Combine(UnitTestAppContext.WebHostApp.DataFolder, "interoperation"));

            // 创建临时数据库
            _testDbPath = Path.Combine(Path.GetTempPath(), $"test_results_{Guid.NewGuid():N}.db");
            _dbContext = new DbContext(_testDbPath);
            _dbContext.Initialize();
            _dbContext.FreeSql.CodeFirst.SyncStructure<TestResult>();
            _dbContext.FreeSql.CodeFirst.SyncStructure<CaseResult>();
            _dbContext.FreeSql.CodeFirst.SyncStructure<CaseStep>();

            // 创建服务实例
            _writerService = new TestResultWriterService();
            _readerService = new TestResultReaderService(_dbContext);

            // 生成唯一的测试文件夹名称
            var testPlanName = "mockPlan_" + Guid.NewGuid().ToString().Substring(0, 6);

            _testResult = _readerService.CreateTestResult(testPlanName, TestType.Case);
            _testFolderName = _testResult.ResultFolderName;
        }

        public override void Dispose()
        {
            _dbContext?.Dispose();

            // 清理测试数据库
            if (File.Exists(_testDbPath))
            {
                try
                {
                    File.Delete(_testDbPath);
                }
                catch
                {
                    // 忽略文件删除失败，可能文件被锁定
                }
            }

            // 清理测试结果文件夹
            string resultFolder = Path.Combine(AppEnv.WebHostApp.DataFolder, "case_results", _testFolderName);
            if (Directory.Exists(resultFolder))
            {
                try
                {
                    Directory.Delete(resultFolder, true);
                }
                catch
                {
                    // 忽略删除失败
                }
            }

            base.Dispose();
        }

        [Fact]
        public void Begin_ShouldCreateTestResult()
        {
            // Act
            _writerService.Begin(_testResult);
            _writerService.End();
            _readerService.UpdateTestResult(_testResult);

            // Assert
            var results = _readerService.GetTestResults();
            results.Length.ShouldBe(1);
            results[0].ResultFolderName.ShouldBe(_testFolderName);
        }

        [Fact]
        public void AddCaseStep_ShouldBeReadable()
        {
            // Arrange
            _writerService.Begin(_testResult);

            // 添加用例
            var results = _readerService.GetTestResults();
            var caseResult = new CaseResult
            {
                Parameter = "{ \"test\": \"withSteps\" }",
                State = ExecutionState.Running,
                Begin = DateTime.Now.AddMinutes(-1),
                TestResultId = results[0].Id
            };
            _writerService.AddOrUpdateCaseResult(caseResult);

            // 获取用例ID
            var caseResults = _readerService.GetCaseResults(results[0]);
            caseResult.Id = caseResults[0].Id;

            // Act - 添加步骤
            var step1 = new CaseStep
            {
                CaseResultId = caseResult.Id,
                State = CaseStepState.Success,
                Detail = "Step 1",
                Begin = DateTime.Now.AddSeconds(-10),
                End = DateTime.Now.AddSeconds(-5),
                Timestamp = 1000000
            };
            _writerService.AddCaseSteps(new[] { step1 });

            var step2 = new CaseStep
            {
                CaseResultId = caseResult.Id,
                State = CaseStepState.Failure,
                Detail = "Step 2",
                Begin = DateTime.Now.AddSeconds(-5),
                End = DateTime.Now,
                Timestamp = 2000000
            };
            _writerService.AddCaseSteps(new[] { step2 });

            // 更新用例状态
            caseResult.State = ExecutionState.Failure;
            caseResult.End = DateTime.Now;
            _writerService.AddOrUpdateCaseResult(caseResult);

            _writerService.End();

            // Assert
            var readResults = _readerService.GetTestResults();
            var readCaseResults = _readerService.GetCaseResults(readResults[0]);
            var steps = _readerService.GetCaseSteps(readCaseResults[0]);

            steps.Length.ShouldBe(2);
            steps[0].Detail.ShouldBe("Step 1");
            steps[0].State.ShouldBe(CaseStepState.Success);
            steps[1].Detail.ShouldBe("Step 2");
            steps[1].State.ShouldBe(CaseStepState.Failure);
        }

        [Fact]
        public void WriteFrame_ShouldBeReadable()
        {
            // Arrange
            _writerService.Begin(_testResult);

            var dataLogStorage = new DataLogStorage();
            dataLogStorage.Initialize(_writerService.ResultContext.DataLogPath, new HashSet<int>());

            // Act - 写入一些CAN帧
            var frame1 = new CanFrame
            {
                Channel = 1,
                Id = 0x100,
                IsTx = true,
                Data = new byte[] { 0x01, 0x02, 0x03, 0x04 },
                Dlc = 4,
                TimeUS = 1000000 // 1秒
            };
            dataLogStorage.WriteFrame(frame1);

            var frame2 = new CanFrame
            {
                Channel = 2,
                Id = 0x200,
                IsTx = false,
                IsCanFd = true,
                IsBrs = true,
                Data = new byte[] { 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88 },
                Dlc = 8,
                TimeUS = 2000000 // 2秒
            };
            dataLogStorage.WriteFrame(frame2);
            dataLogStorage.Close();

            _writerService.End();

            // Assert
            var results = _readerService.GetTestResults();
            var frames = _readerService.GetFrames(results[0]);

            frames.Length.ShouldBe(2);
            frames.Any(f => f.Id == 0x100).ShouldBeTrue();
            frames.Any(f => f.Id == 0x200).ShouldBeTrue();
        }
    }
}
