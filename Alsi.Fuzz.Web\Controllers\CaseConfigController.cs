using Alsi.App.Desktop.Utils;
using Alsi.Common.Parsers.Dbc;
using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service;
using Alsi.Fuzz.Core.Storage;
using Alsi.Fuzz.Web.Dto;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Http;

namespace Alsi.Fuzz.Web.Controllers
{
    public class CaseConfigFromDbc
    {
        public WhiteListFrame[] WhiteListFrames { get; set; } = Array.Empty<WhiteListFrame>();
        public string[] NodeNames { get; set; } = Array.Empty<string>();
    }

    public class CaseConfigController : WebControllerBase
    {
        private TestPlanService _testPlanService;

        public CaseConfigController()
        {
            var historyService = new TestPlanHistoryService();
            _testPlanService = new TestPlanService(new TestPlanStorage(), historyService);
        }

        [HttpGet]
        [Route("api/caseconfig/import-dbc")]
        public IHttpActionResult ImportDbc()
        {
            if (!UiUtils.SelectFile(out var filePath, "Database", "*.dbc"))
            {
                return BadRequest("UserCanceled");
            }

            if (!filePath.EndsWith(".dbc"))
            {
                return BadRequest("InvalidFileFormat");
            }

            var dbcModel = DbcParser.Parse(filePath);
            var caseConfigFromDbc = new CaseConfigFromDbc();
            caseConfigFromDbc.WhiteListFrames = dbcModel.Messages
                .Select(x => new WhiteListFrame(
                    x.ID,
                    x.Name,
                    (byte)x.DLC,
                    x.Transmitter,
                    x.Signals.SelectMany(signal => signal.Receivers).Distinct().OrderBy(receiver => receiver).ToArray(),
                    x.Signals.Select(signal => new WhiteListFrameSignal(signal.Name, signal.StartBit, signal.Offset, signal.Length)).ToArray()))
                .OrderBy(x => x.Id)
                .ToArray();
            caseConfigFromDbc.NodeNames = dbcModel.Nodes.Select(x => x.Name).OrderBy(x => x).ToArray();

            return Ok(caseConfigFromDbc);
        }

        [HttpGet]
        public IHttpActionResult GetCaseConfig()
        {
            var testPlan = TestPlanManager.Instance.GetCurrentPlan();
            if (testPlan == null)
            {
                return NotFound();
            }

            // 构建DTO
            var dto = Mapper.Map<CaseConfigDto>(testPlan.Config.CaseConfig);

            // 填充安全配置信息 - 直接从TestPlanConfig.SecurityConfig获取
            var securityConfig = testPlan.Config.SecurityConfig;
            if (securityConfig != null && securityConfig.DllBytes != null && securityConfig.DllBytes.Length > 0)
            {
                dto.SecurityInfo = new SecurityConfigInfoDto
                {
                    HasDll = true,
                    DllFileName = securityConfig.DllFileName,
                    DllSize = securityConfig.DllBytes.Length
                };
            }
            else
            {
                dto.SecurityInfo = new SecurityConfigInfoDto
                {
                    HasDll = false
                };
            }

            return Ok(dto);
        }

        [HttpPost]
        [ActionName("update")]
        public async Task<IHttpActionResult> UpdateCaseConfig([FromBody] CaseConfigDto dto)
        {
            var currentPlan = TestPlanManager.Instance.GetCurrentPlan();
            if (currentPlan == null)
            {
                return NotFound();
            }

            var path = TestPlanManager.Instance.GetCurrentPlanPath();
            if (string.IsNullOrEmpty(path))
            {
                return BadRequest("当前没有打开的测试计划");
            }

            // 1. 映射基本属性到CaseConfig
            var caseConfig = Mapper.Map<CaseConfig>(dto);

            // 2. 更新基本配置
            var updatedTestPlan = await _testPlanService.UpdateCaseConfigAsync(path, caseConfig);

            // 3. 处理安全配置
            if (dto.RemoveSecurityDll)
            {
                // 清空SecurityConfig
                var emptySecurityConfig = new SecurityConfig
                {
                    DllFileName = string.Empty,
                    DllBytes = Array.Empty<byte>()
                };
                updatedTestPlan = await _testPlanService.UpdateSecurityConfigAsync(path, emptySecurityConfig);
            }
            else if (!string.IsNullOrEmpty(dto.SecurityDllPath))
            {
                // 读取DLL并更新SecurityConfig
                try
                {
                    byte[] dllBytes = File.ReadAllBytes(dto.SecurityDllPath);
                    string dllFileName = Path.GetFileName(dto.SecurityDllPath);

                    var securityConfig = new SecurityConfig
                    {
                        DllFileName = dllFileName,
                        DllBytes = dllBytes
                    };

                    updatedTestPlan = await _testPlanService.UpdateSecurityConfigAsync(path, securityConfig);
                }
                catch (Exception ex)
                {
                    return InternalServerError(ex);
                }
            }

            // 更新内存中的配置
            currentPlan.Config.CaseConfig = updatedTestPlan.Config.CaseConfig;
            if (dto.RemoveSecurityDll || !string.IsNullOrEmpty(dto.SecurityDllPath))
            {
                currentPlan.Config.SecurityConfig = updatedTestPlan.Config.SecurityConfig;
            }

            // 返回更新后的配置
            return GetCaseConfig();
        }

        [HttpPost]
        [ActionName("select-security-dll")]
        public IHttpActionResult SelectSecurityDllPath()
        {
            // 显示文件选择对话框并返回路径（不读取文件内容）
            if (!UiUtils.SelectFile(out var path, "Security DLL", "*.dll"))
            {
                return BadRequest("UserCanceled");
            }

            if (!path.EndsWith(".dll", StringComparison.OrdinalIgnoreCase))
            {
                return BadRequest("InvalidFileFormat");
            }

            return Ok(new { path });
        }
    }
}
