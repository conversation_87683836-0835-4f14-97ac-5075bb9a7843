<template>
  <div class="hardware-setting-container" v-loading="loading">
    <!-- 硬件配置面板 -->
    <hardware-config-panel :test-plan-id="testPlan?.path" />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { testPlanService } from '@/services/testPlanService';
import HardwareConfigPanel from '@/components/hardware/HardwareConfigPanel.vue';

const state = testPlanService.getState();
const testPlan = computed(() => state.currentPlan);
const loading = computed(() => state.isLoading);

const loadCurrentPlan = async () => {
  await testPlanService.getCurrentPlan();
};

onMounted(() => {
  loadCurrentPlan();
});
</script>

<style scoped>
.hardware-setting-container {
  margin: 0 auto;
  background-color: #ffffff;
  border-radius: 8px;
  flex: 1;
  padding: 15px 20px;
}

.section-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
  color: #303133;
}
</style>
