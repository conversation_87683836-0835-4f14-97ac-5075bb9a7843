using Alsi.App.Devices.Core;
using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service.Interoperation;
using System;

namespace Alsi.Fuzz.Core.Service
{
    public class TestPlanManager : IDisposable
    {
        private static TestPlanManager _instance;
        private static readonly object _lock = new object();

        private TestPlan _currentPlan;
        private string _currentPlanPath = string.Empty;

        // 设备扫描相关字段
        private readonly DeviceScanner _deviceScanner;
        private DeviceChannel[] _cachedDeviceChannels = Array.Empty<DeviceChannel>();
        private bool _isDisposed;

        public InteroperationService Interoperation { get; private set; }

        public static TestPlanManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new TestPlanManager();
                        }
                    }
                }
                return _instance;
            }
        }

        private TestPlanManager()
        {
            _deviceScanner = new DeviceScanner(TimeSpan.FromSeconds(2));
            _deviceScanner.DevicesScanned += OnDevicesScanned;
        }

        public void SaveCaseCollection(CaseCollection caseCollection)
        {
            var testPlanService = new TestPlanService(new Storage.TestPlanStorage(), new TestPlanHistoryService());
            testPlanService.SaveCaseCollection(_currentPlanPath, caseCollection);
        }

        public CaseCollection LoadCaseCollection()
        {
            var testPlanService = new TestPlanService(new Storage.TestPlanStorage(), new TestPlanHistoryService());
            return testPlanService.LoadCaseCollection(_currentPlanPath);
        }

        #region 测试计划管理
        public void SetCurrentPlan(TestPlan plan, string path)
        {
            if (!path.Equals(_currentPlanPath, StringComparison.OrdinalIgnoreCase))
            {
                Interoperation = new InteroperationService();
            }

            _currentPlan = plan;
            _currentPlanPath = path;

            TestPlanFileLocker.LockFile(_currentPlanPath);

            // 当打开测试计划时，启动设备扫描
            StartDeviceScanning();
        }

        public TestPlan GetCurrentPlan() => _currentPlan;
        public string GetCurrentPlanPath() => _currentPlanPath;

        public void ClearCurrentPlan()
        {
            TestPlanFileLocker.UnlockFile();

            _currentPlan = null;
            _currentPlanPath = null;

            // 当关闭测试计划时，停止设备扫描
            StopDeviceScanning();
        }
        #endregion

        #region 设备扫描管理
        /// <summary>
        /// 获取缓存的设备通道列表
        /// </summary>
        public DeviceChannel[] GetCachedDeviceChannels()
        {
            return _cachedDeviceChannels;
        }

        /// <summary>
        /// 启动设备扫描
        /// </summary>
        public void StartDeviceScanning()
        {
            _deviceScanner.Start();
        }

        /// <summary>
        /// 停止设备扫描
        /// </summary>
        public void StopDeviceScanning()
        {
            _deviceScanner.Stop();
        }

        /// <summary>
        /// 设备扫描完成事件处理
        /// </summary>
        private void OnDevicesScanned(object sender, DevicesScannedEventArgs e)
        {
            _cachedDeviceChannels = e.DeviceChannels;
        }

        /// <summary>
        /// 设备扫描状态变化事件
        /// </summary>
        public event EventHandler<DevicesScannedEventArgs> DevicesScanned
        {
            add { _deviceScanner.DevicesScanned += value; }
            remove { _deviceScanner.DevicesScanned -= value; }
        }
        #endregion

        #region IDisposable Support
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {
                    // 释放托管资源
                    _deviceScanner.DevicesScanned -= OnDevicesScanned;
                    _deviceScanner.Dispose();
                }

                _isDisposed = true;
            }
        }
        #endregion
    }

    /// <summary>
    /// 设备扫描完成事件参数
    /// </summary>
    public class DevicesScannedEventArgs : EventArgs
    {
        public DeviceChannel[] DeviceChannels { get; }

        public DevicesScannedEventArgs(DeviceChannel[] deviceChannels)
        {
            DeviceChannels = deviceChannels ?? Array.Empty<DeviceChannel>();
        }
    }
}
