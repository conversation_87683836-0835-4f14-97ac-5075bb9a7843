using Alsi.Fuzz.Core.Utils;
using Shouldly;

namespace Alsi.Fuzz.UnitTests.Utils
{
    public class LhsSamplerTests
    {
        [Fact]
        public void GenerateBitBasedSamplesTest()
        {
            var length = 2;
            var frames = new LhsSampler(0).GenerateBitBasedSamples(length, length * 8);
            frames.Count.ShouldBe(length * 8);
        }

        [Fact]
        public void GenerateByteBasedSamplesTest()
        {
            var length = 2;
            var frames = new LhsSampler(0).GenerateByteBasedSamples(length);
            frames.Count.ShouldBe(256);
        }
    }
}
