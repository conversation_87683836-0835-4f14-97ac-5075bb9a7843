<template>
  <div class="app-container">
    <TopMenuBar />
    <div class="main-content">
      <main class="content">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup>
import TopMenuBar from '@/components/layout/TopMenuBar.vue'
</script>

<style lang="scss">
#app {
  color: var(--el-text-color-primary);
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  position: relative;
}

.content {
  flex: 1;
  background-color: var(--el-bg-color);
  overflow: hidden;
}

body {
  margin: 0;
  padding: 0;
  color: var(--el-text-color-primary);
  background-color: var(--el-bg-color);
}
</style>
