using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service;
using Alsi.Fuzz.Core.Storage;
using Shouldly;

namespace Alsi.Fuzz.UnitTests.TestPlans
{
    public class SequenceConfigTests : UnitTestBase
    {
        private readonly TestPlanService _service;
        private readonly ITestPlanHistoryService _historyService;
        private readonly string _testDir;

        private const string TempFolderName = "AlsiFuzzTests";
        private const string TestSuiteName = "CAN-Bus Test Suite";
        private const string BuiltInPackageName = "CAN Frame examples";
        private const string CustomPackageName = "My Custom Package";
        private const string TestXml = @"<package name=""Test Package"">
  <sequence name=""test-sequence"">
    <send name=""test-send"" />
  </sequence>
</package>";

        public SequenceConfigTests() : base()
        {
            _historyService = new TestPlanHistoryService(AppDbContext);
            _service = new TestPlanService(new TestPlanStorage(), _historyService);
            _testDir = Path.Combine(Path.GetTempPath(), TempFolderName, Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDir);
        }

        [Fact]
        public async Task UpdateSequenceConfig_WithBuiltInPackage_ShouldCreateBuiltInConfig()
        {
            // Arrange
            var plan = await _service.CreateAsync("Built-in package test");
            var path = Path.Combine(_testDir, "builtin_test.fzp");
            await _service.SaveAsync(plan, path);

            var request = new SequenceConfigDto
            {
                TestSuiteName = TestSuiteName,
                SequencePackageName = BuiltInPackageName,
                IsBuiltIn = true,
                SequencePackageXml = null // 内建包不存储XML
            };

            // Act
            var updatedPlan = await _service.SequenceService.UpdateSequenceConfigAsync(path, request);

            // Assert
            updatedPlan.ShouldNotBeNull();
            var config = updatedPlan.Config.SequenceConfigList.FirstOrDefault();
            config.ShouldNotBeNull();
            config.TestSuiteName.ShouldBe(TestSuiteName);
            config.SequencePackageName.ShouldBe(BuiltInPackageName);
            config.IsBuiltIn.ShouldBeTrue();
            config.IsSelected.ShouldBeTrue();
            config.SequencePackageXml.ShouldBeNull(); // 内建包不存储XML
            config.CustomName.ShouldBeNull();
        }

        [Fact]
        public async Task UpdateSequenceConfig_WithCustomPackage_ShouldCreateCustomConfig()
        {
            // Arrange
            var plan = await _service.CreateAsync("Custom package test");
            var path = Path.Combine(_testDir, "custom_test.fzp");
            await _service.SaveAsync(plan, path);

            var request = new SequenceConfigDto
            {
                TestSuiteName = TestSuiteName,
                SequencePackageName = BuiltInPackageName,
                IsBuiltIn = false,
                CustomName = CustomPackageName,
                BasePackageName = BuiltInPackageName,
                SequencePackageXml = TestXml
            };

            // Act
            var updatedPlan = await _service.SequenceService.UpdateSequenceConfigAsync(path, request);

            // Assert
            updatedPlan.ShouldNotBeNull();
            var config = updatedPlan.Config.SequenceConfigList.FirstOrDefault();
            config.ShouldNotBeNull();
            config.TestSuiteName.ShouldBe(TestSuiteName);
            config.SequencePackageName.ShouldBe(BuiltInPackageName);
            config.IsBuiltIn.ShouldBeFalse();
            config.IsSelected.ShouldBeTrue();
            config.CustomName.ShouldBe(CustomPackageName);
            config.BasePackageName.ShouldBe(BuiltInPackageName);
            config.SequencePackageXml.ShouldBe(TestXml);
        }

        [Fact]
        public async Task UpdateSequenceConfig_WithCustomPackageNameConflict_ShouldThrow()
        {
            // Arrange
            var plan = await _service.CreateAsync("Name conflict test");
            var path = Path.Combine(_testDir, "conflict_test.fzp");
            await _service.SaveAsync(plan, path);

            var request = new SequenceConfigDto
            {
                TestSuiteName = TestSuiteName,
                SequencePackageName = "CAN Frame sequences (ISO 11898-1)",
                IsBuiltIn = false,
                CustomName = "CAN Frame sequences (ISO 11898-1)", // 与内建包名冲突
                SequencePackageXml = TestXml
            };

            // Act & Assert
            await Should.ThrowAsync<InvalidOperationException>(() =>
                _service.SequenceService.UpdateSequenceConfigAsync(path, request));
        }

        [Fact]
        public async Task UpdateSequenceConfig_WithInvalidXml_ShouldThrow()
        {
            // Arrange
            var plan = await _service.CreateAsync("Invalid XML test");
            var path = Path.Combine(_testDir, "invalid_xml_test.fzp");
            await _service.SaveAsync(plan, path);

            var request = new SequenceConfigDto
            {
                TestSuiteName = TestSuiteName,
                SequencePackageName = BuiltInPackageName,
                IsBuiltIn = false,
                CustomName = CustomPackageName,
                SequencePackageXml = "<invalid>xml" // 无效的XML
            };

            // Act & Assert
            await Should.ThrowAsync<InvalidOperationException>(() =>
                _service.SequenceService.UpdateSequenceConfigAsync(path, request));
        }

        [Fact]
        public async Task UpdateSequenceConfig_UpdateExistingCustomPackage_ShouldUpdateXml()
        {
            // Arrange
            var plan = await _service.CreateAsync("Update custom package test");
            var path = Path.Combine(_testDir, "update_custom_test.fzp");
            await _service.SaveAsync(plan, path);

            // 先创建一个自定义包
            var createRequest = new SequenceConfigDto
            {
                TestSuiteName = TestSuiteName,
                SequencePackageName = BuiltInPackageName,
                IsBuiltIn = false,
                CustomName = CustomPackageName,
                BasePackageName = BuiltInPackageName,
                SequencePackageXml = TestXml
            };
            await _service.SequenceService.UpdateSequenceConfigAsync(path, createRequest);

            // 更新XML内容
            var updatedXml = @"<package name=""Updated Package"">
  <sequence name=""updated-sequence"">
    <send name=""updated-send"" />
    <recv name=""updated-recv"" />
  </sequence>
</package>";

            var updateRequest = new SequenceConfigDto
            {
                TestSuiteName = TestSuiteName,
                SequencePackageName = BuiltInPackageName,
                IsBuiltIn = false,
                CustomName = CustomPackageName,
                BasePackageName = BuiltInPackageName,
                SequencePackageXml = updatedXml
            };

            // Act
            var updatedPlan = await _service.SequenceService.UpdateSequenceConfigAsync(path, updateRequest);

            // Assert
            updatedPlan.ShouldNotBeNull();
            var config = updatedPlan.Config.SequenceConfigList.FirstOrDefault();
            config.ShouldNotBeNull();
            config.SequencePackageXml.ShouldBe(updatedXml);
        }

        [Fact]
        public async Task DeleteCustomSequencePackage_ShouldRemoveCustomPackage()
        {
            // Arrange
            var plan = await _service.CreateAsync("Delete custom package test");
            var path = Path.Combine(_testDir, "delete_custom_test.fzp");
            await _service.SaveAsync(plan, path);

            // 先创建一个自定义包
            var createRequest = new SequenceConfigDto
            {
                TestSuiteName = TestSuiteName,
                SequencePackageName = BuiltInPackageName,
                IsBuiltIn = false,
                CustomName = CustomPackageName,
                BasePackageName = BuiltInPackageName,
                SequencePackageXml = TestXml
            };
            await _service.SequenceService.UpdateSequenceConfigAsync(path, createRequest);

            // 验证包已创建
            var planWithCustom = await _service.LoadAsync(path);
            planWithCustom.Config.SequenceConfigList.Count.ShouldBe(1);

            // Act
            var updatedPlan = await _service.SequenceService.DeleteCustomSequencePackageAsync(path, TestSuiteName, CustomPackageName);

            // Assert
            updatedPlan.ShouldNotBeNull();
            updatedPlan.Config.SequenceConfigList.Count.ShouldBe(0);
        }

        [Fact]
        public async Task DeleteCustomSequencePackage_WithNonExistentPackage_ShouldThrow()
        {
            // Arrange
            var plan = await _service.CreateAsync("Delete non-existent package test");
            var path = Path.Combine(_testDir, "delete_nonexistent_test.fzp");
            await _service.SaveAsync(plan, path);

            // Act & Assert
            await Should.ThrowAsync<InvalidOperationException>(() =>
                _service.SequenceService.DeleteCustomSequencePackageAsync(path, TestSuiteName, "NonExistentPackage"));
        }

        public override void Dispose()
        {
            base.Dispose();

            if (Directory.Exists(_testDir))
            {
                if (_testDir.Contains(TempFolderName))
                {
                    Directory.Delete(_testDir, true);
                }
            }
        }
    }
}
