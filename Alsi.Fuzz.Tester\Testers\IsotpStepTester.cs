using Alsi.App.Devices.Core;
using Alsi.App.Devices.Core.TransportLayer;
using Alsi.App.Devices.Core.TransportLayer.Frames;
using Alsi.Common.Utils;
using Alsi.Common.Utils.Autosar;
using Alsi.Fuzz.Core.Models.Tester;
using Alsi.Fuzz.Core.Models.TestSuites.Steps.Isotp;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.Results;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Alsi.Fuzz.Tester.Testers
{
    public class IsotpStepTester
    {
        private void ExecuteSingleFrame(CaseContext caseContext, CanFrame singleFrame)
        {
            var responseId = caseContext.CaseConfig.ResponseId;
            var diagTimeoutMs = caseContext.CaseConfig.TimeoutMs;

            var responseMonitor = FrameMonitor.CreateIdMatcher(responseId, diagTimeoutMs);
            responseMonitor.StartListening();

            //AppEnv.Logger.Debug($"MultipleFrameSender -> Send: {singleFrame}");
            DataBus.Send(singleFrame);

            if (responseMonitor.WaitForMatch() && responseMonitor.LastMatchedFrame != null)
            {
                lastReceivedFrame = responseMonitor.LastMatchedFrame;
                return;
            }

            var diagRetryTester = new DiagRetryTester();
            diagRetryTester.Run(caseContext);
            retryResponse = diagRetryTester.RetryResponse;
        }

        private void ExecuteMultipleFrame(CaseContext caseContext, CanFrame firstFrame, CanFrame[] consecutiveFrames, bool waitFcInSendingCfs)
        {
            var responseId = caseContext.CaseConfig.ResponseId;
            var diagTimeoutMs = caseContext.CaseConfig.TimeoutMs;

            var mockFlowControl = FlowControl.Build(FlowState.ContinueToSend, 0, 10);
            var multipleFrameSender = new MultipleFrameSender(
                firstFrame,
                consecutiveFrames,
                responseId,
                diagTimeoutMs,
                waitFcInSendingCfs,
                () => mockFlowControl);

            var responseFrame = multipleFrameSender.Execute();
            if (responseFrame != null)
            {
                lastReceivedFrame = responseFrame;
                return;
            }

            var diagRetryTester = new DiagRetryTester();
            diagRetryTester.Run(caseContext);
            retryResponse = diagRetryTester.RetryResponse;
        }

        public Task SendIsotpAsync(SendIsotpStep sendIsotpStep, CaseStep caseStep, CaseContext caseContext)
        {
            var requestId = caseContext.CaseConfig.RequestId;
            var responseId = caseContext.CaseConfig.ResponseId;
            var reqeustIsExt = caseContext.CaseConfig.RequestIsExt;
            var diagTimeoutMs = caseContext.CaseConfig.TimeoutMs;
            var isCanFd = caseContext.HardwareConfig.CommunicationType == CommunicationType.CanFd;

            var caseMutation = CaseMutation.Deserialize(caseContext.CaseResult.Parameter);

            if (caseContext.TestResult.TestType == TestType.Interoperation)
            {
                var payload = sendIsotpStep.HexPayload?.Trim() ?? string.Empty;
                caseContext.EnvVars.Eval(ref payload);

                var bytes = SequenceUtils.ParseBytes(payload);

                var payloadList = new List<byte>();
                var repeat = 1;
                if (sendIsotpStep.RepeatPayload.HasValue && sendIsotpStep.RepeatPayload.Value > 0)
                {
                    repeat = sendIsotpStep.RepeatPayload.Value;
                }

                for (int i = 0; i < repeat; i++)
                {
                    payloadList.AddRange(bytes);
                }

                var payloadBytes = payloadList.ToArray();
                var payloadLength = payloadBytes.Length;

                const byte dlc = 8;

                if (payloadBytes.Length <= 6)
                {
                    var singleFrameBytes = new byte[dlc];
                    singleFrameBytes[0] = (byte)payloadBytes.Length;
                    for (var i = 0; i < payloadBytes.Length; i++)
                    {
                        singleFrameBytes[i + 1] = payloadBytes[i];
                    }

                    var singleFrame = new CanFrame
                    {
                        Id = requestId,
                        Data = singleFrameBytes,
                        IsTx = true,
                        Dlc = dlc,
                        IsCanFd = isCanFd,
                        IsExt = reqeustIsExt
                    };

                    ExecuteSingleFrame(caseContext, singleFrame);
                }
                else
                {
                    var interoperationCaseMutation = new CaseMutation()
                        .MutateTpFfHighNibble(1)
                        .MutateTpFfDl12b(payloadBytes.Length)
                        .MutateTpParameters(payloadList.ToArray());

                    var firstFrame = new CanFrame
                    {
                        Id = requestId,
                        Data = BuildFirstFrameData_FfDl12b(interoperationCaseMutation, isCanFd, out var ffLength12bValue),
                        IsTx = true,
                        Dlc = dlc,
                        IsCanFd = isCanFd,
                        IsExt = reqeustIsExt
                    };

                    var consecutiveFrames = BuildConsecutiveFrameDataList(interoperationCaseMutation, dlc, isCanFd, 6).Select(x => new CanFrame
                    {
                        Id = requestId,
                        Data = x,
                        IsTx = true,
                        Dlc = dlc,
                        IsCanFd = isCanFd,
                        IsExt = reqeustIsExt
                    }).ToArray();

                    ExecuteMultipleFrame(caseContext, firstFrame, consecutiveFrames, true);
                }

                return Task.CompletedTask;
            }
            else if (caseContext.TestResult.TestType == TestType.Case)
            {
                var dlcField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.Dlc);
                if (dlcField == null)
                {
                    throw new Exception("Can't find DLC mutation");
                }

                var dlc = SequenceUtils.ParseByte(dlcField.Value);
                var data = new byte[DlcUtils.GetDataLength(dlc)];

                var caseName = caseContext.CaseResult.Name;

                // 3-1、3-2：处理单帧发送
                if (caseName.StartsWith("G31") || caseName.StartsWith("G32"))
                {
                    var singleFrame = new CanFrame
                    {
                        Id = requestId,
                        Data = data,
                        IsTx = true,
                        Dlc = dlc,
                        IsCanFd = isCanFd,
                        IsExt = reqeustIsExt
                    };

                    var sfByte1 = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_SF_Byte1);
                    if (sfByte1 != null)
                    {
                        data[0] = SequenceUtils.ParseByte(sfByte1.Value);
                    }

                    if (data.Length == 64)
                    {
                        var sfByte2 = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_SF_Byte2);
                        if (sfByte2 != null)
                        {
                            data[1] = SequenceUtils.ParseByte(sfByte2.Value);
                        }
                    }

                    var tpParametersField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_Parameters);
                    if (tpParametersField != null)
                    {
                        var tpParameters = SequenceUtils.ParseBytes(tpParametersField.Value);
                        var parameterIndex = data.Length == 64 ? 2 : 1;
                        for (var i = 0; i < tpParameters.Length && (i + parameterIndex) < data.Length; i++)
                        {
                            data[i + parameterIndex] = tpParameters[i];
                        }
                    }

                    ExecuteSingleFrame(caseContext, singleFrame);
                    return Task.CompletedTask;
                }
                else if (caseName.StartsWith("G33"))
                {
                    var firstFrame = new CanFrame
                    {
                        Id = requestId,
                        Data = BuildFirstFrameData_FfDl12b(caseMutation, isCanFd, out var ffLength12bValue),
                        IsTx = true,
                        Dlc = dlc,
                        IsCanFd = isCanFd,
                        IsExt = reqeustIsExt
                    };

                    var isValid_FfLength12bValue = ffLength12bValue > 7;

                    var consecutiveFrames = BuildConsecutiveFrameDataList(caseMutation, dlc, isCanFd, 6).Select(x => new CanFrame
                    {
                        Id = requestId,
                        Data = x,
                        IsTx = true,
                        Dlc = dlc,
                        IsCanFd = isCanFd,
                        IsExt = reqeustIsExt
                    }).ToArray();

                    if (isValid_FfLength12bValue)
                    {
                        // 当FFDL_12bit取值为invalid时， 只需要发送单帧即可
                        ExecuteSingleFrame(caseContext, firstFrame);
                    }
                    else
                    {
                        ExecuteMultipleFrame(caseContext, firstFrame, consecutiveFrames, true);
                    }
                    return Task.CompletedTask;
                }
                else if (caseName.StartsWith("G34"))
                {
                    var firstFrame = new CanFrame
                    {
                        Id = requestId,
                        Data = BuildFirstFrameData_FfDl32b(caseMutation, isCanFd, out var ffLength32bValue),
                        IsTx = true,
                        Dlc = dlc,
                        IsCanFd = isCanFd,
                        IsExt = reqeustIsExt
                    };

                    var consecutiveFrames = BuildConsecutiveFrameDataList(caseMutation, dlc, isCanFd, 2).Select(x => new CanFrame
                    {
                        Id = requestId,
                        Data = x,
                        IsTx = true,
                        Dlc = dlc,
                        IsCanFd = isCanFd,
                        IsExt = reqeustIsExt
                    }).ToArray();

                    // #3-4-1 when User Check DUT MTU<4096
                    // 此时发送多帧CF时，不需要testWaitFor DUT回复的FlowControl。
                    // #3-4-2 when User unCheck DUT MTU<4096
                    // 此时发送多帧CF时，需要收DUT回复的FlowControl。（并且根据 STMIN及Blocksize规则）
                    var waitFcInSendingCfs = !caseContext.CaseConfig.IsDutMtuLessThan4096;
                    ExecuteMultipleFrame(caseContext, firstFrame, consecutiveFrames, waitFcInSendingCfs);
                    return Task.CompletedTask;
                }
                else if (caseName.StartsWith("G35"))
                {
                    var firstFrame = new CanFrame
                    {
                        Id = requestId,
                        Data = BuildFirstFrameData_FfDl12b(caseMutation, isCanFd, out var ffLength12bValue),
                        IsTx = true,
                        Dlc = dlc,
                        IsCanFd = isCanFd,
                        IsExt = reqeustIsExt
                    };

                    var consecutiveFrames = BuildConsecutiveFrameDataList(caseMutation, dlc, isCanFd, 6).Select(x => new CanFrame
                    {
                        Id = requestId,
                        Data = x,
                        IsTx = true,
                        Dlc = dlc,
                        IsCanFd = isCanFd,
                        IsExt = reqeustIsExt
                    }).ToArray();

                    var cfByte1Field = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_CF_BYTE1_LIST);
                    var cfByte1List = new byte[0];
                    if (cfByte1Field != null)
                    {
                        cfByte1List = SequenceUtils.ParseBytes(cfByte1Field.Value);
                    }

                    for (var i = 0; i < consecutiveFrames.Length && i < cfByte1List.Length; i++)
                    {
                        var cf = consecutiveFrames[i];
                        cf.Data[0] = cfByte1List[i];
                    }

                    ExecuteMultipleFrame(caseContext, firstFrame, consecutiveFrames, true);
                    return Task.CompletedTask;
                }
                else if (caseName.StartsWith("G36"))
                {
                    // FlowControl 错误注入，按照单帧发送即可
                    var fcFrame = new CanFrame
                    {
                        Id = requestId,
                        Data = new byte[0],
                        IsTx = true,
                        Dlc = dlc,
                        IsCanFd = isCanFd,
                        IsExt = reqeustIsExt
                    };

                    var cfByte1Field = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_FC);
                    if (cfByte1Field != null)
                    {
                        fcFrame.Data = SequenceUtils.ParseBytes(cfByte1Field.Value);
                    }

                    ExecuteSingleFrame(caseContext, fcFrame);
                    return Task.CompletedTask;
                }
                else if (caseName.StartsWith("G37"))
                {
                    // 0x40 = 64 = 6 + 7 * 8 + 2
                    var dataList = new List<byte[]>
                    {
                        new byte[8] { 0x10, 0x40, 0x50, 0x50, 0x50, 0x50, 0x50, 0x50 }
                    };
                    byte byte1 = 0x21;
                    for (var i = 0; i < 8; i++)
                    {
                        var fcData = new byte[8] { byte1, 0x50, 0x50, 0x50, 0x50, 0x50, 0x50, 0x50 };
                        dataList.Add(fcData);

                        byte1++;
                        if (byte1 > 0x2F)
                        {
                            byte1 = 0x20;
                        }
                    }
                    dataList.Add(new byte[8] { byte1, 0x50, 0x50, 0, 0, 0, 0, 0 });
                    var canFrames = dataList.Select(x => new CanFrame
                    {
                        Id = requestId,
                        Data = x,
                        IsTx = true,
                        Dlc = dlc,
                        IsCanFd = isCanFd,
                        IsExt = reqeustIsExt
                    }).ToList();

                    // Repeat 场景
                    var repeatIndexField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_Repeat_Index);
                    var repeatTimesField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_Repeat_Times);
                    if (repeatIndexField != null && repeatTimesField != null)
                    {
                        var index = SequenceUtils.ParseByte(repeatIndexField.Value);
                        var frame = canFrames[index];
                        var times = SequenceUtils.ParseByte(repeatTimesField.Value);
                        for (var i = 0; i < times; i++)
                        {
                            canFrames.Insert(index, frame);
                        }
                    }

                    // Skip 场景
                    var skipIndexField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_Skip_Index);
                    if (skipIndexField != null)
                    {
                        var index = SequenceUtils.ParseByte(skipIndexField.Value);
                        if (index >= 0 && index < canFrames.Count)
                        {
                            canFrames.RemoveAt(index);
                        }
                    }

                    // Swap 场景
                    var swapAField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_Swap_A);
                    var swapBField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_Swap_B);
                    if (swapAField != null && swapBField != null)
                    {
                        var indexA = SequenceUtils.ParseByte(swapAField.Value);
                        var indexB = SequenceUtils.ParseByte(swapBField.Value);
                        if (indexA >= 0 && indexA < canFrames.Count && indexB >= 0 && indexB < canFrames.Count)
                        {
                            var temp = canFrames[indexA];
                            canFrames[indexA] = canFrames[indexB];
                            canFrames[indexB] = temp;
                        }
                    }

                    // Reverse 场景
                    var reverseFromField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_Reverse_From);
                    var reverseToField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_Reverse_To);
                    if (reverseFromField != null && reverseToField != null)
                    {
                        var fromIndex = SequenceUtils.ParseByte(reverseFromField.Value);
                        var toIndex = SequenceUtils.ParseByte(reverseToField.Value);
                        if (fromIndex >= 0 && fromIndex < canFrames.Count && toIndex >= 0 && toIndex < canFrames.Count && fromIndex < toIndex)
                        {
                            // 反转指定范围内的帧
                            int reverseCount = (toIndex - fromIndex + 1) / 2;
                            for (int i = 0; i < reverseCount; i++)
                            {
                                var temp = canFrames[fromIndex + i];
                                canFrames[fromIndex + i] = canFrames[toIndex - i];
                                canFrames[toIndex - i] = temp;
                            }
                        }
                    }

                    var firstFrame = canFrames[0];
                    var consecutiveFrames = canFrames.Skip(1).ToArray();
                    ExecuteMultipleFrame(caseContext, firstFrame, consecutiveFrames, true);
                    return Task.CompletedTask;
                }
                else if (caseName.StartsWith("G41"))
                {
                    // 处理 G41 测试用例 - BigData 首帧和连续帧测试
                    var firstFrame = new CanFrame
                    {
                        Id = requestId,
                        Data = BuildBigDataFirstFrameData(caseMutation, isCanFd, out var ffLength12bValue),
                        IsTx = true,
                        Dlc = dlc,
                        IsCanFd = isCanFd,
                        IsExt = reqeustIsExt
                    };

                    // 构建连续帧
                    var consecutiveFrames = BuildBigDataConsecutiveFrameDataList(caseMutation, dlc, isCanFd).Select(x => new CanFrame
                    {
                        Id = requestId,
                        Data = x,
                        IsTx = true,
                        Dlc = dlc,
                        IsCanFd = isCanFd,
                        IsExt = reqeustIsExt
                    }).ToArray();

                    // 处理特定目标序列号的连续帧
                    var targetSNField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_BigData_CF_TargetSN);
                    var cfByte1Field = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_BigData_CF_Byte1);

                    if (targetSNField != null && cfByte1Field != null)
                    {
                        var targetSN = SequenceUtils.ParseInt(targetSNField.Value);
                        var cfByte1 = SequenceUtils.ParseByte(cfByte1Field.Value);

                        // 确保目标序列号在有效范围内
                        if (targetSN > 0 && targetSN <= consecutiveFrames.Length)
                        {
                            // 修改指定序列号的连续帧的第一个字节
                            consecutiveFrames[targetSN - 1].Data[0] = cfByte1;
                        }
                    }

                    ExecuteMultipleFrame(caseContext, firstFrame, consecutiveFrames, true);
                    return Task.CompletedTask;
                }
                else if (caseName.StartsWith("G42"))
                {
                    // 处理 G42 测试用例 - 数据异变场景测试

                    // 构建首帧
                    var firstFrame = new CanFrame
                    {
                        Id = requestId,
                        Data = BuildBigDataFirstFrameData(caseMutation, isCanFd, out var ffLength12bValue),
                        IsTx = true,
                        Dlc = dlc,
                        IsCanFd = isCanFd,
                        IsExt = reqeustIsExt
                    };

                    // 构建连续帧
                    var consecutiveFrames = BuildBigDataConsecutiveFrameDataList(caseMutation, dlc, isCanFd).Select(x => new CanFrame
                    {
                        Id = requestId,
                        Data = x,
                        IsTx = true,
                        Dlc = dlc,
                        IsCanFd = isCanFd,
                        IsExt = reqeustIsExt
                    }).ToArray();

                    // 创建所有帧的列表，包括首帧和连续帧
                    var allFrames = new List<CanFrame> { firstFrame };
                    allFrames.AddRange(consecutiveFrames);

                    // 应用数据异变逻辑

                    // 1. Repeat 场景
                    var repeatIndexField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_BigData_Repeat_Index);
                    var repeatTimesField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_BigData_Repeat_Times);
                    if (repeatIndexField != null && repeatTimesField != null)
                    {
                        ApplyRepeatScenario(allFrames, repeatIndexField.Value, repeatTimesField.Value);
                    }

                    // 2. Skip 场景
                    var skipIndexField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_BigData_Skip_Index);
                    if (skipIndexField != null)
                    {
                        ApplySkipScenario(allFrames, skipIndexField.Value);
                    }

                    // 3. Swap 场景
                    var swapAField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_BigData_Swap_A);
                    var swapBField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_BigData_Swap_B);
                    if (swapAField != null && swapBField != null)
                    {
                        ApplySwapScenario(allFrames, swapAField.Value, swapBField.Value);
                    }

                    // 4. Reverse 场景
                    var reverseFromField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_BigData_Reverse_From);
                    var reverseToField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_BigData_Reverse_To);
                    if (reverseFromField != null && reverseToField != null)
                    {
                        ApplyReverseScenario(allFrames, reverseFromField.Value, reverseToField.Value);
                    }

                    ExecuteMultipleFrame(caseContext, allFrames.First(), allFrames.Skip(1).ToArray(), true);
                    return Task.CompletedTask;
                }
            }

            throw new NotImplementedException("TODO: SendIsotpAsync");
        }

        private Response retryResponse;
        private CanFrame lastReceivedFrame;

        public Task RecvIsotpAsync(RecvIsotpStep recvIsotpStep, CaseStep caseStep, CaseContext caseContext)
        {
            if (caseContext.TestResult.TestType == TestType.Interoperation)
            {
                if (lastReceivedFrame != null)
                {
                    caseStep.State = CaseStepState.Success;
                    caseStep.Detail = $"Response: {lastReceivedFrame}";
                    return Task.CompletedTask;
                }

                var message = $"No response";

                caseStep.State = CaseStepState.Failure;
                caseStep.Detail = message;
                throw new Exception(message);
            }
            else if (caseContext.TestResult.TestType == TestType.Case)
            {
                if (lastReceivedFrame == null)
                {
                    // 当诊断没响应时，根据用户配置，发送其它诊断请求，并收到了诊断响应
                    if (retryResponse != null)
                    {
                        caseStep.State = CaseStepState.Success;
                        caseStep.Detail = $"No response, use [Retry] request and received: {retryResponse.Payload.ToHex()}";

                        return Task.CompletedTask;
                    }

                    var message = $"No response, expect frame with ID={caseContext.CaseConfig.ResponseId:X}";
                    caseStep.State = CaseStepState.Failure;
                    caseStep.Detail = message;
                    throw new Exception(message);
                }
                else
                {
                    var detail = BuildDetail(lastReceivedFrame);
                    caseStep.State = CaseStepState.Success;
                    caseStep.Detail = detail;
                }
            }

            return Task.CompletedTask;
        }

        private static string BuildDetail(CanFrame frame)
        {
            var byteCount = frame.Data.Length;
            var unit = byteCount < 2 ? "byte" : "bytes";
            var data = string.Empty;
            var maxDisplayCount = 10;
            if (byteCount > maxDisplayCount)
            {
                data = $"{frame.Data.Take(maxDisplayCount).ToHex()} ...";
            }
            else
            {
                data = $"{frame.Data.ToHex()}";
            }
            var dir = frame.IsTx ? "Tx" : "Rx";
            return $"Time={TimestampUtils.GetString(frame.TimeUS)}s, Id=0x{frame.Id.ToHex()}, Dir={dir}, DLC={frame.Dlc:X}, Length={byteCount} Data={data}";
        }

        private byte[] BuildFirstFrameData_FfDl32b(CaseMutation caseMutation, bool isCanFd, out uint ffLength32bValue)
        {
            ffLength32bValue = 0;

            var data = new byte[8];
            var byte1 = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_FF_Byte1);
            if (byte1 != null)
            {
                data[0] = SequenceUtils.ParseByte(byte1.Value);
            }

            var byte2 = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_FF_Byte2);
            if (byte2 != null)
            {
                data[1] = SequenceUtils.ParseByte(byte2.Value);
            }

            var ffDl32b = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_FF_DL_32b);
            if (ffDl32b != null)
            {
                ffLength32bValue = SequenceUtils.ParseUint(ffDl32b.Value);
                data[2] = (byte)((ffLength32bValue >> 24) & 0xFF);
                data[3] = (byte)((ffLength32bValue >> 16) & 0xFF);
                data[4] = (byte)((ffLength32bValue >> 8) & 0xFF);
                data[5] = (byte)((ffLength32bValue >> 0) & 0xFF);
            }

            var tpParametersField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_Parameters);
            if (tpParametersField == null)
            {
                throw new Exception($"The {nameof(MutationFieldType.TP_Parameters)} is empty");
            }

            var tpParameters = SequenceUtils.ParseBytes(tpParametersField.Value);
            var start = 6;
            for (var i = 0; i < tpParameters.Length && (i + start) < data.Length; i++)
            {
                data[i + start] = tpParameters[i];
            }
            return data;
        }

        private byte[] BuildFirstFrameData_FfDl12b(CaseMutation caseMutation, bool isCanFd, out int ffLength12bValue)
        {
            ffLength12bValue = 0;

            var data = new byte[8];
            var ffHighNibble = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_FF_HighNibble);
            if (ffHighNibble != null)
            {
                byte highNibbleValue = SequenceUtils.ParseByte(ffHighNibble.Value);
                // 清除高半字节，然后设置新值（保留低半字节不变）
                data[0] = (byte)((data[0] & 0x0F) | ((highNibbleValue << 4) & 0xF0));
            }

            var ffLength12b = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_FF_DL_12b);
            if (ffLength12b != null)
            {
                // 假设 ffLength12b.Value 是一个表示 12 位长度的值
                ffLength12bValue = SequenceUtils.ParseInt(ffLength12b.Value);

                // 确保长度不超过 12 位
                ffLength12bValue &= 0x0FFF;

                // 设置 byte 0 的低半字节（长度的高 4 位）
                data[0] = (byte)((data[0] & 0xF0) | ((ffLength12bValue >> 8) & 0x0F));

                // 设置 byte 1（长度的低 8 位）
                data[1] = (byte)(ffLength12bValue & 0xFF);
            }

            var tpParametersField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_Parameters);
            if (tpParametersField == null)
            {
                throw new Exception($"The {nameof(MutationFieldType.TP_Parameters)} is empty");
            }

            var tpParameters = SequenceUtils.ParseBytes(tpParametersField.Value);
            var start = 2;
            for (var i = 0; i < tpParameters.Length && (i + start) < data.Length; i++)
            {
                data[i + start] = tpParameters[i];
            }
            return data;
        }

        private List<byte[]> BuildConsecutiveFrameDataList(CaseMutation caseMutation, byte dlc, bool isCanFd, int ffPayloadLength)
        {
            var cfDataList = new List<byte[]>();
            var dataLength = DlcUtils.GetDataLength(dlc);

            // 获取 TP_Parameters 字段，用于计算连续帧数量和设置数据
            var tpParametersField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_Parameters);
            if (tpParametersField == null)
            {
                throw new Exception($"The {nameof(MutationFieldType.TP_Parameters)} is empty");
            }

            // 使用 TP_Parameters 的长度计算需要多少个连续帧
            var tpParameters = SequenceUtils.ParseBytes(tpParametersField.Value);

            tpParameters = tpParameters.Skip(ffPayloadLength).ToArray();
            int cfPayloadSize = 7;

            // 计算需要多少个连续帧
            int cfCount = (int)Math.Ceiling((double)tpParameters.Length / cfPayloadSize);
            cfCount = Math.Max(1, cfCount); // 至少需要一个连续帧

            // 创建连续帧
            for (int i = 0; i < cfCount; i++)
            {
                var cfData = new byte[dataLength];

                // 设置连续帧的第一个字节，格式为 0x2X，其中 X 是序列号 (0-F)
                cfData[0] = (byte)(0x20 | ((i + 1) & 0x0F));

                // 计算当前帧需要复制的参数长度
                int startIndex = i * cfPayloadSize;
                int length = Math.Min(cfPayloadSize, tpParameters.Length - startIndex);

                // 复制参数到连续帧
                for (int j = 0; j < length; j++)
                {
                    cfData[j + 1] = tpParameters[startIndex + j];
                }

                // 如果是最后一帧且有空闲字节，填充 0x55
                if (i == cfCount - 1)
                {
                    for (int j = length + 1; j < cfData.Length; j++)
                    {
                        cfData[j] = 0x55;
                    }
                }

                cfDataList.Add(cfData);
            }

            return cfDataList;
        }

        /// <summary>
        /// 构建 BigData 首帧数据
        /// </summary>
        private byte[] BuildBigDataFirstFrameData(CaseMutation caseMutation, bool isCanFd, out int ffLength12bValue)
        {
            ffLength12bValue = 0;

            var data = new byte[8];

            // 处理 HighNibble
            var ffHighNibble = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_BigData_FF_HighNibble);
            if (ffHighNibble != null)
            {
                byte highNibbleValue = SequenceUtils.ParseByte(ffHighNibble.Value);
                // 清除高半字节，然后设置新值（保留低半字节不变）
                data[0] = (byte)((data[0] & 0x0F) | ((highNibbleValue << 4) & 0xF0));
            }
            else
            {
                // 默认使用有效的 HighNibble 值 0x1
                data[0] = (byte)((data[0] & 0x0F) | 0x10);
            }

            // 处理 12 位数据长度
            var ffLength12b = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_BigData_FF_DL_12b);
            if (ffLength12b != null)
            {
                // 解析 12 位长度值
                ffLength12bValue = SequenceUtils.ParseInt(ffLength12b.Value);

                // 确保长度不超过 12 位
                ffLength12bValue &= 0x0FFF;

                // 设置 byte 0 的低半字节（长度的高 4 位）
                data[0] = (byte)((data[0] & 0xF0) | ((ffLength12bValue >> 8) & 0x0F));

                // 设置 byte 1（长度的低 8 位）
                data[1] = (byte)(ffLength12bValue & 0xFF);
            }
            else
            {
                // 默认使用 4095 作为数据长度
                ffLength12bValue = 4095;
                data[0] = (byte)((data[0] & 0xF0) | 0x0F);
                data[1] = 0xFF;
            }

            // 获取 TP_Parameters 字段，用于设置首帧的数据部分
            var tpParametersField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_Parameters);
            if (tpParametersField != null)
            {
                var tpParameters = SequenceUtils.ParseBytes(tpParametersField.Value);
                var start = 2; // 首帧数据从第 2 个字节开始
                for (var i = 0; i < tpParameters.Length && (i + start) < data.Length; i++)
                {
                    data[i + start] = tpParameters[i];
                }
            }

            return data;
        }

        /// <summary>
        /// 构建 BigData 连续帧数据列表
        /// </summary>
        private List<byte[]> BuildBigDataConsecutiveFrameDataList(CaseMutation caseMutation, byte dlc, bool isCanFd)
        {
            var cfDataList = new List<byte[]>();
            var dataLength = DlcUtils.GetDataLength(dlc);

            // 获取 TP_Parameters 字段，用于计算连续帧数量和设置数据
            var tpParametersField = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.TP_Parameters);
            if (tpParametersField == null)
            {
                throw new Exception($"The {nameof(MutationFieldType.TP_Parameters)} is empty");
            }

            // 使用 TP_Parameters 的长度计算需要多少个连续帧
            var tpParameters = SequenceUtils.ParseBytes(tpParametersField.Value);

            // 首帧数据长度为 6 字节（8字节总长 - 2字节首帧头）
            int ffPayloadLength = 6;
            tpParameters = tpParameters.Skip(ffPayloadLength).ToArray();

            // 连续帧每帧可以携带 7 字节数据（8字节总长 - 1字节连续帧头）
            int cfPayloadSize = 7;

            // 计算需要多少个连续帧
            int cfCount = (int)Math.Ceiling((double)tpParameters.Length / cfPayloadSize);
            cfCount = Math.Max(1, cfCount); // 至少需要一个连续帧

            // 创建连续帧
            for (int i = 0; i < cfCount; i++)
            {
                var cfData = new byte[dataLength];

                // 设置连续帧的第一个字节，格式为 0x2X，其中 X 是序列号 (0-F)
                cfData[0] = (byte)(0x20 | ((i + 1) & 0x0F));

                // 计算当前帧需要复制的参数长度
                int startIndex = i * cfPayloadSize;
                int length = Math.Min(cfPayloadSize, tpParameters.Length - startIndex);

                // 复制参数到连续帧
                for (int j = 0; j < length; j++)
                {
                    cfData[j + 1] = tpParameters[startIndex + j];
                }

                // 如果是最后一帧且有空闲字节，填充 0x55
                if (i == cfCount - 1)
                {
                    for (int j = length + 1; j < cfData.Length; j++)
                    {
                        cfData[j] = 0x55;
                    }
                }

                cfDataList.Add(cfData);
            }

            return cfDataList;
        }

        /// <summary>
        /// 应用 Repeat 场景
        /// </summary>
        private void ApplyRepeatScenario(List<CanFrame> frames, string indexValue, string timesValue)
        {
            int index = SequenceUtils.ParseInt(indexValue);
            int times = SequenceUtils.ParseInt(timesValue);

            // 确保索引在有效范围内
            if (index >= 0 && index < frames.Count)
            {
                var frameToRepeat = frames[index];

                // 在指定位置后插入重复的帧
                for (int i = 0; i < times; i++)
                {
                    // 创建帧的副本
                    var repeatedFrame = new CanFrame
                    {
                        Id = frameToRepeat.Id,
                        Data = frameToRepeat.Data.ToArray(), // 创建数据的副本
                        IsTx = frameToRepeat.IsTx,
                        Dlc = frameToRepeat.Dlc,
                        IsCanFd = frameToRepeat.IsCanFd,
                        IsExt = frameToRepeat.IsExt
                    };

                    frames.Insert(index + 1 + i, repeatedFrame);
                }
            }
        }

        /// <summary>
        /// 应用 Skip 场景
        /// </summary>
        private void ApplySkipScenario(List<CanFrame> frames, string indexValue)
        {
            int index = SequenceUtils.ParseInt(indexValue);

            // 确保索引在有效范围内
            if (index >= 0 && index < frames.Count)
            {
                frames.RemoveAt(index);
            }
        }

        /// <summary>
        /// 应用 Swap 场景
        /// </summary>
        private void ApplySwapScenario(List<CanFrame> frames, string indexAValue, string indexBValue)
        {
            int indexA = SequenceUtils.ParseInt(indexAValue);
            int indexB = SequenceUtils.ParseInt(indexBValue);

            // 确保两个索引都在有效范围内
            if (indexA >= 0 && indexA < frames.Count && indexB >= 0 && indexB < frames.Count)
            {
                var temp = frames[indexA];
                frames[indexA] = frames[indexB];
                frames[indexB] = temp;
            }
        }

        /// <summary>
        /// 应用 Reverse 场景
        /// </summary>
        private void ApplyReverseScenario(List<CanFrame> frames, string fromValue, string toValue)
        {
            int fromIndex = SequenceUtils.ParseInt(fromValue);
            int toIndex = SequenceUtils.ParseInt(toValue);

            // 确保索引在有效范围内且 fromIndex < toIndex
            if (fromIndex >= 0 && fromIndex < frames.Count && toIndex >= 0 && toIndex < frames.Count && fromIndex < toIndex)
            {
                // 反转指定范围内的帧
                int reverseCount = (toIndex - fromIndex + 1) / 2;
                for (int i = 0; i < reverseCount; i++)
                {
                    var temp = frames[fromIndex + i];
                    frames[fromIndex + i] = frames[toIndex - i];
                    frames[toIndex - i] = temp;
                }
            }
        }
    }
}
