"use strict";(self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[]).push([[673],{2971:function(e,t,s){s.d(t,{A:function(){return r}});var a=s(6768),l=s(4232),n=s(1021),u=(0,a.pM)({__name:"CaseStateTag",props:{state:{}},setup(e){const t=e,s=(0,a.EW)((()=>{switch(t.state){case n.si.Success:return"success";case n.si.Running:return"warning";case n.si.Failure:return"danger";case n.si.Pending:default:return"info"}})),u=e=>{switch(e){case n.si.Running:return"Running";case n.si.Pending:return"Not Run";case n.si.Success:return"Passed";case n.si.Failure:return"Failed";default:return"Unknown"}},i=(0,a.EW)((()=>u(t.state)));return(e,t)=>{const n=(0,a.g2)("el-tag");return(0,a.uX)(),(0,a.Wv)(n,{type:s.value,size:"small",style:{"min-width":"60px"}},{default:(0,a.k6)((()=>[(0,a.eW)((0,l.v_)(i.value),1)])),_:1},8,["type"])}}});const i=u;var r=i},3673:function(e,t,s){s.r(t),s.d(t,{default:function(){return $}});var a=s(6768),l=s(4232),n=s(144),u=s(1219),i=s(883),r=s(4373),c=s(3144),o=s(1021);function d(e){return e.processState===o.si.Success||e.processState===o.si.Failure}const v="/api/interoperation",p={startTest:()=>c.Xo?c.Z0.interoperation.startTest():r.A.post(`${v}/start`),stopTest:()=>c.Xo?c.Z0.interoperation.stopTest():r.A.post(`${v}/stop`),getStatus:()=>c.Xo?c.Z0.interoperation.getStatus():r.A.get(`${v}/status`)};var k=s(2971),m=s(7477),g=s(9857);const f={key:0,class:"test-monitor"},b={class:"status-area"},R={class:"compact-status"},_={class:"status-header-inline"},y={key:0,class:"stats-row"},L={class:"stat-item success"},C={class:"stat-item failure"},F={class:"stat-item total"};var w=(0,a.pM)({__name:"TestMonitor",props:{runStatus:{},visible:{type:Boolean}},setup(e){const t=e,s=(0,a.EW)((()=>t.runStatus.testResult?.totalCount||0)),u=(0,a.EW)((()=>(t.runStatus.testResult?.successCount||0)+(t.runStatus.testResult?.failureCount||0)));return(e,t)=>{const i=(0,a.g2)("el-icon"),r=(0,a.g2)("el-progress");return e.visible?((0,a.uX)(),(0,a.CE)("div",f,[(0,a.Lk)("div",b,[(0,a.Lk)("div",R,[(0,a.Lk)("div",_,[s.value>0?((0,a.uX)(),(0,a.CE)("div",y,[(0,a.Lk)("div",L,[(0,a.bF)(i,null,{default:(0,a.k6)((()=>[(0,a.bF)((0,n.R1)(m.CircleCheckFilled))])),_:1}),(0,a.Lk)("span",null,(0,l.v_)(e.runStatus.testResult?.successCount||0),1)]),(0,a.Lk)("div",C,[(0,a.bF)(i,null,{default:(0,a.k6)((()=>[(0,a.bF)((0,n.R1)(m.CircleCloseFilled))])),_:1}),(0,a.Lk)("span",null,(0,l.v_)(e.runStatus.testResult?.failureCount||0),1)]),(0,a.Lk)("div",F,[(0,a.bF)(i,null,{default:(0,a.k6)((()=>[(0,a.bF)((0,n.R1)(m.InfoFilled))])),_:1}),(0,a.Lk)("span",null,(0,l.v_)(s.value||0),1)])])):(0,a.Q3)("",!0),(0,a.bF)(g.A,{state:e.runStatus.processState},null,8,["state"])]),(0,a.bF)(r,{percentage:s.value>0?Math.round(u.value/s.value*100):0,"stroke-width":8},null,8,["percentage"])])])])):(0,a.Q3)("",!0)}}}),h=s(1241);const I=(0,h.A)(w,[["__scopeId","data-v-ebdce83c"]]);var S=I,X=s(3855);const E={class:"interoperation-container"},W={class:"toolbar"},A={class:"action-buttons"},T={class:"cases-list"},K={class:"case-header"},N={class:"case-sequence"},P={key:0,class:"case-detail"},Q={class:"detail-content"},z={class:"case-actions"},M={class:"case-status"};var q=(0,a.pM)({__name:"Interoperation",setup(e){(0,a.pM)({name:"InteroperationView"});const t=(0,n.KR)(!0),s=(0,n.KR)(!1),r=(0,n.KR)(!1),c=(0,n.KR)(!1),v=(0,n.KR)({processState:o.si.Pending,currentOperation:"",testResult:{id:"",resultFolderName:"",testType:"",creationTime:"",totalCount:0,successCount:0,failureCount:0},caseResults:[]}),m=(0,n.KR)(!1),g=(0,n.KR)(null),f=(0,n.KR)(null);let b=null;const R=(0,a.EW)((()=>v.value.processState===o.si.Running)),_=(0,a.EW)((()=>v.value.caseResults||[])),y=e=>{f.value=e.testResultId,g.value=e.id,m.value=!0},L=()=>{m.value=!1,g.value=null},C=async()=>{s.value=!0;try{await p.startTest(),c.value=!0,u.nk.success("Interoperation test started"),await w(),h()}catch(e){console.error("Failed to start interoperation test:",e),u.nk.error("Failed to start interoperation test")}finally{s.value=!1}},F=async()=>{r.value=!0;try{await p.stopTest(),u.nk.success("Interoperation test stopped"),await w()}catch(e){console.error("Failed to stop interoperation test:",e),u.nk.error("Failed to stop interoperation test")}finally{r.value=!1}},w=async()=>{try{const e=await p.getStatus();v.value=e.data,d(v.value)&&b&&I(),t.value=!1}catch(e){console.error("Failed to fetch interoperation test status:",e)}},h=()=>{I(),b=window.setInterval(w,300)},I=()=>{b&&(clearInterval(b),b=null)};return(0,a.sV)((()=>{w().then((()=>{R.value&&(c.value=!0,h())}))})),(0,a.hi)((()=>{I()})),(e,t)=>{const u=(0,a.g2)("el-button"),o=(0,a.g2)("el-scrollbar");return(0,a.uX)(),(0,a.CE)("div",E,[(0,a.Lk)("div",W,[t[3]||(t[3]=(0,a.Lk)("h3",null,"Interoperation Test",-1)),(0,a.Lk)("div",A,[(0,a.bF)(u,{type:"success",size:"small",loading:s.value,onClick:C,disabled:R.value},{default:(0,a.k6)((()=>t[1]||(t[1]=[(0,a.eW)(" Start ")]))),_:1},8,["loading","disabled"]),(0,a.bF)(u,{type:"danger",size:"small",loading:r.value,onClick:F,disabled:!R.value},{default:(0,a.k6)((()=>t[2]||(t[2]=[(0,a.eW)(" Stop ")]))),_:1},8,["loading","disabled"])])]),(0,a.bF)(S,{"run-status":v.value,visible:c.value},null,8,["run-status","visible"]),_.value.length?((0,a.uX)(),(0,a.Wv)(o,{key:0,height:"100%"},{default:(0,a.k6)((()=>[(0,a.Lk)("div",T,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(_.value,((e,s)=>((0,a.uX)(),(0,a.CE)("div",{key:s,class:"case-item"},[(0,a.Lk)("div",K,[(0,a.Lk)("div",N,[(0,a.Lk)("div",null,(0,l.v_)(e.sequenceName),1),e.detail?((0,a.uX)(),(0,a.CE)("div",P,[(0,a.Lk)("div",Q,(0,l.v_)(e.detail),1)])):(0,a.Q3)("",!0)]),(0,a.Lk)("div",z,[(0,a.Lk)("div",M,[(0,a.bF)(k.A,{state:e.state},null,8,["state"])]),(0,a.bF)(u,{type:"primary",size:"small",plain:"",onClick:t=>y(e)},{default:(0,a.k6)((()=>t[4]||(t[4]=[(0,a.eW)(" Open ")]))),_:2},1032,["onClick"])])])])))),128))])])),_:1})):((0,a.uX)(),(0,a.Wv)((0,n.R1)(i.x0),{key:1,description:"No items","image-size":150})),(0,a.bF)(X.A,{visible:m.value,"onUpdate:visible":t[0]||(t[0]=e=>m.value=e),testResultId:f.value,caseResultId:g.value,onClose:L},null,8,["visible","testResultId","caseResultId"])])}}});const V=(0,h.A)(q,[["__scopeId","data-v-2a30b206"]]);var $=V},3855:function(e,t,s){s.d(t,{A:function(){return q}});var a=s(6768),l=s(4232),n=s(144),u=s(1219),i=s(1021);const r=e=>{switch(e){case i.si.Success:return"success";case i.si.Running:return"primary";case i.si.Failure:return"danger";case i.si.Pending:default:return"info"}};var c=s(2971),o=s(4441);const d={key:0,class:"loading"},v={key:1,class:"case-detail-content"},p={class:"basic-info"},k={class:"info-grid"},m={key:0,class:"info-item"},g={class:"value"},f={key:1,class:"info-item"},b={class:"value"},R={class:"info-item"},_={class:"value"},y={class:"info-item"},L={class:"value status-combined"},C={key:2,class:"info-item full-width"},F=["title"],w={key:3,class:"info-item full-width"},h=["title"],I={class:"steps-section"},S={key:0,class:"no-steps"},X={class:"step-content"},E={class:"step-row"},W={class:"step-left"},A={class:"step-timestamp"},T=["title"],K=["title"],N={class:"step-right"},P={class:"dialog-footer"};var Q=(0,a.pM)({__name:"CaseDetailDialog",props:{visible:{type:Boolean},testResultId:{},caseResultId:{}},emits:["update:visible","close"],setup(e,{emit:t}){const s=e,Q=t,z=(0,n.KR)(s.visible),M=(0,n.KR)(null),q=(0,n.KR)([]),V=(0,n.KR)(!1);(0,a.wB)((()=>s.visible),(e=>{z.value=e,e&&s.testResultId&&s.caseResultId&&$()})),(0,a.wB)((()=>z.value),(e=>{Q("update:visible",e),e||Q("close")}));const $=async()=>{if(s.testResultId&&s.caseResultId){V.value=!0;try{const[e,t]=await Promise.all([i.GQ.getCaseResult(s.testResultId,s.caseResultId),i.GQ.getCaseSteps(s.testResultId,s.caseResultId)]);M.value=e.data,q.value=t.data}catch(e){console.error("Failed to load case data:",e),u.nk.error("Failed to load case details")}finally{V.value=!1}}else u.nk.warning("Missing required parameters")},B=()=>{z.value=!1,M.value=null,q.value=[]},U=e=>{if(!e&&0!==e)return"N/A";const t=e/1e6;return`${t.toFixed(6)}`};return(0,a.sV)((()=>{z.value&&s.testResultId&&s.caseResultId&&$()})),(e,t)=>{const s=(0,a.g2)("el-skeleton"),u=(0,a.g2)("el-empty"),Q=(0,a.g2)("el-timeline-item"),$=(0,a.g2)("el-timeline"),x=(0,a.g2)("el-button"),D=(0,a.g2)("el-dialog");return(0,a.uX)(),(0,a.Wv)(D,{modelValue:z.value,"onUpdate:modelValue":t[0]||(t[0]=e=>z.value=e),title:`${M.value?.name||M.value?.sequenceName||""}`,width:"60%","destroy-on-close":""},{footer:(0,a.k6)((()=>[(0,a.Lk)("span",P,[(0,a.bF)(x,{onClick:B},{default:(0,a.k6)((()=>t[9]||(t[9]=[(0,a.eW)("Close")]))),_:1})])])),default:(0,a.k6)((()=>[V.value?((0,a.uX)(),(0,a.CE)("div",d,[(0,a.bF)(s,{rows:10,animated:""})])):((0,a.uX)(),(0,a.CE)("div",v,[(0,a.Lk)("div",p,[t[7]||(t[7]=(0,a.Lk)("h4",null,"Information",-1)),(0,a.Lk)("div",k,[M.value?.name?((0,a.uX)(),(0,a.CE)("div",m,[t[1]||(t[1]=(0,a.Lk)("div",{class:"label"},"Case Name:",-1)),(0,a.Lk)("div",g,(0,l.v_)(M.value?.name),1)])):(0,a.Q3)("",!0),M.value?.name?((0,a.uX)(),(0,a.CE)("div",f,[t[2]||(t[2]=(0,a.Lk)("div",{class:"label"},"Sequence Name:",-1)),(0,a.Lk)("div",b,(0,l.v_)(M.value?.sequenceName),1)])):(0,a.Q3)("",!0),(0,a.Lk)("div",R,[t[3]||(t[3]=(0,a.Lk)("div",{class:"label"},"Start Time:",-1)),(0,a.Lk)("div",_,(0,l.v_)((0,n.R1)(o.r)(M.value?.begin)),1)]),(0,a.Lk)("div",y,[t[4]||(t[4]=(0,a.Lk)("div",{class:"label"},"End Time / Status:",-1)),(0,a.Lk)("div",L,[(0,a.eW)((0,l.v_)((0,n.R1)(o.r)(M.value?.end))+" ",1),(0,a.bF)(c.A,{state:M.value?.state||"",class:"status-tag"},null,8,["state"])])]),M.value?.parameter?((0,a.uX)(),(0,a.CE)("div",C,[t[5]||(t[5]=(0,a.Lk)("div",{class:"label"},"Parameter:",-1)),(0,a.Lk)("div",{class:"value",title:M.value?.parameter},(0,l.v_)(M.value?.parameter),9,F)])):(0,a.Q3)("",!0),M.value?.detail?((0,a.uX)(),(0,a.CE)("div",w,[t[6]||(t[6]=(0,a.Lk)("div",{class:"label"},"Detail:",-1)),(0,a.Lk)("div",{class:"value",title:M.value?.detail},(0,l.v_)(M.value.detail),9,h)])):(0,a.Q3)("",!0)])]),(0,a.Lk)("div",I,[t[8]||(t[8]=(0,a.Lk)("h4",null,"Steps",-1)),0===q.value.length?((0,a.uX)(),(0,a.CE)("div",S,[(0,a.bF)(u,{description:"No steps available"})])):((0,a.uX)(),(0,a.Wv)($,{key:1},{default:(0,a.k6)((()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(q.value,(e=>((0,a.uX)(),(0,a.Wv)(Q,{key:e.id,type:(0,n.R1)(r)(e.state),hollow:"Success"!==e.state},{default:(0,a.k6)((()=>[(0,a.Lk)("div",X,[(0,a.Lk)("div",E,[(0,a.Lk)("div",W,[(0,a.Lk)("span",A,(0,l.v_)(U(e.timestamp)),1),(0,a.Lk)("span",{class:"step-name",title:e.name},(0,l.v_)(e.name),9,T),e.detail?((0,a.uX)(),(0,a.CE)("span",{key:0,title:e.detail,class:"step-detail-inline"},(0,l.v_)(e.detail),9,K)):(0,a.Q3)("",!0)]),(0,a.Lk)("div",N,[e.state!=(0,n.R1)(i.si).Completed?((0,a.uX)(),(0,a.Wv)(c.A,{key:0,state:e.state},null,8,["state"])):(0,a.Q3)("",!0)])])])])),_:2},1032,["type","hollow"])))),128))])),_:1}))])]))])),_:1},8,["modelValue","title"])}}}),z=s(1241);const M=(0,z.A)(Q,[["__scopeId","data-v-0bc3c0cc"]]);var q=M},9857:function(e,t,s){s.d(t,{A:function(){return r}});var a=s(6768),l=s(4232),n=s(1021),u=(0,a.pM)({__name:"TestStateTag",props:{state:{}},setup(e){const t=e,s=(0,a.EW)((()=>{switch(t.state){case n.si.Success:return"success";case n.si.Running:return"warning";case n.si.Failure:return"danger";case n.si.Pending:default:return"info"}})),u=e=>{switch(e){case n.si.Running:return"Running";case n.si.Pending:return"Not Run";case n.si.Success:return"Completed";case n.si.Failure:return"Faulted";case n.si.Paused:return"Paused";default:return"Unknown"}},i=(0,a.EW)((()=>u(t.state)));return(e,t)=>{const n=(0,a.g2)("el-tag");return(0,a.uX)(),(0,a.Wv)(n,{type:s.value,size:"small"},{default:(0,a.k6)((()=>[(0,a.eW)((0,l.v_)(i.value),1)])),_:1},8,["type"])}}});const i=u;var r=i}}]);
//# sourceMappingURL=673.c51ef10c.js.map