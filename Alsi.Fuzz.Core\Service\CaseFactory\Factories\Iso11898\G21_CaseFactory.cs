using Alsi.App.Devices.Core;
using Alsi.Common.Utils.Autosar;
using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Iso11898;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso11898
{
    public class G21_CaseFactory
    {
        public CaseMutation[] Generate(MutationOptions options)
        {
            var list = new List<CaseMutation>();
            if (options.CommunicationType != CommunicationType.Can)
            {
                return list.ToArray();
            }

            var random = options.Random;

            // G211
            for (var id = 0; id <= 0x7FF; id++)
            {
                var bytes = new byte[0];
                var mutation = CaseMutation.Create($"G211-ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(0)
                    .MutateRtr(0)
                    .MutateExt(false)
                    .MutateData(bytes);
                list.Add(mutation);
            }

            // G212
            for (var id = 0; id <= 0x7FF; id++)
            {
                var mutationG212 = CaseMutation.Create($"G212-ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(0)
                    .MutateRtr(1)
                    .MutateExt(false)
                    .MutateData(Array.Empty<byte>());
                list.Add(mutationG212);
            }

            // G213
            for (var id = 0; id <= 0x7FF; id++)
            {
                for (var dlc = 0; dlc <= 8; dlc++)
                {
                    var bytes = new byte[DlcUtils.GetDataLength(dlc)];
                    random.NextBytes(bytes);
                    var mutation = CaseMutation.Create($"G213-ID_{id:X}")
                        .MutateId(id)
                        .MutateDlc(dlc)
                        .MutateRtr(0)
                        .MutateExt(false)
                        .MutateData(bytes);
                    list.Add(mutation);
                }
            }

            // G214
            var idG214 = random.Next(0, 0x800);
            var mutationG214 = CaseMutation.Create($"G214-ID_{idG214:X}")
                .MutateId(idG214)
                .MutateDlc(random.Next(1, 9))
                .MutateRtr(1)
                .MutateExt(false)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG214);

            // G215
            for (var dlc = 9; dlc <= 0xF; dlc++)
            {
                var idG215 = random.Next(0, 0x800);
                var bytes8 = new byte[8];
                random.NextBytes(bytes8);
                var mutationG215 = CaseMutation.Create($"G215-ID_{idG215:X}")
                    .MutateId(idG215)
                    .MutateDlc(dlc)
                    .MutateRtr(0)
                    .MutateExt(false)
                    .MutateData(bytes8);
                list.Add(mutationG215);
            }

            // G216
            var idG216 = random.Next(0, 0x800);
            var mutationG216 = CaseMutation.Create($"G216-ID_{idG216:X}")
                .MutateId(idG216)
                .MutateDlc(random.Next(9, 16))
                .MutateRtr(1)
                .MutateExt(false)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG216);

            int[] idArray;
            if (options.WhiteListFrames.Length == 0)
            {
                // 29bit均匀采样4096
                idArray = new int[4096];
                var maxId = 0x1FFFFFFF;
                int step = maxId / 4095; // 4096个点，间隔4095段
                for (var i = 0; i < 4095; i++)
                {
                    idArray[i] = i * step;
                }
                idArray[4095] = maxId;
            }
            else
            {
                idArray = new int[] {
                    0x0, 0x1, 0xFF, 0x100, 0x1FF, 0x200,
                    0x1000, 0x1FFF, 0x2000,
                    0x10000, 0x1FFFF, 0x20000,
                    0x100000, 0x1FFFFF, 0x200000,
                    0x1000000, 0x1FFFFFF, 0x2000000,
                    0xFFFFFFE, 0xFFFFFFF, 0x10000000,
                    0x10000001, 0x1FFFFFF0, 0x1FFFFFF1, 0x1FFFFFF2, 0x1FFFFFF3,
                    0x1FFFFFF4, 0x1FFFFFF5, 0x1FFFFFF6, 0x1FFFFFF7, 0x1FFFFFF8,
                    0x1FFFFFF9, 0x1FFFFFFA, 0x1FFFFFFB, 0x1FFFFFFC, 0x1FFFFFFD,
                    0x1FFFFFFE, 0x1FFFFFFF
                };
            }

            // G217
            foreach (var id in idArray)
            {
                var bytes = new byte[0];
                var mutation = CaseMutation.Create($"G217-ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(0)
                    .MutateRtr(0)
                    .MutateExt(true)
                    .MutateData(bytes);
                list.Add(mutation);
            }

            // G218
            foreach (var id in idArray)
            {
                var mutationG218 = CaseMutation.Create($"G218-ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(random.Next(1, 9))
                    .MutateRtr(1)
                    .MutateExt(true)
                    .MutateData(Array.Empty<byte>());
                list.Add(mutationG218);
            }

            // G219
            foreach (var id in idArray)
            {
                var dlc = random.Next(1, 9);
                var bytes = new byte[DlcUtils.GetDataLength(dlc)];
                random.NextBytes(bytes);
                var mutation = CaseMutation.Create($"G219-ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(dlc)
                    .MutateRtr(0)
                    .MutateExt(true)
                    .MutateData(bytes);
                list.Add(mutation);
            }

            // G21A
            var idG21A = random.Next(0, 0x20000000);
            var mutationG21A = CaseMutation.Create($"G21A-ID_{idG21A:X}")
                .MutateId(idG21A)
                .MutateDlc(random.Next(1, 9))
                .MutateRtr(1)
                .MutateExt(true)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG21A);

            // G21B
            var idG21B = random.Next(0, 0x20000000);
            var bytesG21B = new byte[8];
            random.NextBytes(bytesG21B);
            var mutationG21B = CaseMutation.Create($"G21B-ID_{idG21B:X}")
                .MutateId(idG21B)
                .MutateDlc(random.Next(9, 16))
                .MutateRtr(0)
                .MutateExt(true)
                .MutateData(bytesG21B);
            list.Add(mutationG21B);

            // G21C
            var idG21C = random.Next(0, 0x20000000);
            var mutationG21C = CaseMutation.Create($"G21C-ID_{idG21C:X}")
                .MutateId(idG21C)
                .MutateDlc(random.Next(9, 16))
                .MutateRtr(1)
                .MutateExt(true)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG21C);

            return list.ToArray();
        }

        public (CaseMutation[], List<string> groupPaths) GenerateWithGroup(MutationOptions options)
        {
            var list = new List<CaseMutation>();
            var groupPaths = new List<string>();
            if (options.CommunicationType != CommunicationType.Can)
            {
                return (list.ToArray(), groupPaths);
            }
            var random = options.Random;

            var groupPath = "";
            var name = "";

            // G211
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
                        .GetTxMessages()
                        .Get11BitId()
                        .GetRtr(0)
                        .IsCorrectDLC(true)
                        .DlcPath;
            for (var id = 0; id <= 0x7FF; id++)
            {
                name = $"id:{id:X}";
                var bytes = new byte[0];
                var mutation = CaseMutation.Create(name, groupPath)
                    .MutateId(id)
                    .MutateDlc(0)
                    .MutateRtr(0)
                    .MutateExt(false)
                    .MutateData(bytes);
                list.Add(mutation);
            }
            groupPaths.Add(groupPath);

            // G212
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
            .GetTxMessages()
            .Get11BitId()
            .GetRtr(1)
            .RtrPath;
            for (var id = 0; id <= 0x7FF; id++)
            {
                name = $"id:{id:X}";
                var mutationG212 = CaseMutation.Create(name, groupPath)
                    .MutateId(id)
                    .MutateDlc(0)
                    .MutateRtr(1)
                    .MutateExt(false)
                    .MutateData(Array.Empty<byte>());
                list.Add(mutationG212);
            }
            groupPaths.Add(groupPath);

            // G213
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
            .GetTxMessages()
            .Get11BitId()
            .GetRtr(0)
            .IsCorrectDLC(true)
            .DlcPath;
            for (var id = 0; id <= 0x7FF; id++)
            {
                for (var dlc = 0; dlc <= 8; dlc++)
                {
                    var bytes = new byte[DlcUtils.GetDataLength(dlc)];
                    random.NextBytes(bytes);
                    name = $"id:{id:X} dlc:{dlc}";
                    var mutation = CaseMutation.Create(name, groupPath)
                        .MutateId(id)
                        .MutateDlc(dlc)
                        .MutateRtr(0)
                        .MutateExt(false)
                        .MutateData(bytes);
                    list.Add(mutation);
                }
            }
            groupPaths.Add(groupPath);

            // G214
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
            .GetTxMessages()
            .Get11BitId()
            .GetRtr(1)
            .RtrPath;
			
            var idG214 = random.Next(0, 0x800);
            var dlc214 = random.Next(1, 9);
            name = $"id:{idG214:X} dlc:{dlc214}";
            var mutationG214 = CaseMutation.Create(name, groupPath)
                .MutateId(idG214)
                .MutateDlc(dlc214)
                .MutateRtr(1)
                .MutateExt(false)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG214);
            groupPaths.Add(groupPath);

            // G215
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
            .GetTxMessages()
            .Get11BitId()
            .GetRtr(0)
            .IsCorrectDLC(false)
            .DlcPath;

            for (var dlc = 9; dlc <= 0xF; dlc++)
            {
            	
                var idG215 = random.Next(0, 0x800);
                var bytes8 = new byte[8];
                name = $"id random id:{idG215:X} dlc:{dlc}";
                random.NextBytes(bytes8);
                var mutationG215 = CaseMutation.Create(name, groupPath)
                    .MutateId(idG215)
                    .MutateDlc(dlc)
                    .MutateRtr(0)
                    .MutateExt(false)
                    .MutateData(bytes8);
                list.Add(mutationG215);
            }
            groupPaths.Add(groupPath);
            

            // G216
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
            .GetTxMessages()
            .Get11BitId()
            .GetRtr(1)
            .RtrPath;
            
            var idG216 = random.Next(0, 0x800);
            var mutationG216 = CaseMutation.Create(name, groupPath)
                .MutateId(idG216)
                .MutateDlc(random.Next(9, 16))
                .MutateRtr(1)
                .MutateExt(false)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG216);
			groupPaths.Add(groupPath);
			
			//下记共通结构
            int[] idArray;
            if (options.WhiteListFrames.Length == 0)
            {
                // 29bit均匀采样4096
                idArray = new int[4096];
                var maxId = 0x1FFFFFFF;
                int step = maxId / 4095; // 4096个点，间隔4095段
                for (var i = 0; i < 4095; i++)
                {
                    idArray[i] = i * step;
                }
                idArray[4095] = maxId;
            }
            else
            {
                idArray = new int[] {
                    0x0, 0x1, 0xFF, 0x100, 0x1FF, 0x200,
                    0x1000, 0x1FFF, 0x2000,
                    0x10000, 0x1FFFF, 0x20000,
                    0x100000, 0x1FFFFF, 0x200000,
                    0x1000000, 0x1FFFFFF, 0x2000000,
                    0xFFFFFFE, 0xFFFFFFF, 0x10000000,
                    0x10000001, 0x1FFFFFF0, 0x1FFFFFF1, 0x1FFFFFF2, 0x1FFFFFF3,
                    0x1FFFFFF4, 0x1FFFFFF5, 0x1FFFFFF6, 0x1FFFFFF7, 0x1FFFFFF8,
                    0x1FFFFFF9, 0x1FFFFFFA, 0x1FFFFFFB, 0x1FFFFFFC, 0x1FFFFFFD,
                    0x1FFFFFFE, 0x1FFFFFFF
                };
            }

            // G217
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
            .GetTxMessages()
            .Get29BitId()
            .GetRtr(0)
            .IsCorrectDLC(true)
            .DlcPath;
            foreach (var id in idArray)
            {
                var bytes = new byte[0];
                name = $"id:{id:X}";
                var mutation = CaseMutation.Create(name, groupPath)
                    .MutateId(id)
                    .MutateDlc(0)
                    .MutateRtr(0)
                    .MutateExt(true)
                    .MutateData(bytes);
                list.Add(mutation);
            }
            groupPaths.Add(groupPath);

            // G218
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
            .GetTxMessages()
            .Get29BitId()
            .GetRtr(1)
            .RtrPath;
            foreach (var id in idArray)
            {
                var dlc = random.Next(1, 9);
                name = $"id:{id:X} dlc:{dlc}";
                var mutationG218 = CaseMutation.Create(name, groupPath)
                    .MutateId(id)
                    .MutateDlc(dlc)
                    .MutateRtr(1)
                    .MutateExt(true)
                    .MutateData(Array.Empty<byte>());
                list.Add(mutationG218);
            }
            groupPaths.Add(groupPath);

            // G219
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
            .GetTxMessages()
            .Get29BitId()
            .GetRtr(0)
            .IsCorrectDLC(true)
            .DlcPath;
            foreach (var id in idArray)
            {
                var dlc = random.Next(1, 9);
                var bytes = new byte[DlcUtils.GetDataLength(dlc)];
                name = $"id:{id:X} dlc:{dlc} random";
                random.NextBytes(bytes);
                var mutation = CaseMutation.Create(name, groupPath)
                    .MutateId(id)
                    .MutateDlc(dlc)
                    .MutateRtr(0)
                    .MutateExt(true)
                    .MutateData(bytes);
                list.Add(mutation);
            }
            groupPaths.Add(groupPath);

            // G21A
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
            .GetTxMessages()
            .Get29BitId()
            .GetRtr(1)
            .RtrPath;
			var dlc21A = random.Next(1, 9);
            var idG21A = random.Next(0, 0x20000000);
			name = $"id:{idG21A:X} dlc:{dlc21A}";
            var mutationG21A = CaseMutation.Create(name, groupPath)
                .MutateId(idG21A)
                .MutateDlc(dlc21A)
                .MutateRtr(1)
                .MutateExt(true)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG21A);
 			groupPaths.Add(groupPath);
 
            // G21B
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
            .GetTxMessages()
            .Get29BitId()
            .GetRtr(0)
            .IsCorrectDLC(false)
            .DlcPath;     
                
            var idG21B = random.Next(0, 0x20000000);
            var bytesG21B = new byte[8];
            var dlc21B = random.Next(9, 16);
            name = $"id:{idG21B:X} dlc:{dlc21B} random";
            random.NextBytes(bytesG21B);
            var mutationG21B = CaseMutation.Create(name, groupPath)
                .MutateId(idG21B)
                .MutateDlc(dlc21B)
                .MutateRtr(0)
                .MutateExt(true)
                .MutateData(bytesG21B);
            list.Add(mutationG21B);
            groupPaths.Add(groupPath);

            // G21C
            groupPath = Iso11898CaseGroupConsts.UnknownMessages
            .GetTxMessages()
            .Get29BitId()
            .GetRtr(1)
            .RtrPath;            
            var idG21C = random.Next(0, 0x20000000);
            var dlc21C = random.Next(9, 16);
            name = $"id:{idG21C:X} dlc:{dlc21C} Empty";     
            var mutationG21C = CaseMutation.Create(name, groupPath)
                .MutateId(idG21C)
                .MutateDlc(dlc21C)
                .MutateRtr(1)
                .MutateExt(true)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG21C);
			groupPaths.Add(groupPath);

            return (list.ToArray(), groupPaths);
        }

    }
}
