using Alsi.Fuzz.Core.Service.Results;
using System;

namespace Alsi.Fuzz.Core.Models.TestPlans
{
    public class TestPlan
    {
        public TestPlanManifest Manifest { get; set; } = new TestPlanManifest();
        public TestPlanConfig Config { get; set; } = new TestPlanConfig();
    }

    public class CaseCollection
    {
        public CaseEntry[] CaseEntries { get; set; } = Array.Empty<CaseEntry>();
        public CaseGroup CaseGroup { get; set; }
        public DateTime GenerationTime { get; set; }
    }

    public class CaseGroup
    {
        public string Path { get; set; }
        public string Name { get; set; }
        public int CaseCount { get; set; }
        public CaseGroup[] Children { get; set; } = Array.Empty<CaseGroup>();
    }

    public class CaseEntry
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string SequenceName { get; set; }
        public string Parameter { get; set; }
        public string ResultProps { get; set; }
        public string GroupPath { get; set; }
        public string TesterGroup { get; set; } = string.Empty;

        public CaseResult ToCaseResult(Guid testResultId)
        {
            return new CaseResult
            {
                Id = Id,
                Name = Name,
                TestResultId = testResultId,
                SequenceName = SequenceName,
                Parameter = Parameter,
                ResultProps = ResultProps,
                GroupPath = GroupPath,
                TesterGroup = TesterGroup
            };
        }

        public static CaseEntry Create(CaseResult caseResult)
        {
            return new CaseEntry
            {
                Id = caseResult.Id,
                Name = caseResult.Name,
                SequenceName = caseResult.SequenceName,
                Parameter = caseResult.Parameter,
                ResultProps = caseResult.ResultProps,
                GroupPath = caseResult.GroupPath,
                TesterGroup = caseResult.TesterGroup
            };
        }
    }
}
