using Alsi.App;
using Alsi.Fuzz.Core.Contracts.Tester;
using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service.Results;
using Alsi.Fuzz.Core.Storage;
using Alsi.Fuzz.Tester.Testers;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Tester
{
    internal static class TesterEnv
    {
        public static PackageTester PackageTester { get; set; }

        public static TesterSnapshot Snapshot { get; private set; } = new TesterSnapshot
        {
            ProcessState = ExecutionState.Pending
        };

        public static ITestResultWriterService ResultWriter = new TestResultWriterService();

        public static DataLogStorage DataLogStorage { get; private set; }

        // 测试器开始
        public static void BeginTester(TestResult testResult, CaseResult[] caseResults, CaseEntry[] caseEntries, HashSet<int> rxFrameFilterIds)
        {
            Snapshot.TestResult = testResult;
            Snapshot.ProcessState = ExecutionState.Running;

            ResultWriter.Begin(testResult);

            if (testResult.TestType == TestType.Interoperation)
            {
                // 互操作测试中，测试用例从参数中获取
                Snapshot.CaseResults = caseResults;
            }
            else if (testResult.TestType == TestType.Case)
            {
                // 正式测试中，测试用例从 Case Entry 转换
                Snapshot.CaseResults = caseEntries.Select(x => x.ToCaseResult(testResult.Id)).ToArray();
            }

            foreach (var caseResult in Snapshot.CaseResults)
            {
                caseResult.State = ExecutionState.Pending;
                caseResult.End = null;
                caseResult.Detail = string.Empty;
            }

            testResult.Begin = DateTime.Now;
            testResult.TotalCount = Snapshot.CaseResults.Length;

            // 初始化日志存储
            DataLogStorage = new DataLogStorage();
            DataLogStorage.Initialize(ResultWriter.ResultContext.DataLogPath, rxFrameFilterIds);
        }

        public static void EndTester(TestResult testResult)
        {
            testResult.End = DateTime.Now;

            ResultWriter.End();

            DataLogStorage?.Dispose();
            DataLogStorage = null;
        }

        // 更新状态
        public static void Update(string currentOperation)
        {
            Snapshot.CurrentOperation = currentOperation;
            AppEnv.Logger.Info(currentOperation);
        }
    }
}
