using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts;
using Alsi.Fuzz.Core.Service.CaseFactory.Iso14229;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G3115_G3116_G3117_CaseFactory : CaseFactoryBase
    {
        public override void Generate(MutationOptions options, Action<CreateCaseInfo> createCase)
        {
            var xmlServices = options.XmlServices;
            var supportedXmlServicesWithSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == true)
                .ToArray();

            foreach (var xmlService in supportedXmlServicesWithSubfunction)
            {
                var sid = xmlService.Id;
                var serviceName = xmlService.IsoUdsServiceDisplayName;

                var testedSubFunctionIds = new List<byte>();
                var groupPath = "";

                // 通常覆盖：XML config
                if (options.Coverage == CoverageType.Normal)
                {
                    foreach (var otherXmlService in supportedXmlServicesWithSubfunction)
                    {
                        if (otherXmlService.Id == xmlService.Id)
                        {
                            continue;
                        }

                        if (!otherXmlService.SubfunctionId.HasValue)
                        {
                            continue;
                        }

                        #region G115
                        // OtherSID.Subfunction
                        var payload = new List<byte> { sid, otherXmlService.SubfunctionId.Value };
                        testedSubFunctionIds.Add(otherXmlService.SubfunctionId.Value);

                        // 自己的参数
                        payload.AddRange(xmlService.Parameter2k);
                        var otherServiceName = otherXmlService.IsoUdsServiceDisplayName;
                        groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                                        .GetService(serviceName)
                                        .Invalid()
                                        .Parameter1WithSub(otherServiceName)
                                        .Path;
                        var name = $"Sid{sid:X2}-MyServiceParam2k-OtherSid{otherXmlService.Id:X}-OtherXmlService.SubfunctionId{otherXmlService.SubfunctionId.Value:X2} -{groupPath}";

                        var caseMutation115 = CaseMutation.Create(name, groupPath)
                            .MutatePayload(payload.ToArray())
                            .MutatePayloadLength(payload.Count);
                        createCase(new CreateCaseInfo($"G3115-Sid{sid:X2}-OtherSid{otherXmlService.Id:X}-OtherSidSubfunc{otherXmlService.SubfunctionId.Value:X2}", caseMutation115, sid, xmlService.SubfunctionId));
                        #endregion

                        #region G116
                        // OtherSID.Subfunction
                        payload = new List<byte> { sid, otherXmlService.SubfunctionId.Value };
                        // 别人的参数
                        payload.AddRange(otherXmlService.Parameter2k);


                        groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                                        .GetService(serviceName)
                                        .Invalid()
                                        .Parameter1K(otherServiceName)
                                        .Path;
                        name = $"Sid{sid:X2}-OtherSidParam2k{otherXmlService.Id:X}-OtherSidSubfunc{otherXmlService.SubfunctionId.Value:X2} -{groupPath}";

                        var caseMutation116 = CaseMutation.Create(name, groupPath)
                            .MutatePayload(payload.ToArray())
                            .MutatePayloadLength(payload.Count);
                        createCase(new CreateCaseInfo($"G3116-Sid{sid:X2}-OtherSid{otherXmlService.Id:X}-OtherSidSubfunc{otherXmlService.SubfunctionId.Value:X2}", caseMutation116, sid, xmlService.SubfunctionId));
                        #endregion
                    }
                }

                if (options.Coverage == CoverageType.High)
                {
                    // 加强覆盖：ISO定义的所有 subfunction
                    foreach (var otherService in IsoUdsConsts.Services)
                    {
                        if (otherService.Id == xmlService.Id)
                        {
                            continue;
                        }

                        foreach (var otherSubfunction in otherService.Subfunctions)
                        {
                            if (otherSubfunction.Length > 2)
                            {
                                #region G115
                                // OtherSID.Subfunction
                                var payload = new List<byte> { sid, otherSubfunction.Id };
                                testedSubFunctionIds.Add(otherSubfunction.Id);

                                // 自己的参数
                                payload.AddRange(xmlService.Parameter2k);
                                var otherServiceName = otherService.Name;
                                groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                                                .GetService(serviceName)
                                                .Invalid()
                                                .Parameter1WithSub(otherServiceName)
                                                .Path;
                                var name = $"Sid{sid:X2}-MyServiceParam2k-OtherSid{otherService.Id:X}-OtherXmlService.SubfunctionId{otherSubfunction.Id:X2} -{groupPath}";

                                var caseMutation115 = CaseMutation.Create(name, groupPath)
                                    .MutatePayload(payload.ToArray())
                                    .MutatePayloadLength(payload.Count);
                                createCase(new CreateCaseInfo($"G3115-Sid{sid:X2}-OtherSid{otherService.Id:X}-OtherSidSubfunc{otherSubfunction.Id:X2}", caseMutation115, sid, xmlService.SubfunctionId));
                                #endregion

                                #region G116
                                // OtherSID.Subfunction
                                payload = new List<byte> { sid, otherSubfunction.Id };

                                // 别人的参数
                                // ※：当subfunction=XML.configured, parameter value=xml.configure; else=Random
                                var parameter2k = RandomBytes(otherSubfunction.Length - 2);
                                payload.AddRange(parameter2k);

                                groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                                            .GetService(serviceName)
                                            .Invalid()
                                            .Parameter1K(otherServiceName)
                                            .Path;
                                name = $"Sid{sid:X2}-OtherSidParam2k{otherService.Id:X}-OtherSidSubfunc{otherSubfunction.Id:X2} -{groupPath}";

                                var caseMutation116 = CaseMutation.Create(name, groupPath)
                                    .MutatePayload(payload.ToArray())
                                    .MutatePayloadLength(payload.Count);
                                createCase(new CreateCaseInfo($"G3116-Sid{sid:X2}-OtherSid{otherService.Id:X}-OtherSidSubfunc{otherSubfunction.Id:X2}", caseMutation116, sid, xmlService.SubfunctionId));
                                #endregion
                            }
                        }
                    }
                }

                // G117
                // 2025/4/24 变更：这里应该从 1~0xFF 中去掉 G115、G116 测试过的 subfunction ID
                // var udsSubfunctionIds = IsoUdsConsts.Services.SelectMany(x => x.Subfunctions.Select(subfunction => subfunction.Id)).Distinct().ToArray();
                //Undefined Subfunction?
                var subfunctionIdsOutOfUds = Enumerable.Range(1, 0xFF).Select(x => (byte)x).Except(testedSubFunctionIds).ToArray();
                groupPath = Iso14229CaseGroupConsts.UdsServiceId.ProtocolFormat
                                .GetService(serviceName)
                                .Invalid()
                                .Parameter1WithSub("Undefined Subfunction")
                                .Path;

                foreach (var subfunctionId in subfunctionIdsOutOfUds)
                {
                    if (subfunctionId == xmlService.SubfunctionId)
                    {
                        continue;
                    }

                    var payload = new List<byte> { sid, subfunctionId };
                    payload.AddRange(xmlService.Parameter2k);

                    var name = $"SubfunctionIdsOutOfUds-Sid{sid:X2}-Subfunc{subfunctionId:X2} -{groupPath}";

                    var caseMutation = CaseMutation.Create(name, groupPath)
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    createCase(new CreateCaseInfo($"G3117-Sid{sid:X2}-Subfunc{subfunctionId:X2}", caseMutation, sid, xmlService.SubfunctionId));
                }
            }
        }
    }
}
