using Alsi.App.Devices.Core;
using System;

namespace Alsi.Fuzz.Core.Models.TestPlans
{
    public class CaseConfig
    {
        public WhiteListFrame[] WhiteListFrames { get; set; } = Array.Empty<WhiteListFrame>();

        // 新增：保存选中的目标节点名称
        public string SelectedNodeName { get; set; }

        public bool EnableNmWakeup { get; set; } = true;
        public int NmWakeupId { get; set; } = 0x53F;
        public CommunicationType NmWakeupCommunicationType { get; set; } = CommunicationType.Can;
        public bool NmWakeupIsExt { get; set; }
        public int NmWakeupDlc { get; set; } = 8;
        public int[] NmWakeupData { get; set; } = { 0x3F, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF };
        public int NmWakeupCycleMs { get; set; } = 100;
        public int NmWakeupDelayMs { get; set; } = 2000;

        public int RequestId { get; set; } = 0x731;
        public int ResponseId { get; set; } = 0x631;
        public bool RequestIsExt { get; set; }
        public int TimeoutMs { get; set; } = 500;
        public bool IsDutMtuLessThan4096 { get; set; }
        public bool EnableDiagRetryRequest { get; set; }
        public int[] DiagRetryRequestPayload { get; set; } = { 0x10, 0x01 };

        public bool EnableLogFilter { get; set; } = true;
    }
}
