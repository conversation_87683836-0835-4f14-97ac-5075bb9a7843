{"version": 3, "file": "js/673.c51ef10c.js", "mappings": "iLAOA,GAA4BA,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,eACRC,MAAO,CACLC,MAAO,CAAC,GAEVC,KAAAA,CAAMC,GCFR,MAAMH,EAAQG,EAIRC,GAAUC,EAAAA,EAAAA,KAAoD,KAClE,OAAQL,EAAMC,OACZ,KAAKK,EAAAA,GAAeC,QAClB,MAAO,UACT,KAAKD,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeG,QAClB,MAAO,SACT,KAAKH,EAAAA,GAAeI,QACpB,QACE,MAAO,O,IAIPC,EAAoBV,IACxB,OAAQA,GACN,KAAKK,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeI,QAClB,MAAO,UACT,KAAKJ,EAAAA,GAAeC,QAClB,MAAO,SACT,KAAKD,EAAAA,GAAeG,QAClB,MAAO,SACT,QACE,MAAO,U,EAIPG,GAAYP,EAAAA,EAAAA,KAAS,IAClBM,EAAiBX,EAAMC,SDKhC,MAAO,CAACY,EAAUC,KAChB,MAAMC,GAAoBC,EAAAA,EAAAA,IAAkB,UAE5C,OAAQC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAaH,EAAmB,CACpDI,KAAMf,EAAQgB,MACdC,KAAM,QACNC,MAAO,CAAC,YAAY,SACnB,CACDC,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBC,EAAAA,EAAAA,KAAiBC,EAAAA,EAAAA,IAAiBd,EAAUQ,OAAQ,MAEtDO,EAAG,GACF,EAAG,CAAC,QAAQ,CAEjB,IE5DA,MAAMC,EAAc,EAEpB,O,sJCkDM,SAAUC,EAAkBC,GAChC,OAAOA,EAAOC,eAAiBzB,EAAAA,GAAeC,SAC5CuB,EAAOC,eAAiBzB,EAAAA,GAAeG,OAC3C,CAEA,MAAMuB,EAAW,sBAEJC,EAAoB,CAE/BC,UAAWA,IACLC,EAAAA,GACKC,EAAAA,GAAQC,eAAeH,YAEzBI,EAAAA,EAAMC,KAAK,GAAGP,WAIvBQ,SAAUA,IACJL,EAAAA,GACKC,EAAAA,GAAQC,eAAeG,WAEzBF,EAAAA,EAAMC,KAAK,GAAGP,UAIvBS,UAAWA,IACLN,EAAAA,GACKC,EAAAA,GAAQC,eAAeI,YAEzBH,EAAAA,EAAMI,IAAI,GAAGV,aAIxB,I,8BCrFA,MAAMW,EAAa,CACjBC,IAAK,EACLC,MAAO,gBAEHC,EAAa,CAAED,MAAO,eACtBE,EAAa,CAAEF,MAAO,kBACtBG,EAAa,CAAEH,MAAO,wBACtBI,EAAa,CACjBL,IAAK,EACLC,MAAO,aAEHK,EAAa,CAAEL,MAAO,qBACtBM,EAAa,CAAEN,MAAO,qBACtBO,EAAa,CAAEP,MAAO,mBAS5B,OAA4B/C,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,cACRC,MAAO,CACLqD,UAAW,CAAC,EACZC,QAAS,CAAEnC,KAAMoC,UAEnBrD,KAAAA,CAAMC,GCgBR,MAAMH,EAAQG,EAKRqD,GAAanD,EAAAA,EAAAA,KAAS,IACnBL,EAAMqD,UAAUI,YAAYD,YAAc,IAG7CE,GAAiBrD,EAAAA,EAAAA,KAAS,KACtBL,EAAMqD,UAAUI,YAAYE,cAAgB,IAAM3D,EAAMqD,UAAUI,YAAYG,cAAgB,KDdxG,MAAO,CAAC/C,EAAUC,KAChB,MAAM+C,GAAqB7C,EAAAA,EAAAA,IAAkB,WACvC8C,GAAyB9C,EAAAA,EAAAA,IAAkB,eAEjD,OAAQH,EAAKyC,UACRrC,EAAAA,EAAAA,OAAc8C,EAAAA,EAAAA,IAAoB,MAAOpB,EAAY,EACpDqB,EAAAA,EAAAA,IAAoB,MAAOlB,EAAY,EACrCkB,EAAAA,EAAAA,IAAoB,MAAOjB,EAAY,EACrCiB,EAAAA,EAAAA,IAAoB,MAAOhB,EAAY,CACpCQ,EAAWpC,MAAQ,IACfH,EAAAA,EAAAA,OAAc8C,EAAAA,EAAAA,IAAoB,MAAOd,EAAY,EACpDe,EAAAA,EAAAA,IAAoB,MAAOd,EAAY,EACrCe,EAAAA,EAAAA,IAAaJ,EAAoB,KAAM,CACrCtC,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtByC,EAAAA,EAAAA,KAAaC,EAAAA,EAAAA,IAAOC,EAAAA,uBAEtBxC,EAAG,KAELqC,EAAAA,EAAAA,IAAoB,OAAQ,MAAMtC,EAAAA,EAAAA,IAAiBb,EAAKwC,UAAUI,YAAYE,cAAgB,GAAI,MAEpGK,EAAAA,EAAAA,IAAoB,MAAOb,EAAY,EACrCc,EAAAA,EAAAA,IAAaJ,EAAoB,KAAM,CACrCtC,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtByC,EAAAA,EAAAA,KAAaC,EAAAA,EAAAA,IAAOE,EAAAA,uBAEtBzC,EAAG,KAELqC,EAAAA,EAAAA,IAAoB,OAAQ,MAAMtC,EAAAA,EAAAA,IAAiBb,EAAKwC,UAAUI,YAAYG,cAAgB,GAAI,MAEpGI,EAAAA,EAAAA,IAAoB,MAAOZ,EAAY,EACrCa,EAAAA,EAAAA,IAAaJ,EAAoB,KAAM,CACrCtC,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtByC,EAAAA,EAAAA,KAAaC,EAAAA,EAAAA,IAAOG,EAAAA,gBAEtB1C,EAAG,KAELqC,EAAAA,EAAAA,IAAoB,OAAQ,MAAMtC,EAAAA,EAAAA,IAAiB8B,EAAWpC,OAAS,GAAI,SAG/EkD,EAAAA,EAAAA,IAAoB,IAAI,IAC5BL,EAAAA,EAAAA,IAAaM,EAAAA,EAAc,CACzBtE,MAAOY,EAAKwC,UAAUtB,cACrB,KAAM,EAAG,CAAC,aAEfkC,EAAAA,EAAAA,IAAaH,EAAwB,CACnCU,WAAYhB,EAAWpC,MAAQ,EAAIqD,KAAKC,MAAMhB,EAAetC,MAAQoC,EAAWpC,MAAQ,KAAO,EAC/F,eAAgB,GACf,KAAM,EAAG,CAAC,uBAInBkD,EAAAA,EAAAA,IAAoB,IAAI,EAAK,CAEnC,I,UE1FA,MAAM1C,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,Q,UCLA,MAAMe,EAAa,CAAEE,MAAO,4BACtBC,EAAa,CAAED,MAAO,WACtBE,EAAa,CAAEF,MAAO,kBACtBG,EAAa,CAAEH,MAAO,cACtBI,EAAa,CAAEJ,MAAO,eACtBK,EAAa,CAAEL,MAAO,iBACtBM,EAAa,CACjBP,IAAK,EACLC,MAAO,eAEHO,EAAa,CAAEP,MAAO,kBACtB8B,EAAa,CAAE9B,MAAO,gBACtB+B,EAAc,CAAE/B,MAAO,eAa7B,OAA4B/C,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,iBACRG,KAAAA,CAAMC,ICqCR0E,EAAAA,EAAAA,IAAgB,CACdC,KAAM,uBAIR,MAAMC,GAAUC,EAAAA,EAAAA,KAAI,GACdC,GAAWD,EAAAA,EAAAA,KAAI,GACfE,GAAWF,EAAAA,EAAAA,KAAI,GACfG,GAAiBH,EAAAA,EAAAA,KAAI,GACrB3B,GAAY2B,EAAAA,EAAAA,IAAoB,CACpCjD,aAAczB,EAAAA,GAAeI,QAC7B0E,iBAAkB,GAClB3B,WAAY,CACV4B,GAAI,GACJC,iBAAkB,GAClBC,SAAU,GACVC,aAAc,GACdhC,WAAY,EACZG,aAAc,EACdC,aAAc,GAEhB6B,YAAa,KAITC,GAAsBV,EAAAA,EAAAA,KAAI,GAC1BW,GAAiBX,EAAAA,EAAAA,IAAmB,MACpCY,GAAeZ,EAAAA,EAAAA,IAAmB,MAGxC,IAAIa,EAAoC,KAGxC,MAAMC,GAAYzF,EAAAA,EAAAA,KAAS,IAClBgD,EAAUjC,MAAMW,eAAiBzB,EAAAA,GAAeE,UAGnDuF,GAAiB1F,EAAAA,EAAAA,KAAS,IACvBgD,EAAUjC,MAAMqE,aAAe,KAIlCO,EAAkBC,IACtBL,EAAaxE,MAAQ6E,EAAWL,aAChCD,EAAevE,MAAQ6E,EAAWZ,GAClCK,EAAoBtE,OAAQ,CAAI,EAI5B8E,EAAoBA,KACxBR,EAAoBtE,OAAQ,EAC5BuE,EAAevE,MAAQ,IAAI,EAIvB+E,EAAqBC,UACzBnB,EAAS7D,OAAQ,EACjB,UACQa,EAAkBC,YACxBiD,EAAe/D,OAAQ,EACvBiF,EAAAA,GAAUC,QAAQ,qCAGZC,IACNC,G,CACA,MAAOC,GACPC,QAAQD,MAAM,uCAAwCA,GACtDJ,EAAAA,GAAUI,MAAM,sC,CAChB,QACAxB,EAAS7D,OAAQ,C,GAKfuF,EAAoBP,UACxBlB,EAAS9D,OAAQ,EACjB,UACQa,EAAkBO,WACxB6D,EAAAA,GAAUC,QAAQ,qCAGZC,G,CACN,MAAOE,GACPC,QAAQD,MAAM,sCAAuCA,GACrDJ,EAAAA,GAAUI,MAAM,qC,CAChB,QACAvB,EAAS9D,OAAQ,C,GAKfmF,EAAkBH,UACtB,IACE,MAAMQ,QAAiB3E,EAAkBQ,YACzCY,EAAUjC,MAAQwF,EAASC,KAGvBhF,EAAkBwB,EAAUjC,QAAUyE,GACxCiB,IAEF/B,EAAQ3D,OAAQ,C,CAChB,MAAOqF,GACPC,QAAQD,MAAM,8CAA+CA,E,GAK3DD,EAAqBA,KAEzBM,IACAjB,EAAqBkB,OAAOC,YAAYT,EAAiB,IAAI,EAIzDO,EAAoBA,KACpBjB,IACFoB,cAAcpB,GACdA,EAAqB,K,EDfzB,OCoBAqB,EAAAA,EAAAA,KAAU,KACRX,IAAkBY,MAAK,KAEjBrB,EAAU1E,QACZ+D,EAAe/D,OAAQ,EACvBoF,I,GAEF,KAIJY,EAAAA,EAAAA,KAAY,KACVN,GAAmB,IDhCd,CAACjG,EAAUC,KAChB,MAAMuG,GAAuBrG,EAAAA,EAAAA,IAAkB,aACzCsG,GAA0BtG,EAAAA,EAAAA,IAAkB,gBAElD,OAAQC,EAAAA,EAAAA,OAAc8C,EAAAA,EAAAA,IAAoB,MAAOpB,EAAY,EAC3DqB,EAAAA,EAAAA,IAAoB,MAAOlB,EAAY,CACrChC,EAAO,KAAOA,EAAO,IAAKkD,EAAAA,EAAAA,IAAoB,KAAM,KAAM,uBAAwB,KAClFA,EAAAA,EAAAA,IAAoB,MAAOjB,EAAY,EACrCkB,EAAAA,EAAAA,IAAaoD,EAAsB,CACjClG,KAAM,UACNE,KAAM,QACN0D,QAASE,EAAS7D,MAClBmG,QAASpB,EACTqB,SAAU1B,EAAU1E,OACnB,CACDG,SAASC,EAAAA,EAAAA,KAAS,IAAMV,EAAO,KAAOA,EAAO,GAAK,EAChDW,EAAAA,EAAAA,IAAiB,eAEnBE,EAAG,GACF,EAAG,CAAC,UAAW,cAClBsC,EAAAA,EAAAA,IAAaoD,EAAsB,CACjClG,KAAM,SACNE,KAAM,QACN0D,QAASG,EAAS9D,MAClBmG,QAASZ,EACTa,UAAW1B,EAAU1E,OACpB,CACDG,SAASC,EAAAA,EAAAA,KAAS,IAAMV,EAAO,KAAOA,EAAO,GAAK,EAChDW,EAAAA,EAAAA,IAAiB,cAEnBE,EAAG,GACF,EAAG,CAAC,UAAW,kBAGtBsC,EAAAA,EAAAA,IAAawD,EAAa,CACxB,aAAcpE,EAAUjC,MACxBkC,QAAS6B,EAAe/D,OACvB,KAAM,EAAG,CAAC,aAAc,YAC1B2E,EAAe3E,MAAMsG,SACjBzG,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAaoG,EAAyB,CACnD1E,IAAK,EACL+E,OAAQ,QACP,CACDpG,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBwC,EAAAA,EAAAA,IAAoB,MAAOhB,EAAY,GACpC/B,EAAAA,EAAAA,KAAW,IAAO8C,EAAAA,EAAAA,IAAoB6D,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAY9B,EAAe3E,OAAO,CAAC0G,EAAMC,MACvF9G,EAAAA,EAAAA,OAAc8C,EAAAA,EAAAA,IAAoB,MAAO,CAC/CnB,IAAKmF,EACLlF,MAAO,aACN,EACDmB,EAAAA,EAAAA,IAAoB,MAAOf,EAAY,EACrCe,EAAAA,EAAAA,IAAoB,MAAOd,EAAY,EACrCc,EAAAA,EAAAA,IAAoB,MAAO,MAAMtC,EAAAA,EAAAA,IAAiBoG,EAAKE,cAAe,GACrEF,EAAKG,SACDhH,EAAAA,EAAAA,OAAc8C,EAAAA,EAAAA,IAAoB,MAAOZ,EAAY,EACpDa,EAAAA,EAAAA,IAAoB,MAAOZ,GAAY1B,EAAAA,EAAAA,IAAiBoG,EAAKG,QAAS,OAExE3D,EAAAA,EAAAA,IAAoB,IAAI,MAE9BN,EAAAA,EAAAA,IAAoB,MAAOW,EAAY,EACrCX,EAAAA,EAAAA,IAAoB,MAAOY,EAAa,EACtCX,EAAAA,EAAAA,IAAaiE,EAAAA,EAAc,CACzBjI,MAAO6H,EAAK7H,OACX,KAAM,EAAG,CAAC,aAEfgE,EAAAA,EAAAA,IAAaoD,EAAsB,CACjClG,KAAM,UACNE,KAAM,QACN8G,MAAO,GACPZ,QAAUa,GAAiBpC,EAAe8B,IACzC,CACDvG,SAASC,EAAAA,EAAAA,KAAS,IAAMV,EAAO,KAAOA,EAAO,GAAK,EAChDW,EAAAA,EAAAA,IAAiB,cAEnBE,EAAG,GACF,KAAM,CAAC,qBAId,WAGRA,EAAG,OAEJV,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,KAAagD,EAAAA,EAAAA,IAAOmE,EAAAA,IAAU,CAC3CzF,IAAK,EACL0F,YAAa,WACb,aAAc,QAEpBrE,EAAAA,EAAAA,IAAasE,EAAAA,EAAkB,CAC7BjF,QAASoC,EAAoBtE,MAC7B,mBAAoBN,EAAO,KAAOA,EAAO,GAAMsH,GAAkB1C,EAAqBtE,MAAQgH,GAC9FxC,aAAcA,EAAaxE,MAC3BoH,aAAc7C,EAAevE,MAC7BqH,QAASvC,GACR,KAAM,EAAG,CAAC,UAAW,eAAgB,kBACxC,CAEJ,IErQA,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O,4GCDO,MAAMwC,EAAuBzI,IAClC,OAAQA,GACN,KAAKK,EAAAA,GAAeC,QAClB,MAAO,UACT,KAAKD,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeG,QAClB,MAAO,SACT,KAAKH,EAAAA,GAAeI,QACpB,QACE,MAAO,O,0BCdb,MAAMiC,EAAa,CACjBC,IAAK,EACLC,MAAO,WAEHC,EAAa,CACjBF,IAAK,EACLC,MAAO,uBAEHE,EAAa,CAAEF,MAAO,cACtBG,EAAa,CAAEH,MAAO,aACtBI,EAAa,CACjBL,IAAK,EACLC,MAAO,aAEHK,EAAa,CAAEL,MAAO,SACtBM,EAAa,CACjBP,IAAK,EACLC,MAAO,aAEHO,EAAa,CAAEP,MAAO,SACtB8B,EAAa,CAAE9B,MAAO,aACtB+B,EAAc,CAAE/B,MAAO,SACvB8F,EAAc,CAAE9F,MAAO,aACvB+F,EAAc,CAAE/F,MAAO,yBACvBgG,EAAc,CAClBjG,IAAK,EACLC,MAAO,wBAEHiG,EAAc,CAAC,SACfC,EAAc,CAClBnG,IAAK,EACLC,MAAO,wBAEHmG,EAAc,CAAC,SACfC,EAAc,CAAEpG,MAAO,iBACvBqG,EAAc,CAClBtG,IAAK,EACLC,MAAO,YAEHsG,EAAc,CAAEtG,MAAO,gBACvBuG,EAAc,CAAEvG,MAAO,YACvBwG,EAAc,CAAExG,MAAO,aACvByG,EAAc,CAAEzG,MAAO,kBACvB0G,EAAc,CAAC,SACfC,EAAc,CAAC,SACfC,EAAc,CAAE5G,MAAO,cACvB6G,EAAc,CAAE7G,MAAO,iBAW7B,OAA4B/C,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,mBACRC,MAAO,CACLsD,QAAS,CAAEnC,KAAMoC,SACjBqC,aAAc,CAAC,EACf4C,aAAc,CAAC,GAEjBmB,MAAO,CAAC,iBAAkB,SAC1BzJ,KAAAA,CAAMC,GAAgByJ,KAAMC,ICmB9B,MAAM7J,EAAQG,EAMRyJ,EAAOC,EAKPC,GAAgB9E,EAAAA,EAAAA,IAAIhF,EAAMsD,SAC1ByG,GAAW/E,EAAAA,EAAAA,IAAuB,MAClCgF,GAAQhF,EAAAA,EAAAA,IAAgB,IACxBD,GAAUC,EAAAA,EAAAA,KAAI,IAEpBiF,EAAAA,EAAAA,KAAM,IAAMjK,EAAMsD,UAAU4G,IAC1BJ,EAAc1I,MAAQ8I,EAClBA,GAAYlK,EAAM4F,cAAgB5F,EAAMwI,cAC1C2B,G,KAIJF,EAAAA,EAAAA,KAAM,IAAMH,EAAc1I,QAAQ8I,IAChCN,EAAK,iBAAkBM,GAClBA,GAAUN,EAAK,QAAQ,IAG9B,MAAMO,EAAe/D,UACnB,GAAKpG,EAAM4F,cAAiB5F,EAAMwI,aAAlC,CAKAzD,EAAQ3D,OAAQ,EAChB,IAEE,MAAOgJ,EAAcC,SAAuBC,QAAQC,IAAI,CACtDC,EAAAA,GAAOC,cAAczK,EAAM4F,aAAc5F,EAAMwI,cAC/CgC,EAAAA,GAAOE,aAAa1K,EAAM4F,aAAc5F,EAAMwI,gBAGhDuB,EAAS3I,MAAQgJ,EAAavD,KAC9BmD,EAAM5I,MAAQiJ,EAAcxD,I,CAC5B,MAAOJ,GACPC,QAAQD,MAAM,4BAA6BA,GAC3CJ,EAAAA,GAAUI,MAAM,8B,CAChB,QACA1B,EAAQ3D,OAAQ,C,OAlBhBiF,EAAAA,GAAUsE,QAAQ,8B,EAsBhBC,EAAcA,KAClBd,EAAc1I,OAAQ,EAEtB2I,EAAS3I,MAAQ,KACjB4I,EAAM5I,MAAQ,EAAE,EAIZyJ,EAAsBC,IAC1B,IAAKA,GAAiC,IAAjBA,EAAoB,MAAO,MAGhD,MAAMC,EAAUD,EAAe,IAE/B,MAAO,GAAGC,EAAQC,QAAQ,IAAI,EDdhC,OCkBA9D,EAAAA,EAAAA,KAAU,KACJ4C,EAAc1I,OAASpB,EAAM4F,cAAgB5F,EAAMwI,cACrD2B,G,IDpBG,CAACtJ,EAAUC,KAChB,MAAMmK,GAAyBjK,EAAAA,EAAAA,IAAkB,eAC3CkK,GAAsBlK,EAAAA,EAAAA,IAAkB,YACxCmK,GAA8BnK,EAAAA,EAAAA,IAAkB,oBAChDoK,GAAyBpK,EAAAA,EAAAA,IAAkB,eAC3CqG,GAAuBrG,EAAAA,EAAAA,IAAkB,aACzCqK,GAAuBrK,EAAAA,EAAAA,IAAkB,aAE/C,OAAQC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAamK,EAAsB,CACvDC,WAAYxB,EAAc1I,MAC1B,sBAAuBN,EAAO,KAAOA,EAAO,GAAMsH,GAAkB0B,EAAe1I,MAAQgH,GAC3FmD,MAAO,GAAGxB,EAAS3I,OAAO0D,MAAQiF,EAAS3I,OAAO4G,cAAgB,KAClEwD,MAAO,MACP,mBAAoB,IACnB,CACDC,QAAQjK,EAAAA,EAAAA,KAAS,IAAM,EACrBwC,EAAAA,EAAAA,IAAoB,OAAQ0F,EAAa,EACvCzF,EAAAA,EAAAA,IAAaoD,EAAsB,CAAEE,QAASqD,GAAe,CAC3DrJ,SAASC,EAAAA,EAAAA,KAAS,IAAMV,EAAO,KAAOA,EAAO,GAAK,EAChDW,EAAAA,EAAAA,IAAiB,aAEnBE,EAAG,SAITJ,SAASC,EAAAA,EAAAA,KAAS,IAAM,CACrBuD,EAAQ3D,QACJH,EAAAA,EAAAA,OAAc8C,EAAAA,EAAAA,IAAoB,MAAOpB,EAAY,EACpDsB,EAAAA,EAAAA,IAAagH,EAAwB,CACnCS,KAAM,GACNC,SAAU,UAGb1K,EAAAA,EAAAA,OAAc8C,EAAAA,EAAAA,IAAoB,MAAOjB,EAAY,EACpDkB,EAAAA,EAAAA,IAAoB,MAAOjB,EAAY,CACrCjC,EAAO,KAAOA,EAAO,IAAKkD,EAAAA,EAAAA,IAAoB,KAAM,KAAM,eAAgB,KAC1EA,EAAAA,EAAAA,IAAoB,MAAOhB,EAAY,CACpC+G,EAAS3I,OAAO0D,OACZ7D,EAAAA,EAAAA,OAAc8C,EAAAA,EAAAA,IAAoB,MAAOd,EAAY,CACpDnC,EAAO,KAAOA,EAAO,IAAKkD,EAAAA,EAAAA,IAAoB,MAAO,CAAEnB,MAAO,SAAW,cAAe,KACxFmB,EAAAA,EAAAA,IAAoB,MAAOd,GAAYxB,EAAAA,EAAAA,IAAiBqI,EAAS3I,OAAO0D,MAAO,OAEjFR,EAAAA,EAAAA,IAAoB,IAAI,GAC3ByF,EAAS3I,OAAO0D,OACZ7D,EAAAA,EAAAA,OAAc8C,EAAAA,EAAAA,IAAoB,MAAOZ,EAAY,CACpDrC,EAAO,KAAOA,EAAO,IAAKkD,EAAAA,EAAAA,IAAoB,MAAO,CAAEnB,MAAO,SAAW,kBAAmB,KAC5FmB,EAAAA,EAAAA,IAAoB,MAAOZ,GAAY1B,EAAAA,EAAAA,IAAiBqI,EAAS3I,OAAO4G,cAAe,OAEzF1D,EAAAA,EAAAA,IAAoB,IAAI,IAC5BN,EAAAA,EAAAA,IAAoB,MAAOW,EAAY,CACrC7D,EAAO,KAAOA,EAAO,IAAKkD,EAAAA,EAAAA,IAAoB,MAAO,CAAEnB,MAAO,SAAW,eAAgB,KACzFmB,EAAAA,EAAAA,IAAoB,MAAOY,GAAalD,EAAAA,EAAAA,KAAiBwC,EAAAA,EAAAA,IAAO0H,EAAAA,EAAP1H,CAAuB6F,EAAS3I,OAAOyK,QAAS,MAE3G7H,EAAAA,EAAAA,IAAoB,MAAO2E,EAAa,CACtC7H,EAAO,KAAOA,EAAO,IAAKkD,EAAAA,EAAAA,IAAoB,MAAO,CAAEnB,MAAO,SAAW,sBAAuB,KAChGmB,EAAAA,EAAAA,IAAoB,MAAO4E,EAAa,EACtCnH,EAAAA,EAAAA,KAAiBC,EAAAA,EAAAA,KAAiBwC,EAAAA,EAAAA,IAAO0H,EAAAA,EAAP1H,CAAuB6F,EAAS3I,OAAO0K,MAAQ,IAAK,IACtF7H,EAAAA,EAAAA,IAAaiE,EAAAA,EAAc,CACzBjI,MAAO8J,EAAS3I,OAAOnB,OAAS,GAChC4C,MAAO,cACN,KAAM,EAAG,CAAC,cAGhBkH,EAAS3I,OAAO2K,YACZ9K,EAAAA,EAAAA,OAAc8C,EAAAA,EAAAA,IAAoB,MAAO8E,EAAa,CACrD/H,EAAO,KAAOA,EAAO,IAAKkD,EAAAA,EAAAA,IAAoB,MAAO,CAAEnB,MAAO,SAAW,cAAe,KACxFmB,EAAAA,EAAAA,IAAoB,MAAO,CACzBnB,MAAO,QACP0I,MAAOxB,EAAS3I,OAAO2K,YACtBrK,EAAAA,EAAAA,IAAiBqI,EAAS3I,OAAO2K,WAAY,EAAGjD,OAErDxE,EAAAA,EAAAA,IAAoB,IAAI,GAC3ByF,EAAS3I,OAAO6G,SACZhH,EAAAA,EAAAA,OAAc8C,EAAAA,EAAAA,IAAoB,MAAOgF,EAAa,CACrDjI,EAAO,KAAOA,EAAO,IAAKkD,EAAAA,EAAAA,IAAoB,MAAO,CAAEnB,MAAO,SAAW,WAAY,KACrFmB,EAAAA,EAAAA,IAAoB,MAAO,CACzBnB,MAAO,QACP0I,MAAOxB,EAAS3I,OAAO6G,SACtBvG,EAAAA,EAAAA,IAAiBqI,EAAS3I,MAAM6G,QAAS,EAAGe,OAEjD1E,EAAAA,EAAAA,IAAoB,IAAI,QAGhCN,EAAAA,EAAAA,IAAoB,MAAOiF,EAAa,CACtCnI,EAAO,KAAOA,EAAO,IAAKkD,EAAAA,EAAAA,IAAoB,KAAM,KAAM,SAAU,IAC5C,IAAvBgG,EAAM5I,MAAMsG,SACRzG,EAAAA,EAAAA,OAAc8C,EAAAA,EAAAA,IAAoB,MAAOmF,EAAa,EACrDjF,EAAAA,EAAAA,IAAaiH,EAAqB,CAAE5C,YAAa,4BAElDrH,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAakK,EAAwB,CAAExI,IAAK,GAAK,CAC9DrB,SAASC,EAAAA,EAAAA,KAAS,IAAM,GACrBP,EAAAA,EAAAA,KAAW,IAAO8C,EAAAA,EAAAA,IAAoB6D,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAYmC,EAAM5I,OAAQ4K,KACxE/K,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAaiK,EAA6B,CAC9DvI,IAAKoJ,EAAK3G,GACVlE,MAAM+C,EAAAA,EAAAA,IAAOwE,EAAPxE,CAA4B8H,EAAK/L,OACvCgM,OAAuB,YAAfD,EAAK/L,OACZ,CACDsB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBwC,EAAAA,EAAAA,IAAoB,MAAOmF,EAAa,EACtCnF,EAAAA,EAAAA,IAAoB,MAAOoF,EAAa,EACtCpF,EAAAA,EAAAA,IAAoB,MAAOqF,EAAa,EACtCrF,EAAAA,EAAAA,IAAoB,OAAQsF,GAAa5H,EAAAA,EAAAA,IAAiBmJ,EAAmBmB,EAAKE,YAAa,IAC/FlI,EAAAA,EAAAA,IAAoB,OAAQ,CAC1BnB,MAAO,YACP0I,MAAOS,EAAKlH,OACXpD,EAAAA,EAAAA,IAAiBsK,EAAKlH,MAAO,EAAGyE,GAClCyC,EAAK/D,SACDhH,EAAAA,EAAAA,OAAc8C,EAAAA,EAAAA,IAAoB,OAAQ,CACzCnB,IAAK,EACL2I,MAAOS,EAAK/D,OACZpF,MAAO,uBACNnB,EAAAA,EAAAA,IAAiBsK,EAAK/D,QAAS,EAAGuB,KACrClF,EAAAA,EAAAA,IAAoB,IAAI,MAE9BN,EAAAA,EAAAA,IAAoB,MAAOyF,EAAa,CACrCuC,EAAK/L,QAASiE,EAAAA,EAAAA,IAAO5D,EAAAA,IAAgB6L,YACjClL,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAagH,EAAAA,EAAc,CACxCtF,IAAK,EACL3C,MAAO+L,EAAK/L,OACX,KAAM,EAAG,CAAC,YACbqE,EAAAA,EAAAA,IAAoB,IAAI,YAKpC3C,EAAG,GACF,KAAM,CAAC,OAAQ,cAChB,SAENA,EAAG,aAKnBA,EAAG,GACF,EAAG,CAAC,aAAc,SAAS,CAEhC,I,UE9QA,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O,yFCDA,GAA4B9B,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,eACRC,MAAO,CACLC,MAAO,CAAC,GAEVC,KAAAA,CAAMC,GCFR,MAAMH,EAAQG,EAIRC,GAAUC,EAAAA,EAAAA,KAAoD,KAClE,OAAQL,EAAMC,OACZ,KAAKK,EAAAA,GAAeC,QAClB,MAAO,UACT,KAAKD,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeG,QAClB,MAAO,SACT,KAAKH,EAAAA,GAAeI,QACpB,QACE,MAAO,O,IAIP0L,EAAoBnM,IACxB,OAAQA,GACN,KAAKK,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeI,QAClB,MAAO,UACT,KAAKJ,EAAAA,GAAeC,QAClB,MAAO,YACT,KAAKD,EAAAA,GAAeG,QAClB,MAAO,UACT,KAAKH,EAAAA,GAAe+L,OAClB,MAAO,SACT,QACE,MAAO,U,EAIPzL,GAAYP,EAAAA,EAAAA,KAAS,IAClB+L,EAAiBpM,EAAMC,SDKhC,MAAO,CAACY,EAAUC,KAChB,MAAMC,GAAoBC,EAAAA,EAAAA,IAAkB,UAE5C,OAAQC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAaH,EAAmB,CACpDI,KAAMf,EAAQgB,MACdC,KAAM,SACL,CACDE,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBC,EAAAA,EAAAA,KAAiBC,EAAAA,EAAAA,IAAiBd,EAAUQ,OAAQ,MAEtDO,EAAG,GACF,EAAG,CAAC,QAAQ,CAEjB,IE7DA,MAAMC,EAAc,EAEpB,O", "sources": ["webpack://fuzz-web/./src/components/common/CaseStateTag.vue?c0c7", "webpack://fuzz-web/./src/components/common/CaseStateTag.vue", "webpack://fuzz-web/./src/components/common/CaseStateTag.vue?4980", "webpack://fuzz-web/./src/api/interoperationApi.ts", "webpack://fuzz-web/./src/components/test/TestMonitor.vue?60d6", "webpack://fuzz-web/./src/components/test/TestMonitor.vue", "webpack://fuzz-web/./src/components/test/TestMonitor.vue?602e", "webpack://fuzz-web/./src/views/testplan/Interoperation.vue?6fba", "webpack://fuzz-web/./src/views/testplan/Interoperation.vue", "webpack://fuzz-web/./src/views/testplan/Interoperation.vue?8aca", "webpack://fuzz-web/./src/utils/status.ts", "webpack://fuzz-web/./src/components/test/CaseDetailDialog.vue?5717", "webpack://fuzz-web/./src/components/test/CaseDetailDialog.vue", "webpack://fuzz-web/./src/components/test/CaseDetailDialog.vue?ca65", "webpack://fuzz-web/./src/components/common/TestStateTag.vue?fb59", "webpack://fuzz-web/./src/components/common/TestStateTag.vue", "webpack://fuzz-web/./src/components/common/TestStateTag.vue?d49a"], "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from \"vue\"\n\nimport { computed } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'CaseStateTag',\n  props: {\n    state: {}\n  },\n  setup(__props: any) {\n\r\nconst props = __props;\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getCaseStateName = (state: string): 'Not Run' | 'Running' | 'Passed' | 'Failed' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Passed';\r\n    case ExecutionState.Failure:\r\n      return 'Failed';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getCaseStateName(props.state);\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n\n  return (_openBlock(), _createBlock(_component_el_tag, {\n    type: tagType.value,\n    size: \"small\",\n    style: {\"min-width\":\"60px\"}\n  }, {\n    default: _withCtx(() => [\n      _createTextVNode(_toDisplayString(stateName.value), 1)\n    ]),\n    _: 1\n  }, 8, [\"type\"]))\n}\n}\n\n})", "<template>\r\n  <el-tag :type=\"tagType\" size=\"small\" style=\"min-width: 60px;\">\r\n    {{ stateName }}\r\n  </el-tag>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, defineProps } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\nconst props = defineProps<{\r\n  state: string;\r\n}>();\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getCaseStateName = (state: string): 'Not Run' | 'Running' | 'Passed' | 'Failed' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Passed';\r\n    case ExecutionState.Failure:\r\n      return 'Failed';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getCaseStateName(props.state);\r\n});\r\n</script>\r\n", "import script from \"./CaseStateTag.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./CaseStateTag.vue?vue&type=script&setup=true&lang=ts\"\n\nconst __exports__ = script;\n\nexport default __exports__", "import axios, { AxiosResponse } from 'axios'\r\nimport { USE_MOCK, mockApi } from '@/mock/mockApi'\r\nimport { TesterSnapshot, ExecutionState } from './appApi';\r\n\r\n// 基础响应类型\r\nexport interface BaseResponse {\r\n  success: boolean;\r\n  message: string;\r\n}\r\n\r\n// 用例结果类型\r\nexport interface CaseResult {\r\n  id: number;\r\n  testResultId: string; // Guid in C#\r\n  sequenceId: string; // Guid in C#\r\n  sequenceName: string;\r\n  name: string;\r\n  groupPath: string;\r\n  parameter: string;\r\n  state: ExecutionState;\r\n  begin: string | null;\r\n  end: string | null;\r\n  detail: string;\r\n}\r\n\r\n// 类型定义\r\nexport interface GroupTreeNode {\r\n  name?: string;\r\n  children: GroupTreeNode[];\r\n  id?: string;\r\n  // parent?: GroupTreeNode;\r\n  count: number;\r\n}\r\n\r\nexport interface MyGroupResponse {\r\n  cases: CaseResult[];\r\n  groupTree: GroupTreeNode\r\n}\r\n\r\n\r\n// 互操作测试状态类型\r\nexport interface InteroperationStatus {\r\n  isRunning: boolean;\r\n  progress: number;\r\n  currentOperation: string;\r\n  beginTime: string | null;\r\n  endTime: string | null;\r\n  summary: {\r\n    passed: number;\r\n    failed: number;\r\n    skipped: number;\r\n  };\r\n  caseResults: CaseResult[] | null;\r\n}\r\n\r\nexport function isTesterCompleted(tester: TesterSnapshot): boolean {\r\n  return tester.processState === ExecutionState.Success ||\r\n    tester.processState === ExecutionState.Failure;\r\n}\r\n\r\nconst BASE_URL = '/api/interoperation'\r\n\r\nexport const interoperationApi = {\r\n  // 启动互操作测试\r\n  startTest: (): Promise<AxiosResponse<BaseResponse>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.interoperation.startTest();\r\n    }\r\n    return axios.post(`${BASE_URL}/start`);\r\n  },\r\n\r\n  // 停止互操作测试\r\n  stopTest: (): Promise<AxiosResponse<BaseResponse>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.interoperation.stopTest();\r\n    }\r\n    return axios.post(`${BASE_URL}/stop`);\r\n  },\r\n\r\n  // 获取测试状态\r\n  getStatus: (): Promise<AxiosResponse<TesterSnapshot>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.interoperation.getStatus();\r\n    }\r\n    return axios.get(`${BASE_URL}/status`);\r\n  }\r\n}\r\n\r\nexport default interoperationApi;\r\n", "import { defineComponent as _defineComponent } from 'vue'\nimport { unref as _unref, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode } from \"vue\"\n\nconst _hoisted_1 = {\n  key: 0,\n  class: \"test-monitor\"\n}\nconst _hoisted_2 = { class: \"status-area\" }\nconst _hoisted_3 = { class: \"compact-status\" }\nconst _hoisted_4 = { class: \"status-header-inline\" }\nconst _hoisted_5 = {\n  key: 0,\n  class: \"stats-row\"\n}\nconst _hoisted_6 = { class: \"stat-item success\" }\nconst _hoisted_7 = { class: \"stat-item failure\" }\nconst _hoisted_8 = { class: \"stat-item total\" }\n\nimport { computed } from 'vue';\r\nimport { CircleCheckFilled, CircleCloseFilled, InfoFilled } from '@element-plus/icons-vue';\r\nimport { TesterSnapshot } from '@/api/appApi';\r\nimport TestStateTag from '@/components/common/TestStateTag.vue';\r\n\r\n// 组件属性定义\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TestMonitor',\n  props: {\n    runStatus: {},\n    visible: { type: Boolean }\n  },\n  setup(__props: any) {\n\r\nconst props = __props;\r\n\r\nconst totalCount = computed(() => {\r\n  return props.runStatus.testResult?.totalCount || 0;\r\n});\r\n\r\nconst completedCount = computed(() => {\r\n  return (props.runStatus.testResult?.successCount || 0) + (props.runStatus.testResult?.failureCount || 0);\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_progress = _resolveComponent(\"el-progress\")!\n\n  return (_ctx.visible)\n    ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n        _createElementVNode(\"div\", _hoisted_2, [\n          _createElementVNode(\"div\", _hoisted_3, [\n            _createElementVNode(\"div\", _hoisted_4, [\n              (totalCount.value > 0)\n                ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [\n                    _createElementVNode(\"div\", _hoisted_6, [\n                      _createVNode(_component_el_icon, null, {\n                        default: _withCtx(() => [\n                          _createVNode(_unref(CircleCheckFilled))\n                        ]),\n                        _: 1\n                      }),\n                      _createElementVNode(\"span\", null, _toDisplayString(_ctx.runStatus.testResult?.successCount || 0), 1)\n                    ]),\n                    _createElementVNode(\"div\", _hoisted_7, [\n                      _createVNode(_component_el_icon, null, {\n                        default: _withCtx(() => [\n                          _createVNode(_unref(CircleCloseFilled))\n                        ]),\n                        _: 1\n                      }),\n                      _createElementVNode(\"span\", null, _toDisplayString(_ctx.runStatus.testResult?.failureCount || 0), 1)\n                    ]),\n                    _createElementVNode(\"div\", _hoisted_8, [\n                      _createVNode(_component_el_icon, null, {\n                        default: _withCtx(() => [\n                          _createVNode(_unref(InfoFilled))\n                        ]),\n                        _: 1\n                      }),\n                      _createElementVNode(\"span\", null, _toDisplayString(totalCount.value || 0), 1)\n                    ])\n                  ]))\n                : _createCommentVNode(\"\", true),\n              _createVNode(TestStateTag, {\n                state: _ctx.runStatus.processState\n              }, null, 8, [\"state\"])\n            ]),\n            _createVNode(_component_el_progress, {\n              percentage: totalCount.value > 0 ? Math.round(completedCount.value / totalCount.value * 100) : 0,\n              \"stroke-width\": 8\n            }, null, 8, [\"percentage\"])\n          ])\n        ])\n      ]))\n    : _createCommentVNode(\"\", true)\n}\n}\n\n})", "<template>\r\n  <div class=\"test-monitor\" v-if=\"visible\">\r\n    <!-- 优化后的紧凑测试状态区域 -->\r\n    <div class=\"status-area\">\r\n      <div class=\"compact-status\">\r\n        <!-- 内联状态头部 -->\r\n        <div class=\"status-header-inline\">\r\n          <div class=\"stats-row\" v-if=\"totalCount > 0\">\r\n            <div class=\"stat-item success\">\r\n              <el-icon>\r\n                <CircleCheckFilled />\r\n              </el-icon>\r\n              <span>{{ runStatus.testResult?.successCount || 0 }}</span>\r\n            </div>\r\n            <div class=\"stat-item failure\">\r\n              <el-icon>\r\n                <CircleCloseFilled />\r\n              </el-icon>\r\n              <span>{{ runStatus.testResult?.failureCount || 0 }}</span>\r\n            </div>\r\n\r\n            <div class=\"stat-item total\">\r\n              <el-icon>\r\n                <InfoFilled />\r\n              </el-icon>\r\n              <span>{{ totalCount || 0 }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <TestStateTag :state=\"runStatus.processState\" />\r\n        </div>\r\n\r\n        <!-- 进度条 -->\r\n        <el-progress :percentage=\"totalCount > 0 ? Math.round(completedCount / totalCount * 100) : 0\" :stroke-width=\"8\">\r\n        </el-progress>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { defineProps, computed } from 'vue';\r\nimport { CircleCheckFilled, CircleCloseFilled, InfoFilled } from '@element-plus/icons-vue';\r\nimport { TesterSnapshot } from '@/api/appApi';\r\nimport TestStateTag from '@/components/common/TestStateTag.vue';\r\n\r\n// 组件属性定义\r\nconst props = defineProps<{\r\n  runStatus: TesterSnapshot;\r\n  visible: boolean;\r\n}>();\r\n\r\nconst totalCount = computed(() => {\r\n  return props.runStatus.testResult?.totalCount || 0;\r\n});\r\n\r\nconst completedCount = computed(() => {\r\n  return (props.runStatus.testResult?.successCount || 0) + (props.runStatus.testResult?.failureCount || 0);\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.test-monitor {\r\n  width: 100%;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n/* 状态区域样式 */\r\n.status-area {\r\n  padding: 0 12px;\r\n\r\n  .compact-status {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n\r\n  .status-header-inline {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .progress-numbers {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 4px;\r\n  }\r\n\r\n  .stats-row {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n  }\r\n\r\n  .stat-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 4px;\r\n\r\n    &.success {\r\n      color: var(--el-color-success);\r\n    }\r\n\r\n    &.failure {\r\n      color: var(--el-color-danger);\r\n    }\r\n\r\n    &.total {\r\n      color: var(--el-color-primary);\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./TestMonitor.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TestMonitor.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./TestMonitor.vue?vue&type=style&index=0&id=ebdce83c&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-ebdce83c\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createCommentVNode as _createCommentVNode, createBlock as _createBlock, unref as _unref } from \"vue\"\n\nconst _hoisted_1 = { class: \"interoperation-container\" }\nconst _hoisted_2 = { class: \"toolbar\" }\nconst _hoisted_3 = { class: \"action-buttons\" }\nconst _hoisted_4 = { class: \"cases-list\" }\nconst _hoisted_5 = { class: \"case-header\" }\nconst _hoisted_6 = { class: \"case-sequence\" }\nconst _hoisted_7 = {\n  key: 0,\n  class: \"case-detail\"\n}\nconst _hoisted_8 = { class: \"detail-content\" }\nconst _hoisted_9 = { class: \"case-actions\" }\nconst _hoisted_10 = { class: \"case-status\" }\n\nimport { ref, onMounted, onUnmounted, computed, defineComponent } from 'vue';\r\nimport { ElMessage, ElEmpty } from 'element-plus';\r\nimport { CaseResult, interoperationApi, isTesterCompleted } from '@/api/interoperationApi';\r\nimport CaseStateTag from '@/components/common/CaseStateTag.vue';\r\nimport TestMonitor from '@/components/test/TestMonitor.vue';\r\nimport { TesterSnapshot, ExecutionState } from '@/api/appApi';\r\n// 导入用例详情对话框组件\r\nimport CaseDetailDialog from '@/components/test/CaseDetailDialog.vue';\r\n\r\n// 定义组件名称\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'Interoperation',\n  setup(__props) {\n\r\ndefineComponent({\r\n  name: 'InteroperationView'\r\n});\r\n\r\n// 状态变量\r\nconst loading = ref(true);\r\nconst starting = ref(false);\r\nconst stopping = ref(false);\r\nconst hasEverStarted = ref(false);\r\nconst runStatus = ref<TesterSnapshot>({\r\n  processState: ExecutionState.Pending,\r\n  currentOperation: '',\r\n  testResult: {\r\n    id: '',\r\n    resultFolderName: '',\r\n    testType: '',\r\n    creationTime: '',\r\n    totalCount: 0,\r\n    successCount: 0,\r\n    failureCount: 0\r\n  },\r\n  caseResults: []\r\n});\r\n\r\n// 用例详情对话框相关\r\nconst detailDialogVisible = ref(false);\r\nconst selectedCaseId = ref<number | null>(null);\r\nconst testResultId = ref<string | null>(null);\r\n\r\n// 状态轮询定时器\r\nlet statusPollingTimer: number | null = null;\r\n\r\n// 计算属性\r\nconst isRunning = computed(() => {\r\n  return runStatus.value.processState === ExecutionState.Running;\r\n});\r\n\r\nconst displayedCases = computed(() => {\r\n  return runStatus.value.caseResults || [];\r\n});\r\n\r\n// 查看用例详情\r\nconst viewCaseDetail = (caseResult: CaseResult) => {\r\n  testResultId.value = caseResult.testResultId;\r\n  selectedCaseId.value = caseResult.id;\r\n  detailDialogVisible.value = true;\r\n};\r\n\r\n// 关闭详情对话框\r\nconst closeDetailDialog = () => {\r\n  detailDialogVisible.value = false;\r\n  selectedCaseId.value = null;\r\n};\r\n\r\n// 开始测试执行\r\nconst startTestExecution = async () => {\r\n  starting.value = true;\r\n  try {\r\n    await interoperationApi.startTest();\r\n    hasEverStarted.value = true;\r\n    ElMessage.success('Interoperation test started');\r\n\r\n    // 立即获取状态并开始轮询\r\n    await fetchTestStatus();\r\n    startStatusPolling();\r\n  } catch (error) {\r\n    console.error('Failed to start interoperation test:', error);\r\n    ElMessage.error('Failed to start interoperation test');\r\n  } finally {\r\n    starting.value = false;\r\n  }\r\n};\r\n\r\n// 停止测试执行\r\nconst stopTestExecution = async () => {\r\n  stopping.value = true;\r\n  try {\r\n    await interoperationApi.stopTest();\r\n    ElMessage.success('Interoperation test stopped');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('Failed to stop interoperation test:', error);\r\n    ElMessage.error('Failed to stop interoperation test');\r\n  } finally {\r\n    stopping.value = false;\r\n  }\r\n};\r\n\r\n// 获取测试状态\r\nconst fetchTestStatus = async () => {\r\n  try {\r\n    const response = await interoperationApi.getStatus();\r\n    runStatus.value = response.data;\r\n\r\n    // 如果测试完成则停止轮询\r\n    if (isTesterCompleted(runStatus.value) && statusPollingTimer) {\r\n      stopStatusPolling();\r\n    }\r\n    loading.value = false;\r\n  } catch (error) {\r\n    console.error('Failed to fetch interoperation test status:', error);\r\n  }\r\n};\r\n\r\n// 开始状态轮询\r\nconst startStatusPolling = () => {\r\n  // 清除可能存在的轮询定时器\r\n  stopStatusPolling();\r\n  statusPollingTimer = window.setInterval(fetchTestStatus, 300);\r\n};\r\n\r\n// 停止状态轮询\r\nconst stopStatusPolling = () => {\r\n  if (statusPollingTimer) {\r\n    clearInterval(statusPollingTimer);\r\n    statusPollingTimer = null;\r\n  }\r\n};\r\n\r\n// 组件挂载时获取测试状态\r\nonMounted(() => {\r\n  fetchTestStatus().then(() => {\r\n    // 如果测试正在运行，开始轮询\r\n    if (isRunning.value) {\r\n      hasEverStarted.value = true;\r\n      startStatusPolling();\r\n    }\r\n  });\r\n});\r\n\r\n// 组件卸载时停止轮询\r\nonUnmounted(() => {\r\n  stopStatusPolling();\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_scrollbar = _resolveComponent(\"el-scrollbar\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _cache[3] || (_cache[3] = _createElementVNode(\"h3\", null, \"Interoperation Test\", -1)),\n      _createElementVNode(\"div\", _hoisted_3, [\n        _createVNode(_component_el_button, {\n          type: \"success\",\n          size: \"small\",\n          loading: starting.value,\n          onClick: startTestExecution,\n          disabled: isRunning.value\n        }, {\n          default: _withCtx(() => _cache[1] || (_cache[1] = [\n            _createTextVNode(\" Start \")\n          ])),\n          _: 1\n        }, 8, [\"loading\", \"disabled\"]),\n        _createVNode(_component_el_button, {\n          type: \"danger\",\n          size: \"small\",\n          loading: stopping.value,\n          onClick: stopTestExecution,\n          disabled: !isRunning.value\n        }, {\n          default: _withCtx(() => _cache[2] || (_cache[2] = [\n            _createTextVNode(\" Stop \")\n          ])),\n          _: 1\n        }, 8, [\"loading\", \"disabled\"])\n      ])\n    ]),\n    _createVNode(TestMonitor, {\n      \"run-status\": runStatus.value,\n      visible: hasEverStarted.value\n    }, null, 8, [\"run-status\", \"visible\"]),\n    (displayedCases.value.length)\n      ? (_openBlock(), _createBlock(_component_el_scrollbar, {\n          key: 0,\n          height: \"100%\"\n        }, {\n          default: _withCtx(() => [\n            _createElementVNode(\"div\", _hoisted_4, [\n              (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(displayedCases.value, (item, index) => {\n                return (_openBlock(), _createElementBlock(\"div\", {\n                  key: index,\n                  class: \"case-item\"\n                }, [\n                  _createElementVNode(\"div\", _hoisted_5, [\n                    _createElementVNode(\"div\", _hoisted_6, [\n                      _createElementVNode(\"div\", null, _toDisplayString(item.sequenceName), 1),\n                      (item.detail)\n                        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [\n                            _createElementVNode(\"div\", _hoisted_8, _toDisplayString(item.detail), 1)\n                          ]))\n                        : _createCommentVNode(\"\", true)\n                    ]),\n                    _createElementVNode(\"div\", _hoisted_9, [\n                      _createElementVNode(\"div\", _hoisted_10, [\n                        _createVNode(CaseStateTag, {\n                          state: item.state\n                        }, null, 8, [\"state\"])\n                      ]),\n                      _createVNode(_component_el_button, {\n                        type: \"primary\",\n                        size: \"small\",\n                        plain: \"\",\n                        onClick: ($event: any) => (viewCaseDetail(item))\n                      }, {\n                        default: _withCtx(() => _cache[4] || (_cache[4] = [\n                          _createTextVNode(\" Open \")\n                        ])),\n                        _: 2\n                      }, 1032, [\"onClick\"])\n                    ])\n                  ])\n                ]))\n              }), 128))\n            ])\n          ]),\n          _: 1\n        }))\n      : (_openBlock(), _createBlock(_unref(ElEmpty), {\n          key: 1,\n          description: \"No items\",\n          \"image-size\": 150\n        })),\n    _createVNode(CaseDetailDialog, {\n      visible: detailDialogVisible.value,\n      \"onUpdate:visible\": _cache[0] || (_cache[0] = ($event: any) => ((detailDialogVisible).value = $event)),\n      testResultId: testResultId.value,\n      caseResultId: selectedCaseId.value,\n      onClose: closeDetailDialog\n    }, null, 8, [\"visible\", \"testResultId\", \"caseResultId\"])\n  ]))\n}\n}\n\n})", "<template>\r\n  <div class=\"interoperation-container\">\r\n    <div class=\"toolbar\">\r\n      <h3>Interoperation Test</h3>\r\n      <div class=\"action-buttons\">\r\n        <el-button type=\"success\" size=\"small\" :loading=\"starting\" @click=\"startTestExecution\" :disabled=\"isRunning\">\r\n          Start\r\n        </el-button>\r\n        <el-button type=\"danger\" size=\"small\" :loading=\"stopping\" @click=\"stopTestExecution\" :disabled=\"!isRunning\">\r\n          Stop\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 使用TestMonitor组件显示执行状态进度 -->\r\n    <TestMonitor :run-status=\"runStatus\" :visible=\"hasEverStarted\" />\r\n\r\n    <el-scrollbar v-if=\"displayedCases.length\" height=\"100%\">\r\n      <div class=\"cases-list\">\r\n        <div v-for=\"(item, index) in displayedCases\" :key=\"index\" class=\"case-item\">\r\n          <div class=\"case-header\">\r\n            <div class=\"case-sequence\">\r\n              <div>{{ item.sequenceName }}</div>\r\n              <div v-if=\"item.detail\" class=\"case-detail\">\r\n                <div class=\"detail-content\">{{ item.detail }}</div>\r\n              </div>\r\n            </div>\r\n            <div class=\"case-actions\">\r\n              <div class=\"case-status\">\r\n                <CaseStateTag :state=\"item.state\" />\r\n              </div>\r\n              <el-button \r\n                type=\"primary\" \r\n                size=\"small\" \r\n                plain \r\n                @click=\"viewCaseDetail(item)\">\r\n                Open\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n\r\n    <el-empty v-else description=\"No items\" :image-size=\"150\" />\r\n\r\n    <!-- 添加用例详情对话框组件 -->\r\n    <CaseDetailDialog\r\n      v-model:visible=\"detailDialogVisible\"\r\n      :testResultId=\"testResultId\"\r\n      :caseResultId=\"selectedCaseId\"\r\n      @close=\"closeDetailDialog\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, onMounted, onUnmounted, computed, defineComponent } from 'vue';\r\nimport { ElMessage, ElEmpty } from 'element-plus';\r\nimport { CaseResult, interoperationApi, isTesterCompleted } from '@/api/interoperationApi';\r\nimport CaseStateTag from '@/components/common/CaseStateTag.vue';\r\nimport TestMonitor from '@/components/test/TestMonitor.vue';\r\nimport { TesterSnapshot, ExecutionState } from '@/api/appApi';\r\n// 导入用例详情对话框组件\r\nimport CaseDetailDialog from '@/components/test/CaseDetailDialog.vue';\r\n\r\n// 定义组件名称\r\ndefineComponent({\r\n  name: 'InteroperationView'\r\n});\r\n\r\n// 状态变量\r\nconst loading = ref(true);\r\nconst starting = ref(false);\r\nconst stopping = ref(false);\r\nconst hasEverStarted = ref(false);\r\nconst runStatus = ref<TesterSnapshot>({\r\n  processState: ExecutionState.Pending,\r\n  currentOperation: '',\r\n  testResult: {\r\n    id: '',\r\n    resultFolderName: '',\r\n    testType: '',\r\n    creationTime: '',\r\n    totalCount: 0,\r\n    successCount: 0,\r\n    failureCount: 0\r\n  },\r\n  caseResults: []\r\n});\r\n\r\n// 用例详情对话框相关\r\nconst detailDialogVisible = ref(false);\r\nconst selectedCaseId = ref<number | null>(null);\r\nconst testResultId = ref<string | null>(null);\r\n\r\n// 状态轮询定时器\r\nlet statusPollingTimer: number | null = null;\r\n\r\n// 计算属性\r\nconst isRunning = computed(() => {\r\n  return runStatus.value.processState === ExecutionState.Running;\r\n});\r\n\r\nconst displayedCases = computed(() => {\r\n  return runStatus.value.caseResults || [];\r\n});\r\n\r\n// 查看用例详情\r\nconst viewCaseDetail = (caseResult: CaseResult) => {\r\n  testResultId.value = caseResult.testResultId;\r\n  selectedCaseId.value = caseResult.id;\r\n  detailDialogVisible.value = true;\r\n};\r\n\r\n// 关闭详情对话框\r\nconst closeDetailDialog = () => {\r\n  detailDialogVisible.value = false;\r\n  selectedCaseId.value = null;\r\n};\r\n\r\n// 开始测试执行\r\nconst startTestExecution = async () => {\r\n  starting.value = true;\r\n  try {\r\n    await interoperationApi.startTest();\r\n    hasEverStarted.value = true;\r\n    ElMessage.success('Interoperation test started');\r\n\r\n    // 立即获取状态并开始轮询\r\n    await fetchTestStatus();\r\n    startStatusPolling();\r\n  } catch (error) {\r\n    console.error('Failed to start interoperation test:', error);\r\n    ElMessage.error('Failed to start interoperation test');\r\n  } finally {\r\n    starting.value = false;\r\n  }\r\n};\r\n\r\n// 停止测试执行\r\nconst stopTestExecution = async () => {\r\n  stopping.value = true;\r\n  try {\r\n    await interoperationApi.stopTest();\r\n    ElMessage.success('Interoperation test stopped');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('Failed to stop interoperation test:', error);\r\n    ElMessage.error('Failed to stop interoperation test');\r\n  } finally {\r\n    stopping.value = false;\r\n  }\r\n};\r\n\r\n// 获取测试状态\r\nconst fetchTestStatus = async () => {\r\n  try {\r\n    const response = await interoperationApi.getStatus();\r\n    runStatus.value = response.data;\r\n\r\n    // 如果测试完成则停止轮询\r\n    if (isTesterCompleted(runStatus.value) && statusPollingTimer) {\r\n      stopStatusPolling();\r\n    }\r\n    loading.value = false;\r\n  } catch (error) {\r\n    console.error('Failed to fetch interoperation test status:', error);\r\n  }\r\n};\r\n\r\n// 开始状态轮询\r\nconst startStatusPolling = () => {\r\n  // 清除可能存在的轮询定时器\r\n  stopStatusPolling();\r\n  statusPollingTimer = window.setInterval(fetchTestStatus, 300);\r\n};\r\n\r\n// 停止状态轮询\r\nconst stopStatusPolling = () => {\r\n  if (statusPollingTimer) {\r\n    clearInterval(statusPollingTimer);\r\n    statusPollingTimer = null;\r\n  }\r\n};\r\n\r\n// 组件挂载时获取测试状态\r\nonMounted(() => {\r\n  fetchTestStatus().then(() => {\r\n    // 如果测试正在运行，开始轮询\r\n    if (isRunning.value) {\r\n      hasEverStarted.value = true;\r\n      startStatusPolling();\r\n    }\r\n  });\r\n});\r\n\r\n// 组件卸载时停止轮询\r\nonUnmounted(() => {\r\n  stopStatusPolling();\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.interoperation-container {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 15px 20px;\r\n}\r\n\r\n/* 顶部工具栏样式 */\r\n.toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 1px solid #dcdfe6;\r\n  padding-bottom: 10px;\r\n  margin-bottom: 10px;\r\n  height: 30px;\r\n\r\n  h3 {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    color: #303133;\r\n  }\r\n\r\n  .action-buttons {\r\n    display: flex;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n.cases-list {\r\n  padding: 8px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.case-item {\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  padding: 6px 10px;\r\n  background-color: #fff;\r\n  transition: all 0.3s;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n  font-size: 13px;\r\n\r\n  &:hover {\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  }\r\n}\r\n\r\n.case-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n\r\n  .case-index {\r\n    font-weight: bold;\r\n    width: 40px;\r\n    color: #909399;\r\n  }\r\n\r\n  .case-sequence {\r\n    display: flex;\r\n    flex-direction: row;\r\n    flex: 1;\r\n    font-weight: 500;\r\n    color: #303133;\r\n  }\r\n}\r\n\r\n.case-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px; /* 按钮和状态标签之间的间距 */\r\n}\r\n\r\n.case-status {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.case-body {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n\r\n  .case-parameter {\r\n    flex: 1;\r\n    color: #606266;\r\n    font-size: 13px;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n    max-width: 60vw;\r\n  }\r\n\r\n  .case-group {\r\n    margin-left: 8px;\r\n  }\r\n}\r\n\r\n.case-detail {\r\n  font-size: 12px;\r\n  margin-left: 10px;\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .detail-content {\r\n    color: #909399;\r\n    word-break: break-word;\r\n    flex: 1;\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./Interoperation.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./Interoperation.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./Interoperation.vue?vue&type=style&index=0&id=2a30b206&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-2a30b206\"]])\n\nexport default __exports__", "import { ExecutionState } from '@/api/appApi';\r\n\r\n/**\r\n * 获取执行状态对应的时间线项类型\r\n * @param state 执行状态\r\n * @returns 时间线项类型\r\n */\r\nexport const getTimelineItemType = (state: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {\r\n  switch (state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'primary';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n};", "import { defineComponent as _defineComponent } from 'vue'\nimport { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, unref as _unref, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, createBlock as _createBlock, withCtx as _withCtx } from \"vue\"\n\nconst _hoisted_1 = {\n  key: 0,\n  class: \"loading\"\n}\nconst _hoisted_2 = {\n  key: 1,\n  class: \"case-detail-content\"\n}\nconst _hoisted_3 = { class: \"basic-info\" }\nconst _hoisted_4 = { class: \"info-grid\" }\nconst _hoisted_5 = {\n  key: 0,\n  class: \"info-item\"\n}\nconst _hoisted_6 = { class: \"value\" }\nconst _hoisted_7 = {\n  key: 1,\n  class: \"info-item\"\n}\nconst _hoisted_8 = { class: \"value\" }\nconst _hoisted_9 = { class: \"info-item\" }\nconst _hoisted_10 = { class: \"value\" }\nconst _hoisted_11 = { class: \"info-item\" }\nconst _hoisted_12 = { class: \"value status-combined\" }\nconst _hoisted_13 = {\n  key: 2,\n  class: \"info-item full-width\"\n}\nconst _hoisted_14 = [\"title\"]\nconst _hoisted_15 = {\n  key: 3,\n  class: \"info-item full-width\"\n}\nconst _hoisted_16 = [\"title\"]\nconst _hoisted_17 = { class: \"steps-section\" }\nconst _hoisted_18 = {\n  key: 0,\n  class: \"no-steps\"\n}\nconst _hoisted_19 = { class: \"step-content\" }\nconst _hoisted_20 = { class: \"step-row\" }\nconst _hoisted_21 = { class: \"step-left\" }\nconst _hoisted_22 = { class: \"step-timestamp\" }\nconst _hoisted_23 = [\"title\"]\nconst _hoisted_24 = [\"title\"]\nconst _hoisted_25 = { class: \"step-right\" }\nconst _hoisted_26 = { class: \"dialog-footer\" }\n\nimport { ref, watch, onMounted } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport { appApi, CaseStep, ExecutionState } from '@/api/appApi';\r\nimport { getTimelineItemType } from '@/utils/status';\r\nimport CaseStateTag from '@/components/common/CaseStateTag.vue';\r\nimport { formatDateTime } from '@/utils/timeUtils';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'CaseDetailDialog',\n  props: {\n    visible: { type: Boolean },\n    testResultId: {},\n    caseResultId: {}\n  },\n  emits: [\"update:visible\", \"close\"],\n  setup(__props: any, { emit: __emit }) {\n\r\nconst props = __props;\r\n\r\nconst emit = __emit;\r\n\r\nconst dialogVisible = ref(props.visible);\r\nconst caseData = ref<CaseResult | null>(null);\r\nconst steps = ref<CaseStep[]>([]);\r\nconst loading = ref(false);\r\n\r\nwatch(() => props.visible, (newValue) => {\r\n  dialogVisible.value = newValue;\r\n  if (newValue && props.testResultId && props.caseResultId) {\r\n    loadCaseData();\r\n  }\r\n});\r\n\r\nwatch(() => dialogVisible.value, (newValue) => {\r\n  emit('update:visible', newValue);\r\n  if (!newValue) emit('close');\r\n});\r\n\r\nconst loadCaseData = async () => {\r\n  if (!props.testResultId || !props.caseResultId) {\r\n    ElMessage.warning('Missing required parameters');\r\n    return;\r\n  }\r\n\r\n  loading.value = true;\r\n  try {\r\n    // 并行加载用例数据和步骤数据\r\n    const [caseResponse, stepsResponse] = await Promise.all([\r\n      appApi.getCaseResult(props.testResultId, props.caseResultId),\r\n      appApi.getCaseSteps(props.testResultId, props.caseResultId)\r\n    ]);\r\n\r\n    caseData.value = caseResponse.data;\r\n    steps.value = stepsResponse.data;\r\n  } catch (error) {\r\n    console.error('Failed to load case data:', error);\r\n    ElMessage.error('Failed to load case details');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst closeDialog = () => {\r\n  dialogVisible.value = false;\r\n  // 清空数据\r\n  caseData.value = null;\r\n  steps.value = [];\r\n};\r\n\r\n// 添加新的微秒格式化方法\r\nconst formatMicroseconds = (microseconds: number): string => {\r\n  if (!microseconds && microseconds !== 0) return 'N/A';\r\n\r\n  // 转换为秒并保留6位小数\r\n  const seconds = microseconds / 1000000;\r\n  // 使用toFixed(6)确保始终有6位小数\r\n  return `${seconds.toFixed(6)}`;\r\n};\r\n\r\n// 组件挂载时，如果对话框是可见的且有必要参数，则加载数据\r\nonMounted(() => {\r\n  if (dialogVisible.value && props.testResultId && props.caseResultId) {\r\n    loadCaseData();\r\n  }\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\")!\n  const _component_el_empty = _resolveComponent(\"el-empty\")!\n  const _component_el_timeline_item = _resolveComponent(\"el-timeline-item\")!\n  const _component_el_timeline = _resolveComponent(\"el-timeline\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_dialog = _resolveComponent(\"el-dialog\")!\n\n  return (_openBlock(), _createBlock(_component_el_dialog, {\n    modelValue: dialogVisible.value,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((dialogVisible).value = $event)),\n    title: `${caseData.value?.name || caseData.value?.sequenceName || ''}`,\n    width: \"60%\",\n    \"destroy-on-close\": \"\"\n  }, {\n    footer: _withCtx(() => [\n      _createElementVNode(\"span\", _hoisted_26, [\n        _createVNode(_component_el_button, { onClick: closeDialog }, {\n          default: _withCtx(() => _cache[9] || (_cache[9] = [\n            _createTextVNode(\"Close\")\n          ])),\n          _: 1\n        })\n      ])\n    ]),\n    default: _withCtx(() => [\n      (loading.value)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n            _createVNode(_component_el_skeleton, {\n              rows: 10,\n              animated: \"\"\n            })\n          ]))\n        : (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [\n            _createElementVNode(\"div\", _hoisted_3, [\n              _cache[7] || (_cache[7] = _createElementVNode(\"h4\", null, \"Information\", -1)),\n              _createElementVNode(\"div\", _hoisted_4, [\n                (caseData.value?.name)\n                  ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [\n                      _cache[1] || (_cache[1] = _createElementVNode(\"div\", { class: \"label\" }, \"Case Name:\", -1)),\n                      _createElementVNode(\"div\", _hoisted_6, _toDisplayString(caseData.value?.name), 1)\n                    ]))\n                  : _createCommentVNode(\"\", true),\n                (caseData.value?.name)\n                  ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [\n                      _cache[2] || (_cache[2] = _createElementVNode(\"div\", { class: \"label\" }, \"Sequence Name:\", -1)),\n                      _createElementVNode(\"div\", _hoisted_8, _toDisplayString(caseData.value?.sequenceName), 1)\n                    ]))\n                  : _createCommentVNode(\"\", true),\n                _createElementVNode(\"div\", _hoisted_9, [\n                  _cache[3] || (_cache[3] = _createElementVNode(\"div\", { class: \"label\" }, \"Start Time:\", -1)),\n                  _createElementVNode(\"div\", _hoisted_10, _toDisplayString(_unref(formatDateTime)(caseData.value?.begin)), 1)\n                ]),\n                _createElementVNode(\"div\", _hoisted_11, [\n                  _cache[4] || (_cache[4] = _createElementVNode(\"div\", { class: \"label\" }, \"End Time / Status:\", -1)),\n                  _createElementVNode(\"div\", _hoisted_12, [\n                    _createTextVNode(_toDisplayString(_unref(formatDateTime)(caseData.value?.end)) + \" \", 1),\n                    _createVNode(CaseStateTag, {\n                      state: caseData.value?.state || '',\n                      class: \"status-tag\"\n                    }, null, 8, [\"state\"])\n                  ])\n                ]),\n                (caseData.value?.parameter)\n                  ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [\n                      _cache[5] || (_cache[5] = _createElementVNode(\"div\", { class: \"label\" }, \"Parameter:\", -1)),\n                      _createElementVNode(\"div\", {\n                        class: \"value\",\n                        title: caseData.value?.parameter\n                      }, _toDisplayString(caseData.value?.parameter), 9, _hoisted_14)\n                    ]))\n                  : _createCommentVNode(\"\", true),\n                (caseData.value?.detail)\n                  ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [\n                      _cache[6] || (_cache[6] = _createElementVNode(\"div\", { class: \"label\" }, \"Detail:\", -1)),\n                      _createElementVNode(\"div\", {\n                        class: \"value\",\n                        title: caseData.value?.detail\n                      }, _toDisplayString(caseData.value.detail), 9, _hoisted_16)\n                    ]))\n                  : _createCommentVNode(\"\", true)\n              ])\n            ]),\n            _createElementVNode(\"div\", _hoisted_17, [\n              _cache[8] || (_cache[8] = _createElementVNode(\"h4\", null, \"Steps\", -1)),\n              (steps.value.length === 0)\n                ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [\n                    _createVNode(_component_el_empty, { description: \"No steps available\" })\n                  ]))\n                : (_openBlock(), _createBlock(_component_el_timeline, { key: 1 }, {\n                    default: _withCtx(() => [\n                      (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(steps.value, (step) => {\n                        return (_openBlock(), _createBlock(_component_el_timeline_item, {\n                          key: step.id,\n                          type: _unref(getTimelineItemType)(step.state),\n                          hollow: step.state !== 'Success'\n                        }, {\n                          default: _withCtx(() => [\n                            _createElementVNode(\"div\", _hoisted_19, [\n                              _createElementVNode(\"div\", _hoisted_20, [\n                                _createElementVNode(\"div\", _hoisted_21, [\n                                  _createElementVNode(\"span\", _hoisted_22, _toDisplayString(formatMicroseconds(step.timestamp)), 1),\n                                  _createElementVNode(\"span\", {\n                                    class: \"step-name\",\n                                    title: step.name\n                                  }, _toDisplayString(step.name), 9, _hoisted_23),\n                                  (step.detail)\n                                    ? (_openBlock(), _createElementBlock(\"span\", {\n                                        key: 0,\n                                        title: step.detail,\n                                        class: \"step-detail-inline\"\n                                      }, _toDisplayString(step.detail), 9, _hoisted_24))\n                                    : _createCommentVNode(\"\", true)\n                                ]),\n                                _createElementVNode(\"div\", _hoisted_25, [\n                                  (step.state != _unref(ExecutionState).Completed)\n                                    ? (_openBlock(), _createBlock(CaseStateTag, {\n                                        key: 0,\n                                        state: step.state\n                                      }, null, 8, [\"state\"]))\n                                    : _createCommentVNode(\"\", true)\n                                ])\n                              ])\n                            ])\n                          ]),\n                          _: 2\n                        }, 1032, [\"type\", \"hollow\"]))\n                      }), 128))\n                    ]),\n                    _: 1\n                  }))\n            ])\n          ]))\n    ]),\n    _: 1\n  }, 8, [\"modelValue\", \"title\"]))\n}\n}\n\n})", "<template>\r\n  <el-dialog v-model=\"dialogVisible\" :title=\"`${caseData?.name || caseData?.sequenceName || ''}`\" width=\"60%\"\r\n    destroy-on-close>\r\n    <div v-if=\"loading\" class=\"loading\">\r\n      <el-skeleton :rows=\"10\" animated />\r\n    </div>\r\n\r\n    <div v-else class=\"case-detail-content\">\r\n      <!-- 基本信息区域 -->\r\n      <div class=\"basic-info\">\r\n        <h4>Information</h4>\r\n        <div class=\"info-grid\">\r\n          <div class=\"info-item\" v-if=\"caseData?.name\">\r\n            <div class=\"label\">Case Name:</div>\r\n            <div class=\"value\">{{ caseData?.name }}</div>\r\n          </div>\r\n          <div class=\"info-item\" v-if=\"caseData?.name\">\r\n            <div class=\"label\">Sequence Name:</div>\r\n            <div class=\"value\">{{ caseData?.sequenceName }}</div>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <div class=\"label\">Start Time:</div>\r\n            <div class=\"value\">{{ formatDateTime(caseData?.begin) }}</div>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <div class=\"label\">End Time / Status:</div>\r\n            <div class=\"value status-combined\">\r\n              {{ formatDateTime(caseData?.end) }}\r\n              <CaseStateTag :state=\"caseData?.state || ''\" class=\"status-tag\" />\r\n            </div>\r\n          </div>\r\n          <div v-if=\"caseData?.parameter\" class=\"info-item full-width\">\r\n            <div class=\"label\">Parameter:</div>\r\n            <div class=\"value\" :title=\"caseData?.parameter\">{{ caseData?.parameter }}</div>\r\n          </div>\r\n          <div v-if=\"caseData?.detail\" class=\"info-item full-width\">\r\n            <div class=\"label\">Detail:</div>\r\n            <div class=\"value\" :title=\"caseData?.detail\">{{ caseData.detail }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 步骤列表区域 -->\r\n      <div class=\"steps-section\">\r\n        <h4>Steps</h4>\r\n\r\n        <div v-if=\"steps.length === 0\" class=\"no-steps\">\r\n          <el-empty description=\"No steps available\" />\r\n        </div>\r\n\r\n        <el-timeline v-else>\r\n          <el-timeline-item v-for=\"step in steps\" :key=\"step.id\" :type=\"getTimelineItemType(step.state)\"\r\n            :hollow=\"step.state !== 'Success'\">\r\n            <div class=\"step-content\">\r\n              <div class=\"step-row\">\r\n                <div class=\"step-left\">\r\n                  <span class=\"step-timestamp\">{{ formatMicroseconds(step.timestamp) }}</span>\r\n                  <span class=\"step-name\" :title=\"step.name\" >{{ step.name }}</span>\r\n                  <span v-if=\"step.detail\" :title=\"step.detail\" class=\"step-detail-inline\">{{ step.detail }}</span>\r\n                </div>\r\n                <div class=\"step-right\">\r\n                  <CaseStateTag v-if=\"step.state != ExecutionState.Completed\" :state=\"step.state\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n      </div>\r\n    </div>\r\n\r\n    <template #footer>\r\n      <span class=\"dialog-footer\">\r\n        <el-button @click=\"closeDialog\">Close</el-button>\r\n      </span>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, watch, defineProps, defineEmits, onMounted } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport { appApi, CaseStep, ExecutionState } from '@/api/appApi';\r\nimport { getTimelineItemType } from '@/utils/status';\r\nimport CaseStateTag from '@/components/common/CaseStateTag.vue';\r\nimport { formatDateTime } from '@/utils/timeUtils';\r\n\r\nconst props = defineProps<{\r\n  visible: boolean;\r\n  testResultId?: string | null;\r\n  caseResultId?: number | null;\r\n}>();\r\n\r\nconst emit = defineEmits<{\r\n  (e: 'update:visible', value: boolean): void;\r\n  (e: 'close'): void;\r\n}>();\r\n\r\nconst dialogVisible = ref(props.visible);\r\nconst caseData = ref<CaseResult | null>(null);\r\nconst steps = ref<CaseStep[]>([]);\r\nconst loading = ref(false);\r\n\r\nwatch(() => props.visible, (newValue) => {\r\n  dialogVisible.value = newValue;\r\n  if (newValue && props.testResultId && props.caseResultId) {\r\n    loadCaseData();\r\n  }\r\n});\r\n\r\nwatch(() => dialogVisible.value, (newValue) => {\r\n  emit('update:visible', newValue);\r\n  if (!newValue) emit('close');\r\n});\r\n\r\nconst loadCaseData = async () => {\r\n  if (!props.testResultId || !props.caseResultId) {\r\n    ElMessage.warning('Missing required parameters');\r\n    return;\r\n  }\r\n\r\n  loading.value = true;\r\n  try {\r\n    // 并行加载用例数据和步骤数据\r\n    const [caseResponse, stepsResponse] = await Promise.all([\r\n      appApi.getCaseResult(props.testResultId, props.caseResultId),\r\n      appApi.getCaseSteps(props.testResultId, props.caseResultId)\r\n    ]);\r\n\r\n    caseData.value = caseResponse.data;\r\n    steps.value = stepsResponse.data;\r\n  } catch (error) {\r\n    console.error('Failed to load case data:', error);\r\n    ElMessage.error('Failed to load case details');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst closeDialog = () => {\r\n  dialogVisible.value = false;\r\n  // 清空数据\r\n  caseData.value = null;\r\n  steps.value = [];\r\n};\r\n\r\n// 添加新的微秒格式化方法\r\nconst formatMicroseconds = (microseconds: number): string => {\r\n  if (!microseconds && microseconds !== 0) return 'N/A';\r\n\r\n  // 转换为秒并保留6位小数\r\n  const seconds = microseconds / 1000000;\r\n  // 使用toFixed(6)确保始终有6位小数\r\n  return `${seconds.toFixed(6)}`;\r\n};\r\n\r\n// 组件挂载时，如果对话框是可见的且有必要参数，则加载数据\r\nonMounted(() => {\r\n  if (dialogVisible.value && props.testResultId && props.caseResultId) {\r\n    loadCaseData();\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.loading {\r\n  min-height: 200px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.case-detail-content {\r\n  padding: 0 20px;\r\n}\r\n\r\n.basic-info {\r\n  margin-bottom: 20px;\r\n\r\n  h4 {\r\n    margin-top: 0;\r\n    margin-bottom: 16px;\r\n    font-size: 16px;\r\n    color: #303133;\r\n    border-bottom: 1px solid #ebeef5;\r\n    padding-bottom: 8px;\r\n  }\r\n\r\n  .info-grid {\r\n    display: grid;\r\n    grid-template-columns: 1fr 1fr;\r\n    gap: 12px 24px;\r\n\r\n    .info-item {\r\n      .label {\r\n        font-size: 13px;\r\n        color: #909399;\r\n        margin-bottom: 4px;\r\n      }\r\n\r\n      .value {\r\n        font-size: 14px;\r\n        color: #303133;\r\n        word-break: break-word;\r\n\r\n        &.status-combined {\r\n          display: flex;\r\n          align-items: center;\r\n          \r\n          .status-tag {\r\n            margin-left: 12px;\r\n          }\r\n        }\r\n      }\r\n\r\n      &.full-width {\r\n        grid-column: span 2;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.steps-section {\r\n  h4 {\r\n    margin-top: 20px;\r\n    margin-bottom: 16px;\r\n    font-size: 16px;\r\n    color: #303133;\r\n    border-bottom: 1px solid #ebeef5;\r\n    padding-bottom: 8px;\r\n  }\r\n\r\n  .no-steps {\r\n    padding: 20px 0;\r\n  }\r\n\r\n  .step-content {\r\n    font-size: 13px;\r\n\r\n    .step-row {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      width: 100%;\r\n      padding: 8px 4px;\r\n      transition: background-color 0.2s;\r\n    }\r\n\r\n    .step-left {\r\n      display: flex;\r\n      align-items: center;\r\n      flex: 1;\r\n      min-width: 0; // 防止flex子元素溢出\r\n    }\r\n\r\n    .step-right {\r\n      margin-left: 10px;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .step-timestamp {\r\n      min-width: 80px;\r\n      display: inline-block;\r\n      color: #606266;\r\n      margin-right: 12px;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .step-name {\r\n      min-width: 80px;\r\n      font-weight: 500;\r\n      display: inline-block;\r\n      color: #303133;\r\n      margin-right: 8px;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n      max-width: 200px; /* 限制最大宽度，可根据实际情况调整 */\r\n    }\r\n\r\n    .step-detail-inline {\r\n      color: #606266;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n    }\r\n  }\r\n\r\n  .el-timeline-item:nth-child(odd) .step-row {\r\n    background-color: #f5f5fa;\r\n  }\r\n\r\n  .el-timeline-item:nth-child(even) .step-row {\r\n    background-color: #f5f5fa;\r\n  }\r\n\r\n  :deep(.el-timeline-item__node) {\r\n    margin-top: 10px;\r\n  }\r\n\r\n  :deep(.el-timeline-item__tail) {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./CaseDetailDialog.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./CaseDetailDialog.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./CaseDetailDialog.vue?vue&type=style&index=0&id=0bc3c0cc&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-0bc3c0cc\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from \"vue\"\n\nimport { computed } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TestStateTag',\n  props: {\n    state: {}\n  },\n  setup(__props: any) {\n\r\nconst props = __props;\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getTestStateName = (state: string): 'Not Run' | 'Running' | 'Completed' | 'Faulted' | 'Paused' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Completed';\r\n    case ExecutionState.Failure:\r\n      return 'Faulted';\r\n    case ExecutionState.Paused:\r\n      return 'Paused';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getTestStateName(props.state);\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n\n  return (_openBlock(), _createBlock(_component_el_tag, {\n    type: tagType.value,\n    size: \"small\"\n  }, {\n    default: _withCtx(() => [\n      _createTextVNode(_toDisplayString(stateName.value), 1)\n    ]),\n    _: 1\n  }, 8, [\"type\"]))\n}\n}\n\n})", "<template>\r\n  <el-tag :type=\"tagType\" size=\"small\">\r\n    {{ stateName }}\r\n  </el-tag>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, defineProps } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\nconst props = defineProps<{\r\n  state: string;\r\n}>();\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getTestStateName = (state: string): 'Not Run' | 'Running' | 'Completed' | 'Faulted' | 'Paused' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Completed';\r\n    case ExecutionState.Failure:\r\n      return 'Faulted';\r\n    case ExecutionState.Paused:\r\n      return 'Paused';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getTestStateName(props.state);\r\n});\r\n</script>\r\n", "import script from \"./TestStateTag.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TestStateTag.vue?vue&type=script&setup=true&lang=ts\"\n\nconst __exports__ = script;\n\nexport default __exports__"], "names": ["_defineComponent", "__name", "props", "state", "setup", "__props", "tagType", "computed", "ExecutionState", "Success", "Running", "Failure", "Pending", "getCaseStateName", "stateName", "_ctx", "_cache", "_component_el_tag", "_resolveComponent", "_openBlock", "_createBlock", "type", "value", "size", "style", "default", "_withCtx", "_createTextVNode", "_toDisplayString", "_", "__exports__", "isTesterCompleted", "tester", "processState", "BASE_URL", "interoperationApi", "startTest", "USE_MOCK", "mockApi", "interoperation", "axios", "post", "stopTest", "getStatus", "get", "_hoisted_1", "key", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "runStatus", "visible", "Boolean", "totalCount", "testResult", "completedCount", "successCount", "failureCount", "_component_el_icon", "_component_el_progress", "_createElementBlock", "_createElementVNode", "_createVNode", "_unref", "CircleCheckFilled", "CircleCloseFilled", "InfoFilled", "_createCommentVNode", "TestStateTag", "percentage", "Math", "round", "_hoisted_9", "_hoisted_10", "defineComponent", "name", "loading", "ref", "starting", "stopping", "hasEverStarted", "currentOperation", "id", "resultFolderName", "testType", "creationTime", "caseResults", "detailDialogVisible", "selectedCaseId", "testResultId", "statusPollingTimer", "isRunning", "displayedCases", "viewCaseDetail", "caseResult", "closeDetailDialog", "startTestExecution", "async", "ElMessage", "success", "fetchTestStatus", "startStatusPolling", "error", "console", "stopTestExecution", "response", "data", "stopStatusPolling", "window", "setInterval", "clearInterval", "onMounted", "then", "onUnmounted", "_component_el_button", "_component_el_scrollbar", "onClick", "disabled", "TestMonitor", "length", "height", "_Fragment", "_renderList", "item", "index", "sequenceName", "detail", "CaseStateTag", "plain", "$event", "ElEmpty", "description", "CaseDetailDialog", "caseResultId", "onClose", "getTimelineItemType", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "emits", "emit", "__emit", "dialogVisible", "caseData", "steps", "watch", "newValue", "loadCaseData", "caseResponse", "stepsResponse", "Promise", "all", "appApi", "getCaseResult", "getCaseSteps", "warning", "closeDialog", "formatMicroseconds", "microseconds", "seconds", "toFixed", "_component_el_skeleton", "_component_el_empty", "_component_el_timeline_item", "_component_el_timeline", "_component_el_dialog", "modelValue", "title", "width", "footer", "rows", "animated", "formatDateTime", "begin", "end", "parameter", "step", "hollow", "timestamp", "Completed", "getTestStateName", "Paused"], "sourceRoot": ""}