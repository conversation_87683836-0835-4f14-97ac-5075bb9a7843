namespace Alsi.Fuzz.Core.Service.Results
{
    public interface ITestResultWriterService
    {
        // Tester 和主程序都使用
        ResultContext ResultContext { get; }

        // Tester 和主程序都使用
        void Begin(TestResult testResult);
        // Tester 和主程序都使用
        void End();

        // Tester 和主程序都使用
        CaseResult[] GetCaseResults();

        // 仅在 Tester 中使用
        void AddOrUpdateCaseResult(CaseResult caseResult);

        void AddCaseSteps(CaseStep[] caseSteps);
        CaseStep[] GetCaseSteps();
    }
}
